﻿namespace Centerpoint.Common.Constants
{
    public static class SeverityConstant
    {

        public const string NotApplicable = "NOA";
        public const string NearMiss = "NEM";
        public const string LT15 = "LT1";
        public const string LT60 = "LT6";
        public const string LT12 = "L12";
        public const string LT48 = "L48";
        public const string LTOver48 = "LTO";

        public const string NMiss = "Near Miss";
        public const string To15Min = "Lost Time < 15 Minutes";
        public const string Bet15mTo60m = "15 Minutes < Lost Time  < 1 Hour";
        public const string Bet1To6 = "1 Hour < Lost Time  < 6 Hours";
        public const string Bet24To48 = "24 Hours < Lost Time < 48 Hours";
        public const string To48 = "Lost Time > 48 Hours";
        public const string Bet6To12 = "6 Hours < Lost Time  < 12 Hours";
        public const string Bet12To24 = "12 Hours < Lost Time  < 24 Hours";
        public const string NotAppl = "Not applicable";



        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {NotApplicable,"Not Applicable"},
                    {NearMiss,"Near Miss"},
                    {LT15,"LT < 15 mins"},
                    {LT60,"LT 15 - 60 mins"},
                    {LT12,"LT 1hr - 12hrs"},
                    {LT48,"LT 12hrs - 48hrs"},
                    {LTOver48,"LT > 48hrs"},
                };
            }
        }
    }
}
