﻿using Centerpoint.Extensions;
using Centerpoint.Model.ViewModels;
using Centerpoint.Service.Interfaces;
using Centerpoint.Services;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Text.Json;

namespace Centerpoint.Controllers
{
    [Authorize]
    public class HomeController : Controller
    {
        private readonly IIdentityService _identityService;
        private readonly ICurrentUserService _currentUser;
        private readonly ILessonService _lessonService;
        private readonly IProjectService _projectService;
        private readonly ICommentService _commentService;
        private readonly ISifService _sifService;
        private readonly IRiskService _riskService;
        private readonly ICertificateService _certificateService;
        private readonly ICrewService _crewService;
        private readonly IAdminService _adminService;

        public HomeController(IIdentityService identityService,
                               ICurrentUserService currentUser,
                               ILessonService lessonService,
                               IProjectService projectService,
                               ICommentService commentService,
                               ISifService sifService,
                               IRiskService riskService,
                               ICertificateService certificateService,
                               ICrewService crewService,
                               IAdminService adminService)
        {
            _identityService = identityService;
            _currentUser = currentUser;
            _lessonService = lessonService;
            _projectService = projectService;
            _commentService = commentService;
            _sifService = sifService;
            _riskService = riskService;
            _certificateService = certificateService;
            _crewService = crewService;
            _adminService = adminService;
        }

        public async Task<ActionResult> Index()
        {
            var viewModel = await _identityService.GetUserModelAsync(_currentUser.UserId);
            this.SetTitle(viewModel.Name);
            return View(viewModel);
        }

        #region User
        [HttpGet]
        public async Task<ActionResult> EditUser(int id, string tab)
        {
            var model = await _identityService.GetUserModelAsync(id);
            ViewBag.Tab = tab;

            return View(model);
        }

        [HttpPost]
        public async Task<ActionResult> EditUser(UserModel model)
        {
            if (!ModelState.IsValid)
            {
                // FIX ME: show errors
                return RedirectToAction("Index", new { @id = model.UserId, @tab = "personnel" });
            }
            model.RolesList = JsonSerializer.Deserialize<List<SelectListItem>>(model.Roles);
            await _adminService.EditUserDetails(model);

            return RedirectToAction("Index", new { @id = model.UserId, @tab = "personnel" });
        }

        #endregion

        #region Lessons

        public async Task<ActionResult> GetMyLessonsLearnedSummary()
        {
            var model = await _lessonService.GetMyLessonsLearnedSummaryAsync(_currentUser.UserId);

            return Json(model);
        }

        public async Task<ActionResult> GetMyLessons([DataSourceRequest] DataSourceRequest request, string type)
        {
            var model = await _lessonService.GetMyLessonsAsync(type, _currentUser.UserId);
            return Json(model.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetLessonsByServiceEquipment([DataSourceRequest] DataSourceRequest request, int? projectId)
        {
            if (projectId is 0)
                projectId = null;
            var model = await _lessonService.GetLessonsByServiceEquipmentAsync(projectId, _currentUser.UserId);
            return Json(model.ToDataSourceResult(request));
        }

        #endregion

        #region Projects

        public async Task<ActionResult> GetProjectsByUser([DataSourceRequest] DataSourceRequest request)
        {
            var model = await _projectService.GetProjectsByUserAsync(_currentUser.UserId);
            return Json(model.ToDataSourceResult(request));
        }

        #endregion

        #region Comments

        public async Task<ActionResult> GetCommentsByUser([DataSourceRequest] DataSourceRequest request)
        {
            var result = await _commentService.GetCommentsByUserAsync(_currentUser.UserId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetAllComments([DataSourceRequest] DataSourceRequest request)
        {
            var result = await _commentService.GetAllCommentsAsync();
            return Json(result.ToDataSourceResult(request));
        }
        #endregion

        #region RISC

        public async Task<ActionResult> GetMyRiscMonthlySummary(int month)
        {
            var result = await _riskService.GetMyRiscMonthlySummaryAsync(month, _currentUser.UserId);
            return Json(result);
        }

        public async Task<ActionResult> GetRiscChartData()
        {
            var result = await _riskService.GetRiscChartDataAsync(_currentUser.UserId);
            return Json(result);
        }

        public async Task<ActionResult> GetMyRiskIdentificationSafetyControls([DataSourceRequest] DataSourceRequest request, string type)
        {
            var result = await _riskService.GetMyRiskIdentificationSafetyControlsAsync(type, _currentUser.UserId);
            return Json(result.ToDataSourceResult(request));
        }

        #endregion

        #region SIF

        public async Task<ActionResult> GetMySifs()
        {
            var result = await _sifService.GetMySifsAsync(_currentUser.UserId);
            return Json(result);
        }

        public async Task<ActionResult> GetMyServiceImprovements([DataSourceRequest] DataSourceRequest request, string type)
        {
            var result = await _sifService.GetMyServiceImprovementsAsync(_currentUser.UserId, type);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetSifStatusChartData()
        {
            var result = await _sifService.GetSifStatusChartDataAsync(_currentUser.UserId);
            return Json(result);
        }

        #endregion

        #region Certificates 

        public async Task<ActionResult> GetMyCertificates()
        {
            var result = await _certificateService.GetMyCertificates(_currentUser.UserId);
            return Json(result);
        }

        public async Task<ActionResult> GetAllMyPersonnelCertificates([DataSourceRequest] DataSourceRequest request, int? month, int? year)
        {
            var result = await _certificateService.GetAllMyPersonnelCertificates(month, year, _currentUser.UserId);
            return Json(await result.ToDataSourceResultAsync(request));

        }

        [HttpPost]
        public async Task<ActionResult> EmailPersonnelCertificate(string emailAddress, int[] documentIds)
        {
            await _certificateService.EmailPersonnelCertificate(emailAddress, documentIds);

            this.SetMessage(MessageType.Success, $"The Personnel Certificates was sent to {emailAddress}");
            return Json(new { success = "true" });
        }

        #endregion

        #region Crew

        public async Task<ActionResult> GetMyCrewSummary(int month)
        {
            var result = await _crewService.GetMyCrewSummaryAsync(month, _currentUser.UserId);
            return Json(result);
        }

        #endregion

        #region Export

        [HttpPost]
        public ActionResult Export(string contentType, string base64, string fileName)
        {
            byte[] fileContents = Convert.FromBase64String(base64);

            return File(fileContents, contentType, fileName);
        }
        #endregion

        //public ActionResult CreateWordDocument()
        //{
        //    try
        //    {
        //        //Create an instance for word app
        //        Microsoft.Office.Interop.Word.Application winword = new Microsoft.Office.Interop.Word.Application();

        //        //Set animation status for word application
        //        winword.ShowAnimation = false;

        //        //Set status for word application is to be visible or not.
        //        winword.Visible = false;

        //        //Create a missing variable for missing value
        //        object missing = System.Reflection.Missing.Value;

        //        //Create a new document
        //        Microsoft.Office.Interop.Word.Document document = winword.Documents.Add(ref missing, ref missing, ref missing, ref missing);

        //        //Add header into the document
        //        foreach (Microsoft.Office.Interop.Word.Section section in document.Sections)
        //        {
        //            //Get the header range and add the header details.
        //            Microsoft.Office.Interop.Word.Range headerRange = section.Headers[Microsoft.Office.Interop.Word.WdHeaderFooterIndex.wdHeaderFooterPrimary].Range;
        //            headerRange.Fields.Add(headerRange, Microsoft.Office.Interop.Word.WdFieldType.wdFieldPage);
        //            headerRange.ParagraphFormat.Alignment = Microsoft.Office.Interop.Word.WdParagraphAlignment.wdAlignParagraphCenter;
        //            headerRange.Font.ColorIndex = Microsoft.Office.Interop.Word.WdColorIndex.wdBlue;
        //            headerRange.Font.Size = 10;
        //            headerRange.Text = "Header text goes here";
        //        }

        //        //Add the footers into the document
        //        foreach (Microsoft.Office.Interop.Word.Section wordSection in document.Sections)
        //        {
        //            //Get the footer range and add the footer details.
        //            Microsoft.Office.Interop.Word.Range footerRange = wordSection.Footers[Microsoft.Office.Interop.Word.WdHeaderFooterIndex.wdHeaderFooterPrimary].Range;
        //            footerRange.Font.ColorIndex = Microsoft.Office.Interop.Word.WdColorIndex.wdDarkRed;
        //            footerRange.Font.Size = 10;
        //            footerRange.ParagraphFormat.Alignment = Microsoft.Office.Interop.Word.WdParagraphAlignment.wdAlignParagraphCenter;
        //            footerRange.Text = "Footer text goes here";
        //        }

        //        //adding text to document
        //        document.Content.SetRange(0, 0);
        //        document.Content.Text = "This is test document " + Environment.NewLine;

        //        //Add paragraph with Heading 1 style
        //        Microsoft.Office.Interop.Word.Paragraph para1 = document.Content.Paragraphs.Add(ref missing);
        //        object styleHeading1 = "Heading 1";
        //        para1.Range.set_Style(ref styleHeading1);
        //        para1.Range.Text = "Para 1 text";
        //        para1.Range.InsertParagraphAfter();

        //        //Add paragraph with Heading 2 style
        //        Microsoft.Office.Interop.Word.Paragraph para2 = document.Content.Paragraphs.Add(ref missing);
        //        object styleHeading2 = "Heading 2";
        //        para2.Range.set_Style(ref styleHeading2);
        //        para2.Range.Text = "Para 2 text";
        //        para2.Range.InsertParagraphAfter();

        //        //Create a 5X5 table and insert some dummy record
        //        Table firstTable = document.Tables.Add(para1.Range, 5, 5, ref missing, ref missing);

        //        firstTable.Borders.Enable = 1;
        //        foreach (Row row in firstTable.Rows)
        //        {
        //            foreach (Cell cell in row.Cells)
        //            {
        //                //Header row
        //                if (cell.RowIndex == 1)
        //                {
        //                    cell.Range.Text = "Column " + cell.ColumnIndex.ToString();
        //                    cell.Range.Font.Bold = 1;
        //                    //other format properties goes here
        //                    cell.Range.Font.Name = "verdana";
        //                    cell.Range.Font.Size = 10;
        //                    //cell.Range.Font.ColorIndex = WdColorIndex.wdGray25;                            
        //                    cell.Shading.BackgroundPatternColor = WdColor.wdColorGray25;
        //                    //Center alignment for the Header cells
        //                    cell.VerticalAlignment = WdCellVerticalAlignment.wdCellAlignVerticalCenter;
        //                    cell.Range.ParagraphFormat.Alignment = WdParagraphAlignment.wdAlignParagraphCenter;

        //                }
        //                //Data row
        //                else
        //                {
        //                    cell.Range.Text = (cell.RowIndex - 2 + cell.ColumnIndex).ToString();
        //                }
        //            }
        //        }

        //        //Save the document
        //        object filename = Server.MapPath("~/App_Data/Downloads/" + "testDoc" + ".docx");
        //        document.SaveAs2(ref filename);
        //        document.Close(ref missing, ref missing, ref missing);
        //        document = null;
        //        winword.Quit(ref missing, ref missing, ref missing);
        //        winword = null;
        //    }
        //    catch (Exception ex)
        //    {

        //    }

        //    return RedirectToAction("Index");
        //}

        public async Task EmailThresholdPersonnelCertificates()
        {
            await _certificateService.EmailThresholdPersonnelCertificates();
        }

    }
}