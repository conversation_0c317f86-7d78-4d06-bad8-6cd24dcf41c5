﻿@model OpportunityEventModel


<div class="header-container-between">
    <h4>
        <i class="fa fa-calendar"></i>
        @(Model.OpportunityEventId.HasValue ? string.Format("Edit - {0} - {1}", Model.Name, Model.EventTypeName) : "Add New Event")
    </h4>
    <div class="actionsContainer">
       @if (Model.OpportunityId.HasValue) {
            <a class="btn btn-info btn-sm"  href="@Url.Action("Edit", "Sales", new { @id = Model.OpportunityId, @revision = ViewBag.Revision })"><i class="fa fa-refresh"></i>Go to Lead / Opportunity</a>
        }
        @if (ViewBag.OpportunityActionId != null) {
            <a class="btn btn-info btn-sm" href="@Url.Action("EditAction","Sales",new { @id = ViewBag.OpportunityActionId} )"><i class="fa fa-refresh"></i>Go to Action</a>
        } 
    </div>
</div>
<hr />

@using (Html.BeginForm("EditEvent", "Sales", new { @opportunityActionId = Model.OpportunityActionId, @revision = ViewBag.Revision })) {
    @Html.ValidationSummary(false)

    @(Html.Kendo().TabStrip()
    .Name("salesHistoryStrips")
    .SelectedIndex(0)
    .Animation(false)
    .Items( tabstrip => {

    tabstrip.Add().Text("")
        .HtmlAttributes(new { @data_bind = "html:tabStripHeaderDetails" })
        .Selected(true)
        .Content(@<text>

            <div id="details">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Lead / Opportunity</label>
                            <br />
                            @(Html.Kendo().DropDownListFor(a => a.OpportunityId)
                                            .Filter(FilterType.Contains)
                                            .OptionLabel("Select Lead / Opportunity")
                                            .DataTextField("Text")
                                            .DataValueField("Value")
                                            .Events(m => m.Change("opportunityChange").DataBound("opportunityDatabound"))
                                            .DataSource(source => {
                                                source.Read(read => {
                                                    read.Action("GetAllOpportunities", "Lookup").Data("opportunityData");
                                                });
                                            }))
                        </div>
                        <div class="form-group">
                            <label>Event Date </label>
                            <br />
                            @(Html.Kendo().DateTimePickerFor(s => s.EventDate).HtmlAttributes(new { @class = "utcTimePicker" }))
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Company</label>
                            <br />
                            @(Html.Kendo().DropDownListFor(a => a.CompanyId)
                                            .Filter(FilterType.Contains)
                                            .OptionLabel("Select Company")
                                            .DataTextField("Text")
                                            .DataValueField("Value")
                                            .Events(m => m.Change("companyChange").DataBound("companyDatabound"))
                                            .DataSource(source => {
                                                source.Read(read => {
                                                    read.Action("GetActionEventCompanies", "Lookup");
                                                });
                                            }))
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Event Type <span class="text-danger">*</span></label>
                            <br />
                            @(Html.Kendo().DropDownListFor(a => a.EventTypeId)
                                            .OptionLabel("Select Event Type")
                                            .DataTextField("Name")
                                            .DataValueField("EventTypeId")
                                            .Filter(FilterType.Contains)
                                            .DataSource(d => d.Read("GetEventTypes", "Lookup"))
                                            )
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group" data-bind="visible:opportunityId">
                            <label>Contact(s) <a data-bind="click:showContactWindow" style="cursor:pointer;" class="text-success"><i class="fa fa-plus" data-toggle="tooltip" title="Add New Contact"></i></a></label>
                            <br />
                            @(Html.Kendo().MultiSelectFor(m => m.CompanyContactIds)
                                    .Filter(FilterType.Contains)
                                    .DataTextField("Name")
                                    .DataValueField("CompanyContactId")
                                    .Placeholder("Select Contactss...")
                                    .HtmlAttributes(new { @id = "leadCompanyContacts" })
                                    .DataSource(source => {
                                        source.Read(read => {
                                            read.Action("GetLeadCompanyContactsByOpportunityCustomerCountry", "Lookup").Data("leadContactData");
                                        })
                                        .ServerFiltering(true);
                                    }))
                        </div>
                        <div class="form-group" data-bind="visible:companyId">
                            <label>Contact(s)<a data-bind="click:showContactWindow" style="cursor:pointer" class="text-success"><i class="fa fa-plus" data-toggle="tooltip" title="Add New Contact"></i></a></label>
                            <br />
                            @(Html.Kendo().MultiSelectFor(m => m.CompanyContactIds)
                                .Filter(FilterType.Contains)
                                .DataTextField("Text")
                                .DataValueField("Value")
                                .Placeholder("Select Contacts...")
                                .HtmlAttributes(new { @style = "font-size: 14px;", @id = "operatorCompanyContacts" })
                                .DataSource(source => {
                                    source.Read(read => {
                                        read.Action("GetContactsByCompanyId", "Lookup").Data("companyContactData");
                                    })
                                    .ServerFiltering(true);
                                }))
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Topic <span class="text-danger">*</span></label>
                            @(Html.TextAreaFor(p => p.Topic, new { @class = "k-textbox", @style = "width:100%", @rows = "5" }))
                        </div>
                    </div>
                </div>
                @Html.HiddenFor(m => m.OpportunityEventId)
                @Html.HiddenFor(m => m.OpportunityId)
                @Html.HiddenFor(m => m.OpportunityActionId)
                @Html.HiddenFor(m => m.Name)
                @Html.HiddenFor(m => m.Created)
                @Html.HiddenFor(m => m.CreatedBy)

                <input type="submit" class="btn btn-sm btn-primary mt-2" value="Save Details" />
            </div>

        </text>);

    if (Model.OpportunityEventId.HasValue) {

        tabstrip.Add().Text("")
            .HtmlAttributes(new { @data_bind="html:attachmentsStripText"})
            .Content(@<text>

                <div id="documents">
                    <br />
                    <p>Click the link below to attach documents</p>
                    @if (!Model.IsLeadOpportunityClosed) {
                        @(Html.Kendo().Upload()
                                .Name("opportunityEventDocuments")
                                .Messages(m => m.Select("Attach Event Documents"))
                                .Multiple(true)
                                .Events(e => e.Success("onOpportunityEventDocumentAttached").Complete("onOpportunityEventDocumentComplete").Upload("onOpportunityEventDocumentUpload"))
                                .HtmlAttributes(new { @style = "width:300px" })
                                .Async(async => async.Save("AttachOpportunityEventDocuments", "Sales", new { @id = Model.OpportunityEventId }).Batch(true)))
                        <br />
                    }
                    @(Html.Kendo().Grid<DocumentModel>()
                                .Name("opportunityEventDocumentsGrid")
                                .Columns(c => {
                                    c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                                    c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
                                    c.Bound(p => p.UserName).Title("Created By");
                                    c.Command(command => { 
                                            command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
                                        });
                                    })
                                        .Events(e => e.DataBound("updateOpportunityEventDocumentsGrid"))
                                        .Sortable()
                                        .Resizable(r => r.Columns(true))
                                        .ColumnMenu(c => c.Columns(true))
                                        .Filterable()
                                        .Groupable()
                                        .Editable(e => e.Mode(GridEditMode.InLine))
                                        .Scrollable(s => s.Height(300))
                                        .DataSource(dataSource => dataSource
                                            .Ajax()
                                            .ServerOperation(false)
                                            .Model(model => model.Id(p => p.DocumentId))
                                            .Read(read => read.Action("GetOpportunityEventDocuments", "Sales", new { @opportunityEventId = Model.OpportunityEventId }))
                                            .Destroy(destroy => destroy.Action("DeleteOpportunityEventDocument", "Sales", new { @opportunityEventId = Model.OpportunityEventId }))))
                </div>

            </text>);

        tabstrip.Add().Text("")
            .HtmlAttributes(new { @data_bind="html:followUpActionsStripText"})
            .Content(@<text>

                <div id="actions">
                    <br />
                    @(Html.Kendo().Grid<OpportunityActionModel>()
                            .Name("opportunityEventActionsGrid")
                            .Columns(c => {
                                c.Bound(p => p.NewName).Title("Action ID").ClientTemplate("<a href='" + @Url.Action("EditAction", "Sales", new { @id = "" }) + "/#=OpportunityActionId#?revision=" + ViewBag.Revision + "'>#=NewName#</a>");
                                c.Bound(p => p.AssignedUserName).Title("Assignee");
                                c.Bound(p => p.TargetDate).Title("Target Date").Format(DateConstants.DateFormat);
                                c.Bound(p => p.CompletedDate).Title("Completed Date").Format(DateConstants.DateFormat);
                                c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
                                c.Bound(p => p.CreatedBy).Title("Created By");
                                c.Bound(p => p.Status).Title("Status").ClientTemplate("<span class='badge' style='background:#=StatusColor#;color:#=StatusTextColor#'>#=Status#</span>");
                                c.Command(command => { 
                                        command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
                                    });
                                })   
                                .ToolBar(t => t.ClientTemplateId("opportunityEventActionsGridToolbar"))
                                .Events(e => e.DataBound("updateOpportunityEventActionsGrid"))
                                .Sortable()
                                .Resizable(r => r.Columns(true))
                                .ColumnMenu(c => c.Columns(true))
                                .Filterable()
                                .Groupable()
                                .Editable(e => e.Mode(GridEditMode.InLine))
                                .Scrollable(s => s.Height(300))
                                .DataSource(dataSource => dataSource
                                    .Ajax()
                                    .ServerOperation(false)
                                    .Model(model => model.Id(p => p.OpportunityActionId))
                                    .Read(read => read.Action("GetAllFollowupActionsByOpportunityEventId", "Sales", new { @opportunityEventId = Model.OpportunityEventId }))
                                    .Destroy(destroy => destroy.Action("DeleteOpportunityEventAction", "Sales", new { @opportunityEventId = Model.OpportunityEventId }))))
                </div>

            </text>);   
    }
    }))
}

@(Html.Kendo().Window().Name("contactWindow")
         .Title("Contact Details")
         .Events( e => e.Close("onWindowClose"))
         .Content(@<text>
    <form id="contactWindowForm">
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label>Location</label><br />
                    @(Html.Kendo().DropDownList()
                            .Name("companyLocationId")
                            .OptionLabel("Select Location")
                            .Filter(FilterType.Contains)
                            .DataTextField("Name")
                            .DataValueField("CompanyLocationId")
                            .Events( c=> c.Change("locationChange"))
                            .DataSource(source => {
                                               source.Read(read => {
                                                   read.Action("GetEventLocationsByCompanyId", "Lookup").Data("companyData");
                                               });
                                            })
                                            .AutoBind(false)
                            .HtmlAttributes(new { @data_value_primitive = "true", @style = "font-size: 14px;", @required="required" }))
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label>Not Active</label>
                    <br />
                    @(Html.Kendo().CheckBox().Name("NotActive"))
                </div>
            </div>
        </div>
        <hr />
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label>First Name</label>
                    <br />
                    @(Html.Kendo().TextBox().Name("FirstName")
                                     .HtmlAttributes(new { @class = "form-control", @required="required", @style = "width:100%; font-size: 14px;", tabindex = 1 }))
                </div>
                <div class="form-group">
                    <label>Position</label>
                    <br />
                    @(Html.Kendo().TextBox().Name("Position")
                                   .HtmlAttributes(new { @class = "form-control", @style = "width:100%; font-size: 14px;", tabindex = 3 }))
                </div>
                <div class="form-group">
                    <label>Mobile</label>
                    <br />
                    @(Html.Kendo().TextBox().Name("Mobile")
                                   .HtmlAttributes(new { @class = "form-control", @style = "width:100%; font-size: 14px;", tabindex = 5 }))
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label>Last Name</label>
                    <br />
                    @(Html.Kendo().TextBox().Name("LastName")
                                   .HtmlAttributes(new { @class = "form-control", @required="required", @style = "width:100%; font-size: 14px;", tabindex = 2 }))
                </div>
                <div class="form-group">
                    <label>Email Address</label>
                    <br />
                    @(Html.Kendo().TextBox().Name("EmailAddress")
                                   .HtmlAttributes(new { @class = "form-control", @style = "width:100%; font-size: 14px;", tabindex = 4 }))
                </div>
                <div class="form-group">
                    <label>Work Telephone</label>
                    <br />
                    @(Html.Kendo().TextBox().Name("WorkTelephone")
                                   .HtmlAttributes(new { @class = "form-control", @style = "width:100%; font-size: 14px;", tabindex = 6 }))
                </div>
            </div>
            <hr />  
            <div class="col-md-12">
                <div class="form-group">
                    <label>Comment</label>
                    @(Html.Kendo().TextArea().Name("Comment").HtmlAttributes( new { @class = "form-control", @style = "width:100%", @rows = "5", tabindex = 7 }))
                </div>
            </div>
        </div>
    </form>
    <button id="addContact" class="btn btn-success btn-sm">Add Contact</button>
    </text>)
                .Width(600)
                .Modal(true)
                .Visible(false))

<script id="opportunityEventActionsGridToolbar" type="text/x-kendo-tmpl">
<a class="btn btn-success" href="@Url.Action("AddAction", "Sales", new { @opportunityEventId = Model.OpportunityEventId, @opportunityId = Model.OpportunityId})">
    <i class="fa fa-plus"></i>Create New Follow-up Action
</a>
</script>

<script>
    const editEventModel = {
        modelCompanyId: "@Model.CompanyId",
        modelOpportunityId: "@Model.OpportunityId",
        modelOpportunityCustomerCompanyId: "@Model.OpportunityCustomerCompanyId",
        modelOpportunityCustomerCountry: "@Model.OpportunityCustomerCountry",
    }
</script>

<environment include="Development">
    <script src="~/js/views/sales/editEvent.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/sales/editEvent.min.js" asp-append-version="true"></script>
</environment>