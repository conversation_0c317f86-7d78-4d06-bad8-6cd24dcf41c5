﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
	  <Version>2.0.4.2</Version>
	  <AssemblyVersion>2.0.4.2</AssemblyVersion>
	  <FileVersion>2.0.4.2</FileVersion>
    <Nullable>disable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RunAnalyzersDuringBuild>True</RunAnalyzersDuringBuild>
    <AnalysisLevel>latest</AnalysisLevel>
    <PreserveCompilationContext>true</PreserveCompilationContext>
	<SatelliteResourceLanguages>en-GB</SatelliteResourceLanguages>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Views\Query\**" />
    <Compile Remove="wwwroot\css\**" />
    <Compile Remove="wwwroot\dist\**" />
    <Content Remove="Views\Query\**" />
    <Content Remove="wwwroot\css\**" />
    <Content Remove="wwwroot\dist\**" />
    <EmbeddedResource Remove="Views\Query\**" />
    <EmbeddedResource Remove="wwwroot\css\**" />
    <EmbeddedResource Remove="wwwroot\dist\**" />
    <None Remove="Views\Query\**" />
    <None Remove="wwwroot\css\**" />
    <None Remove="wwwroot\dist\**" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="Controllers\DownloadController.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Remove="Views\Emails\NewAccount.Html.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <None Remove=".editorconfig" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="BuildBundlerMinifier" Version="3.2.449" />
    <PackageReference Include="FluentEmail.Razor" Version="3.0.2" />
    <PackageReference Include="FluentEmail.SendGrid" Version="3.0.2" />
    <PackageReference Include="FluentEmail.Smtp" Version="3.0.2" />
    <PackageReference Include="Hangfire" Version="1.8.14" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.10" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.10">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="NLog.Extensions.Logging" Version="5.3.14" />
    <PackageReference Include="Telerik.UI.for.AspNet.Core" Version="2024.2.514" />

	</ItemGroup>
  <ItemGroup>
    <PackageReference Include="FluentEmail.Razor" Version="3.0.3" />
    <PackageReference Include="FluentEmail.SendGrid" Version="3.0.3" />
    <PackageReference Include="FluentEmail.Smtp" Version="3.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Views\Home\EditorTemplates\" />
    <Folder Include="wwwroot\doc\" />
    <Folder Include="wwwroot\lib\kendo\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Views\Company\Companies.cshtml" />
    <None Include="Views\Company\ViewCompany.cshtml" />
    <None Include="Views\Company\ViewCompanyWell.cshtml" />
    <None Include="Views\Download\Index.cshtml" />
    <None Include="Views\ExchangeRate\Index.cshtml" />
    <None Include="Views\Feedback\FeedbackConfirmation.cshtml" />
    <None Include="Views\Feedback\SubmitFeedback.cshtml" />
    <None Include="Views\Personnel\EditorTemplates\UserLocation.cshtml" />
    <None Include="Views\Personnel\Employees.cshtml" />
    <None Include="Views\Personnel\UserLocation.cshtml" />
    <None Include="Views\Portal\Index.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Centerpoint.Excel\Centerpoint.Excel.csproj" />
    <ProjectReference Include="..\Centerpoint.Service\Centerpoint.Service.csproj" />
    <ProjectReference Include="..\Centerpoint.Utils\Centerpoint.Utils.csproj" />
  </ItemGroup>
  <ItemGroup>
    <EditorConfigFiles Remove="C:\Working\centerpoint\Centerpoint\.editorconfig" />
  </ItemGroup>
  <ItemGroup>
    <EditorConfigFiles Remove="C:\Working\centerpoint\Centerpoint\.editorconfig" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Views\Emails\NewAccount.Html.cshtml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Content Update="wwwroot\doc\Centerpoint User Manual.pptx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\font\Raleway-Regular.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\img\favicon.ico">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\img\favicon.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\img\login-bg.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\img\logo-fav.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\img\logo-nav.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\img\logo-nav.svg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\img\logo.svg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\img\pdf\header_centerpoint_pdf.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\style\packingListPDF.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  
</ItemGroup>
  <ItemGroup>
    <None Update="QtBinaries\icudt49.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\icuin49.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\icuuc49.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\imageformats\qdds.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\imageformats\qgif.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\imageformats\qicns.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\imageformats\qico.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\imageformats\qjp2.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\imageformats\qjpeg.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\imageformats\qmng.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\imageformats\qsvg.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\imageformats\qtga.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\imageformats\qtiff.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\imageformats\qwbmp.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\imageformats\qwebp.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\libEGL.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\libGLESv2.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\platforms\qminimal.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\platforms\qoffscreen.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\platforms\qwindows.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\Qt5Core.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\Qt5Gui.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\Qt5Multimedia.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\Qt5MultimediaWidgets.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\Qt5Network.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\Qt5OpenGL.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\Qt5Positioning.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\Qt5PrintSupport.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\Qt5Qml.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\Qt5Quick.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\Qt5Sensors.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\Qt5Sql.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\Qt5Svg.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\Qt5WebChannel.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\Qt5WebKit.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\Qt5WebKitWidgets.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\Qt5Widgets.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\Syncfusion.WebKitWrapper">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="QtBinaries\zlib1.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>