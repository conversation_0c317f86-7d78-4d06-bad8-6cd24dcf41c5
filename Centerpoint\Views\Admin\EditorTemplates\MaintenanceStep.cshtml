﻿@model EquipmentCategoryMaintenanceStepModel
<div class="row">
    <div class="col-md-5">
        <div class="form-group">
            <label> Maintenance Blueprint</label>
            <br />
            @(Html.Kendo()
                .DropDownListFor(m => m.MaintenanceBlueprintId)
                .DataValueField("MaintenanceBlueprintId")
                .DataTextField("Name")
                .Filter("contains")
                .OptionLabel("Select Maintenance Blueprint")
                .HtmlAttributes(new { @style = "width:100%", @data_value_primitive = "true" })
                .DataSource(dataSource => dataSource.Read(read => read.Action("GetAllMaintenanceBlueprintsByCategoryId", "Lookup").Data("equipmentCategoryMaintenanceStepData"))))
        </div>
    </div>
    <div class="col-md-5">
        <div class="form-group">
            <label>Run Enabled</label>
            <br />
            @(Html.<PERSON>do().DropDownListFor(m => m.RunEnabled)
                .DataValueField("Key")
                .DataTextField("Value")
                .Filter("contains")
                .Events(e =>
                {
                    e.Change("runEnabledChanged");
                })
                .BindTo(new List<KeyValuePair<string, string>> {
                    new KeyValuePair<string,string>("false", "No"),
                    new KeyValuePair<string,string>("true", "Yes")
                })
                .HtmlAttributes(new { @style = "width:100%", @data_value_primitive = "true" }))
        </div>
    </div>
</div>

