﻿namespace Centerpoint.Common.Constants
{
    public static class ViewReportConstant
    {
        public static string GetViewName(string paperworkType)
        {
            switch (paperworkType)
            {
                case EquipmentShipmentPaperworkConstant.CommercialInvoice:
                    return "_CommercialInvoice";
                case EquipmentShipmentPaperworkConstant.DeliveryNote:
                    return "_DeliveryNote";
                case EquipmentShipmentPaperworkConstant.MaintenanceSchedule:
                    return "_MaintenanceSchedule";
                default:
                    throw new ArgumentException("Paperwork Type is not presented.");
            }
        }
    }
}
