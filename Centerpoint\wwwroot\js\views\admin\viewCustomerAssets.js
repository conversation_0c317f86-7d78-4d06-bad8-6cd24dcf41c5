$(document).ready(function () {
    var companyGrid = $('#companyGrid').data("kendoGrid");
    var companyWellGrid = $('#companyWellGrid').data("kendoGrid");

    companyGrid.bind('dataBound', function (e) {
        this.element.find('.k-i-excel').remove();
    });
    companyWellGrid.bind('dataBound', function (e) {
        this.element.find('.k-add').remove();
        this.element.find('.k-i-excel').remove();
    });
});
function onEdit(e) {
    //on row edit replace the Delete and Edit buttons with Update and Cancel
    $(e.container).find("td:last").html("<a href='javascript: void(0)' class='btn btn-success btn-sm' onclick='updateFieldRow()' title='update button'><i class='fa fa-check-circle'></i>Update</a> " +
        "<a href='javascript: void(0)' class='btn btn-warning btn-sm' onclick='cancelFieldRow()' title='cancel button'><i class='fa fa-ban'></i>Cancel</a>");
}

function cancelFieldRow() {
    grid = $("#companyFieldGrid").data("kendoGrid");
    grid.cancelRow();
}

function editFieldRow(element) {
    grid = $("#companyFieldGrid").data("kendoGrid");
    grid.editRow($(element).closest("tr"));
}

function updateFieldRow() {
    grid = $("#companyFieldGrid").data("kendoGrid");
    grid.saveRow();
}

function deleteFieldRow(element) {
    grid = $("#companyFieldGrid").data("kendoGrid");
    grid.removeRow($(element).closest("tr"));
}
function createFieldRow() {
    grid = $("#companyFieldGrid").data("kendoGrid");
    grid.addRow();
}

function onEditWell(e) {
    //on row edit replace the Delete and Edit buttons with Update and Cancel
    $(e.container).find(".k-edit-buttons").html("<a class='btn btn-primary btn-sm k-grid-update' href='#'>Update</a> " +
       "<a class='btn btn-primary btn-sm k-grid-cancel' href='#'>Cancel</a>");
}

function cancelWellRow() {
    grid = $("#companyWellGrid").data("kendoGrid");
    grid.cancelRow();
}

function editWellRow(element) {
    grid = $("#companyWellGrid").data("kendoGrid");
    grid.editRow($(element).closest("tr"));
}

function updateWellRow() {
    grid = $("#companyWellGrid").data("kendoGrid");
    grid.saveRow();
}

function deleteWellRow(element) {
    grid = $("#companyWellGrid").data("kendoGrid");
    grid.removeRow($(element).closest("tr"));
}

function createWellRow() {
    grid = $("#companyWellGrid").data("kendoGrid");
    grid.addRow();
}

function updateCompanyFieldTotal() {
    var companyFieldGrid = $("#companyFieldGrid").data("kendoGrid");
    var totalCompanyFields = companyFieldGrid.dataSource.total();
    viewModel.set("totalCompanyFields", totalCompanyFields);
}

function updateCompanyWellTotal() {
    var companyWellGrid = $("#companyWellGrid").data("kendoGrid");
    var totalCompanyWells = companyWellGrid.dataSource.total();
    var data = this.dataSource.view();
    for (var i = 0; i < data.length; i++) {
        var dataItem = data[i];
        var companyWellId = dataItem.CompanyWellId
        viewModel.set("companyWellId", companyWellId);
    }
    viewModel.set("totalCompanyWells", totalCompanyWells);
}

function onWellDocumentAttached() {
    var wellDocumentsGrid = $("#wellDocumentsGrid").data("kendoGrid");
    wellDocumentsGrid.dataSource.read();
}

function onWellDocumentUpload(e) {
    uploadValidation(e);

    var companyWellId = viewModel.get("companyWellId");
    e.data = { companyWellId: companyWellId };
    $(".k-upload-files.k-reset").show();
}

function onWellDocumentComplete(e) {
    $(".k-upload-files.k-reset").find("li").remove();
    $(".k-upload-files.k-reset").slideUp();
}

function updateWellDocumentsGrid() {
    var wellDocumentsGrid = $("#wellDocumentsGrid").data("kendoGrid");
    var totalWellDocuments = wellDocumentsGrid.dataSource.total();
    viewModel.set("totalWellDocuments", totalWellDocuments);
}

function companyWellData() {
    return {
        companyWellId: viewModel.get("companyWellId"),
    };
}

function onError(e, status) {
    if (e.status == "customerror") {
        alert(e.errors);

        var companyGrid = $("#companyGrid").data("kendoGrid");
        companyGrid.dataSource.cancelChanges();
    }
}

function updateCompanyTotals() {
    var companyGrid = $("#companyGrid").data("kendoGrid");
    var totalCompanies = companyGrid.dataSource.total();
    viewModel.set("totalCompanies", totalCompanies);
}

function companyContactEdit(e) {
    $(e.container).find(".k-edit-buttons").html("<a class='btn btn-primary btn-sm k-grid-update' href='#'>Update</a> " +
       "<a class='btn btn-primary btn-sm k-grid-cancel' href='#'>Cancel</a>");
}

function offshoreChanged(e) {
    $(e.container).find(".k-edit-buttons").html("<a class='btn btn-primary btn-sm k-grid-update' href='#'>Update</a> " +
       "<a class='btn btn-primary btn-sm k-grid-cancel' href='#'>Cancel</a>");

    var isOffshore = $("#IsOffshore").data("kendoDropDownList");
    var isOffshoreValue = isOffshore.value();

    if (isOffshoreValue == "true") {
        $("#Street").removeAttr("data-val-required");
        $("#City").removeAttr("data-val-required");
    } else {
        $("#Street").attr("data-val-required", "Address Line 1 is required");
        $("#City").attr("data-val-required", "Address Line 4 is required");
    }
}

var viewModel = new kendo.observable({
    totalcompanies: 0,
    totalcompanyLocations: 0,
    totalcompanyContacts: 0,
    totalCompanyFields: 0,
    totalCompanyWells: 0,
    totalWellDocuments: 0,
    companyWellId : 0
});
kendo.bind(document.body.children, viewModel);