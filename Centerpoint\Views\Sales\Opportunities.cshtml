﻿@model SalesDashboardModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-lightbulb"></i>
        Opportunities
        (<span data-bind="text:totalOpportunities"></span>)
    </h4>
</div>
<hr />

@* to do we could not find navigation to visual of this page *@
<div>
    @(Html.Kendo().Grid<OpportunityModel>()
        .Name("opportunitiesGrid")
        .Columns(columns => {
            columns.Bound(c => c.GridName).Title("Opportunity ID").ClientTemplate("<a href='" + @Url.Action("Edit", "Sales") + "/#=LinkOpportunityId#?revision=#=MaxRevision#'>#=GridName#</a>");
            columns.Bound(c => c.CompanyName).Title("Operator").ClientTemplate("#if(CompanyName){#<a href='" + @Url.Action("EditCompany", "Admin") + "/#=CompanyId#'>#=CompanyName#</a>#}else{#N/A#}#").Hidden(true);
            columns.Bound(c => c.PartnerCompanyName).Title("Partner").ClientTemplate("#if(PartnerCompanyName){#<a href='" + @Url.Action("EditCompany", "Admin") + "/#=PartnerCompanyId#'>#=PartnerCompanyName#</a>#}else{#N/A#}#").Hidden(true);
            columns.Bound(c => c.CustomerCompanyName).Title("Customer").ClientTemplate("<a href='" + @Url.Action("EditCompany", "Admin") + "/#=CustomerCompanyId#'>#=CustomerCompanyName#</a>");
            columns.Bound(c => c.ProjectName).Title("Project").ClientTemplate("#if(ProjectName){#<a href='" + @Url.Action("EditProject", "Operation") + "/#=ProjectId#'>#=ProjectName##} else{#N/A#}#</a>");
            columns.Bound(c => c.Services).Title("Service").Hidden(true);
            columns.Bound(c => c.Description).Title("Description").Hidden(true);
            columns.Bound(c => c.Comments).Title("Comments").Hidden(true);
            columns.Bound(c => c.OpportunityObjectives).Title("Objectives").Hidden(true);
            columns.Bound(c => c.CompanyFields).Title("Fields").ClientTemplate("#=CompanyFields ? CompanyFields : 'N/A'#");
            columns.Bound(c => c.CompanyWells).Title("Wells").ClientTemplate("#=CompanyWells ? CompanyWells : 'N/A'#");
            columns.Bound(c => c.RigTypeDescription).Title("Rig Type").ClientTemplate("#=RigTypeDescription ? RigTypeDescription : 'N/A'#").Hidden(true);
            columns.Bound(c => c.CurrencyValue).Title("Currency").ClientTemplate("#=CurrencyValue ? CurrencyValue : 'N/A'#").Hidden(true);
            columns.Bound(c => c.Value).Title("Value").Format("{0:n2}").Hidden(true);
            columns.Bound(c => c.Probability).Title("Probability Value").Format("{0:n2}").Hidden(true);
            columns.Bound(c => c.Conveyances).Title("Conveyance").ClientTemplate("#=Conveyances ? Conveyances : 'N/A'#");
            columns.Bound(c => c.CloseoutDate).Title("Closeout").Format(DateConstants.DateFormat);
            columns.Bound(c => c.MobilisationDate).Title("Mobilisation").Format(DateConstants.DateTimeFormat);
            columns.Bound(c => c.Revision).Title("Revision").Hidden(true);
            columns.Bound(c => c.Contacts).Title("Contacts");
            columns.Bound(c => c.StageDescription).Title("Stage").ClientTemplate("<span class='badge' style='background:#=StageColour#;color:#=StagetextColour#'>#=StageDescription#</span>");
            columns.Bound(c => c.TotalOpportunityAttachments).Title("Attachments").ClientTemplate("#if(TotalOpportunityAttachments){#<a class='badge' style='background:\\#0073D0;color:\\#fff' href='\\#' onclick='opportunityAttachmentCount(#=LinkOpportunityId#,#=OpportunityId#)'>#=TotalOpportunityAttachments#</a>#} else {##=TotalOpportunityAttachments##}#");
            columns.Bound(c => c.OpportunityEventCount).Title("Events").ClientTemplate("#if(OpportunityEventCount){#<a class='badge' style='background:\\#0073D0;color:\\#fff' href='\\#' onclick='opportunityEventCount(#=LinkOpportunityId#)'>#=OpportunityEventCount#</a>#} else {##=OpportunityEventCount##}#");
            columns.Bound(c => c.OpportunityActionCount).Title("Actions").ClientTemplate("#if(OpportunityActionCount && IsActionOverdue){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else if(OpportunityActionCount && IsActionUpcoming){#<a class='badge' style='background:\\#F7A54A;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else if(OpportunityActionCount && IsActionRegistered){#<a class='badge' style='background:\\#e5e500 ;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else if(OpportunityActionCount && IsActionCompleted) {#<a class='badge' style='background:\\#7CBB00;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else {##=OpportunityActionCount##}#");
            columns.Bound(c => c.Created).Title("Created Date").Hidden(true).Format(DateConstants.DateTimeFormat);
            columns.Bound(c => c.CreatedByUserName).Title("Owner").Hidden(true);
        })
        .ToolBar(t => {
            t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetOpportunitiesGrid", @class="bg-danger text-white"});
        }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
        .ColumnMenu(c => c.Columns(true))
        .Sortable()
        .Groupable()
        .Filterable()
        .Reorderable(c => c.Columns(true))
        .Resizable(c => c.Columns(true))
        .Scrollable(s => s.Height("auto"))
        .Events(e => e.DataBound("updateOpportunityGrid").ColumnReorder("saveOpportunityGrid").ColumnResize("saveOpportunityGrid").ColumnShow("saveOpportunityGrid").ColumnHide("saveOpportunityGrid").Filter("saveOpportunityGrid").Sort("saveOpportunityGrid").Group("saveOpportunityGrid"))
        .Excel(excel => excel
        .FileName(string.Format("Centerpoint_Sales_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
        .Filterable(true)
        .ProxyURL(Url.Action("Export", "Admin")))
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(model => {
                model.Id(m => m.OpportunityId);
            })
            .Read(read => read.Action("GetAllOpportunities", "Sales")))
    )
</div>
@(Html.Kendo().Window().Name("customerWindow")
 .Title("Customers")
 .Content(@<text>@Html.Partial("OpportunityCustomer")</text>)
 .Width(500)
 .Modal(true)
 .Draggable()
 .Visible(false))
@(Html.Kendo().Window().Name("closedReasonWindowOpen")
 .Title("Reason for closure")
 .Content(@<text>@Html.Partial("ClosureReason")</text>)
 .Width(600)
 .Modal(true)
 .Visible(false))
@(Html.Kendo().Window()
.Name("opportunityEventWindow")
.Width(1000)
.Height(500)
.Title("Events")
.Visible(false)
.Modal(true)
.Events(e => e.Open("opportunityEventWindowOpened"))
.Content(@<text>
        @(Html.Kendo().Grid<OpportunityEventModel>()
        .Name("opportunityEventsGrid")
        .Columns(c => {
            c.Bound(p => p.NewName).Title("Event ID").ClientTemplate("<a href='" + @Url.Action("EditEvent", "Sales") + "/#=OpportunityEventId#?revision=#=MaxRevision#'>#=NewName#</a>"); ;
            c.Bound(p => p.OpportunityName).Title("Lead / Opportunity ID").ClientTemplate("<a href='" + @Url.Action("Edit", "Sales") + "/#=OpportunityId#?revision=#=MaxRevision#'>#=OpportunityName#</a>");
            c.Bound(p => p.GridCompanyName).Title("Company");
            c.Bound(p => p.CompanyContacts).Title("Contact(s)");
            c.Bound(p => p.TrimmedTopic).Title("Topic").ClientTemplate("#if(IsTopicLength){#<a href='\\#' onclick=\"showEventTopic()\">#=TrimmedTopic#</a>#} else {##=Topic##}#");
            c.Bound(p => p.EventDate).Title("Event Date").Format(DateConstants.DateTimeFormat);
            c.Bound(p => p.TotalEventActionCount).Title("Follow-up Actions").ClientTemplate("#if(TotalEventActionCount){#<a class='badge' style='background:\\#0073D0;color:\\#fff' href='" + @Url.Action("EditEvent", "Sales") + "/#=OpportunityEventId#?tab=actions'>#=TotalEventActionCount#</a>#} else {##=TotalEventActionCount##}#");
            c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
            c.Bound(p => p.CreatedBy).Title("Created By");
        })
        .Events(e => e.DataBound("updateOpportunityEventsGrid"))
        .Sortable()
        .Resizable(r => r.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .Filterable()
        .Groupable()
        .Scrollable(s => s.Height(400))
        .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Model(model => model.Id(p => p.OpportunityEventId))
        .Read(read => read.Action("GetAllEventsByOpportunityId", "Sales").Data("opportunityData"))))
</text>
         ))
@(Html.Kendo().Window()
.Name("opportunityActionWindow")
.Width(1000)
.Height(500)
.Title("Actions")
.Visible(false)
.Modal(true)
.Events(e => e.Open("opportunityActionWindowOpened"))
.Content(@<text>
        @(Html.Kendo().Grid<OpportunityActionModel>()
        .Name("opportunityActionsGrid")
        .Columns(c => {
            c.Bound(p => p.NewName).Title("Action ID").ClientTemplate("<a href='" + @Url.Action("EditAction", "Sales", new { @id = "" }) + "/#=OpportunityActionId#?revision=#=MaxRevision#'>#=NewName#</a>"); ;
            c.Bound(p => p.OpportunityName).Title("Lead / Opportunity ID").ClientTemplate("<a href='" + @Url.Action("Edit", "Sales") + "/#=OpportunityId#?revision=#=MaxRevision#'>#=OpportunityName#</a>");
            c.Bound(p => p.GridCompanyName).Title("Company");
            c.Bound(p => p.AssignedUserName).Title("Assignee");
            c.Bound(p => p.TargetDate).Title("Target Date").Format(DateConstants.DateFormat);
            c.Bound(p => p.CompletedDate).Title("Completed Date").Format(DateConstants.DateFormat);
            c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
            c.Bound(p => p.CreatedBy).Title("Created By");
            c.Bound(p => p.Status).Title("Status").ClientTemplate("<span class='badge' style='background:#=StatusColor#;color:#=StatusTextColor#'>#=Status#</span>");
        })
        .Events(e => e.DataBound("updateOpportunityActionsGrid"))
        .Sortable()
        .Resizable(r => r.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .Filterable()
        .Groupable()
        .Scrollable(s => s.Height(400))
        .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Aggregates(aggregates => {
            aggregates.Add(p => p.OpportunityCompanyName).Min().Max().Count();
        })
        .Model(model => model.Id(p => p.OpportunityActionId))
        .Read(read => read.Action("GetAllActionsByOpportunityId", "Sales").Data("opportunityData"))))
</text>
         ))
@(Html.Kendo().Window()
.Name("followUpActionWindow")
.Width(1000)
.Height(500)
.Title("Follow-up Actions")
.Visible(false)
.Modal(true)
.Events(e => e.Open("followUpActionWindowOpened"))
.Content(@<text>
        @(Html.Kendo().Grid<OpportunityActionModel>()
        .Name("followUpActionsGrid")
        .Columns(c => {
            c.Bound(p => p.NewName).Title("Action ID").ClientTemplate("<a href='" + @Url.Action("EditAction", "Sales", new { @id = "" }) + "/#=OpportunityActionId#?revision=#=MaxRevision#'>#=NewName#</a>"); ;
            c.Bound(p => p.OpportunityName).Title("Lead / Opportunity ID").ClientTemplate("<a href='" + @Url.Action("Edit", "Sales") + "/#=OpportunityId#?revision=#=MaxRevision#'>#=OpportunityName#</a>");
            c.Bound(p => p.GridCompanyName).Title("Company");
            c.Bound(p => p.AssignedUserName).Title("Assignee");
            c.Bound(p => p.TargetDate).Title("Target Date").Format(DateConstants.DateFormat);
            c.Bound(p => p.CompletedDate).Title("Completed Date").Format(DateConstants.DateFormat);
            c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
            c.Bound(p => p.CreatedBy).Title("Created By");
            c.Bound(p => p.Status).Title("Status").ClientTemplate("<span class='badge' style='background:#=StatusColor#;color:#=StatusTextColor#'>#=Status#</span>");
        })
        .Events(e => e.DataBound("updateFollowUpActionsGrid"))
        .Sortable()
        .Resizable(r => r.Columns(true))
        .Filterable()
        .ColumnMenu(c => c.Columns(true))
        .Groupable()
        .Scrollable(s => s.Height(400))
        .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Aggregates(aggregates => {
            aggregates.Add(p => p.OpportunityCompanyName).Min().Max().Count();
        })
        .Model(model => model.Id(p => p.OpportunityActionId))
        .Read(read => read.Action("GetAllFollowupActionsByOpportunityEventId", "Sales").Data("opportunityEventData"))))
</text>
         ))
@(Html.Kendo().Window()
.Name("opportunityAttachmentWindow")
.Width(1000)
.Title("Attachments")
.Visible(false)
.Modal(true)
.Events(e => e.Open("opportunityAttachmentWindowOpened"))
.Content(@<text>
        <ul class="nav nav-tabs" id="opportunityAttachmentWindowTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="pill1-tab" data-bs-toggle="tab" data-bs-target="#pill1" type="button" role="tab" aria-controls="pill1" aria-selected="true">
                    General (<span data-bind="text:totalOpportunityDocuments"></span>)
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="pill2-tab" data-bs-toggle="tab" data-bs-target="#pill2" type="button" role="tab" aria-controls="pill2" aria-selected="false">
                    Events (<span data-bind="text:totalOpportunityEventDocuments"></span>)
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="pill3-tab" data-bs-toggle="tab" data-bs-target="#pill3" type="button" role="tab" aria-controls="pill3" aria-selected="false">
                    Actions (<span data-bind="text:totalOpportunityActionDocuments"></span>)
                </button>        
            </li>
            <li class="nav-item" role="presentation" data-bind="visible:totalOpportunityWellDocuments">
                <button class="nav-link" id="pill4-tab" data-bs-toggle="tab" data-bs-target="#pill4" type="button" role="tab" aria-controls="pill4" aria-selected="false">
                    Wells (<span data-bind="text:totalOpportunityWellDocuments"></span>)
                </button>  
            </li>
        </ul>
        <div class="tab-content" id="opportunityAttachmentWindowTab">
            <div class="tab-pane show active" id="pill1" role="tabpanel" aria-labelledby="pill1-tab">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"></h6>
                    </div>
                    <div class="card-body">
                        @(Html.Kendo().Grid<DocumentModel>()
                            .Name("opportunityDocumentsGrid")
                            .Columns(c => {
                                c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                                c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
                                c.Bound(p => p.UserName).Title("Created By");
                            })
                            .Sortable()
                            .Events(e => e.DataBound("updateOpportunityDocumentsGrid"))
                            .Resizable(r => r.Columns(true))
                            .ColumnMenu(c => c.Columns(true))
                            .Filterable()
                            .Groupable()
                            .Scrollable(s => s.Height(300))
                            .DataSource(dataSource => dataSource
                            .Ajax()
                            .ServerOperation(false)
                            .Model(model => model.Id(p => p.DocumentId))
                            .Read(read => read.Action("GetOpportunityDocuments", "Sales").Data("opportunityData"))))
                    </div>
                </div>
            </div>
            <div class="tab-pane fade" id="pill2" role="tabpanel" aria-labelledby="pill2-tab">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"></h6>
                    </div>
                    <div class="card-body">
                        @(Html.Kendo().Grid<OpportunityEventDocumentModel>()
                            .Name("opportunityEventDocumentsGrid")
                            .Columns(c => {
                                c.Bound(p => p.Event).Title("Event").ClientGroupHeaderTemplate("Event : #= value # (#= count#)").Width(200).Hidden(true);
                                c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                                c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateFormat);
                                c.Bound(p => p.Username).Title("Created By");
                            })
                            .Sortable()
                            .Events(e => e.DataBound("updateOpportunityEventDocumentsGrid"))
                            .Resizable(r => r.Columns(true))
                            .ColumnMenu(c => c.Columns(true))
                            .Editable(e => e.Mode(GridEditMode.InLine))
                            .Filterable()
                            .Groupable()
                            .Scrollable(s => s.Height(300))
                            .DataSource(dataSource => dataSource
                            .Ajax()
                            .ServerOperation(false)
                            .Aggregates(aggregates => {
                                aggregates.Add(p => p.Event).Min().Max().Count();
                            })
                            .Group(group => group.Add(p => p.Event))
                            .Model(model => model.Id(p => p.DocumentId))
                            .Read(read => read.Action("GetOpportunityEventDocumentsByOpportunityId", "Sales").Data("opportunityData"))))
                    </div>
                </div>
            </div>
            <div class="tab-pane fade" id="pill3" role="tabpanel" aria-labelledby="pill3-tab">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"></h6>
                    </div>
                    <div class="card-body">
                        @(Html.Kendo().Grid<OpportunityActionDocumentModel>()
                            .Name("opportunityActionDocumentsGrid")
                            .Columns(c => {
                                c.Bound(p => p.Action).Title("Action").ClientGroupHeaderTemplate("Action : #= value # (#= count#)").Width(200).Hidden(true);
                                c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                                c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateFormat);
                                c.Bound(p => p.Username).Title("Created By");
                            })
                                .Events(e => e.DataBound("updateOpportunityActionDocumentsGrid"))
                                .Editable(e => e.Mode(GridEditMode.InLine))
                                .Sortable()
                                .Resizable(r => r.Columns(true))
                                .ColumnMenu(c => c.Columns(true))
                                .Filterable()
                                .Groupable()
                                .Scrollable(s => s.Height(300))
                                .DataSource(dataSource => dataSource
                                    .Ajax()
                                    .ServerOperation(false)
                                    .Aggregates(aggregates => {
                                        aggregates.Add(p => p.Action).Min().Max().Count();
                                    })
                                    .Group(group => group.Add(p => p.Action))
                                    .Model(model => model.Id(p => p.DocumentId))
                                    .Read(read => read.Action("GetOpportunityActionDocumentsByOpportunityId", "Sales").Data("opportunityData"))))
                    </div>
                </div>
            </div>
            <div class="tab-pane fade" id="pill4" data-bind="visible:totalOpportunityWellDocuments" role="tabpanel" aria-labelledby="pill4-tab">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"></h6>
                    </div>
                    <div class="panel-body">
                        @(Html.Kendo().Grid<CompanyWellDocumentModel>()
                            .Name("wellDocumentsGrid")
                            .Columns(c => {
                                c.Bound(p => p.CompanyWellName).Title("Well").ClientGroupHeaderTemplate("Well : #= value # (#= count#)").Width(200).Hidden(true);
                                c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                                c.Bound(p => p.CompanyWellDocumentTypeDescription).Title("Type").ClientTemplate("#=CompanyWellDocumentTypeDescription ? CompanyWellDocumentTypeDescription : 'N/A'#");
                                c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateFormat);
                                c.Bound(p => p.Username).Title("Created By");
                            })
                            .Events(e => e.DataBound("updateWellDocumentsGrid"))
                            .Sortable()
                            .Resizable(r => r.Columns(true))
                            .ColumnMenu(c => c.Columns(true))
                            .Filterable()
                            .Groupable()
                            .Scrollable(s => s.Height(300))
                            .DataSource(dataSource => dataSource
                            .Ajax()
                            .ServerOperation(false)
                            .Aggregates(aggregates => {
                                aggregates.Add(p => p.CompanyWellName).Min().Max().Count();
                            })
                            .Group(group => group.Add(p => p.CompanyWellName))
                            .Model(model => model.Id(p => p.CompanyWellDocumentId))
                            .Read(read => read.Action("GetCompanyWellDocumentByOpportunityId", "Sales").Data("linkOpportunityData"))))
                    </div>
                </div>
            </div>
        </div>
</text>
         ))

    <script type="text/javascript">
        function format(value) {
            return value.substring(0, 100);
        }
        function getTheSubstring(value, length) {
            if (value.length > length)
                return kendo.toString(value.substring(0, length)) + "...";
            else return kendo.toString(value);
        }
        function showTopic() {
            var grid = $("#eventsGrid").data("kendoGrid");
            var model = grid.dataItem($(event.target).closest("tr"));
            var topic = model.Topic;
            swal({ html: true, title: '<i>Topic Details</i>', text: '<b> ' + topic + ' </b>' });
        }
        function showEventTopic() {
            var grid = $("#opportunityEventsGrid").data("kendoGrid");
            var model = grid.dataItem($(event.target).closest("tr"));
            var topic = model.Topic;
            swal({ html: true, title: '<i>Topic Details</i>', text: '<b> ' + topic + ' </b>' });
        }
    </script>
    <script>
        $(document).ready(function () {
            loadOpportunitiesGrid();
        });

        function deleteCustomer(companyId) {
            $.ajax({
                type: 'POST',
                cache: false,
                dataType: 'json',
                url: '@Url.Action("DeleteFocusCustomer", "Sales")',
                data: {
                    customerId: companyId,
                },
                success: function (result) {
                    window.location.reload();
                },
            });
        }

        $("#confirmCustomer").click(function () {
            $.ajax({
                type: 'POST',
                cache: false,
                dataType: 'json',
                url: '@Url.Action("AddFocusCustomer", "Sales")',
                data: {
                    customerId: viewModel.get("customerId"),
                },
                success: function (result) {
                    window.location.reload();
                },
            });
        });

        function onDB(e) {
            e.sender.options.series[0].labels.visible = function (point) {
                if (point.value < 0) {
                    return false
                }
                else {
                    return point.value
                }
            }
        }

        function opportunityData() {
            return {
                opportunityId: viewModel.get("opportunityId")
            }
        }

        function linkOpportunityData() {
            return {
                opportunityId: viewModel.get("linkOpportunityId")
            }
        }

        function opportunityEventData() {
            return {
                opportunityEventId: viewModel.get("opportunityEventId")
            }
        }

        function opportunityEventCount(opportunityId) {
            viewModel.set("opportunityId", opportunityId);

            $("#opportunityEventWindow").data("kendoWindow").center().open();
        }

        function opportunityEventWindowOpened() {
            var opportunityEventsGrid = $("#opportunityEventsGrid").data("kendoGrid");
            opportunityEventsGrid.dataSource.read();
        }

        function opportunityAttachmentCount(opportunityId, linkOpportunityId) {
            viewModel.set("linkOpportunityId", linkOpportunityId);
            viewModel.set("opportunityId", opportunityId);

            $("#opportunityAttachmentWindow").data("kendoWindow").center().open();
        }

        function opportunityAttachmentWindowOpened() {
            var opportunityDocumentsGrid = $("#opportunityDocumentsGrid").data("kendoGrid");
            var opportunityActionDocumentsGrid = $("#opportunityActionDocumentsGrid").data("kendoGrid");
            var opportunityEventDocumentsGrid = $("#opportunityEventDocumentsGrid").data("kendoGrid");
            var wellDocumentsGrid = $("#wellDocumentsGrid").data("kendoGrid");

            opportunityDocumentsGrid.dataSource.read();
            opportunityActionDocumentsGrid.dataSource.read();
            opportunityEventDocumentsGrid.dataSource.read();
            wellDocumentsGrid.dataSource.read();
        }

        function opportunityActionCount(opportunityId) {
            viewModel.set("opportunityId", opportunityId);

            $("#opportunityActionWindow").data("kendoWindow").center().open();
        }

        function opportunityActionWindowOpened() {
            var opportunityActionsGrid = $("#opportunityActionsGrid").data("kendoGrid");
            opportunityActionsGrid.dataSource.read();
        }

        function updateOpportunityActionsGrid() {
            var opportunityActionsGrid = $("#opportunityActionsGrid").data("kendoGrid");
            var totalOpportunityActions = opportunityActionsGrid.dataSource.total();
            viewModel.set("totalOpportunityActions", totalOpportunityActions);
        }

        function followUpActionCount(opportunityEventId) {
            viewModel.set("opportunityEventId", opportunityEventId);

            $("#followUpActionWindow").data("kendoWindow").center().open();
        }

        function followUpActionWindowOpened() {
            var followUpActionsGrid = $("#followUpActionsGrid").data("kendoGrid");
            followUpActionsGrid.dataSource.read();
        }

        function updateFollowUpActionsGrid() {
            var followUpActionsGrid = $("#followUpActionsGrid").data("kendoGrid");
            var totalFollowUpActions = followUpActionsGrid.dataSource.total();
            viewModel.set("totalFollowUpActions", totalFollowUpActions);
        }

        function updateOpportunityEventsGrid() {
            var opportunityEventsGrid = $("#opportunityEventsGrid").data("kendoGrid");
            var totalOpportunityEvents = opportunityEventsGrid.dataSource.total();
            viewModel.set("totalOpportunityEvents", totalOpportunityEvents);
        }
        
        function saveOpportunityGrid(e) {
            setTimeout(function () {
                var grid = $("#opportunitiesGrid").data("kendoGrid");
                localStorage["opportunitiesGrid"] = kendo.stringify(grid.getOptions());
            }, 10);
        }

        function loadOpportunitiesGrid() {
            var grid = $("#opportunitiesGrid").data("kendoGrid");
            var toolBar = $("#opportunitiesGrid .k-grid-toolbar").html();
            var options = localStorage["opportunitiesGrid"];
            viewModel.set("initialOpportunitiesGridOptions", kendo.stringify(grid.getOptions()));
            if (options) {
                grid.setOptions(JSON.parse(options));
                $("#opportunitiesGrid .k-grid-toolbar").html(toolBar);
                $("#opportunitiesGrid .k-grid-toolbar").addClass("k-grid-top");
            }
        }


        function updateOpportunityGrid() {
            $("#resetOpportunitiesGrid").click(function (e) {
                e.preventDefault();
                resetGridView('opportunitiesGrid', 'initialOpportunitiesGridOptions')
            });

            var opportunitiesGrid = $("#opportunitiesGrid").data("kendoGrid");
            var totalOpportunities = opportunitiesGrid.dataSource.total();
            viewModel.set("totalOpportunities", totalOpportunities);
        }

        function refreshOpportunityGrid() {
            var opportunitiesGrid = $("#opportunitiesGrid").data("kendoGrid");
            opportunitiesGrid.dataSource.read();
        }

        function updateOpportunityEventDocumentsGrid() {
            var opportunityEventDocumentsGrid = $("#opportunityEventDocumentsGrid").data("kendoGrid");
            var totalOpportunityEventDocuments = opportunityEventDocumentsGrid.dataSource.total();
            viewModel.set("totalOpportunityEventDocuments", totalOpportunityEventDocuments);
        }

        function updateOpportunityActionDocumentsGrid() {
            var opportunityActionDocumentsGrid = $("#opportunityActionDocumentsGrid").data("kendoGrid");
            var totalOpportunityActionDocuments = opportunityActionDocumentsGrid.dataSource.total();
            viewModel.set("totalOpportunityActionDocuments", totalOpportunityActionDocuments);
        }

        function updateWellDocumentsGrid() {
            var wellDocumentsGrid = $("#wellDocumentsGrid").data("kendoGrid");
            var totalOpportunityWellDocuments = wellDocumentsGrid.dataSource.total();
            viewModel.set("totalOpportunityWellDocuments", totalOpportunityWellDocuments);
        }

        function updateOpportunityDocumentsGrid() {
            var opportunityDocumentsGrid = $("#opportunityDocumentsGrid").data("kendoGrid");
            var totalOpportunityDocuments = opportunityDocumentsGrid.dataSource.total();
            viewModel.set("totalOpportunityDocuments", totalOpportunityDocuments);
        }

        var viewModel = kendo.observable({
            hasCustomer :false,
            totalSales: 0,
            totalOpportunities: 0,
            totalEvents: 0,
            totalActions: 0,
            type: "",
            opportunityId: 0,
            totalOpportunityActions: 0,
            totalOpportunityEvents: 0,
            totalOpportunityActionDocuments: 0,
            totalOpportunityEventDocuments: 0,
            totalOpportunityWellDocuments: 0,
            totalOpportunityDocuments: 0,
            opportunityEventId: 0,
            linkOpportunityId: 0,
            totalFollowUpActions: 0,
            customerId: "",

            totalClick: function () {
                this.set("type", "total");
                refreshSalesGrid();
            },
            leadClick: function () {
                this.set("type", "lead");
                refreshSalesGrid();
            },
            opportunityClick: function () {
                this.set("type", "opportunity");
                refreshSalesGrid();
            },
            enquiryClick: function () {
                this.set("type", "enquiry");
                refreshSalesGrid();
            },
            proposalClick: function () {
                this.set("type", "proposal");
                refreshSalesGrid();
            },
            reviewClick: function () {
                this.set("type", "review");
                refreshSalesGrid();
            },
            awardClick: function () {
                this.set("type", "award");
                refreshSalesGrid();
            },

            showCustomerWindow: function () {
                $("#customerWindow").data("kendoWindow").center().open();
            },

        });

        kendo.bind(document.body.children, viewModel);
    </script>
