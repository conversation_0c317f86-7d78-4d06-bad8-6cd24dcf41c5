﻿using Centerpoint.Authorization;
using Centerpoint.Common;
using Centerpoint.Controllers;
using Centerpoint.DataAccess;
using Centerpoint.DataAccess.Persistence;
using Centerpoint.Model.Configuration;
using Centerpoint.Model.MappingProfile;
using Centerpoint.Service;
using Centerpoint.Services;
using Centerpoint.Storage.Interfaces;
using Centerpoint.Storage.Services;
using Centerpoint.Storage.Settings;
using Centerpoint.Utils;
using Hangfire;
using Hangfire.SqlServer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.CookiePolicy;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using NLog.Extensions.Logging;
using System.Globalization;
using System.Net.Mail;
using static Centerpoint.Common.GlobalSettings;

namespace Centerpoint.Extensions
{
    public static class StartupExtension
    {
        public static string _supportedCulture = "en-GB";

        public static void ConfigureServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddServices(configuration);
        }

        public static void ConfigureAuthentication(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddCenterPointIdentity(configuration);
            services.AddAuthorization();
            services.AddScoped<ICurrentUserService, CurrentUserService>();
        }

        public static void ConfigureAutoMapper(this IServiceCollection services)
        {
            services.AddAutoMapper(typeof(ActionTypeMappingProfile));
        }

        public static void ConfigureEmail(this IServiceCollection services, IConfiguration Configuration)
        {
            services.Configure<EmailSettings>(Configuration.GetSection("EmailSettings"));
            services.AddScoped<IEmailManager, EmailManager>();
            services.AddScoped<WebContext>();
            services.AddSingleton<ISmsManager, SmsManager>();


            if (!string.IsNullOrEmpty(Configuration["EmailSettings:SendGridKey"]))
            {
                services.AddFluentEmail(Configuration["EmailSettings:From"])
                .AddRazorRenderer()
                .AddSendGridSender(Configuration["EmailSettings:SendGridKey"]);
            }
            else
            {
                var smtpClient = new SmtpClient()
                {
                    Host = Configuration["EmailSettings:SmtpHost"],
                    Port = int.Parse(Configuration["EmailSettings:SmtpPort"]),
                    Credentials = new System.Net.NetworkCredential(Configuration["EmailSettings:Username"], Configuration["EmailSettings:Password"]),
                    EnableSsl = true
                };

                services.AddFluentEmail(Configuration["EmailSettings:From"])
                    .AddRazorRenderer()
                    .AddSmtpSender(smtpClient);
            }
        }

        public static void ConfigureSettings(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<Settings>(configuration.GetSection(nameof(Settings)));
            services.Configure<GeneralSettings>(configuration.GetSection("Settings"));

            services.Configure<AzureBlobStorageSettings>(configuration.GetSection("BlobStorage"));
            services.Configure<AzureAdSettings>(configuration.GetSection("AzureAdSettings"));
            services.AddScoped<IStorage, BlobStorage>();

            var serviceProvider = services.BuildServiceProvider();

            GlobalSettings.Initialize(serviceProvider);

        }

        public static void ConfigureFormOptions(this IServiceCollection services)
        {
            services.Configure<FormOptions>(x =>
            {
                x.ValueLengthLimit = int.MaxValue;
                x.MultipartBodyLengthLimit = int.MaxValue; // In case of multipart
            });
        }

        public static void ConfigureLogger(this IServiceCollection services)
        {
            services.AddLogging(b =>
            {
                b.SetMinimumLevel(LogLevel.Trace);
                b.AddNLog(new NLogProviderOptions
                {
                    CaptureMessageTemplates = true,
                    CaptureMessageProperties = true
                });
            });
        }

        public static void ConfigureUtilities(this IServiceCollection services)
        {
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            //services.AddScoped<WebContext>();
        }

        public static void ConfigureHangFire(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHangfire(options => options
              .SetDataCompatibilityLevel(CompatibilityLevel.Version_170)
              .UseSimpleAssemblyNameTypeSerializer()
              .UseRecommendedSerializerSettings()
              .UseFilter(new AutomaticRetryAttribute { Attempts = 2 })
              .UseSqlServerStorage(configuration.GetConnectionString("DefaultConnection"), new SqlServerStorageOptions
              {
                  CommandBatchMaxTimeout = TimeSpan.FromMinutes(5),
                  SlidingInvisibilityTimeout = TimeSpan.FromMinutes(5),
                  QueuePollInterval = TimeSpan.Zero,
                  UseRecommendedIsolationLevel = true,
                  DisableGlobalLocks = true
              }).WithJobExpirationTimeout(TimeSpan.FromDays(180)));

            // Add the processing server as IHostedService
            services.AddHangfireServer(options =>
            {
                options.WorkerCount = 1;
            });
        }

        public static void ConfigureHangfire(this WebApplication app)
        {
            var options = new DashboardOptions
            {
                Authorization = new[] { new HangfireAuthorizationFilter() }
            };

            app.UseHangfireDashboard("/jobs", options);

            RecurringJob.AddOrUpdate<AssetsController>("Schedule Maintenance", job => job.StartScheduledMaintenance(), Cron.Daily);
            RecurringJob.AddOrUpdate<AssetsController>("Calculate Points Per Months", job => job.UpdatePointsPerMonth(), Cron.Daily);
            RecurringJob.AddOrUpdate<ReportController>("Schedule Generate Equipment Report", job => job.GenerateEquipmentReport(), Cron.Daily);
            RecurringJob.AddOrUpdate<HomeController>("Schedule Personnel Certificates", job => job.EmailThresholdPersonnelCertificates(), Cron.Daily);

        }

        public static void ApplyOneTimeMigrations(this IApplicationBuilder app)
        {
            using var scope = app.ApplicationServices.CreateScope();
            var services = scope.ServiceProvider;
            using var context = services.GetRequiredService<DataContext>();
            OneTimeMigrations.Initialize(context, services).Wait();
        }

        public static void ConfigureMvc(this IServiceCollection services)
        {
            services.AddMvc(config =>
            {
                var policy = new AuthorizationPolicyBuilder()
                                 .RequireAuthenticatedUser()
                                 .Build();
                config.Filters.Add(new AuthorizeFilter(policy));
            });
        }

        public static void ConfigureControllers(this IServiceCollection services)
        {
            services.AddControllers()
                .AddNewtonsoftJson(options =>
                {
                    options.SerializerSettings.DateFormatHandling = DateFormatHandling.IsoDateFormat;
                    options.SerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.Utc;
                    options.SerializerSettings.ContractResolver = new DefaultContractResolver();
                })
                .AddRazorRuntimeCompilation();
        }

        public static void ConfigurationQuerySplittingBehavior(this IServiceCollection services,
            IConfiguration configuration)
        {
            services.AddDbContext<DataContext>(options => options.UseSqlServer(
                configuration.GetConnectionString("DefaultConnection"),
                sqlServerOptions =>
                {
                    sqlServerOptions.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
                    sqlServerOptions.CommandTimeout(60);
                }));
        }


        public static void ConfigureCookiePolicy(this WebApplication app)
        {
            app.UseCookiePolicy(new CookiePolicyOptions()
            {
                OnAppendCookie = x =>
                {
                    x.CookieOptions.HttpOnly = true;
                    x.CookieOptions.Secure = true;
                    x.CookieOptions.SameSite = SameSiteMode.None;
                }
            });
        }

        public static void MigrateDatabase(this WebApplication app)
        {
            using (var scope = app.Services.CreateScope())
            {
                var dataContext = scope.ServiceProvider.GetRequiredService<DataContext>();
                dataContext.Database.Migrate();
            }
        }

        public static void ConfigureEndpoints(this WebApplication app)
        {
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllerRoute("default", "{controller=home}/{action=index}/{id?}")
                    .RequireAuthorization();
            });
        }

        public static void ConfigureExceptionHandler(this WebApplication app)
        {
            if (app.Environment.IsDevelopment() || app.Environment.IsStaging())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseExceptionHandler("/Error/Index");
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
                app.UseStatusCodePagesWithRedirects("/Error/Index?statusCode={0}");
            }
        }

        public static void ConfigureLocalizationService(this IServiceCollection services)
        {
            var defaultDateTimeFormat = new DateTimeFormatInfo
            {
                ShortDatePattern = "dd/MM/yyyy",
                LongDatePattern = "dd/MM/yyyy hh:mm tt"
            };

            services.Configure<RequestLocalizationOptions>(options =>
                {
                    var supportedCultures = new[] { new CultureInfo(_supportedCulture) { DateTimeFormat = defaultDateTimeFormat } };

                    options.DefaultRequestCulture = new RequestCulture(culture: _supportedCulture, uiCulture: _supportedCulture);
                    options.SupportedCultures = supportedCultures;
                    options.SupportedUICultures = supportedCultures;
                    options.SetDefaultCulture(_supportedCulture);
                });
        }


        public static void ConfigureLocalizationApp(this WebApplication app)
        {
            var localizationOptions = new RequestLocalizationOptions()
                .SetDefaultCulture(_supportedCulture)
                .AddSupportedCultures(_supportedCulture)
                .AddSupportedUICultures(_supportedCulture);

            app.UseRequestLocalization(localizationOptions);
        }

        public static void ConfigureSyncfusion(this IApplicationBuilder app)
        {
            Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("Mgo+DSMBaFt6QHFqVkNrXVNbdV5dVGpAd0N3RGlcdlR1fUUmHVdTRHRbQlthTn5Wd0NjXn1Zd3U=;Mgo+DSMBPh8sVXJxS0d+X1RPd11dXmJWd1p/THNYflR1fV9DaUwxOX1dQl9nSXhRcUVmWHled3ZcRGc=;Mgo+DSMBMAY9C3t2UFhhQlJBfV5AQmBIYVp/TGpJfl96cVxMZVVBJAtUQF1hTX5VdEJjXH5YcHNXT2Jb");

            //Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("Mgo+DSMBaFt/QHRqVVhkVVpFdEBBXHxAd1p/VWJYdVt5flBPcDwsT3RfQF5jS39QdkBnX31ed31cQw==;Mgo+DSMBPh8sVXJ0S0J+XE9Af1RDX3xKf0x/TGpQb19xflBPallYVBYiSV9jS31TdERgWXpadnZVTmFeVA==;NRAiBiAaIQQuGjN/V0Z+WE9EaFtLVmJLYVB3WmpQdldgdVRMZVVbQX9PIiBoS35RdUVhWHxedXBTRGBUU0Fz;Mgo+DSMBMAY9C3t2VVhkQlFaclZJXGFWfVJpTGpQdk5xdV9DaVZUTWY/P1ZhSXxQdkdiWn9bdHJXRmhfWUE=");
        }
    }
}
