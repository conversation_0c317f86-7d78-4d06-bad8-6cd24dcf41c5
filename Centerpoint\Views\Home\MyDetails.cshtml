﻿@model UserModel

<div class="row">
    <div class="col-md-4">
        <div class="form-group">
            @Html.LabelFor(m => m.Name)
            @(Html.Kendo().TextBoxFor(m => m.Name)
             .HtmlAttributes(new { @class = "form-control", @readonly = "readonly" }))
        </div>
        <div class="form-group">
            @Html.LabelFor(m => m.EmailAddress)
            @(Html.Kendo().TextBoxFor(m => m.EmailAddress)
             .HtmlAttributes(new { @class = "form-control", @readonly = "readonly" }))
        </div>
        <div class="form-group">
            @Html.LabelFor(m => m.DateOfBirth)
            @(Html.Kendo().DatePickerFor(m => m.DateOfBirth))
        </div>
        <div class="form-group">
            @Html.LabelFor(m => m.Address)
            @Html.TextAreaFor(m => m.Address, new { @class = "form-control", @rows ="5" })
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            @Html.LabelFor(m => m.JobTitle)
            @(Html.Kendo().TextBoxFor(m => m.JobTitle)
             .HtmlAttributes(new { @class = "form-control", @readonly = "readonly" }))
        </div>
        <div class="form-group">
            @Html.LabelFor(m => m.WorkTelephone)
            @(Html.Kendo().TextBoxFor(m => m.WorkTelephone)
             .HtmlAttributes(new { @class = "form-control", @readonly = "readonly" }))
        </div>
        <div class="form-group">
            @Html.LabelFor(m => m.MobileTelephone)
            @(Html.Kendo().TextBoxFor(m => m.MobileTelephone)
             .HtmlAttributes(new { @class = "form-control"}))
        </div>
        <div class="form-group">
            @Html.LabelFor(m => m.HomeTelephone)
            @(Html.Kendo().TextBoxFor(m => m.HomeTelephone)
             .HtmlAttributes(new { @class = "form-control"}))
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group input-group">
            @Html.LabelFor(m => m.Roles)
            @(Html.Kendo().MultiSelectFor(m => m.RolesList)
                .Placeholder("Select Role")
                .Filter(FilterType.Contains)
                .DataValueField("Value")
                .DataTextField("Text")
                .AutoBind(true)
                .ValuePrimitive(true)
                .Enable(false)
                .BindTo(UserRoleConstant.GetRolesSelectListItems()))
        </div>
        <div class="form-group">
            @Html.LabelFor(m => m.BaseCompanyLocationId)
            @(Html.Kendo().DropDownListFor(m => m.BaseCompanyLocationId)
              .Filter("contains")
              .OptionLabel("Select Base Location")
              .DataTextField("Text")
              .DataValueField("Value")
              .DataSource(d => d.Read("GetBaseCompanyLocations", "Lookup"))
              .HtmlAttributes(new {@readonly = "readonly" }))
        </div>
    </div>
</div>
<hr />
<h3 class="text-primary mb-3 mt-5">Medical Details</h3>
<div class="row">
    <div class="col-md-4">
        <div class="form-group">
            @Html.LabelFor(m => m.BloodType)
                @(Html.Kendo().DropDownListFor(m => m.BloodType)
                  .Filter("contains")
                  .OptionLabel("Select Type")
                  .DataValueField("Key")
                  .DataTextField("Value")
                  .BindTo(BloodTypeConstant.ValuesAndDescriptions.ToList())
                 )
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            @Html.LabelFor(m => m.OrganDonor)
            @(Html.Kendo().DropDownListFor(m => m.OrganDonor)
                .DataValueField("Key")
                .DataTextField("Value")
                .Filter("contains")
                .BindTo(Html.BooleanListValues().ToList())
            )
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            @Html.LabelFor(m => m.MedicalConditions)
            @Html.TextAreaFor(m => m.MedicalConditions, new { @class = "form-control" })
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            @Html.LabelFor(m => m.AllergiesAndReaction)
            @Html.TextAreaFor(m => m.AllergiesAndReaction, new { @class = "form-control"})
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            @Html.LabelFor(m => m.Medications)
            @Html.TextAreaFor(m => m.Medications, new { @class = "form-control"})
        </div>
    </div>
</div>
<hr />
<h3 class="text-primary mb-3 mt-5"> Emergency Contact Details (NOK 1)</h3>
<div class="row">
    <div class="col-md-4">
        <div class="form-group">
            @Html.LabelFor(m => m.ContactName)
            @(Html.Kendo().TextBoxFor(m => m.ContactName)
            .HtmlAttributes(new { @class = "form-control"}))
        </div>
        <div class="form-group">
            @Html.LabelFor(m => m.WorkTelephone1)
            @(Html.Kendo().TextBoxFor(m => m.WorkTelephone1)
             .HtmlAttributes(new { @class = "form-control"}))
        </div>
        <div class="form-group">
            @Html.LabelFor(m => m.Address1)
            @Html.TextAreaFor(m => m.Address1, new { @class = "form-control"})
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            @Html.LabelFor(m => m.ContactRelationship)
            @(Html.Kendo().TextBoxFor(m => m.ContactRelationship)
             .HtmlAttributes(new { @class = "form-control"}))
        </div>
        <div class="form-group">
             @Html.LabelFor(m => m.MobileTelephone1)
             @(Html.Kendo().TextBoxFor(m => m.MobileTelephone1)
             .HtmlAttributes(new { @class = "form-control"}))
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            @Html.LabelFor(m => m.HomeTelephone1)
            @(Html.Kendo().TextBoxFor(m => m.HomeTelephone1)
             .HtmlAttributes(new { @class = "form-control"}))
        </div>
    </div>
</div>
<hr />
<h3 class="text-primary mb-3 mt-5"> Emergency Contact Details (NOK 2)</h3>
<div class="row">
    <div class="col-md-4">
        <div class="form-group">
            @Html.LabelFor(m => m.ContactName1)
            @(Html.Kendo().TextBoxFor(m => m.ContactName1)
            .HtmlAttributes(new { @class = "form-control"}))
        </div>
        <div class="form-group">
            @Html.LabelFor(m => m.WorkTelephone2)
            @(Html.Kendo().TextBoxFor(m => m.WorkTelephone2)
             .HtmlAttributes(new { @class = "form-control"}))
        </div>
        <div class="form-group">
            @Html.LabelFor(m => m.Address2)
            @Html.TextAreaFor(m => m.Address2, new { @class = "form-control"})
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            @Html.LabelFor(m => m.ContactRelationship1)
            @(Html.Kendo().TextBoxFor(m => m.ContactRelationship1)
             .HtmlAttributes(new { @class = "form-control"}))
        </div>
        <div class="form-group">
            <label>Mobile Telephone</label>
            @(Html.Kendo().TextBoxFor(m => m.MobileTelephone2)
             .HtmlAttributes(new { @class = "form-control"}))
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            <label>Home Telephone</label>
            @(Html.Kendo().TextBoxFor(m => m.HomeTelephone2)
             .HtmlAttributes(new { @class = "form-control"}))
        </div>
    </div>
</div>
<hr />
<h3 class="text-primary mb-3 mt-5">Notes (<span data-bind="text:totalUserNotes"></span>)</h3>
<div>
    @(Html.Kendo().Grid<UserNoteModel>()
              .Name("userNoteGrid")
              .Columns(c => {
                c.Bound(p => p.Note).Encoded(false).ClientTemplate("#= getHtmlNewLinesString(Note) #");
                c.Bound(p => p.Date).Format(DateConstants.DateTimeFormat).Width(125);
              })
               .Editable(editable => editable.Mode(GridEditMode.PopUp).DisplayDeleteConfirmation("Are you sure you want to delete this note?").TemplateName("UserNoteWindow")
               .Window(w => w.Name("UserNoteWindow").Title("User Notes").Width(850).Draggable(false)))
               .Sortable()
               .Filterable()
               .Groupable()
               .Events(e => e.Edit("userNoteEdit"))
               .Events(e => e.DataBound("updateUserNoteTotal"))
               .Scrollable(s => s.Height(500))
               .Resizable(c => c.Columns(true))
               .ColumnMenu(c => c.Columns(true))
               .DataSource(dataSource => dataSource
               .Ajax()
               .ServerOperation(false)
               .Model(m => m.Id(p => p.UserNoteId))
               .Read(read => read.Action("GetUserNotes", "Admin", new { @userId = Model.UserId }))))
</div>
<div class="d-flex justify-content-end mt-3">
   <button type="button" onclick="validateHomeEditUserForm(event)" class="btn btn-primary btn-sm">Save My Details</button>   
</div>


<script>
    function getHtmlNewLinesString(text) {
        var regexp = new RegExp('\n', 'g');
        return text ? text.replace(regexp, '<br>') : text;
    }
</script>

