﻿<form id="customerForm">
    <div>
                            <div class="container-fluid pl-0 pr-0">
                                <div class="row p-0">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>Name</label>
                                            @(Html.Kendo().TextBox().Name("Name").HtmlAttributes(new { @class = "form-control"}))
                                        </div>
                                        @if (Html.IsGlobalAdmin()) {
                                            <div class="form-group">
                                                <label>Is Internal</label>
                                                @(Html.Kendo().DropDownList()
                                                .Name("IsInternal")
                                                .Filter("contains")
                                                .DataValueField("Key")
                                                .DataTextField("Value")
                                                .BindTo(Html.BooleanListValues().ToList()))
                                            </div>
                                        }
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>Category</label>
                                            @(Html.Kendo().MultiSelect()
                                            .Name("CategoryIds")
                                            .DataTextField("Text")
                                            .DataValueField("Value")
                                            .Placeholder("Select Categories")
                                            .Filter("contains")
                                            .DataSource(source => {
                                                source.Read(read => {
                                                     read.Action("GetCategories", "Lookup");
                                                }).ServerFiltering(true);
                                            }))
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>Is Active</label>
                                            @(Html.Kendo().DropDownList()
                                            .Name("IsActive")
                                            .Filter("contains")
                                            .DataValueField("Key")
                                            .DataTextField("Value")
                                            .BindTo(Html.BooleanListValues().ToList()))
                                        </div>
                                    </div>
                                </div>
                            </div>

                            @*<button type="submit" class="btn btn-sm btn-primary">Save Entity Details</button>*@

        <button type="button" onclick="addCompany()" class="btn btn-sm btn-primary">save</button>
            
    </div>
</form>
