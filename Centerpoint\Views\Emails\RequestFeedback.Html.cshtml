﻿@model RequestFeedbackEmail
<html>
<head>
    <style>
        p.<PERSON>, li.<PERSON>, div.<PERSON><PERSON><PERSON><PERSON><PERSON> {
            margin: 0cm;
            margin-bottom: .0001pt;
            font-size: 11.0pt;
            font-family: "Calibri",sans-serif;
            mso-fareast-language: EN-US;
        }
      
        a:link, span.MsoHyperlink {
            mso-style-priority: 99;
            color: #0563C1;
            text-decoration: underline;
        }
        a:visited, span.MsoHyperlinkFollowed {
            mso-style-priority: 99;
            color: #954F72;
            text-decoration: underline;
        }
        p {
            mso-style-priority: 99;
            mso-margin-top-alt: auto;
            margin-right: 0cm;
            mso-margin-bottom-alt: auto;
            margin-left: 0cm;
            font-size: 12.0pt;
            font-family: "Times New Roman",serif;
        }
       
    </style>
</head>
<body>
    <p><span style="font-family:Calibri,sans-serif">Dear @Model.Username,<o:p></o:p></span></p>
    <p><span style="font-family:<PERSON>ibri,sans-serif">Please click the button below to give us your feedback regarding the work recently completed.<o:p></o:p></span></p>
    @foreach (var deliverable in Model.Deliverables) {
        <p class="MsoNormal" style="font-family:Calibri,sans-serif">@deliverable</p>
        }
    <br />
    @*<a href="@Model.Url"><span style="display:block;margin-left:auto;margin-right:auto;text-align:center">@Html.EmbedImage("~/Content/img/Leave_Feedback.jpg")</span></a>*@

    <p><span style="font-family:Calibri,sans-serif">Your feedback is essential to our continual improvement!<o:p></o:p></span></p>
    <p><span style="font-family:Calibri,sans-serif">Best Regards,</span><span style="font-size:11.0pt;font-family:Calibri,sans-serif;mso-fareast-language:EN-US"><o:p></o:p></span></p>
    <p class="MsoNormal"><b><span style="mso-fareast-language:EN-GB">@Model.RequestedUser</span></b><span style="mso-fareast-language:EN-GB"><o:p></o:p></span></p>
    <p class="MsoNormal"><b><span style="color:#009DE0;mso-fareast-language:EN-GB"><o:p>&nbsp;</o:p></span></b></p>
    <p class="MsoNormal"><b><span style="color:#009DE0;mso-fareast-language:EN-GB">Centerpoint</span></b><span style="mso-fareast-language:EN-GB"><o:p></o:p></span></p>
    <p class="MsoNormal"><span style="font-size:9.0pt;color:#2C2C75;mso-fareast-language:EN-GB">Viking House</span><span style="mso-fareast-language:EN-GB"><o:p></o:p></span></p>
    <p class="MsoNormal"><span style="font-size:9.0pt;color:#2C2C75;mso-fareast-language:EN-GB">1 Claymore Avenue</span><span style="mso-fareast-language:EN-GB"><o:p></o:p></span></p>
    <p class="MsoNormal"><span style="font-size:9.0pt;color:#2C2C75;mso-fareast-language:EN-GB">Aberdeen, AB23 8GW</span><span style="mso-fareast-language:EN-GB"><o:p></o:p></span></p>
    <p class="MsoNormal"><b><span style="color:#2C2C75;mso-fareast-language:EN-GB"><a href="http://www.readcasedhole.com/"><span style="color:#2C2C75">www.readcasedhole.com</span></a></span></b><span style="mso-fareast-language:EN-GB"><o:p></o:p></span></p>
</body>
</html>