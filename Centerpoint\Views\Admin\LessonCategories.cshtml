﻿@model LessonCategoryModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-bars"></i>
        Lessons Categories
        (<span data-bind="text: totalLessonCategories"></span>)
    </h4>
</div>
<hr />

<div class="grid-container">
    @(Html.Kendo().Grid<LessonCategoryModel>()
        .Name("lessonCategoryGrid")
        .Columns(c => {
            c.Bound(p => p.Name);
            c.Command(command => { 
                command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
            }).Width(250);
        })
        .Editable(editable => editable.Mode(GridEditMode.InLine))
        .ToolBar(t => {
            t.Create().Text("Add Lesson Category");
            t.Excel().Text("Export");
        }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
        .Sortable()
        .Filterable()
        .Scrollable()
        .Resizable(c => c.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .Events(e => e.DataBound("updateLessonCategoryTotal"))
        .Excel(excel => excel
            .FileName(string.Format("Centerpoint_QHSE_LessonLearned_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Admin"))
        )
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(m => m.Id(p => p.LessonCategoryId))
            .Events(e => e.Error("onError"))
            .Read(read => read.Action("GetLessonCategories", "Admin"))
            .Create(create => create.Action("UpdateLessonCategory", "Admin"))
            .Update(update => update.Action("UpdateLessonCategory", "Admin"))
            .Destroy(destroy => destroy.Action("DeleteLessonCategory", "Admin"))
        )
    )  
</div>
<script>
    $(document).ready(function () {
        var lessonCategoryGrid = $('#lessonCategoryGrid').data("kendoGrid");
        lessonCategoryGrid.bind('dataBound', function (e) {
            this.element.find('.k-add').remove();
            this.element.find('.k-i-excel').remove();
        });
    });
    function updateLessonCategoryTotal() {
        var lessonCategoryGrid = $("#lessonCategoryGrid").data("kendoGrid");
        var totalLessonCategories = lessonCategoryGrid.dataSource.total();
        viewModel.set("totalLessonCategories", totalLessonCategories);
    }

    function onError(e, status) {
        if (e.status == "customerror") {
            alert(e.errors);

            var lessonCategoryGrid = $("#lessonCategoryGrid").data("kendoGrid");
            lessonCategoryGrid.dataSource.cancelChanges();
        }
    }

    var viewModel = new kendo.observable({
        totalLessonCategories: 0
    });

    kendo.bind(document.body.children, viewModel);
</script>