﻿namespace Centerpoint.Common.Constants
{
    public static class WellTypeConstant
    {

        public const string Injector = "INJ";
        public const string Producer = "PRD";
        public const string GasStorage = "GAS";
        public const string Geothermal = "GEO";
        public const string Monitoring = "MON";
        public const string Appraisal = "APP";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Injector,"Injector"},
                    {Producer,"Producer"},
                    {GasStorage,"Gas Storage"},
                    {Geothermal,"Geothermal"},
                    {Monitoring,"Monitoring"},
                    {Appraisal,"Appraisal"}
                };
            }
        }
    }
}
