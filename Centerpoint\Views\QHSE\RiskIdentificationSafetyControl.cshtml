﻿@model RiskIdentificationSafetyControlContributorModel
@{var cardName = GlobalSettings.IsWellsense ? "SOC" : "RISC";}  
<div class="header-container-between">
    @if (GlobalSettings.IsWellsense)
    {
        <h4><i class="fa fa-bars"></i> SOC Safety Observation Cards</h4>
        <div>
            <a href="@Url.Action("AddRiskIdentificationSafetyControl", "Qhse")" class="btn btn-primary btn-sm pull-right">New SOC Card</a>
        </div>
    }
    else
    {
        <h4><i class="fa fa-bars"></i> RISC Identification and Safety Control Cards</h4>
        <div>
            <a href="@Url.Action("AddRiskIdentificationSafetyControl", "Qhse")" class="btn btn-primary btn-sm pull-right">New RISC Card</a>
        </div>
    }
    
</div>
<hr />

<div class="row">
    <div class="col-md-5">
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Monthly Summary</h6>
                    </div>
                    <div class="card-body">
                        <div class="card-list-item">
                            <div>
                                @(Html.Kendo().DropDownList()
                                    .Name("years")
                                    .Filter(FilterType.Contains)
                                    .DataSource(d => d.Read("GetRISCYears", "Lookup"))
                                    .Events(e => e.Change("yearChanged"))
                                    .HtmlAttributes(new { @data_bind = "value:year", @style = "width:100px;" }))
                            </div>
                            <div>
                                @(Html.Kendo().DropDownList()
                                    .Name("summaryDropdown")
                                    .DataTextField("Value")
                                    .DataValueField("Key")
                                    .Filter(FilterType.Contains)
                                    .Events(e => e.Change("summaryMonthChanged"))
                                    .DataSource(d => d.Read(read => read.Action("GetMonths", "Lookup")))
                                    .HtmlAttributes(new { @style = "width:100px;", @data_bind = "value:summaryMonth" }))  
                            </div>
                        </div>
                        <div class="card-list-item">
                            <span class="card-list-item-name">Total</span>
                            <a class="card-list-item-count" href="#riscGrid" style="background: #000000" data-bind="click:totalClick,text:totalCreatedMonth"></a>
                        </div>
                        <div class="card-list-item">
                            <span class="card-list-item-name">Positive</span>
                            <a class="card-list-item-count" href="#riscGrid" style="background: #7CBB00" data-bind="click:positiveMonthClick,text:totalPositiveMonth"></a>
                        </div>
                        <div class="card-list-item">
                            <span class="card-list-item-name">Corrective</span>
                            <a class="card-list-item-count" href="#riscGrid" style="background: #BA141A" data-bind="click:negativeMonthClick,text:totalNegativeMonth"></a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Staff Contribution</h6>
                    </div>
                    <div class="card-body">
                        <div class="card-list-item">
                            <span class="card-list-item-name">Contributors</span>
                            <span class="card-list-item-count btn disabled" style="background: #7CBB00" data-bind="text:contributorsTotal"></span>
                        </div>
                        <div class="card-list-item">
                            <span class="card-list-item-name">No Contributors</span>
                            <span class="card-list-item-count btn disabled" style="background: #BA141A" data-bind="text:noContributorTotal"></span>
                        </div>       
                        <div class="card-list-item">
                            <span class="card-list-item-name">Onshore</span>
                            <span class="card-list-item-count btn" style="background: #16185F" data-bind="click:onshoreClick,text:onshoreTotal"></span>
                        </div>                     
                        <div class="card-list-item">
                            <span class="card-list-item-name">Offshore</span>
                            <span class="card-list-item-count btn" style="background: #C78A5D" data-bind="click:offshoreClick,text:offshoreTotal"></span>
                        </div>  

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-7">
        @(Html.Kendo().Chart<RiskIdentificationSafetyControlChartModel>()
                .Name("riscChart")
                .Series(c => { 
                    c.Column(p => p.TotalYear).Name(DateTime.Now.Year.ToString());
                    c.Column(p => p.Average).Name("Average");
                    c.Column(p => p.TotalLastYear).Name(DateTime.Now.AddYears(-1).Year.ToString());
                 })
                .ValueAxis(v => v.Numeric().MajorGridLines(l => l.Visible(false)).MinorGridLines(l => l.Visible(false)))
                .Legend(l => l.Visible(true).Position(ChartLegendPosition.Right))
                .CategoryAxis(c => c.Categories(p => p.Month))
                .Tooltip(t => t.Visible(true).Template("#=category# #=series.name# - #=value#"))
                .HtmlAttributes(new { @style = "height:300px; border:none" })
                .DataSource(d => d.Read("GetRiscChartData", "Qhse")))
    </div>
</div>
<hr />
<div>
    @(Html.Kendo().Grid<RiskIdentificationSafetyControlModel>()
    .Name("riscGrid")
    .Columns(columns => {
        columns.Bound(c => c.RiscNumber).ClientTemplate("<a href='" + @Url.Action("EditRiskIdentificationSafetyControl", "Qhse") + "/#=RiskIdentificationSafetyControlId#'>#=RiscNumber#</a>").Width(100).Title($"{cardName} ID");
        columns.Bound(c => c.Date).Title("Created Date").Format(DateConstants.DateTimeFormat).Hidden(true);
        columns.Bound(c => c.RaisedByUser).Title("Raised By").Width(150);
        columns.Bound(c => c.PositiveNegative).Title("Emphasis").Hidden(true);
        columns.Bound(c => c.HazardObservation).Title("Type");
        columns.Bound(c => c.SafetyEnvironment).Title("Safety or Environment").Width(180);
        columns.Bound(c => c.ObservationDetails).Title("Observed");
        columns.Bound(c => c.ActionDetails).Title("Action").Width(150);
        columns.Bound(c => c.FollowUpDetails).Title("Follow-up").Width(150);
        columns.Bound(c => c.HseComment).Title("HSE Comments").Hidden(true);
        columns.Bound(c => c.JobProjectCompanyName).Title("Client Name").Hidden(true);
        columns.Bound(c => c.JobProjectCompanyLocationName).Title("Client Location").Hidden(true);
        columns.Bound(c => c.StatusDescription).Title("Status").Width(150);
        columns.Bound(c => c.HseName).Title("HSE Sign-off").Hidden(true);
        columns.Bound(c => c.HseDate).Title("Sign-off Date").Format(DateConstants.DateTimeFormat).Hidden(true);
        columns.Bound(c => c.ItemName).Hidden(true);
        columns.Bound(c => c.IsNearMiss).Title("Near Miss").Hidden(true);
    })
         .Sortable()
          .ToolBar(t => {
              t.Custom().Text("Reset Grid View").HtmlAttributes(new { @id="resetRiscGrid", @class="bg-danger text-white"});
              t.Excel().Text("Export");
          }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
        .Filterable()
        .Events(e => e.DataBound("updateRiscGrid").ColumnReorder("saveRiscGrid").ColumnResize("saveRiscGrid").ColumnShow("saveRiscGrid").ColumnHide("saveRiscGrid"))
        .Excel(excel => excel
            .FileName(string.Format("Centerpoint_RISC_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Qhse")))
        .Groupable()
        .Scrollable(s => s.Height("auto"))
        .Resizable(c => c.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .Reorderable(c => c.Columns(true))
        .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Read(read => read.Action("GetRiskIdentificationSafetyControls", "Qhse").Data("riscData"))))
</div>
<script>
    const riskIdentificationSafetyControlModel = {
        dateTimeNowMonth: @DateTime.Now.Month,
        viewBagMonth: @ViewBag.Month,
        viewBagYear: @ViewBag.Year,
    }
</script>

<environment include="Development">
    <script src="~/js/views/qhse/riskIdentificationSafetyControl.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/qhse/riskIdentificationSafetyControl.min.js" asp-append-version="true"></script>
</environment>