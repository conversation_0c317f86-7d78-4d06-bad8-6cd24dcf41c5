﻿@model ServiceModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-list-alt"></i>
        Elements
        (<span data-bind="text:totalServices"></span>)
    </h4>
</div>
<hr />

@(Html.Kendo().Grid<ServiceModel>()
    .Name("serviceGrid")
    .Columns(c =>{
        c.Bound(p => p.Name);
        c.Command(command => { 
            command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
            command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
        }).Width(200);
    })
    .Editable(editable => editable.Mode(GridEditMode.InLine))
    .ToolBar(t => {
            t.Create().Text("Add Element");
            t.Excel().Text("Export");
        }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
    .Sortable()
    .Filterable()
    .Scrollable(s => s.Height(500))
    .Resizable(c => c.Columns(true))
    .ColumnMenu(c => c.Columns(true))
    .Events(e => e.DataBound("updateServiceTotal"))
    .Excel(excel => excel
            .FileName(string.Format("Centerpoint_Operations_Elements_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Admin"))
        )
    .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Model(m => m.Id(p => p.ServiceId))
        .Events(e => e.Error("onError"))
        .Read(read => read.Action("GetServices", "Admin"))
        .Create(create => create.Action("UpdateService", "Admin"))
        .Update(update => update.Action("UpdateService", "Admin"))
        .Destroy(destroy => destroy.Action("DeleteService", "Admin"))))

    <script>
        $(document).ready(function () {
            var serviceGrid = $('#serviceGrid').data("kendoGrid");
            serviceGrid.bind('dataBound', function (e) {
                this.element.find('.k-add').remove();
                this.element.find('.k-i-excel').remove();
            });
        });

        function updateServiceTotal() {
            var serviceGrid = $("#serviceGrid").data("kendoGrid");
            var totalServices = serviceGrid.dataSource.total();
            viewModel.set("totalServices", totalServices);
        }

        function onError(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var serviceGrid = $("#serviceGrid").data("kendoGrid");
                serviceGrid.dataSource.cancelChanges();
            }
        }

        var viewModel = new kendo.observable({
            totalServices: 0
        });

        kendo.bind(document.body.children, viewModel);
    </script>
