﻿namespace Centerpoint.Common.Constants
{
    public static class ServiceImprovmentEquipmentConstant
    {

        public const string NoEquipment = "NOE";
        public const string WorkshopEquipment = "SHE";
        public const string SurfaceEquipment = "SUE";
        public const string DownholeEquipment = "DOE";
        public const string OtherEquipment = "OTE";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {NoEquipment,"No"},
                    {WorkshopEquipment,"Yes, Workshop Equipment"},
                    {SurfaceEquipment,"Yes, Surface Equipment"},
                    {DownholeEquipment,"Yes, Downhole Equipment"},
                    {OtherEquipment,"Yes, Other Equipment"},
                };
            }
        }
    }
}
