$("#no").click(function () {
    var companyDropDownList = $("#CompanyId").data("kendoDropDownList");
    companyDropDownList.value("");
});

function onLessonDocumentAttached() {
    var lessonDocumentsGrid = $("#lessonDocumentsGrid").data("kendoGrid");
    lessonDocumentsGrid.dataSource.read();
}

function onLessonDocumentUpload(e) {
    uploadValidation(e);
    $(".k-upload-files.k-reset").show();
}

function onLessonDocumentComplete(e) {
    $(".k-upload-files.k-reset").find("li").remove();
    $(".k-upload-files.k-reset").slideUp();
}

function updateLessonDocumentGrid() {
    var lessonDocumentsGrid = $("#lessonDocumentsGrid").data("kendoGrid");
    var totaldocuments = lessonDocumentsGrid.dataSource.total();
    viewModel.set("totaldocuments", totaldocuments);
}

function filterJobs(e) {
    return {
        companyId: $("#CompanyId").data("kendoDropDownList").value(),
        text: getTextValue(e)
    };
}
$("#rejectLessonConfirm").click(function () {
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: '/Qhse/RejectLessonReason',
        data: {
            lessonId: editLessonModel.modelLessonId,
            comment: $("#RejectLessonReasonComment").val()
        },
        success: function () {
            window.location.reload();
        },
    });
});

function divisionChange(){
var equipmentCategoryIds = $("#EquipmentCategoryIds").data("kendoMultiSelect");

equipmentCategoryIds.dataSource.read();
}

function divisionData(e) {
    var divisionId = $("#DivisionId").data("kendoDropDownList").value();

    return {
        divisionId: divisionId,
        text: getTextValue(e)
    };
}

var viewModel = new kendo.observable({

    notJobRelated: editLessonModel.modelIsOnJob,
    equipmentCategoryIds: editLessonModel.modelEquipmentCategoryIds,
    totaldocuments: 0,
    tabStripHeaderDetails: function () {
        return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Details </span>`;
    },
    attachmentsTabStrip: function () {
        return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Attachments (<span data-bind="text: totaldocuments"></span>)</span>`;   
    },


    deleteLesson:function(){
        var confirmDelete = confirm("Are you sure you wish to delete this lesson");

        if (confirmDelete) {
            $.ajax({
                type: 'POST',
                url: `/Qhse/DeleteLesson`,
                data: {
                    id: editLessonModel.modelLessonId
                },
                success: function () {
                    window.location.href = `/Qhse/Lesson`;
                }
            });
        }
    },

    otherFieldsVisible: function () {
        var onJobChecked = this.get("onJobChecked");

        return onJobChecked ;
    },

    reSubmitLessonClick: function () {
        var confirmation = confirm("Are you sure you want to Re-Submit this Lesson?");

        if (confirmation) {
            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url: `/Qhse/ReSubmitLesson?lessonId=${editLessonModel.modelLessonId}`,
                data: {
                    lessonId: editLessonModel.modelLessonId,
                    description: $("#Description").val()
                },
                success: function () {
                    window.location.reload();
                },
                dataType: "json"
            });
        }
    },
    approveLessonClick: function () {
        var confirmation = confirm("Are you sure you want to approve this Lesson?");

        if (confirmation) {
            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url: `/Qhse/UpdateLessonStatus?id=${editLessonModel.modelLessonId}&status=${editLessonModel.lessonStatusConstantApproved}`,
                data: {
                    id: editLessonModel.modelLessonId,
                },
                success: function () {
                    window.location.reload();
                },
                dataType: "json"
            });
        }
    },

    showRejectWindow: function () {
        $("#rejectWindow").data("kendoWindow").center().open();
    },

});
kendo.bind(document.body.children, viewModel);