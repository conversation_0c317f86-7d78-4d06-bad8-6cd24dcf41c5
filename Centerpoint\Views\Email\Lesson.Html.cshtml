﻿@model Centerpoint.Model.ViewModels.LessonEmail
<html>
<body>
    <p style="font-family:Arial,sans-serif">staff,</p>
    <p style="font-family:Arial,sans-serif">A new lesson learned has been made available on Centerpoint. Please check if it is applicable to your role and make yourself familiar with its content.</p>
    <p style="font-family:Arial,sans-serif">Lesson Title : @Model.Title</p>

    <p style="font-family:Arial,sans-serif">Category : @Model.Category</p>
    @if (Model.RelatedServices.Count > 0) {
        <p style="font-family:Arial,sans-serif">Related Services : @Model.RelatedServiceList</p>
    } else {
        <p style="font-family:Arial,sans-serif">Related Services  : N/A</p>
    }
    @if (Model.RelatedEquipments.Count > 0) {
        <p style="font-family:Arial,sans-serif">Related Equipments : @Model.RelatedEquipmentsList</p>
    } else {
        <p style="font-family:Arial,sans-serif">Related Equipments : N/A</p>
    }
    <br />
    <p style="font-family:Arial,sans-serif">Best Regards,</p>
    <br />
    <p style="font-family:Arial,sans-serif">Centerpoint Auto-warning</p>
</body>
</html>