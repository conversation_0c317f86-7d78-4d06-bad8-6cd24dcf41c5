﻿namespace Centerpoint.Common.Constants
{
    public static class AnsaProjectStatusConstant
    {

        public const string Upcoming = "UPC";
        public const string Active = "ACT";
        public const string Closed = "CLO";
        public const string Archived = "ARC";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Upcoming,"Upcoming"},
                    {Active,"Active"},
                    {Closed,"Issued"},
                    {Archived,"Archived"},
                };
            }
        }
    }
}
