﻿@model CompanyModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-building"></i>
        Entities
        (<span data-bind="text:totalCompanies"></span>)
    </h4>
</div>
<hr />

<div>
    @(Html.Kendo().Grid<CompanyModel>()
    .Name("companyGrid")
    .Columns(columns => {
        columns.Bound(c => c.Name).ClientTemplate("<a class='text-primary' href='" + @Url.Action("ViewCompany", "Company") + "/#=CompanyId#'>#=Name#</a>").Width(200); ;
        columns.Bound(c => c.Categories).Title("Category").Width(200);
        columns.Bound(c => c.IsActive).Title("Active").ClientTemplate("#if(IsActive){#Yes#}else{#No#}#").Width(80);
        columns.Bound(c => c.CompanyLocationCount).Title("Locations").Width(80);
        columns.Bound(c => c.CompanyContactCount).Title("Contacts").Width(80);
        columns.Bound(c => c.CompanyFieldsCount).Title("Fields").Width(80);
        columns.Bound(c => c.CompanyWellsCount).Title("Wells").Width(80);
    })
    .Events(e => e.DataBound("updateCompanyTotals").ColumnReorder("saveCompanyGrid").ColumnResize("saveCompanyGrid").ColumnShow("saveCompanyGrid").ColumnHide("saveCompanyGrid"))
    .ToolBar(t => {
        t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetCompanyGrid", @class="bg-danger text-white"});
        t.Excel().Text("Export");
    }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
    .ColumnMenu(c => c.Columns(true))
    .Filterable()
    .Sortable()
    .Groupable()
    .Reorderable(c => c.Columns(true))
    .Scrollable(s => s.Height("auto"))
    .Excel(excel => excel
            .FileName(string.Format("Centerpoint_ClientCompanies_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Admin"))
            )
    .DataSource(dataSource => dataSource
    .Ajax()
    .ServerOperation(false)
    .Model(model => {
        model.Id(m => m.CompanyId);
    })
    .Events(e => e.Error("onError"))
    .Read(read => read.Action("GetCompanies", "Admin"))))
</div>    

    <environment include="Development">
        <script src="~/js/views/companies/companies.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script src="~/js/views/companies/companies.min.js" asp-append-version="true"></script>
    </environment>