﻿@model  PersonnelCertificateDashboardModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-file-text"></i>
        Certificates
        (<span data-bind="text:totalCertificates"></span>)
    </h4>
</div>
<hr />

<div class="row mb-2">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Certificates</h6>
            </div>
            <div class="card-body">
                <div class="card-list-item">
                    <span class="card-list-item-name">All Certificates</span>
                    <span class="card-list-item-count" style="background: #43AC6A" data-bind="click:totalClick">@Model.Total</span>
                </div>

                <div class="card-list-item">
                    <span class="card-list-item-name">Expiring this month</span>
                    <span class="card-list-item-count" style="background: #BA141A" data-bind="click:thisMonthClick">@Model.TotalExpiryCurrentMonth</span>
                </div>

                <div class="card-list-item">
                    <span class="card-list-item-name">Expiring within one month</span>
                    <span class="card-list-item-count" style="background: #BA141A" data-bind="click:oneMonthClick">@Model.TotalExpiryOneMonth</span>
                </div>

                <div class="card-list-item">
                    <span class="card-list-item-name">Expiring within two months</span>
                    <span class="card-list-item-count" style="background: #BA141A" data-bind="click:twoMonthClick">@Model.TotalExpiryTwoMonths</span>
                </div>
                
                <div class="card-list-item">
                    <span class="card-list-item-name">Expiring within three months</span>
                    <span class="card-list-item-count" style="background: #BA141A" data-bind="click:threeMonthClick">@Model.TotalExpiryThreeMonths</span>
                </div>

                <div class="card-list-item">
                    <span class="card-list-item-name">Expiring this year</span>
                    <span class="card-list-item-count" style="background: #BA141A" data-bind="click:thisYearClick">@Model.TotalExpiryCurrentYear</span>
                </div>
            </div>
        </div>
    </div>
</div>
<div style="height: calc(100vh - 500px)">
    @(Html.Kendo().Grid<PersonnelCertificateDocumentModel>()
        .Name("personnelCertificateDocumentGrid")
        .Columns(c => {
            c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='" + @Url.Action("Index", "Document", new { @id = "#=DocumentId#" }) + "'>#=FileName ? FileName : ''#</a>").Width(250);
            c.Bound(p => p.UserName).Title("Employee").ClientTemplate("<a href='" + @Url.Action("EditUser", "Admin") + "/#=UserId#'>#=UserName#</a>").Width(200);
            c.Bound(p => p.CertificateCategoryId).EditorTemplateName("CertificateCategory").Title("Category").Width(250).ClientTemplate("#=CertificateCategoryName ? CertificateCategoryName : ''#").Width(150);
            c.Bound(p => p.Details).Title("Details").Width(200);
            c.Bound(p => p.ExpiryDate).EditorTemplateName("ExpiryDate").Title("Expiry Date").Format(DateConstants.DateFormat).Width(150);
            c.Bound(p => p.WarningThreshold).EditorTemplateName("WarningThreshold").Title("Expiry Warning (weeks)");
            c.Bound(p => p.AquisitionDate).EditorTemplateName("AquisitionDate").Title("Aquisition Date").Format(DateConstants.DateFormat).Width(150);
        c.Bound(p => p.DateCreated).Title("Created").Width(150).Format(DateConstants.DateFormat);
            c.Command(command => { 
                command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
            }).Width(200);
        })
        .Events(e => e.DataBound("updatePersonnelCertificateDocumentGrid").ColumnReorder("saveCertificateGrid").ColumnResize("saveCertificateGrid").ColumnShow("saveCertificateGrid").ColumnHide("saveCertificateGrid"))
        .Sortable()
        .ToolBar(t => {
            t.Custom().Text("Reset Grid View").HtmlAttributes(new{ @id="resetPersonnelCertificateDocumentGrid", @class="bg-danger text-white" });
            t.Excel().Text("Export");
        }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
        .Resizable(r => r.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .Filterable()
        .Groupable()
        .Pageable(pageable => pageable.ButtonCount(10))
        .Reorderable(c => c.Columns(true))
        .Editable(editable => editable.Mode(GridEditMode.InLine))
        .Excel(excel => excel
            .FileName(string.Format("Centerpoint_Personnel_Certificates_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Admin"))
        )
        .Scrollable()
        .DataSource(dataSource => dataSource
            .Ajax()
            .PageSize(50)
            .ServerOperation(false)
            .Model(m => {
                m.Id(p => p.PersonnelCertificateDocumentId);
            })
            .Read(read => read.Action("GetAllPersonnelCertificates", "Admin").Data("certificateData"))
            .Update(update => update.Action("UpdatePersonnelCertificate", "Admin").Data("selectedCertificateData"))
            .Destroy(destroy => destroy.Action("DeletePersonnelCertificate", "Admin"))
        )
    )
</div>

<script>
    $(document).ready(function () {
        loadCertificateGrid();
        var personnelCertificateDocumentGrid = $('#personnelCertificateDocumentGrid').data("kendoGrid");
        personnelCertificateDocumentGrid.bind('dataBound', function (e) {
            this.element.find('.k-i-excel').remove();
        });
    });

    function updatePersonnelCertificateDocumentGrid() {
        var personnelCertificateDocumentGrid = $("#personnelCertificateDocumentGrid").data("kendoGrid");
        var totalCertificates = personnelCertificateDocumentGrid.dataSource.total();
        viewModel.set("totalCertificates", totalCertificates);

        $("#resetPersonnelCertificateDocumentGrid").click(function (e) {
            e.preventDefault();
            resetGridView('personnelCertificateDocumentGrid', 'initialPersonnelCertificateDocumentGridOptions')
        });
    }

    function loadCertificateGrid() {
        var grid = $("#personnelCertificateDocumentGrid").data("kendoGrid");
        var toolBar = $("#personnelCertificateDocumentGrid .k-grid-toolbar").html();
        var options = localStorage["personnelCertificateDocumentGrid"];
        viewModel.set("initialPersonnelCertificateDocumentGridOptions", kendo.stringify(grid.getOptions()));
        if (options) {
            grid.setOptions(JSON.parse(options));
            $("#personnelCertificateDocumentGrid .k-grid-toolbar").html(toolBar);
            $("#personnelCertificateDocumentGrid .k-grid-toolbar").addClass("k-grid-top");
        }
    }

    function saveCertificateGrid(e) {
        setTimeout(function () {
            var grid = $("#personnelCertificateDocumentGrid").data("kendoGrid");
            localStorage["personnelCertificateDocumentGrid"] = kendo.stringify(grid.getOptions());
        }, 10);
    }

    function certificateData() {
        return {
            month: viewModel.get("month"),
            year: viewModel.get("year")
        }

    }

    function selectedCertificateData(e) {

        e.ExpiaryDate = toUTCString(e.ExpiaryDate);
        e.DateCreated = toUTCString(e.DateCreated);
        e.AquisitionDate = toUTCString(e.AquisitionDate);
     
        return {
            uId: e.UserId
        }
    }

    function refreshCertificatesGrid() {
        var personnelCertificateDocumentGrid = $("#personnelCertificateDocumentGrid").data("kendoGrid");
        personnelCertificateDocumentGrid.dataSource.read();
    }

    var viewModel = new kendo.observable({
        month: "",
        year: "",
        totalCertificates: 0,

        thisMonthClick: function () {
            this.set("month", 0);
            this.set("year", "");
            refreshCertificatesGrid();
        },

        oneMonthClick: function () {
            this.set("month", 1);
            this.set("year", "");
            refreshCertificatesGrid();
        },

        twoMonthClick: function () {
            this.set("month", 2);
            this.set("year", "");
        refreshCertificatesGrid();
        },

        threeMonthClick: function () {
            this.set("month", 3);
            this.set("year", "");
         refreshCertificatesGrid();
        },

        thisYearClick: function () {
            this.set("year", 0);
            this.set("month", "");
            refreshCertificatesGrid();
        },
        totalClick: function () {
            this.set("month", "");
            this.set("year", "");
            refreshCertificatesGrid();
         }
    });

    kendo.bind(document.body.children, viewModel);
    </script>
