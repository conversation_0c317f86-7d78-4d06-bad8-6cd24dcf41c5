﻿@model MaintenanceRecordStepModel

<div class="header-container-between">
    <h4>
        <i class="fa fa-file"></i>
        Maintenance Record Step
    </h4>
    <div>
       <a class="btn btn-info btn-sm" href="@Url.Action("EditMaintenanceRecord","Maintenance",new { @id = Model.MaintenanceRecordId} )"><i class="fa fa-refresh"></i>Go to MR</a>
    </div>
</div>
<hr />


@using (Html.BeginForm("EditMaintenanceRecordStep", "Maintenance", FormMethod.Post, new { @enctype = "multipart/form-data" })) {
    @Html.ValidationSummary(false)
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label>Type</label>
                <br />
                @(Html.Kendo()
                 .DropDownListFor(m => m.Type)
                 .OptionLabel("Select Type")
                 .DataValueField("Key")
                 .DataTextField("Value")
                 .Filter("contains")
                 .HtmlAttributes(new { @style = "width:225px", @data_value_primitive = "true", @data_bind = "value:stepType", @disabled = "disabled" })
                 .BindTo(Centerpoint.Common.Constants.MaintenanceBlueprintStepTypeConstant.ValuesAndDescriptions.ToList()))
            </div>
        </div>
    </div>
    <div class="row" data-bind="visible:freeTextVisible">
        <div class="col-md-8">
            <div class="form-group">
                <label>Step Name</label>
                <br />
                @(Html.Kendo().TextBoxFor(m => m.Name)
                .HtmlAttributes(new { @style = "width:505px", @disabled = "disabled" }))
            </div>
        </div>
    </div>
    <div class="row" data-bind="visible:freeTextVisible">
        <div class="col-md-12">
            <div class="form-group">
                <br />
                <label>Step Description</label>
                @(Html.Kendo().EditorFor(m => m.Description)
                         .Tools(t => t.Clear()
                         .Bold().Italic().Underline().Strikethrough()
                         .FontColor().FontSize()
                         .JustifyCenter().JustifyFull().JustifyLeft().JustifyRight()
                         .InsertUnorderedList().InsertOrderedList().Indent().Outdent()
                         .CreateLink().Unlink()
                         .SubScript()
                         .SuperScript())
                         .Resizable(resizable => resizable.Content(true).Toolbar(true))
                         .HtmlAttributes(new { @style = "width:77%", @rows = "10" })
                         .Encoded(false))
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12" data-bind="visible:resultVisible">
            <label>Results</label>
            @(Html.TextAreaFor(m => m.Result, new { @class = "form-control", @style = "width:50%", @rows = "5" }))
            <br />
        </div>
        <div class="col-md-12">
            <label>Comments and Notes</label>
            @(Html.TextAreaFor(m => m.Note, new { @class = "form-control", @style = "width:50%", @rows = "5" }))
            <br />
        </div>
    </div>
    <div class="row" data-bind="visible:checkBoxVisible">
        <div class="col-md-8">
            <div class="form-group">
                <label>Task</label>
                <br />
                @(Html.Kendo().TextBox()
                .Name("taskInput")
                .HtmlAttributes(new { @class = "form-control", @data_bind = "value:task", @data_value_update = "keyup", @style = "width:505px" }))
                @Html.HiddenFor(m => m.Tasks, new { @data_bind = "value:stepTasks" })
            </div>
        </div>
        <div class="col-md-2" style="margin-top:25px; margin-left:-100px;">
            <a class="btn btn-info btn-sm" data-bind="click:addtaskClick, enabled:canAddTask">Add Task</a>
        </div>
    </div>
        <div class="box-col" style="width: 77% " data-bind="visible:checkBoxVisible">
            <div data-role="listview"
                 data-template="taskTemplate"
                 data-bind="source: tasks, click:deleteTaskClick"
                 style="width: 100%; height: 125px; overflow:auto"></div>
        </div>
    if (!Model.DocumentId.HasValue) {
        <div class="row" data-bind="visible:documentVisible">
            <div class="col-md-6">
                @(Html.Kendo().Upload()
         .Name("file")
         .Messages(m => m.Select("Select a file"))
         .HtmlAttributes(new { @style = "width:50%" })
         .Events(e => e.Upload("uploadValidation"))
         .Multiple(false))
            </div>
        </div>
    }
    if (Model.DocumentId.HasValue) {
    <div class="row" data-bind="visible:documentVisible">
        <div class="col-md-6">
            <label>Attachment</label>
            <br />
            <a href="@Url.Action("Index", "Document", new { @id = Model.DocumentId})">@Model.DocumentFileName</a>
        </div>
    </div>
    <br />
    <button class="btn btn-danger btn-sm " data-bind="click:deleteAttachment"><i class="fa fa-ban"></i>Delete Attachment</button>
    }
<br />
<input type="submit" class="btn btn-primary btn-sm" value="Save MR Step Details" />
@Html.HiddenFor(m => m.MaintenanceRecordId)
@Html.HiddenFor(m => m.MaintenanceRecordStepId)
@Html.HiddenFor(m => m.Type)
@Html.HiddenFor(m => m.Name)
@Html.HiddenFor(m => m.Description)
            }


    <script type="text/x-kendo-tmpl" id="taskTemplate">
        <div style="padding:3px 6px; border-bottom:1px solid \\#ccc">
            <a class="btn btn-danger btn-xs pull-right" onclick="deleteTaskClick('#:data#');" title="Delete Task"><i class="fa fa-trash" style="margin:0px"></i></a>
            <p style="padding:3px 6px 0px 3px; margin-bottom:0; color:\\#555555;">#:data#</p>
        </div>
    </script>
    
    <script>
        const editMaintenanceRecordStepModel = {
            modelTasks: "@Model.Tasks",
            modelType: "@Model.Type",
            centerpointCommonConstantsMaintenanceBlueprintStepTypeConstantBoolean: "@Centerpoint.Common.Constants.MaintenanceBlueprintStepTypeConstant.Boolean",
            centerpointCommonConstantsMaintenanceBlueprintStepTypeConstantText: "@Centerpoint.Common.Constants.MaintenanceBlueprintStepTypeConstant.Text",
            centerpointCommonConstantsMaintenanceBlueprintStepTypeConstantDocument: "@Centerpoint.Common.Constants.MaintenanceBlueprintStepTypeConstant.Document",
            modelMaintenanceRecordStepId: "@Model.MaintenanceRecordStepId"
        }
    </script>

    <environment include="Development">
        <script src="~/js/views/maintenance/EditMaintenanceRecordStep.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script src="~/js/views/maintenance/EditMaintenanceRecordStep.min.js" asp-append-version="true"></script>
    </environment>
