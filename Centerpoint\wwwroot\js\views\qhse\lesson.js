
$(document).ready(function () {
    var lessonGrid = $('#lessonGrid').data("kendoGrid");
    lessonGrid.bind('dataBound', function (e) {
        this.element.find('.k-i-excel').remove();
    });
    getLessonsLearnedSummary();
    loadLessonGrid();

});



function loadLessonGrid() {
    var grid = $("#lessonGrid").data("kendoGrid");
    var toolBar = $("#lessonGrid .k-grid-toolbar").html();
    var options = localStorage["lessonGrid"];
    viewModel.set("initialLessonGridOptions", kendo.stringify(grid.getOptions()));
    if (options) {
        grid.setOptions(JSON.parse(options));
        $("#lessonGrid .k-grid-toolbar").html(toolBar);
        $("#lessonGrid .k-grid-toolbar").addClass("k-grid-top");
    }
}

function saveLessonGrid(e) {
    setTimeout(function () {
        var grid = $("#lessonGrid").data("kendoGrid");
        localStorage["lessonGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}


function updateLessonGrid() {
    $("#resetLessonGrid").click(function (e) {
        e.preventDefault();
        resetGridView('lessonGrid', 'initialLessonGridOptions')
    });
    var lessonGrid = $("#lessonGrid").data("kendoGrid");
    var total = lessonGrid.dataSource.total();
    viewModel.set("lessonCount", total);
}

function getLessonsLearnedSummary() {
    $.ajax({
        url: "/Qhse/GetLessonsLearnedSummary",
        data: {
        },
        success: function (result) {
            if (result) {
                viewModel.set("totalCreated", result.TotalCreated);
                viewModel.set("totalByYou", result.TotalByYou);
                viewModel.set("totalPendingByYou", result.TotalPendingByYou);
            }
        },
        dataType: "json"
    });
}

function lessonData() {
    return {
        lessonCategoryId: viewModel.get("lessonCategoryId"),
        titleOnly: viewModel.get("titleOnly"),
        equipmentCategoryIds: viewModel.get("equipmentCategoryIds").toJSON(),
        objectiveIds: viewModel.get("objectiveIds").toJSON(),
        clientId: viewModel.get("clientId"),
        query: viewModel.get("query"),
        type: viewModel.get("type"),
    };
}

function refreshLessonsGrid() {
    var lessonGrid = $("#lessonGrid").data("kendoGrid");
    lessonGrid.dataSource.read();
}

var viewModel = new kendo.observable({
    lessonCount: 0,
    totalCreated: 0,
    totalByYou: 0,
    initialLessonGridOptions: null,
    totalPendingByYou: 0,
    titleOnly: true,
    lessonCategoryId: "",
    equipmentCategoryIds: [],
    objectiveIds: [],
    clientId: "",
    query: "",
    type: "",

    totalClick: function () {
        this.set("type", "total");
        refreshLessonsGrid();
    },
    byYouClick: function () {
        this.set("type", "byYou");
        refreshLessonsGrid();
    },
    pendingApprovalClick: function () {
        this.set("type", "pendingApproval");
        refreshLessonsGrid();
    },
    searchButtonClick: function () {
        refreshLessonsGrid();
    },

    resetButtonClick: function () {
        this.set("titleOnly", true);
        this.set("lessonCategoryId", "");
        this.set("equipmentCategoryIds", []);
        this.set("objectiveIds", []);
        this.set("clientId", "");
        this.set("query", "");

        $("#lessonGrid").data("kendoGrid").dataSource.read();
    }
});

kendo.bind(document.body.children, viewModel);