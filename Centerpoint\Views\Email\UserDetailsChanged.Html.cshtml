﻿@model Centerpoint.Model.ViewModels.UserDetailsChangedEmail
<html>
<body>      
    <p style="font-family:Arial,sans-serif">The user @Model.Username has updated their details on Centerpoint.</p>
    <p style="font-family:Arial,sans-serif">The following items where changed:</p>
    @foreach (var userDetails in Model.UserPropertiesChanged) { 
        <p style="font-family:Arial,sans-serif">@userDetails</p>
    }
</body>
</html>