﻿using Centerpoint.Extensions;
using Centerpoint.Service.Interfaces;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Centerpoint.Controllers
{
    [Authorize]
    public class ExchangeRateController : Controller
    {
        private readonly IExchangeRateService _exchangeRateService;

        public ExchangeRateController(IExchangeRateService exchangeRateService)
        {
            _exchangeRateService = exchangeRateService;
        }

        public ActionResult Index()
        {
            this.SetTitle("Exchange Rates");
            return View();
        }

        public async Task< ActionResult> GetCurrencies([DataSourceRequest] DataSourceRequest request)
        {
            var result = await _exchangeRateService.GetCurrenciesAsync();

            return Json(result.ToDataSourceResult(request));
        }
    }
}