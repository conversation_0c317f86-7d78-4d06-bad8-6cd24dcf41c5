﻿@model RiskIdentificationSafetyControlModel
@{var cardName = GlobalSettings.IsWellsense ? "SOC" : "RISC";}  
@Html.Partial("_GridNotification", EntityType.RiskIdentificationSafetyControl)

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-bars"></i>
        @(Model.RiskIdentificationSafetyControlId.HasValue ? Model.NameandStatus : $"Create New {cardName} Card")
    </h4>
</div>
<hr />


@using (Html.BeginForm("EditRiskIdentificationSafetyControl", "Qhse", FormMethod.Post, new { @id = "riscForm" })) {
    @Html.ValidationSummary(false)
    <div>
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    @Html.LabelFor(m => m.Date)
                    <br />
                    @(Html.Kendo().DateTimePickerFor(m => m.Date)
                    .Interval(15)
                    .Enable(false)
                    .HtmlAttributes(new { @id = "dateTimePicker", @class = "utcTimePicker" }))
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    @Html.LabelFor(m => m.Name)
                    <br />
                    @(Html.Kendo().TextBoxFor(m => m.Name)
                    .HtmlAttributes(new { @class = "form-control", @style = "height :38px; font-size:14px", @disabled = "disabled" }))
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    @Html.LabelFor(m => m.OnshoreOffshore)
                    <br />
                    @(Html.Kendo().DropDownListFor(m => m.OnshoreOffshore)
                        .HtmlAttributes(new { @style = "font-size: 14px" })
                        .BindTo(OnshoreOffshoreConstants.Values))

                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-8">
                @if (GlobalSettings.IsAisus)
                {
                    <div class="form-group">
                        <label>Project</label>
                        <br />
                        @(Html.Kendo().DropDownListFor(m => m.ItemId)
                            .Filter(FilterType.Contains)
                            .DataTextField("ProjectTitle")
                            .OptionLabel("Select Project")
                            .DataValueField("ProjectId")
                            .Enable(Model.NotRelated ? false : true)
                            .HtmlAttributes(new { @style = "font-size: 14px" })
                            .DataSource(d => d.Read("GetAllProjects", "Lookup")))
                    </div>
                }
                else
                {
                    <div class="form-group">
                        <label>Job</label>
                        <br />
                        @(Html.Kendo().DropDownListFor(m => m.ItemId)
                            .Filter(FilterType.Contains)
                            .DataTextField("JobName")
                            .OptionLabel("Select Job")
                            .DataValueField("JobId")
                            .Enable(Model.NotRelated ? false : true)
                            .HtmlAttributes(new { @style = "font-size: 14px" })
                            .DataSource(d => d.Read("GetJobs", "Lookup")))
                    </div>
                }
                
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    @if (GlobalSettings.IsAisus)
                    {
                        <label>Not Project Related</label>
                    }
                    else
                    {
                        <label>Not Job Related</label>
                    }
                    <br />
                    @(Html.CheckBoxFor(m => m.NotRelated, new {@onclick = "resetJob(this.checked)" }))
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    @Html.LabelFor(m => m.Location)
                    <br />
                    @(Html.Kendo().TextBoxFor(m => m.Location)
                    .HtmlAttributes(new { @class = "form-control", @style = "height :38px; font-size:14px" }))
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    @Html.LabelFor(m => m.Area)
                    <br />
                    @(Html.Kendo().TextBoxFor(m => m.Area)
                    .HtmlAttributes(new { @class = "form-control", @style = "height :38px; font-size:14px" }))
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12 col-md-10 col-lg-7 col-xl-5">
                <div class="form-group d-flex">
                    <div class="radio-inline col-6 ">
                        @Html.RadioButton("HazardObservation", "Hazard Identification", true, new { id = "hazard" }) Hazard Identification
                    </div>
                    <div class="radio-inline col-6">
                        @Html.RadioButton("HazardObservation", "Observation/Intervention", new { id = "observation" }) Observation/Intervention
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12 col-md-10 col-lg-7 col-xl-5">
                <div class="form-group d-flex">
                    <div class="radio-inline col-6 ">
                        @Html.RadioButton("PositiveNegative", "Positive", true, new { id = "positive" }) Positive
                    </div>
                    <div class="radio-inline col-6">
                        @Html.RadioButton("PositiveNegative", "Corrective", new { id = "corrective" }) Corrective
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12 col-md-10 col-lg-7 col-xl-5">
                <div class="form-group d-flex">
                    <div class="radio-inline col-6">
                        @Html.RadioButton("SafetyEnvironment", "Safety", true, new { id = "safety" }) Safety
                    </div>
                    <div class="radio-inline col-6">
                        @Html.RadioButton("SafetyEnvironment", "Environment", new { id = "environment" }) Environment
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12 col-md-10 col-lg-7 col-xl-5">
                <div class="form-group d-flex">
                    <div class="radio-inline col-6">
                        @Html.RadioButton("BehaviourCondition", "Behaviour", true, new { id = "behaviour" }) Behaviour
                    </div>
                    <div class="radio-inline col-6">
                        @Html.RadioButton("BehaviourCondition", "Condition", new { id = "condition" }) Condition
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-2">
                <div class="form-group">
                    @if (GlobalSettings.IsAisus)
                    {
                        <label>Was the project stopped?</label>
                    }
                    else{
                        <label>Was the job stopped?</label>
                    }
                    @(Html.Kendo().DropDownListFor(p => p.ItemStopped)
                        .DataValueField("Key")
                        .DataTextField("Value")
                        .Filter(FilterType.Contains)
                        .BindTo(Centerpoint.Common.Constants.JobStoppedConstant.ValuesAndDescriptions.ToList()))
                </div>
            </div>
            <div class="col-md-2">
                <div class="form-group">
                    @Html.LabelFor(m => m.NearMiss)
                    <br />
                    @(Html.Kendo().DropDownListFor(p => p.NearMiss)
                    .DataValueField("Key")
                    .DataTextField("Value")
                    .Filter(FilterType.Contains)
                    .BindTo(new List<KeyValuePair<string, string>> { new KeyValuePair<string, string>(Boolean.FalseString, "No"), new KeyValuePair<string, string>(Boolean.TrueString, "Yes"), }))
                </div>
            </div>
        </div>
        <hr />
        <div class="row">
            <div class="col-md-10">
                <div class="form-group">
                    @Html.LabelFor(m => m.ObservationDetails)
                    @Html.TextAreaFor(m => m.ObservationDetails, new { @class = "form-control", @rows = 5, @style = "width:100%" })
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-10">
                <div class="form-group">
                    @Html.LabelFor(m => m.ActionDetails)
                    @Html.TextAreaFor(m => m.ActionDetails, new { @class = "form-control", @rows = 5, @style = "width:100%" })
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-10">
                <div class="form-group">
                    @Html.LabelFor(m => m.FollowUpDetails)
                    @Html.TextAreaFor(m => m.FollowUpDetails, new { @class = "form-control", @rows = 5, @style = "width:100%" })
                </div>
            </div>
        </div>

        @if ((Html.IsGlobalAdmin() || Html.IsQhseAdmin() || Html.IsLessonsLearnedAdmin()) && Model.RiskIdentificationSafetyControlId.HasValue) {
            <div class="row">
                <div class="col-md-10">
                    <div class="form-group">
                        @Html.LabelFor(m => m.HseComment)
                        @Html.TextAreaFor(m => m.HseComment, new { @class = "form-control", @rows = 5, @style = "width:100%" })
                    </div>
                </div>
            </div>
        } else {
            <div class="row">
                <div class="col-md-10">
                    <div class="form-group">
                        @Html.LabelFor(m => m.HseComment)
                        @Html.TextAreaFor(m => m.HseComment, new { @class = "form-control", @rows = 5, @data_bind = "value:comment", @style = "width:100%", @readonly = "readonly" })
                    </div>
                </div>
            </div>
        }
        <br />
        <br />
        <div class="d-flex actionsContainer">
            @if (!string.IsNullOrEmpty(Model.Status) && !Model.RiskIdentificationSafetyControlId.HasValue) {
                @if (GlobalSettings.IsWellsense)
                {
                    <button type="submit" class="btn btn-success btn-sm">Submit SOC Card</button>
                }
                else
                {
                    <button type="submit" class="btn btn-success btn-sm">Submit RISC Card</button>
                }
            }
            @if ((Html.IsGlobalAdmin() || Html.IsQhseAdmin()) && Model.RiskIdentificationSafetyControlId.HasValue && Model.Status != RiscStatusConstant.Completed) {
                <button type="submit" class="btn btn-primary btn-sm">Save</button>
            }
                    @if ((Html.IsGlobalAdmin() || Html.IsQhseAdmin()) && Model.Status == RiscStatusConstant.Completed) {
                <button class="btn btn-danger btn-sm" data-bind="click:deleteRisc">Delete</button>
            }
            @if ((Html.IsGlobalAdmin() || Html.IsQhseAdmin()) && Model.RiskIdentificationSafetyControlId.HasValue && Model.Status != RiscStatusConstant.Completed) {
                <button class="btn btn-sm btn-success" data-bind="click:signOffRisc">Sign off</button>
                <button class="btn btn-danger btn-sm" data-bind="click:deleteRisc">Delete</button>
            }
        </div>
        
        @if (Model.Status == RiscStatusConstant.Completed) {
        <hr />
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label>Sign Off By </label>
                    <br />
                    <h4>@Model.HseName</h4>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label>Sign Off Date </label>
                    <br />
                        <h4 class="utcTimePicker">@Model.HseDate.GetValueOrDefault()</h4>
                </div>
            </div>
        </div>
        }

        @Html.HiddenFor(m => m.RiskIdentificationSafetyControlId)
        @Html.HiddenFor(m => m.Status)
        @Html.HiddenFor(m => m.RiscNumber)
        @Html.HiddenFor(m => m.RaisedByUser)
        @Html.HiddenFor(m => m.Name)
        @Html.HiddenFor(m => m.Date)
        @Html.HiddenFor(m => m.OnshoreOffshore)
    </div>
}

<script>
    const editRiskIdentificationSafetyControlModel = {
        modelNotJobRelated: "@(Model.NotRelated ? "true" : "false")",
        modelComment: "@(Html.Raw(!string.IsNullOrWhiteSpace(Model.HseComment) ? Model.HseComment.Replace(System.Environment.NewLine, "").Replace("\"", "") : string.Empty))",
        modelRiskIdentificationSafetyControlId: "@Model.RiskIdentificationSafetyControlId",
        modelStatus: "@Model.Status",
        riscStatusConstantSubmitted:"@RiscStatusConstant.Submitted",
        riscStatusConstantCompleted:"@RiscStatusConstant.Completed",
        modelDateMonth: "@Model.Date.Month",
        cardName:"@cardName",
    }

    $(document).ready(function () {
        $('#riscForm').submit(function (e) {
            e.preventDefault();

            var formData = $(this).serialize();

            $.ajax({
                type: 'POST',
                url: '/Qhse/EditRiskIdentificationSafetyControl',
                data: formData,
                success: function (result) {
                    $("<div id='dialog'></div>").kendoDialog({
                        closable: false,
                        title: result.RiscNumber + editRiskIdentificationSafetyControlModel.cardName,
                        close: () => {
                            var dialog = $("#dialog").data("kendoDialog");
                            dialog.destroy();
                        },
                        width: 500,
                        buttonLayout: "normal",
                        content: result.RiscNumber + editRiskIdentificationSafetyControlModel.cardName + " details have been successfully updated",
                        actions: [
                            {
                                text: "OK",
                                action: function () {
                                    window.location.href = '/Qhse/EditRiskIdentificationSafetyControl?id=' + result.RiskIdentificationSafetyControlId;
                                },
                                cssClass: 'btn-primary'
                            }]
                    }).data("kendoDialog").open().center()
                },
                error: function (xhr, textStatus, errorThrown) {
                    jqXHRErrors(e)
                }
            });
        });

    });

</script>

<environment include="Development">
    <script src="~/js/views/qhse/editRiskIdentificationSafetyControl.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/qhse/editRiskIdentificationSafetyControl.min.js" asp-append-version="true"></script>
</environment>
