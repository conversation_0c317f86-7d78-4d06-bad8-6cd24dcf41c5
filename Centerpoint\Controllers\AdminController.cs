﻿using Centerpoint.Common;
using Centerpoint.Common.Constants;
using Centerpoint.Excel.Import;
using Centerpoint.Extensions;
using Centerpoint.Model.Configuration;
using Centerpoint.Model.Entities;
using Centerpoint.Model.ViewModels;
using Centerpoint.Service.Interfaces;
using Centerpoint.Service.Services;
using Centerpoint.Services;
using Centerpoint.Storage.Interfaces;
using Centerpoint.Utils;
using Hangfire;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Options;
using System.Net;
using static Centerpoint.Common.Constants.AllowedExt;

namespace Centerpoint.Controllers
{
    [Authorize]
    public class AdminController : Controller
    {
        private readonly IAdminService _adminService;
        private readonly ICurrentUserService _currentUser;
        private readonly IIdentityService _identityService;
        private readonly IEmailManager _emailManager;
        private readonly IDocumentService _documentService;
        private readonly IOptions<Settings> _settingsClient;
        private readonly UserManager<User> _userManager;
        private readonly IAssetService _assetService;
        private readonly IExportBackgroundService _exportBackgroundService;
        private readonly IStorage _storage;

        private readonly Settings _settings;

        public AdminController(IAdminService adminService,
            ICurrentUserService currentUser,
            IIdentityService identityService,
            IEmailManager emailManager,
            IDocumentService documentService,
            UserManager<User> userManager,
            IOptions<Settings> settings,
            IOptions<Settings> settingsClient,
            IAssetService assetService,
            IExportBackgroundService exportBackgroundService,
            IStorage storage)
        {
            _adminService = adminService;
            _currentUser = currentUser;
            _identityService = identityService;
            _emailManager = emailManager;
            _documentService = documentService;
            _userManager = userManager;
            _settings = settings.Value;
            _settingsClient = settingsClient;
            _assetService = assetService;
            _exportBackgroundService = exportBackgroundService;
            _storage = storage;
        }
        [Authorize(Roles = $"{UserRoleConstant.GlobalAdministrator},{UserRoleConstant.PersonnelAdministrator}")]
        public async Task<ActionResult> Certificates()
        {
            var model = await _adminService.Certificates();
            this.SetTitle("Certificates");
            return View(model);
        }

        [Authorize(Roles = UserRoleConstant.AdminRoles)]
        public async Task<ActionResult> Companies()
        {
            this.SetTitle("Companies");
            return View();
        }

        [Authorize(Roles = $"{UserRoleConstant.GlobalAdministrator}")]
        public async Task<ActionResult> Categories()
        {
            this.SetTitle("Categories");
            return View();
        }

        [Authorize(Roles = $"{UserRoleConstant.GlobalAdministrator},{UserRoleConstant.DownloadAdministrator}")]
        public async Task<ActionResult> Downloads()
        {
            this.SetTitle("Downloads");
            return View();
        }

        public async Task<ActionResult> Elements()
        {
            this.SetTitle("Elements");
            return View();
        }

        [Authorize(Roles = $"{UserRoleConstant.GlobalAdministrator},{UserRoleConstant.OperationsAdministrator}")]
        public async Task<ActionResult> Services()
        {
            this.SetTitle("Services");
            return View();
        }

        public async Task<ActionResult> Bases()
        {
            this.SetTitle("Base");
            return View();
        }

        [Authorize(Roles = $"{UserRoleConstant.GlobalAdministrator}")]
        public async Task<ActionResult> Divisions()
        {
            this.SetTitle("Division");
            return View();
        }

        [Authorize(Roles = $"{UserRoleConstant.GlobalAdministrator}, {UserRoleConstant.LogisticsAdministrator}")]
        public async Task<ActionResult> ShipmentMethods()
        {
            this.SetTitle("Shipment Method");
            return View();
        }

        public async Task<ActionResult> Severities()
        {
            ViewBag.Title = "Severity";
            ViewBag.Tab = "severity";
            return View("ServiceImprovementForms");
        }

        [Authorize(Roles = UserRoleConstant.CurrenyRoles)]
        public async Task<ActionResult> Currencies()
        {
            this.SetTitle("Currencies");
            return View();
        }

        [Authorize(Roles = $"{UserRoleConstant.GlobalAdministrator}, {UserRoleConstant.LogisticsAdministrator}")]
        public async Task<ActionResult> CustomStatusCodes()
        {
            this.SetTitle("Custom Status");
            return View();
        }

        [Authorize(Roles = UserRoleConstant.AssetAdminRoles)]
        public async Task<ActionResult> Equipment(string equipmentCategory)
        {
            this.SetTitle("Equipment");

            ViewBag.DepreciationYears = _settings.DepreciationTotalYears;
            ViewBag.EquipmentCategory = equipmentCategory;

            var location = new Uri($"{Request.Scheme}://{Request.Host}{Request.Path}");

            ViewBag.Url = location.AbsoluteUri;
            return View();
        }

        public async Task<ActionResult> ArchivedItems(string equipmentCategory)
        {
            this.SetTitle("Equipment");

            ViewBag.DepreciationYears = _settings.DepreciationTotalYears;
            ViewBag.EquipmentCategory = equipmentCategory;

            var location = new Uri($"{Request.Scheme}://{Request.Host}{Request.Path}");

            ViewBag.Url = location.AbsoluteUri;
            return View();
        }



        [Authorize(Roles = UserRoleConstant.SifRoles)]
        public async Task<ActionResult> ServiceImprovementForms()
        {
            this.SetTitle("Service Improvement Form");
            return View();
        }

        public async Task<ActionResult> ServiceImprovementLocations()
        {
            ViewBag.Title = "Service Improvement Location";
            ViewBag.Tab = "serviceImprovmentLocation";
            return View("ServiceImprovementForms");
        }

        public async Task<ActionResult> Lessons()
        {
            this.SetTitle("Lessons");
            return View();
        }

        [Authorize(Roles = $"{UserRoleConstant.GlobalAdministrator}, {UserRoleConstant.QHSEAdministrator},{UserRoleConstant.LessonsLearnedAdmin}")]
        public async Task<ActionResult> LessonCategories()
        {
            this.SetTitle("Lesson Categories");
            return View();
        }

        public async Task<ActionResult> Manufacturers()
        {
            this.SetTitle("Manufacturers");
            return View();
        }

        public async Task<ActionResult> SubCategories()
        {
            ViewBag.Title = "Sub Category";
            ViewBag.Tab = "subCategory";
            return View("ServiceImprovementForms");
        }

        [Authorize(Roles = $"{UserRoleConstant.GlobalAdministrator},{UserRoleConstant.PersonnelAdministrator}")]
        public async Task<ActionResult> Users()
        {
            this.SetTitle("Users");
            return View();
        }


        [Authorize(Roles = $"{UserRoleConstant.GlobalAdministrator},{UserRoleConstant.PersonnelAdministrator}")]
        public async Task<ActionResult> CertificateCategories()
        {
            this.SetTitle("Certificate Category");
            return View();
        }

        [Authorize(Roles = $"{UserRoleConstant.GlobalAdministrator},{UserRoleConstant.SalesAdministrator}")]
        public async Task<ActionResult> ActionTypes()
        {
            this.SetTitle("Action Types");
            return View();
        }

        [Authorize(Roles = $"{UserRoleConstant.GlobalAdministrator},{UserRoleConstant.SalesAdministrator}")]
        public async Task<ActionResult> EventTypes()
        {
            this.SetTitle("Event Types");
            return View();
        }

        [Authorize(Roles = $"{UserRoleConstant.GlobalAdministrator},{UserRoleConstant.SalesAdministrator}")]
        public async Task<ActionResult> OpportunityClosedReasons()
        {
            this.SetTitle("Event Types");
            return View();
        }

        public async Task<ActionResult> FluidTypes()
        {
            this.SetTitle("Fluid Types");
            return View();
        }

        public async Task<ActionResult> CustomerAssets()
        {
            this.SetTitle("Customer Assets");
            return View();
        }

        [Authorize(Roles = $"{UserRoleConstant.GlobalAdministrator}")]
        public async Task<ActionResult> SystemSettings()
        {
            var model = await _adminService.GetSettings();
            if (model == null)
            {
                model = new SystemSettingsModel
                {
                    Event = _settings.EventTime,
                    MaxLoginAttempts = _settings.LoginAttempts,
                    DepreciationYears = _settings.DepreciationTotalYears
                };
            }


            this.SetTitle("System Settings");
            return View(model);
        }

        public async Task<ActionResult> ViewCustomerAssets(int id, int? OpportunityId, int? revision, int? ansaProjectId)
        {
            var model = await _adminService.GetCompany(id);

            this.SetTitle(string.Format("{0} {1}", model.Name, model.Description));

            if (OpportunityId.HasValue)
            {
                ViewBag.OpportunityId = OpportunityId;
            }

            if (revision.HasValue)
            {
                ViewBag.revision = revision;
            }

            if (ansaProjectId.HasValue)
            {
                ViewBag.AnsaProjectId = ansaProjectId;
            }

            ViewBag.CompanyId = id;

            return View(model);
        }

        #region Company

        public async Task<ActionResult> GetCompanies([DataSourceRequest] DataSourceRequest dataCompany)
        {
            var data = await _adminService.GetCompanies();

            return Json(data.ToDataSourceResult(dataCompany));
        }

        public async Task<ActionResult> GetCompaniesByCustomerAssets([DataSourceRequest] DataSourceRequest dataCompany)
        {
            var data = await _adminService.GetCompaniesByCustomerAssets();

            return Json(data.ToDataSourceResult(dataCompany));
        }

        public async Task<ActionResult> AddCompany(int? OpportunityId, int? revision)
        {
            var model = new CompanyModel
            {
                IsActive = true
            };

            if (OpportunityId.HasValue)
            {
                ViewBag.OpportunityId = OpportunityId;
            }

            if (revision.HasValue)
            {
                ViewBag.Revision = revision;
            }

            this.SetTitle("Add New Entity");

            return View("EditCompany", model);
        }

        public async Task<ActionResult> EditCompany(int id)
        {
            var model = await _adminService.GetCompany(id);

            this.SetTitle(string.Format("{0} {1}", model.Name, model.Description));

            ViewBag.CompanyId = id;

            return View(model);
        }

        [HttpPost]
        public async Task<ActionResult> EditCompany([DataSourceRequest] DataSourceRequest request, CompanyModel model)
        {
            if (!ModelState.IsValid)
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)));
            }

            var result = await _adminService.EditCompany(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new { id = result.Response.CompanyId });
        }

        [HttpPost]
        public async Task<ActionResult> CreateCompany([DataSourceRequest] DataSourceRequest request, CompanyModel model)
        {
            if (!ModelState.IsValid)
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)));
            }

            var result = await _adminService.CreateCompany(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }
            return Json(new { id = result.Response.CompanyId });
        }

        public async Task<ActionResult> DeleteCompany([DataSourceRequest] DataSourceRequest request, CompanyModel model)
        {
            var result = await _adminService.DeleteCompany(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        #endregion

        #region Company Locations

        public async Task<ActionResult> GetCompanyLocations([DataSourceRequest] DataSourceRequest request, int cId)
        {
            return Json((await _adminService.GetCompanyLocations(cId)).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateCompanyLocation([DataSourceRequest] DataSourceRequest request, CompanyLocationModel model, int cId)
        {
            return Json((await _adminService.UpdateCompanyLocation(model, cId, _currentUser.UserId)).ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteCompanyLocation([DataSourceRequest] DataSourceRequest request, CompanyLocationModel model)
        {
            return Json((await _adminService.DeleteCompanyLocation(model)).ToDataSourceResult(request));
        }

        #endregion

        #region Company Contacts

        public async Task<ActionResult> GetCompanyContacts([DataSourceRequest] DataSourceRequest request, int companyId)
        {
            return Json((await _adminService.GetCompanyContacts(companyId)).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateCompanyContact([DataSourceRequest] DataSourceRequest request, CompanyContactModel model)
        {
            var result = await _adminService.UpdateCompanyContact(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteCompanyContact([DataSourceRequest] DataSourceRequest request, CompanyContactModel model)
        {
            var result = await _adminService.DeleteCompanyContact(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        #endregion

        #region Lesson Category

        public async Task<ActionResult> GetLessonCategories([DataSourceRequest] DataSourceRequest request)
        {
            return Json((await _adminService.GetLessonCategories()).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateLessonCategory([DataSourceRequest] DataSourceRequest request, LessonCategoryModel model)
        {
            var result = await _adminService.UpdateLessonCategory(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteLessonCategory([DataSourceRequest] DataSourceRequest request, LessonCategoryModel model)
        {
            return Json((await _adminService.DeleteLessonCategory(model)).ToDataSourceResult(request));
        }

        #endregion

        #region Objectives

        public async Task<ActionResult> GetObjectives([DataSourceRequest] DataSourceRequest request)
        {
            var result = await _adminService.GetObjectives();
            return Json(result.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateObjective([DataSourceRequest] DataSourceRequest request, ObjectiveModel model)
        {
            var result = await _adminService.UpdateObjective(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }
        public async Task<ActionResult> DeleteObjective([DataSourceRequest] DataSourceRequest request, ObjectiveModel model)
        {
            return Json((await _adminService.DeleteObjective(model)).ToDataSourceResult(request));
        }

        #endregion

        #region Currencies

        public async Task<ActionResult> GetCurrencies([DataSourceRequest] DataSourceRequest request)
        {
            return Json((await _adminService.GetCurrencies()).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateCurrency([DataSourceRequest] DataSourceRequest request, CurrencyModel model)
        {
            var result = await _adminService.UpdateCurrency(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteCurrency([DataSourceRequest] DataSourceRequest request, CurrencyModel model)
        {
            return Json((await _adminService.DeleteCurrency(model)).ToDataSourceResult(request));
        }

        #endregion

        #region Custom Status Codes

        public async Task<ActionResult> GetCustomStatusCodes([DataSourceRequest] DataSourceRequest request)
        {
            return Json((await _adminService.GetCustomStatusCodes()).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateCustomStatusCode([DataSourceRequest] DataSourceRequest request, CustomStatusCodeModel model)
        {
            var result = await _adminService.UpdateCustomStatusCode(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteCustomStatusCode([DataSourceRequest] DataSourceRequest request, CustomStatusCodeModel model)
        {
            var result = await _adminService.DeleteCustomStatusCode(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> Export(string contentType, string base64, string fileName)
        {
            var fileContents = Convert.FromBase64String(base64);

            return File(fileContents, contentType, fileName);
        }

        [HttpPost]
        public async Task<ActionResult> Pdf(string contentType, string base64, string fileName)
        {
            var fileContents = Convert.FromBase64String(base64);

            return File(fileContents, contentType, fileName);
        }

        #endregion

        #region Manufacturers

        public async Task<ActionResult> GetManufacturers([DataSourceRequest] DataSourceRequest request)
        {
            return Json((await _adminService.GetManufacturers()).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateManufacturer([DataSourceRequest] DataSourceRequest request, ManufacturerModel model)
        {
            var result = await _adminService.UpdateManufacturer(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteManufacturer([DataSourceRequest] DataSourceRequest request, ManufacturerModel model)
        {
            return Json((await _adminService.DeleteManufacturer(model)).ToDataSourceResult(request));
        }

        #endregion

        #region Divisions

        public async Task<ActionResult> GetDivisions([DataSourceRequest] DataSourceRequest request)
        {
            return Json((await _adminService.GetDivisions()).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateDivision([DataSourceRequest] DataSourceRequest request, DivisionModel model)
        {
            var result = await _adminService.UpdateDivision(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteDivision([DataSourceRequest] DataSourceRequest request, DivisionModel model)
        {
            var result = await _adminService.DeleteDivision(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        #endregion

        #region ShipmentMethods

        public async Task<ActionResult> GetShipmentMethods([DataSourceRequest] DataSourceRequest request)
        {
            return Json((await _adminService.GetShipmentMethods()).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateShipmentMethod([DataSourceRequest] DataSourceRequest request, ShipmentMethodModel model)
        {
            var result = await _adminService.UpdateShipmentMethod(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteShipmentMethod([DataSourceRequest] DataSourceRequest request, ShipmentMethodModel model)
        {
            return Json((await _adminService.DeleteShipmentMethod(model)).ToDataSourceResult(request));
        }

        #endregion

        #region ServiceImprovementLocations

        public async Task<ActionResult> GetServiceImprovementLocations([DataSourceRequest] DataSourceRequest request)
        {
            return Json((await _adminService.GetServiceImprovementLocations()).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateServiceImprovementLocation([DataSourceRequest] DataSourceRequest request, ServiceImprovementLocationModel model)
        {
            var result = await _adminService.UpdateServiceImprovementLocation(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteServiceImprovementLocation([DataSourceRequest] DataSourceRequest request, ServiceImprovementLocationModel model)
        {
            return Json((await _adminService.DeleteServiceImprovementLocation(model)).ToDataSourceResult(request));
        }

        #endregion

        #region Severities

        public async Task<ActionResult> GetSeverities([DataSourceRequest] DataSourceRequest request)
        {
            return Json((await _adminService.GetSeverities()).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateSeverity([DataSourceRequest] DataSourceRequest request, SeverityModel model)
        {
            var result = await _adminService.UpdateSeverity(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteSeverity([DataSourceRequest] DataSourceRequest request, SeverityModel model)
        {
            return Json((await _adminService.DeleteSeverity(model)).ToDataSourceResult(request));
        }

        #endregion

        #region SubCategories

        public async Task<ActionResult> GetSubCategories([DataSourceRequest] DataSourceRequest request)
        {
            return Json((await _adminService.GetSubCategories()).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateSubCategory([DataSourceRequest] DataSourceRequest request, SubCategoryModel model)
        {
            var result = await _adminService.UpdateSubCategory(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteSubCategory([DataSourceRequest] DataSourceRequest request, SubCategoryModel model)
        {
            return Json((await _adminService.DeleteSubCategory(model)).ToDataSourceResult(request));
        }

        #endregion

        #region Equipment Category

        public async Task<ActionResult> GetEquipmentCategories()
        {
            var result = await _adminService.GetEquipmentCategories();
            return Json(result);
        }

        public async Task<ActionResult> GetEquipmentCategory(int equipmentCategorId)
        {
            var result = await _adminService.GetEquipmentCategory(equipmentCategorId);
            return Json(result);
        }

        [HttpPost]
        public async Task<ActionResult> UpdateEquipmentCategoryParent(int equipmentCategoryId, int? parentEquipmentCategoryId)
        {
            await _adminService.UpdateEquipmentCategoryParent(equipmentCategoryId, parentEquipmentCategoryId);

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<ActionResult> UpdateEquipmentCategory(EquipmentCategoryModel model)
        {
            var EquipmentCategoryId = await _adminService.UpdateEquipmentCategory(model);

            return Json(new { id = EquipmentCategoryId });
        }

        [HttpPost]
        public async Task<ActionResult> DeleteEquipmentCategory(int equipmentCategoryId)
        {
            var result = await _adminService.DeleteEquipmentCategory(equipmentCategoryId);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new { success = "true" });
        }


        #endregion

        #region Equipment Item

        public async Task<ActionResult> GetEquipmentItems([DataSourceRequest] DataSourceRequest request, int? equipmentCategoryId)
        {
            return Json((await _adminService.GetEquipmentItems(equipmentCategoryId, _currentUser.UserId)).ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetArchivedEquipmentItems([DataSourceRequest] DataSourceRequest request)
        {
            var result = await _adminService.GetArchivedEquipmentItems(_currentUser.UserId);
            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(result.Response.ToDataSourceResult(request));
        }
        [HttpPost]
        public async Task<ActionResult> CreateEquipmentItem([DataSourceRequest] DataSourceRequest request, EquipmentItemModel model)
        {
            var result = await _adminService.CreateEquipmentItem(model, _settingsClient.Value.Client);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> DeleteEquipmentItem([DataSourceRequest] DataSourceRequest request, EquipmentItemModel model)
        {
            return Json((await _adminService.DeleteEquipmentItem(model)).ToDataSourceResult(request));
        }

        [HttpGet]
        public async Task<IActionResult> ExportAllAssets(string jobId = null)
        {
            // If jobId is provided, this is a status check
            if (!string.IsNullOrEmpty(jobId))
            {
                var status = await _exportBackgroundService.GetExportJobStatusAsync(jobId);
                if (status == null)
                {
                    return Json(new { error = "Job not found" });
                }

                return Json(new
                {
                    status = status.Status.ToLower(),
                    totalItems = status.TotalItems,
                    processedItems = status.ProcessedItems,
                    progressPercentage = status.ProgressPercentage,
                    downloadUrl = status.DownloadUrl,
                    errorMessage = status.ErrorMessage
                });
            }

            // Start new export job
            var newJobId = Guid.NewGuid().ToString();

            // Queue the background job
            BackgroundJob.Enqueue<IExportBackgroundService>(service =>
                service.ProcessAllEquipmentExportAsync(newJobId, _currentUser.UserId));

            return Json(new { jobId = newJobId, status = "started" });
        }

        [HttpGet]
        public async Task<IActionResult> DownloadExport(string jobId)
        {
            var status = await _exportBackgroundService.GetExportJobStatusAsync(jobId);
            if (status == null || status.Status != "Completed")
            {
                return NotFound("Export not found or not completed");
            }

            // Use the stored blob filename from the job status
            if (string.IsNullOrEmpty(status.BlobFileName))
            {
                return NotFound("Export file path not found");
            }

            var blobClient = await _storage.GetBlobClient(status.BlobFileName);

            if (!await blobClient.ExistsAsync())
            {
                return NotFound("Export file not found");
            }

            var stream = await blobClient.OpenReadAsync();
            var fileName = $"All_Equipment_Items_{DateTime.UtcNow:yyyyMMdd_HHmmss}.zip";

            return File(stream, "application/zip", fileName);
        }

        public async Task<ActionResult> EquipmentImport()
        {
            this.SetTitle("Equipment Data Import");
            return View();
        }

        [HttpPost]
        public async Task<ActionResult> ImportEquipmentItems(IFormFile formFile)
        {
            byte[] equipmentCategoryImportData = this.FileToByteArray(formFile);

            BackgroundJob.Enqueue<ImportUtility>(task => task.ImportEquipmentCategory(equipmentCategoryImportData));

            return this.Json(new { success = "true" });
        }
        public IActionResult GetEquipmentItemImportTemplate()
        {
            FileStream fileStreamPath = new FileStream("wwwroot/doc/Equipment_Item_Template.xlsx", FileMode.Open, FileAccess.Read, FileShare.ReadWrite);

            return File(fileStreamPath, "application/vnd.ms-excel", "Equipment_Item_Template.xlsx");
        }
        #endregion

        #region Fields

        public async Task<ActionResult> GetCompanyFields([DataSourceRequest] DataSourceRequest request, int companyId)
        {
            return Json((await _adminService.GetCompanyFields(companyId)).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateCompanyField([DataSourceRequest] DataSourceRequest request, CompanyFieldModel model, int compId)
        {
            var result = await _adminService.UpdateCompanyField(model, compId);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteCompanyField([DataSourceRequest] DataSourceRequest request, CompanyFieldModel model)
        {
            return Json((await _adminService.DeleteCompanyField(model)).ToDataSourceResult(request));
        }

        #endregion

        #region Wells

        public async Task<ActionResult> AddCompanyWell(int id, int? opportunityId, int? revision, int? ansaProjectId)
        {
            var model = new CompanyWellModel
            {
                CompanyId = id
            };

            if (opportunityId.HasValue)
            {
                ViewBag.OpportunityId = opportunityId.Value;
            }

            if (revision.HasValue)
            {
                ViewBag.Revision = revision.Value;
            }

            if (ansaProjectId.HasValue)
            {
                ViewBag.AnsaProjectId = ansaProjectId.Value;
                model.AnsaProjectId = ansaProjectId.Value;
            }

            this.SetTitle("Add New Well");

            return View("EditCompanyWell", model);
        }

        [HttpPost]
        public async Task<ActionResult> AddCompanyWell(CompanyWellModel model)
        {
            if (!ModelState.IsValid)
            {
                var error = string.Join(" | ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage));
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(new { success = "false", error });
            }

            var result = await _adminService.EditCompanyWell(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            this.SetMessage(MessageType.Success, string.Format("{0} company well details have been successfully updated", result.Response.Name));
            return Json(new { success = "true" });
        }

        public async Task<ActionResult> EditCompanyWell(int id, string tab, int? opportunityId, int? revision, int? projectId, int? ansaProjectId)
        {
            var model = await _adminService.GetCompanyWell(id, opportunityId, revision);
            this.SetTitle(string.Format("{0}", model.Name));

            if (opportunityId.HasValue)
            {
                ViewBag.OpportunityId = opportunityId.Value;
                model.OpportunityId = opportunityId.Value;
            }

            if (revision.HasValue)
            {
                ViewBag.Revision = revision.Value;
                model.Revision = revision.Value;
            }

            if (projectId.HasValue)
            {
                ViewBag.ProjectId = projectId.Value;
            }

            if (ansaProjectId.HasValue)
            {
                ViewBag.AnsaProjectId = ansaProjectId.Value;
            }

            ViewBag.Tab = tab;

            return View(model);
        }

        [HttpPost]
        public async Task<ActionResult> EditCompanyWell(CompanyWellModel model)
        {
            if (!ModelState.IsValid)
                return View(model);

            var opportunityId = model.OpportunityId;
            var revision = model.Revision;
            var ansaProjectId = model.AnsaProjectId;

            var result = await _adminService.EditCompanyWell(model);

            if (result.HasErrors())
            {
                this.SetMessage(MessageType.Error, string.Join(" | ", result.Errors));
                return View(model);
            }

            this.SetMessage(MessageType.Success, string.Format("{0} company well details have been successfully updated", result.Response.Name));

            if (opportunityId.HasValue && revision.HasValue)
                return RedirectToAction("EditCompanyWell", new { @id = result.Response.CompanyWellId, @opportunityId = opportunityId, @revision = revision });
            else if (ansaProjectId.HasValue)
                return RedirectToAction("EditCompanyWell", new { @id = result.Response.CompanyWellId, @ansaProjectId = ansaProjectId });

            return RedirectToAction("EditCompanyWell", new { @id = result.Response.CompanyWellId });
        }

        public async Task<ActionResult> GetCompanyWells([DataSourceRequest] DataSourceRequest request, int companyId)
        {
            return Json((await _adminService.GetCompanyWells(companyId)).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateCompanyWell([DataSourceRequest] DataSourceRequest request, CompanyWellModel model)
        {
            var result = await _adminService.UpdateCompanyWell(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteCompanyWell([DataSourceRequest] DataSourceRequest request, CompanyWellModel model)
        {
            var result = await _adminService.DeleteCompanyWell(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        #region Well Documents

        public async Task<ActionResult> GetCompanyWellDocuments([DataSourceRequest] DataSourceRequest request, int companyWellId)
        {
            return Json((await _adminService.GetCompanyWellDocuments(companyWellId)).ToDataSourceResult(request));

        }

        public async Task<ActionResult> AttachWellDocuments(IEnumerable<IFormFile> wellDocuments, int companyWellId, string documentType, DocumentModel model)
        {
            await _adminService.AttachWellDocuments(wellDocuments, companyWellId, documentType, _currentUser.UserId);
            return Json(true);
        }

        public async Task<ActionResult> DeleteCompanyWellDocument([DataSourceRequest] DataSourceRequest request, CompanyWellDocumentModel model)
        {
            await _adminService.DeleteCompanyWellDocument(model, _currentUser.UserId);
            return Json(new[] { model }.ToDataSourceResult(request));

        }
        #endregion

        #endregion

        #region User

        public async Task<ActionResult> GetUsers([DataSourceRequest] DataSourceRequest request, string baseCompanyLocationName)
        {
            return Json((await _adminService.GetUsers(baseCompanyLocationName)).ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetActiveUsers([DataSourceRequest] DataSourceRequest request)
        {
            return Json((await _adminService.GetActiveUsers()).ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetAllUsers([DataSourceRequest] DataSourceRequest request)
        {
            return Json((await _adminService.GetAllUsers()).ToDataSourceResult(request));
        }

        [Authorize(Roles = $"{UserRoleConstant.GlobalAdministrator},{UserRoleConstant.PersonnelAdministrator}")]
        public async Task<ActionResult> AddUser()
        {
            var model = new UserModel
            {
                IsEnabled = true,
                RolesList = new List<SelectListItem>
                { new SelectListItem
                    {
                        Value = "STU",
                        Text= "Standard User"
                    }
                }
            };

            this.SetTitle("Add New User");

            return View("EditUser", model);
        }

        [Authorize(Roles = $"{UserRoleConstant.GlobalAdministrator},{UserRoleConstant.PersonnelAdministrator}")]
        public async Task<ActionResult> EditUser(int id, bool? isCreated)
        {
            var model = await _identityService.GetUserModelAsync(id);
            this.SetTitle(string.Format("Edit User - {0}", model.Name));
            return View(model);
        }

        [HttpPost]
        public async Task<ActionResult> EditUser([DataSourceRequest] DataSourceRequest request, UserModel model)
        {
            if (!ModelState.IsValid)
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)));
            }

            var result = await _adminService.EditUser(Request, Url, model, _currentUser.UserId);
            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new { id = result.Response.Id });
        }

        [HttpPost]
        public async Task<ActionResult> CreateUser([DataSourceRequest] DataSourceRequest request, UserModel model)
        {
            if (!ModelState.IsValid)
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)));
            }

            var result = await _identityService.CreateUser(model);
            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new { id = result.Response.Id });
        }

        public async Task<ActionResult> DeleteUser([DataSourceRequest] DataSourceRequest request, UserModel model)
        {
            var result = await _adminService.DeleteUser(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { model }.ToDataSourceResult(request));
        }

        public async Task<ActionResult> ReplaceUser(int userId, int replacementUserId)
        {
            var result = await _adminService.ReplaceUser(userId, replacementUserId);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new { success = "true" });
        }

        #region Personnel Certificate Attachment Documents

        public async Task<ActionResult> GetPersonnelCertificates([DataSourceRequest] DataSourceRequest request, int uId)
        {
            return Json((await _adminService.GetPersonnelCertificates(uId)).ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetPersonnelCertificatesbyUser([DataSourceRequest] DataSourceRequest request)
        {
            return Json((await _adminService.GetPersonnelCertificatesbyUser(_currentUser.UserId)).ToDataSourceResult(request));

        }
        public async Task<ActionResult> GetAllPersonnelCertificates([DataSourceRequest] DataSourceRequest request, int? month, int? year)
        {
            return Json((await _adminService.GetAllPersonnelCertificates(month, year)).ToDataSourceResult(request));
        }

        public async Task<ActionResult> AttachPersonnelCertificateDocuments(IEnumerable<IFormFile> personnelCertificateAttachmentDocuments, PersonnelCertificateDocumentModel model)
        {
            await _adminService.AttachPersonnelCertificateDocuments(personnelCertificateAttachmentDocuments, model, _currentUser.UserId);
            return Json(true);
        }

        [HttpPost]
        public async Task<ActionResult> UpdatePersonnelCertificate([DataSourceRequest] DataSourceRequest request, PersonnelCertificateDocumentModel model, int uId)
        {
            var result = await _adminService.UpdatePersonnelCertificate(model, uId);
            return Json(result.ToDataSourceResult(request));

        }

        public async Task<ActionResult> DeletePersonnelCertificate([DataSourceRequest] DataSourceRequest request, PersonnelCertificateDocumentModel model)
        {
            await _adminService.DeletePersonnelCertificate(model);

            return Json(ModelState.ToDataSourceResult());
        }

        #endregion

        #region User Note

        public async Task<ActionResult> GetUserNotes([DataSourceRequest] DataSourceRequest request, int userId)
        {
            return Json((await _adminService.GetUserNotes(userId)).ToDataSourceResult(request));

        }

        [HttpPost]
        public async Task<ActionResult> UpdateUserNote([DataSourceRequest] DataSourceRequest request, UserNoteModel model, int uId)
        {
            return Json((await _adminService.UpdateUserNote(model, uId, _currentUser.UserId)).ToDataSourceResult(request));

        }

        public async Task<ActionResult> DeleteUserNote([DataSourceRequest] DataSourceRequest request, UserNoteModel model)
        {
            return Json((await _adminService.DeleteUserNote(model)).ToDataSourceResult(request));
        }

        #endregion

        #endregion

        #region CertificateCategories

        public async Task<ActionResult> GetCertificateCategories([DataSourceRequest] DataSourceRequest request)
        {
            return Json((await _adminService.GetCertificateCategories()).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateCertificateCategory([DataSourceRequest] DataSourceRequest request, CertificateCategoryModel model)
        {
            var result = await _adminService.UpdateCertificateCategory(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteCertificateCategory([DataSourceRequest] DataSourceRequest request, CertificateCategoryModel model)
        {
            return Json((await _adminService.DeleteCertificateCategory(model)).ToDataSourceResult(request));
        }

        #endregion

        #region Categories

        public async Task<ActionResult> GetCategories([DataSourceRequest] DataSourceRequest request)
        {
            return Json((await _adminService.GetCategories()).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateCategory([DataSourceRequest] DataSourceRequest request, CategoryModel model)
        {
            var result = await _adminService.UpdateCategory(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteCategory([DataSourceRequest] DataSourceRequest request, CategoryModel model)
        {
            return Json((await _adminService.DeleteCategory(model)).ToDataSourceResult(request));
        }

        #endregion

        #region Maintenance Blueprints

        public async Task<ActionResult> MaintenanceBlueprints()
        {
            this.SetTitle("Maintenance Blueprints");
            return View();
        }

        public async Task<ActionResult> GetMaintenanceBlueprints([DataSourceRequest] DataSourceRequest request)
        {
            return Json((await _adminService.GetMaintenanceBlueprints()).ToDataSourceResult(request));
        }

        // [IsMaintenanceAdmin]
        public async Task<ActionResult> AddMaintenanceBlueprint()
        {
            var model = new MaintenanceBlueprintModel();

            this.SetTitle("Add New Maintenance Blueprint");

            return View("EditMaintenanceBlueprint", model);
        }

        public async Task<ActionResult> EditMaintenanceBlueprint(int id, int? maintenanceRecordId)
        {
            var model = await _adminService.GetMaintenanceBlueprint(id);
            this.SetTitle(string.Format("Edit MaintenanceBlueprint - {0}", model.Name));

            return View(model);
        }

        [HttpPost]
        public async Task<ActionResult> EditMaintenanceBlueprint([DataSourceRequest] DataSourceRequest request, MaintenanceBlueprintModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            var maintenanceBlueprintId = await _adminService.EditMaintenanceBlueprint(model, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("'{0}' maintenance blueprint has been successfully updated", model.Name));

            return RedirectToAction("EditMaintenanceBlueprint", new { @id = maintenanceBlueprintId });
        }

        [HttpPost]
        public async Task<ActionResult> DeleteMaintenanceBlueprint(int id)
        {
            var result = await _adminService.DeleteMaintenanceBlueprint(id);

            if (result.HasErrors())
            {
                Response.StatusCode = (int) HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new { success = "true" });
        }

        public async Task<ActionResult> GetMaintenanceBlueprintLogByMaintenanceBlueprintId([DataSourceRequest] DataSourceRequest request, int maintenanceBlueprintId)
        {
            var result = await _adminService.GetAllMaintenanceBlueprintLogsByMaintenanceBlueprintId(maintenanceBlueprintId);
            return Json(result.ToDataSourceResult(request));
        }
        #endregion

        #region Clone Maintenance Blueprint

        public async Task<ActionResult> CloneMaintenanceBlueprint(int id)
        {
            int? maintenanceBlueprintId = await _adminService.CloneMaintenanceBlueprint(id, _currentUser.UserId);
            return RedirectToAction("EditMaintenanceBlueprint", new { @id = maintenanceBlueprintId });
        }

        #endregion

        #region MaintenanceBlueprint Attachment Documents

        public async Task<ActionResult> GetMaintenanceBlueprintDocuments([DataSourceRequest] DataSourceRequest request, int maintenanceBlueprintId)
        {
            return Json((await _adminService.GetMaintenanceBlueprintDocuments(maintenanceBlueprintId)).ToDataSourceResult(request));
        }

        public async Task<ActionResult> AttachMaintenanceBlueprintDocuments(IEnumerable<IFormFile> maintenanceBlueprintAttachmentDocuments, int mId, DocumentModel model)
        {
            await _adminService.AttachMaintenanceBlueprintDocuments(maintenanceBlueprintAttachmentDocuments, mId, _currentUser.UserId);

            return Json(true);
        }

        public async Task<ActionResult> DeleteMaintenanceBlueprintDocument([DataSourceRequest] DataSourceRequest request, DocumentModel document, int maintenanceBlueprintId)
        {
            await _adminService.DeleteMaintenanceBlueprintDocument(document, _currentUser.UserId, maintenanceBlueprintId);

            return Json(ModelState.ToDataSourceResult());
        }

        #endregion

        #region Maintenance Blueprint Steps

        public async Task<ActionResult> AddMaintenanceBlueprintStep(int id)
        {
            var model = new MaintenanceBlueprintStepModel
            {
                MaintenanceBlueprintId = id
            };
            this.SetTitle("Add New Maintenance Blueprint Step");

            return View("EditMaintenanceBlueprintStep", model);
        }
        public async Task<ActionResult> EditMaintenanceBlueprintStep(int id)
        {
            var model = await _adminService.EditMaintenanceBlueprintStep(id);
            this.SetTitle(string.Format("Edit MaintenanceBlueprintStep - {0}", model.Name));

            return View(model);
        }

        [HttpPost]
        public async Task<ActionResult> EditMaintenanceBlueprintStep(MaintenanceBlueprintStepModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }
            var newId = await _adminService.EditMaintenanceBlueprintStep(model, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("'{0}' maintenance blueprint step has been successfully updated", model.Name));

            return RedirectToAction("EditMaintenanceBlueprintStep", new { @id = newId });
        }

        [HttpPost]
        public async Task<ActionResult> UpdateMaintenanceBlueprintStepTasks(int maintenanceBlueprintStepId, string tasks)
        {
            await _adminService.UpdateMaintenanceBlueprintStepTasks(maintenanceBlueprintStepId, tasks, _currentUser.UserId);

            return Json(new { success = "true" });
        }

        public async Task<ActionResult> GetMaintenanceBlueprintSteps(int maintenanceBlueprintId)
        {
            return Json(await _adminService.GetMaintenanceBlueprintSteps(maintenanceBlueprintId));
        }

        public async Task<ActionResult> DeleteMaintenanceBlueprintStep(int id)
        {
            await _adminService.DeleteMaintenanceBlueprintStep(id, _currentUser.UserId);

            return Json(new { success = "true" });
        }

        public async Task<ActionResult> MoveMaintenanceBlueprintStepUp(int maintenanceBlueprintStepId)
        {
            await _adminService.MoveMaintenanceBlueprintStepUp(maintenanceBlueprintStepId, _currentUser.UserId);

            return Json(new { success = "true" });
        }

        public async Task<ActionResult> MoveMaintenanceBlueprintStepDown(int maintenanceBlueprintStepId)
        {
            await _adminService.MoveMaintenanceBlueprintStepDown(maintenanceBlueprintStepId, _currentUser.UserId);

            return Json(new { success = "true" });
        }

        #endregion

        #region Equipment Category Maintenance Blueprint Step
        public async Task<ActionResult> GetEquipmentCategoryMaintenanceSteps([DataSourceRequest] DataSourceRequest request, int? eCId)
        {
            return Json((await _adminService.GetEquipmentCategoryMaintenanceSteps(eCId)).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateEquipmentCategoryMaintenanceStep([DataSourceRequest] DataSourceRequest request, EquipmentCategoryMaintenanceStepModel model, int equipmentCategoryId)
        {
            return Json((await _adminService.UpdateEquipmentCategoryMaintenanceStep(model, equipmentCategoryId, _currentUser.UserId)).ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteEquipmentCategoryMaintenanceStep([DataSourceRequest] DataSourceRequest request, EquipmentCategoryMaintenanceStepModel model)
        {
            return Json((await _adminService.DeleteEquipmentCategoryMaintenanceStep(model)).ToDataSourceResult(request));
        }

        #endregion

        #region Action Types

        public async Task<ActionResult> GetActionTypes([DataSourceRequest] DataSourceRequest request)
        {
            return Json((await _adminService.GetActionTypes()).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateActionType([DataSourceRequest] DataSourceRequest request, ActionTypeModel model)
        {
            var result = await _adminService.UpdateActionType(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteActionType([DataSourceRequest] DataSourceRequest request, ActionTypeModel model)
        {
            return Json((await _adminService.DeleteActionType(model)).ToDataSourceResult(request));

        }

        #endregion

        #region Opportunity Closed Reasons

        public async Task<ActionResult> GetOpportunityClosedReasons([DataSourceRequest] DataSourceRequest request)
        {
            return Json((await _adminService.GetOpportunityClosedReasons()).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateOpportunityClosedReason([DataSourceRequest] DataSourceRequest request, OpportunityClosedReasonModel model)
        {
            var result = await _adminService.UpdateOpportunityClosedReason(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteOpportunityClosedReason([DataSourceRequest] DataSourceRequest request, OpportunityClosedReasonModel model)
        {
            return Json((await _adminService.DeleteOpportunityClosedReason(model)).ToDataSourceResult(request));

        }

        #endregion

        #region Event Types

        public async Task<ActionResult> GetEventTypes([DataSourceRequest] DataSourceRequest request)
        {
            return Json((await _adminService.GetEventTypes()).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateEventType([DataSourceRequest] DataSourceRequest request, EventTypeModel model)
        {
            var result = await _adminService.UpdateEventType(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteEventType([DataSourceRequest] DataSourceRequest request, EventTypeModel model)
        {
            return Json((await _adminService.DeleteEventType(model)).ToDataSourceResult(request));

        }

        #endregion

        #region Fluid Types

        public async Task<ActionResult> GetFluidTypes([DataSourceRequest] DataSourceRequest request)
        {
            return Json((await _adminService.GetFluidTypes()).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateFluidType([DataSourceRequest] DataSourceRequest request, FluidTypeModel model)
        {
            var result = await _adminService.UpdateFluidType(model);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new[] { result.Response }.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteFluidType([DataSourceRequest] DataSourceRequest request, FluidTypeModel model)
        {
            return Json((await _adminService.DeleteFluidType(model)).ToDataSourceResult(request));
        }

        #endregion

        #region Other
        public async Task<ActionResult> UpdateSystemSettings(SystemSettingsModel model)
        {
            await _adminService.UpdateSystemSettings(model);

            return Json(new { success = "true" });
        }

        #endregion

        [Authorize(Roles = "MAE")]
        public async Task<ActionResult> TemplateView(string callbackUrl, string templateName)
        {
            var model = new LessonEmail("", "", "", new List<string>(), new List<string>());
            await _emailManager.SendEmailAsync("", "Testing", model, templateName);

            return View($"~/views/Emails/{templateName}.cshtml", new LessonEmail("", "", "", new List<string>(), new List<string>()));
        }


        [Authorize(Roles = "OAD")]
        public async Task<ActionResult> UserRole(string email, string roleName)
        {
            var result = new IdentityResult();
            var user = await _identityService.GetUserByEmailAsync(email);

            result = await _userManager.AddToRoleAsync(user, roleName);

            return Json(result);
        }
        #region LOGO
        [HttpPost]
        public async Task<ActionResult> UploadLogo(IEnumerable<IFormFile> logoImage)
        {
            if (logoImage.FirstOrDefault() == null)
                return StatusCode((int)HttpStatusCode.InternalServerError, "File not found!");

            if (!Constants.AllowedLogoExt.Contains(Path.GetExtension(logoImage.FirstOrDefault().FileName).ToLower()))
                return StatusCode((int)HttpStatusCode.InternalServerError, "The files should have .jpg, .jpeg or .png extension");


            var result = await _documentService.UploadLogo(logoImage.FirstOrDefault());

            if (result.Errors != null)
                return StatusCode((int)HttpStatusCode.InternalServerError, result.Errors);

            return Ok();
        }

        [AllowAnonymous]
        public async Task<IActionResult> LogoImage()
        {
            var result = await _documentService.GetLogo();

            return File(result.Item1, result.Item2);
        }

        [HttpPost]
        public async Task<IActionResult> DeleteLogo()
        {
            try
            {
                var result = await _documentService.DeleteLogo();

                if (result.HasErrors())
                {
                    Response.StatusCode = (int)HttpStatusCode.BadRequest;
                    return Json(result.Errors);
                }

                return Json(new { success = "true" });

            } catch (Exception ex)
            {
                throw;
            }

        }

        #endregion
    }
}