﻿@model CompanyModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-building"></i>
        Entities
        (<span data-bind="text:totalCompanies"></span>)
    </h4>
</div>
<hr />


<div class="h-75">
    @(Html.Kendo().Grid<CompanyModel>()
        .Name("companyGrid")
        .Columns(columns => {
        columns.Bound(c => c.Name).ClientTemplate("<a class='text-primary' href='" + @Url.Action("ViewCompany", "Company", new { @id = "" }) + "/#=CompanyId#'>#=Name#</a>").Width(200); ;
        columns.Bound(c => c.Categories).Title("Category").Width(200);
        columns.Bound(c => c.IsActive).Title("Active").ClientTemplate("#if(IsActive){#Yes#}else{#No#}#").Width(80);
        columns.Bound(c => c.CompanyLocationCount).Title("Locations").Width(80);
        columns.Bound(c => c.CompanyContactCount).Title("Contacts").Width(80);
        columns.Bound(c => c.CompanyFieldsCount).Title("Fields").Width(80);
        columns.Bound(c => c.CompanyWellsCount).Title("Wells").Width(80);
        })
        .Events(e => e.DataBound("updateCompanyTotals"))
        .Editable(e => e.Mode(GridEditMode.InLine).DisplayDeleteConfirmation("Are you sure you wish to delete this entity?"))
        .ToolBar(t => {
            t.Excel().Text("Export");
        }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
        .ColumnMenu(c => c.Columns(true))
        .Filterable()
        .Sortable()
        .Groupable()
        .Reorderable(c => c.Columns(true))
        .Scrollable()
        .Excel(excel => excel
                .FileName(string.Format("Centerpoint_ClientCompanies_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                .Filterable(true)
                .ProxyURL(Url.Action("Export", "Admin"))
                )
        .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Model(model => {
            model.Id(m => m.CompanyId);
        })
        .Events(e => e.Error("onError"))
        .Read(read => read.Action("GetCompanies", "Admin"))
        .Destroy(destroy => destroy.Action("DeleteCompany", "Admin"))))
</div>

     
@using (Html.BeginForm("EditCompany", "Admin")) {
    @Html.ValidationSummary(false)

    <div class="header-container-single-item-with-hr">
        <h4>
            <i class="fa fa-building"></i>
            @Model.Name
        </h4>
    </div>
    <hr />

    <div>
        @(Html.Kendo().TabStrip()
        .Name("viewCompanyStrips")
        .SelectedIndex(0)
        .Animation(false)
        .Items( tabstrip => {

        tabstrip.Add().Text("")
            .HtmlAttributes(new { @data_bind="html:tabStripHeaderDetails" })
            .Selected(true)
            .Content(@<text>

                <div id="details">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                @Html.LabelFor(m => m.Name)<br />
                                @(Html.Kendo().TextBoxFor(m => m.Name)
                            .HtmlAttributes(new { @class = "form-control", @style = "font-size:14px" }))
                            </div>
                            @if (Html.IsGlobalAdmin()) {
                                <div class="form-group">
                                    @Html.LabelFor(m => m.IsInternal)
                                    <br />
                                    @(Html.Kendo().DropDownListFor(m => m.IsInternal)
                                    .Filter("contains")
                                    .DataValueField("Key")
                                    .DataTextField("Value")
                                    .BindTo(Html.BooleanListValues().ToList()))
                                </div>
                            }
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Category</label>
                                <br />
                                @(Html.Kendo().MultiSelectFor(m => m.CategoryIds)
                                .DataTextField("Text")
                                .DataValueField("Value")
                                .Filter(FilterType.Contains)
                                .Placeholder("Select Categories...")
                                .HtmlAttributes(new { @class = "form-control"})
                                .DataSource(source => {
                                                source.Read(read => {
                                                     read.Action("GetCategories", "Lookup");
                                                }).ServerFiltering(true);
                                            }))
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                @Html.LabelFor(m => m.IsActive)
                                <br />
                                @(Html.Kendo().DropDownListFor(m => m.IsActive)
                                .Filter("contains")
                                .DataValueField("Key")
                                .DataTextField("Value")
                                .BindTo(Html.BooleanListValues().ToList())
                                )
                            </div>
                        </div>
                    </div>
                </div>

            </text>);

        tabstrip.Add().Text("")
            .HtmlAttributes(new { @data_bind="html:locationsStripText"})
            .Content(@<text>

                <div id="location">
                    @(Html.Kendo().Grid<CompanyLocationModel>()
                            .Name("companyLocationGrid")
                            .Columns(columns => {
                            columns.Bound(c => c.Name);
                            columns.Bound(c => c.Street).Title("Address Line 1");
                            columns.Bound(c => c.HouseNumber).Title("Address Line 2").Hidden(true);
                            columns.Bound(c => c.City);
                            columns.Bound(c => c.Postcode).Title("Address Line 3").Hidden(true);
                            columns.Bound(c => c.Country).Hidden(true);
                            columns.Bound(c => c.Telephone).Hidden(true);
                            columns.Bound(c => c.Comment).Hidden(true);
                            columns.Bound(c => c.IsOffshore).Title("Offshore").ClientTemplate("#if(IsOffshore){#Yes#}else{#No#}#");
                            columns.Bound(c => c.IsLocationActive).Title("Active").ClientTemplate("#if(IsLocationActive){#Yes#}else{#No#}#");
                            })
                            .ColumnMenu(c => c.Columns(true))
                            .ToolBar(t => {
                                t.Excel().Text("Export");
                            }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
                            .Events(e => e.DataBound("updateCompanyLocationTotals"))
                            .Sortable()
                            .Groupable()
                            .Filterable()
                            .Scrollable(s => s.Height(200))
                            .Resizable(resize => resize.Columns(true))
                            .Reorderable(reorder => reorder.Columns(true))
                            .Excel(excel => excel
                                .FileName(string.Format("Centerpoint_CompanyLocation_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                                .Filterable(true)
                                .ProxyURL(Url.Action("Export", "Admin"))
                            )
                            .DataSource(dataSource => dataSource
                            .Ajax()
                            .ServerOperation(false)
                            .Model(model => {
                                model.Id(m => m.CompanyLocationId);
                                model.Field(f => f.IsLocationActive).DefaultValue(true);
                                model.Field(f => f.IsOffshore).DefaultValue(false);
                            })
                            .Read(read => read.Action("GetCompanyLocations", "Admin", new { @cId = Model.CompanyId }))))
                </div>
            
            </text>);

        tabstrip.Add().Text("")
            .HtmlAttributes(new { @data_bind="html:contactsStripText"})
            .Content(@<text>

                <div id="contact">
                    @(Html.Kendo().Grid<CompanyContactModel>()
                            .Name("companyContactGrid")
                            .Columns(columns => {
                            columns.Bound(c => c.CompanyLocationName).Title("Location");
                            columns.Bound(c => c.FirstName);
                            columns.Bound(c => c.LastName);
                            columns.Bound(c => c.Position).Hidden(true);
                            columns.Bound(c => c.EmailAddress).Title("Email Address").ClientTemplate("<a href='mailto:#=EmailAddress#'>#=EmailAddress ? EmailAddress : '' #</a>");
                            columns.Bound(c => c.Mobile).Hidden(true);
                            columns.Bound(c => c.WorkTelephone).Hidden(true);
                            columns.Bound(c => c.Comment).Hidden(true);
                            })
                            .ColumnMenu(c => c.Columns(true))
                            .ToolBar(t => {
                                t.Excel().Text("Export");
                            }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
                            .Events(e => e.DataBound("updateCompanyContactTotals"))
                            .Excel(excel => excel
                                .FileName(string.Format("Centerpoint_CompanyContact_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                                .Filterable(true)
                                .ProxyURL(Url.Action("Export", "Admin"))
                            )
                            .Sortable()
                            .Groupable()
                            .Filterable()
                            .Scrollable(s => s.Height(200))
                            .DataSource(dataSource => dataSource
                            .Ajax()
                            .ServerOperation(false)
                            .Model(model => {
                                model.Id(m => m.CompanyContactId);
                            })
                            .Read(read => read.Action("GetCompanyContacts", "Admin", new { @companyId = Model.CompanyId }))))
                </div>

            </text>);   

        tabstrip.Add().Text("")
            .HtmlAttributes(new { @data_bind="html:tabStripHeaderAssets"})
            .Content(@<text>

                <div id="asset">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Fields (<span data-bind="text:totalCompanyFields"></span>)</h6>
                                </div>
                                @(Html.Kendo().Grid<CompanyFieldModel>()
                                .Name("companyFieldGrid")
                                .Columns(c => {
                                    c.Bound(p => p.Name);
                                })
                                    .Sortable()
                                    .Filterable()
                                    .Scrollable(s => s.Height(500))
                                    .Resizable(c => c.Columns(true))
                                    .ColumnMenu(c => c.Columns(true))
                                    .Events(e => e.DataBound("updateCompanyFieldTotal"))
                                    .DataSource(dataSource => dataSource
                                        .Ajax()
                                        .ServerOperation(false)
                                        .Model(m => m.Id(p => p.CompanyFieldId))
                                        .Read(read => read.Action("GetCompanyFields", "Admin", new { @companyId = Model.CompanyId }))))
                            </div>
                        </div>

                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Wells (<span data-bind="text:totalCompanyWells"></span>)</h6>
                                </div>
                                <div>
                                    <div id="wellDetails">
                                        @(Html.Kendo().Grid<CompanyWellModel>()
                                    .Name("companyWellGrid")
                                    .Columns(c => {
                                        c.Bound(p => p.Name).ClientTemplate("<a class='text-primary' href='" + @Url.Action("ViewCompanyWell", "Company") + "/#=CompanyWellId#'>#=Name#</a>");
                                        c.Bound(p => p.CompanyFieldName).Title("Field").ClientTemplate("#=CompanyFieldName#").ClientGroupHeaderTemplate("Field : #= value # (#= count#)");
                                        c.Bound(p => p.MinimumId);
                                        c.Bound(p => p.MaximumDeviation);
                                        c.Bound(p => p.MaximumPressure).Hidden(true);
                                        c.Bound(p => p.MaximumPressureUnits).Hidden(true);
                                        c.Bound(p => p.MaximumTemperature).Hidden(true);
                                        c.Bound(p => p.MaximumTemperatureDegrees).Hidden(true);
                                        c.Bound(p => p.H2S).Hidden(true);
                                        c.Bound(p => p.CO2).Hidden(true);
                                        c.Bound(p => p.FluidTypes).Hidden(true);
                                    })
                                        .Sortable()
                                        .Filterable()
                                        .Groupable()
                                        .Scrollable(s => s.Height(500))
                                        .Resizable(c => c.Columns(true))
                                        .ColumnMenu(c => c.Columns(true))
                                        .Events(e => e.Edit("onEditWell"))
                                        .Events(e => e.DataBound("updateCompanyWellTotal"))
                                        .DataSource(dataSource => dataSource
                                        .Ajax()
                                        .ServerOperation(false)
                                        .Aggregates(aggregates => {
                                            aggregates.Add(p => p.CompanyFieldName).Min().Max().Count();
                                        })
                                        .Group(group => group.Add(p => p.CompanyFieldName))
                                        .Sort(sort => sort.Add(p => p.Name))
                                        .Model(model => {
                                            model.Id(m => m.CompanyWellId);
                                        })
                                        .Read(read => read.Action("GetCompanyWells", "Admin", new { @companyId = Model.CompanyId }))))
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </text>);     
        }
        ))
    </div>

   
    @Html.HiddenFor(m => m.CompanyId)
}

    <environment include="Development">
        <script src="~/js/views/companies/viewCompany.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script src="~/js/views/companies/viewCompany.min.js" asp-append-version="true"></script>
    </environment>