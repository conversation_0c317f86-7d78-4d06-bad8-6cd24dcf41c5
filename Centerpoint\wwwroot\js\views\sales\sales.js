function deleteCustomer(companyId) {
    $.ajax({
        type: 'POST',
        cache: false,
        dataType: 'json',
        url: '/Sales/DeleteFocusCustomer',
        data: {
            customerId: companyId,
        },
        success: function (result) {
            window.location.reload();
        },
    });
}

$("#confirmCustomer").click(function () {
    $.ajax({
        type: 'POST',
        cache: false,
        dataType: 'json',
        url: '/Sales/AddFocusCustomer',
        data: {
            customerId: viewModel.get("customerId"),
        },
        success: function (result) {
            window.location.reload();
        },
    });
});

function onDB(e) {
    e.sender.options.series[0].labels.visible = function (point) {
        if (point.value < 0) {
            return false
        }
        else {
            return point.value
        }
    }
}

function downloadFile() {
    var element = $("#page");
    kendo.ui.progress(element, true);
    setTimeout(function () { kendo.ui.progress(element, false); }, 10000);
    $("<div id='dialog'></div>").kendoDialog({
        closable: false,
        title: 'Sales Report',
        width: 500,
        buttonLayout: "normal",
        content: `The Sales Report is been generated, please wait for it.`,
        actions: [{ text: "OK", cssClass: 'btn-primary' }]
    }).data("kendoDialog").open().center()
}

var viewModel = kendo.observable({
    hasCustomer :false,
    totalSales: 0,
    totalOpportunities: 0,
    totalEvents: 0,
    totalActions: 0,
    type: "",
    opportunityId: 0,
    totalOpportunityActions: 0,
    totalOpportunityEvents: 0,
    totalOpportunityActionDocuments: 0,
    totalOpportunityEventDocuments: 0,
    totalOpportunityWellDocuments: 0,
    totalOpportunityDocuments: 0,
    opportunityEventId: 0,
    linkOpportunityId: 0,
    totalFollowUpActions: 0,
    customerId: "",

    showCustomerWindow: function () {
        $("#customerWindow").data("kendoWindow").center().open();
    },

});

kendo.bind(document.body.children, viewModel);