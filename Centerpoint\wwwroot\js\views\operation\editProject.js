if(editProjectModel.projectId) {
    getActiveJobs();
}

$(document).ready(function () {
    submitSuccessHandler(editProjectModel.name);

    if(editProjectModel.projectId) {
        loadShipmentGrid();
        loadBackloadShipmentGrid();
        var shipmentGrid = $('#shipmentGrid').data("kendoGrid");
        shipmentGrid.bind('dataBound', function (e) {
            this.element.find('.k-i-excel').remove();
        });
    }
    // loadEquipmentItemGrid();
    loadEquipmentShipmentNonAssetItemGrid();
    if(editProjectModel.oppsProjectId) {
        var companyWellGrid = $("#companyWellGrid").data("kendoGrid");
        companyWellGrid.dataSource.read();
    }
});

function companyFieldChange(e) {
    var oppsWellIds = $("#OppsWellIds").data("kendoMultiSelect");
    oppsWellIds.dataSource.read();
}

function projectCompanyFieldChange(e) {
    var wellIds = $("#CompanyWellIds").data("kendoMultiSelect");
    wellIds.dataSource.read();
}

function filterCompanyWells(e) {
    if(viewModel.get("companyFieldIds")) {
        $.ajaxSetup({
            dataType: 'json',
            traditional: true,
            data: {
                companyFieldIds: viewModel.get("companyFieldIds").toJSON(),
                text: getTextValue(e)
            },
        });
    }
}

function filterProjectCompanyWells(e) {
    if(viewModel.get("projectCompanyFieldIds")) {
           $.ajaxSetup({
        dataType: 'json',
        traditional: true,
        data: {
            companyFieldIds: viewModel.get("projectCompanyFieldIds").toJSON(),
            text: getTextValue(e)
        },
    }); 
    }
}

function parseDate() {
    var now = new Date();
    return new Date(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), now.getUTCHours(), now.getUTCMinutes(), now.getUTCSeconds());

}

function partnerCompanyChange(e) {
    var partnerCompanyId = this.value();
    viewModel.set("partnerCompanyId", partnerCompanyId);

    refreshProjectCompanyData();
}

function refreshProjectCompanyData() {
    let selector = editProjectModel.oppsProjectId ? 'OppsFieldIds' : 'CompanyFieldIds';
    var fieldsMultiSelect = $(`#${selector}`).data("kendoMultiSelect");
    if (fieldsMultiSelect) {
        fieldsMultiSelect.dataSource.read().then(() => {
            fieldsMultiSelect.value(fieldsMultiSelect.value());
            fieldsMultiSelect.trigger("change");
        });
    }

    var customerDropDown = $("#CompanyId").data("kendoDropDownList");
    if (customerDropDown) {
        customerDropDown.enable(true);
        customerDropDown.dataSource.read().then(() => customerDropDown.trigger("change"));
    }
}

function projectOperatorChange(e) {
    var operatorId = this.value();
    viewModel.set("operatorId", operatorId);

    refreshProjectCompanyData();

    viewModel.set("isWellVisible", true);
}

function projectOperatorDataBound(e) {
    var operatorId = this.value();
    viewModel.set("operatorId", operatorId);

    if(operatorId) {
        $("#CompanyId").data("kendoDropDownList").enable(true);
    }
    $("#CompanyId").data("kendoDropDownList").dataSource.read();
    viewModel.set("isWellVisible", true);
}
function projectPartnerDataBound(e) {
    var partnerCompanyId = this.value();
    viewModel.set("partnerCompanyId", partnerCompanyId);

    if (partnerCompanyId) {
        $("#CompanyId").data("kendoDropDownList").enable(true);
    }
    $("#CompanyId").data("kendoDropDownList").dataSource.read();
    viewModel.set("isWellVisible", true);
}
function projectCompanyData(e) {
    var operatorId = viewModel.get("operatorId");
    var partnerCompanyId = viewModel.get("partnerCompanyId");

    return {
        companyId: operatorId,
        partnerCompanyId: partnerCompanyId,
        text: getTextValue(e)
    };
}

function getHtmlNewLinesString(text) {
    var regexp = new RegExp('\n', 'g');
    return text ? text.replace(regexp, '<br>') : text;
}

function saveShipmentGrid(e) {
    setTimeout(function(){
        var grid = $("#shipmentGrid").data("kendoGrid");
        localStorage["shipmentGrid"] = kendo.stringify(grid.getOptions());
    },10);
}

function loadShipmentGrid() {
    var grid = $("#shipmentGrid").data("kendoGrid");
    var options = localStorage["shipmentGrid"];
    if(grid) {
        viewModel.set("initialShipmentGridOptions", kendo.stringify(grid.getOptions()));
    }
    if (options) {
        grid.setOptions(JSON.parse(options));
    }
}

function saveBackloadShipmentGrid(e) {
    setTimeout(function () {
        var grid = $("#backloadShipmentGrid").data("kendoGrid");
        localStorage["backloadShipmentGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}

function loadBackloadShipmentGrid() {
    var grid = $("#backloadShipmentGrid").data("kendoGrid");
    var options = localStorage["backloadShipmentGrid"];
    if(grid) {
        viewModel.set("initialBackloadShipmentGridOptions", kendo.stringify(grid.getOptions()));
    }
    if (options) {
        grid.setOptions(JSON.parse(options));
    }
}

function saveEquipmentItemGrid(e) {
    setTimeout(function(){
        var grid = $("#equipmentItemGrid").data("kendoGrid");
        localStorage["equipmentItemGrid"] = kendo.stringify(grid.getOptions());
    },10);
}

// function loadEquipmentItemGrid() {
//     var grid = $("#equipmentItemGrid").data("kendoGrid");
//     var options = localStorage["equipmentItemGrid"];
//     if (options && grid) {
//         grid.setOptions(JSON.parse(options));
//     }
// }

function loadSelectedEquipmentItemsGrid() {
    let grid = $("#selectedEquipmentItemsGrid").data("kendoGrid");
    if(grid) {
        viewModel.set("initialSelectedEquipmentItemsGridOptions", kendo.stringify(grid.getOptions()));
    } 
}

function saveEquipmentShipmentNonAssetItemGrid(e) {
    setTimeout(function(){
        var grid = $("#equipmentShipmentNonAssetItemGrid").data("kendoGrid");
        localStorage["equipmentShipmentNonAssetItemGrid"] = kendo.stringify(grid.getOptions());
    },10);
}

function loadEquipmentShipmentNonAssetItemGrid() {
    var grid = $("#equipmentShipmentNonAssetItemGrid").data("kendoGrid");
    var options = localStorage["equipmentShipmentNonAssetItemGrid"];
    if(grid) {
        viewModel.set("initialEquipmentShipmentNonAssetItemGridOptions", kendo.stringify(grid.getOptions()));
    }
    if (options) {
        grid.setOptions(JSON.parse(options));
    }
}

function companyChange(e) {
    var customerId = this.value();
    viewModel.set("selectedCustomerId", customerId)
    viewModel.set("customerId", customerId);

    if (!customerId)
        this.value(null);

    if ($("#ObjectiveIds").data("kendoMultiSelect"))
        $("#ObjectiveIds").data("kendoMultiSelect").dataSource.read();

    $("#CompanyFieldIds").data("kendoMultiSelect").dataSource.read();
    cascadeDropdownFilterHelper(e)
}

function companyDataForCostomerContact() {
    var customerId = $("#CompanyId").data("kendoDropDownList").value();
    console.log(customerId, 'customerId in companyDataForCostomerContact')
    return{
        companyId: customerId
    };
}

function companyData(e) {
    var companyId = viewModel.get("operatorId");
    return{
        companyId: companyId,
        text: getTextValue(e)
    };
}

function customerCompanyData(e){
    return{
        oppsCompanyId: editProjectModel.oppsCompanyId,
        oppsPartnerCompanyId: editProjectModel.oppsPartnerCompanyId,
        text: getTextValue(e)
    };
}

function filterByCurrentCompanyLocation() {
    return {
        companyLocationId: $("#currentLocation").data("kendoDropDownList").value(),
        currentProjectId: editProjectModel.projectId
    };
}

function logisticsData() {
    return{
        projectId: editProjectModel.projectId
    }
}

function backloadShipmentData() {
    return {
        id: viewModel.projectId
    }
}

function lessonData() {
    return{
        lessonProjectId: editProjectModel.projectId
    }
}

function updateLessonGrid() {
    var lessonGrid = $("#lessonGrid").data("kendoGrid");
    var total = lessonGrid.dataSource.total();
    viewModel.set("totalProjectLessons", total);
}

function updateCompanyWellTotal() {
    var companyWellGrid = $("#companyWellGrid").data("kendoGrid");
    var total = companyWellGrid.dataSource.total();
    viewModel.set("totalWells", total);
}

function wellAttachmentCount(companyWellId) {
    viewModel.set("companyWellId", companyWellId);

    $("#wellAttachmentWindow").data("kendoWindow").center().open();
}

function wellAttachmentWindowOpened() {
    var wellDocumentsGrid = $("#companyWellDocumentsGrid").data("kendoGrid");

    wellDocumentsGrid.dataSource.read();
}

function companyWellData() {
    return {
        cwId: viewModel.get("companyWellId")
    }
}

function companyFieldData() {

    $.ajaxSetup({
        dataType: 'json',
        traditional: true,
        data: {
            companyWellIds: $("#OppsWellIds").data("kendoMultiSelect").value()
        },
    });
}

function projectData() {
    return{
        pId: editProjectModel.projectId
    }
}
function equipmentShipmentdata() {
    return{
        equipmentShipmentId: viewModel.get("equipmentShipmentId")
    }
}

function getActiveJobs() {
    $.ajax({
        url: `/Operation/GetJobListItem`,
        type: "GET",
        dataType: "json",
        global: false,
        data: {
            projectId : editProjectModel.projectId
        },
        success: function (result) { 
            result.map( item =>{ 
                item.children = true,
                item.content = " "
            } )
            viewModel.set("jobs", result)

            var homogeneous = new kendo.data.HierarchicalDataSource({
                data: result,
                schema: {
                    model: {
                        id: "JobId",
                        hasChildren: "children",
                    }
                }
            });

            $("#jobPanelBar").kendoPanelBar({
                dataSource: homogeneous,
                template: "<div class='d-flex w-100 justify-content-between toolbar-inline-padding'><span>#=item.JobName#</span> <span>#=item.OppsName#</span></div>",
                expand: onExpand
            });
            if(result.length) {
              $("#jobPanelBar").data("kendoPanelBar").expand($("#jobPanelBar li:first"))
            }
        }
    });
}


function onExpand(e) {
    let selectedItem = $("#jobPanelBar").data("kendoPanelBar").dataItem(e.item)
    if(selectedItem.hasExpanded) {
      return true
    }
    kendo.ui.progress($(e.item), true)
    $.ajax({
        url: "/Operation/GetJobInfoById",
        dataType: "json",
        type: "GET",
        data: {
            jobId: selectedItem.JobId,
        },
        success: function (result) {
            let projectTemplate = kendo.template($("#jobTemplate").html()); // load detail template
            let projectHtml = projectTemplate(result); // fill template with data
            if(e && e.item) {
                if(!$(e.item).find(".k-content").length) {
                    $(e.item).append('<div class="k-panelbar-content k-content"></div>')
                }
                $(e.item).find(".k-panelbar-content.k-content").html(projectHtml)
            }
            selectedItem.hasExpanded = true
            kendo.ui.progress($(e.item), false)   
        }
    });        
   
}

function onError(e, status) {
    if (e.status == "customerror") {
        alert(e.errors);
        $("#crewGrid").data("kendoGrid").dataSource.cancelChanges();
    }
}

function ProjectCommentError(e, status) {
    if (e.status == "customerror") {
        alert(e.errors);
        $("#projectCommentGrid").data("kendoGrid").dataSource.cancelChanges();
    }
}

function receiveShipment(equipmentShipmentId) {
    var confirmation = confirm ("Are you sure you wish to mark as received");

    if(confirmation) {
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: `/Logistics/UpdateStatus?status=${editProjectModel.status.received}`,
            data: {
                id: equipmentShipmentId
            },
            success: function () {
                window.location.reload();
                window.close();
            },
            dataType:"json"
        });
    }
}

function cancelRow() {
    grid = $("#crewGrid").data("kendoGrid");
    grid.cancelRow();
}

function editRow(element) {
    grid = $("#crewGrid").data("kendoGrid");
    grid.editRow($(element).closest("tr"));
}

function updateRow() {
    grid = $("#crewGrid").data("kendoGrid");
    grid.saveRow();
}

function deleteRow(element) {
    grid = $("#crewGrid").data("kendoGrid");
    grid.removeRow($(element).closest("tr"));
}

function createRow(event) {
    event.preventDefault();
    grid = $("#crewGrid").data("kendoGrid");
    grid.addRow();
}

function crewData(data) {
    data.DateIn = toUTCString(data.DateIn);
    data.DateOut = toUTCString(data.DateOut);

    return {
        projectId: editProjectModel.projectId
    }
}
function commentData(data) {
    data.Date = toUTCString(data.Date);
}

function updateCrewGrid() {
    var crewGrid = $("#crewGrid").data("kendoGrid");
    var totalCrews = crewGrid.dataSource.total();
    viewModel.set("totalCrews", totalCrews);

    var crewData = crewGrid.dataSource.data();

    var crewOut = true;

    for(var i= 0; i < crewData.length; i++){
        if(!crewData[i].DateOut){
            crewOut = false;
        }
    }

    // getActiveJobs();
    viewModel.set("crewOut",crewOut);
}

function onRequestEndRefreshLogGrid(e) {
    if(e.type !== "read") {
        refreshProjectLogs();
    }
}
function onRequestEndRefreshCrewGrid(e) {
    if (e.type !== "read") {
        $("#crewGrid").data("kendoGrid").dataSource.read();
        refreshProjectLogs();
    }
}
function onRequestEndRefreshCommentGrid(e) {
    if (e.type !== "read") {
        $("#projectCommentGrid").data("kendoGrid").dataSource.read();
        refreshProjectLogs();
    }
}

function updateProjectDocumentGrid() {
    var projectDocumentsGrid = $("#projectDocumentsGrid").data("kendoGrid");
    var totalProjectDocuments = projectDocumentsGrid.dataSource.total();
    viewModel.set("totalProjectDocuments", totalProjectDocuments);
    refreshProjectLogs();
}

function updateJobTotal() {
    var jobList = $("#jobList").data("kendoListView");
    viewModel.set("totalJobs", jobList.dataSource.total());
}

function updateProjectCommentGrid() {
    var projectCommentGrid = $("#projectCommentGrid").data("kendoGrid");
    var totalProjectComments = projectCommentGrid.dataSource.total();
    viewModel.set("totalProjectComments", totalProjectComments);
}

function updateEquipmentTotals() {
    var equipmentItemGrid = $("#availableEquipmentItemsGrid").data("kendoGrid");
    var totalEquipmentItems = equipmentItemGrid.dataSource.total();
    viewModel.set("totalEquipmentItems", totalEquipmentItems);

    var equipmentData = equipmentItemGrid.dataSource.data();

    $.each(equipmentData, function (i, item) {
        if (item.MaintenanceScheduleDaysAlert) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(15)').css("color", "#ff7f7f   !important");
        }
        if (item.MaintenanceSchedulePointsAlert) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(14)').css("color", "#ff7f7f   !important");
        }
    });
}

function updateEquipmentShipmentNonAssetItemTotals() {
    $("#resetEquipmentShipmentNonAssetItemGrid").click(function (e) {
        e.preventDefault();
        resetGridView('equipmentShipmentNonAssetItemGrid', 'initialEquipmentShipmentNonAssetItemGridOptions')
        $("#selectedEquipmentItemsGrid").data("kendoGrid").dataSource.read()
    });

    var equipmentShipmentNonAssetItemGrid = $("#equipmentShipmentNonAssetItemGrid").data("kendoGrid");
    var totalEquipmentShipmentNonAssetItems = equipmentShipmentNonAssetItemGrid.dataSource.total();
    viewModel.set("totalEquipmentShipmentNonAssetItems", totalEquipmentShipmentNonAssetItems);
}

function projectCommentEdit(e) {
    $(e.container).find(".k-edit-buttons").html("<a class='btn btn-primary btn-sm k-grid-update' href='#'>Update</a> " +
        "<a class='btn btn-primary btn-sm k-grid-cancel' href='#'>Cancel</a>");
    $(e.container).data('kendoWindow').bind('activate',function(e){
        $('#Comment').focus();
    })
}
function onProjectDocumentAttached() {
    var projectDocumentsGrid = $("#projectDocumentsGrid").data("kendoGrid");
    projectDocumentsGrid.dataSource.read();
}

function onProjectDocumentUpload(e) {
    uploadValidation(e);
    $(".k-upload-files.k-reset").show();
}

function onProjectDocumentComplete(e) {
    $(".k-upload-files.k-reset").find("li").remove();
    $(".k-upload-files.k-reset").slideUp();
}

function filterCompanyLocations() {
    return {
        companyId: $("#CompanyId").data("kendoDropDownList").value()
    };
}

function filterBackLoadCompanyLocations() {
    return {
        companyId: $("#currentClient").data("kendoDropDownList").value()
    };
}

function filterCustomerCompanyLocations() {
    return {
        companyId: editProjectModel.oppsCustomerCompanyId ? editProjectModel.oppsCustomerCompanyId : null 
    };
}

function filterCompanyContacts() {
    return {
        companyId: $("#CompanyId").data("kendoDropDownList").value()
    };
}

function projectData() {
    return {
        pId: editProjectModel.projectId ? editProjectModel.projectId : null 
    };
}

function refreshProjectLogs(){
    var projectLogGrid = $("#projectLogGrid").data("kendoGrid");
    if(projectLogGrid){
        projectLogGrid.dataSource.read();
    }
}


function refreshEquipmentItemsGrid(){
    var equipmentItemGrid = $("#equipmentItemGrid").data("kendoGrid");
    if(equipmentItemGrid) {
       equipmentItemGrid.dataSource.read(); 
    }
}

function updateTotalLogs() {
    var projectLogGrid = $("#projectLogGrid").data("kendoGrid");
    var totalProjectLogs = projectLogGrid.dataSource.total();
    viewModel.set("totalProjectLogs", totalProjectLogs);
}

function updateShipmentGrid() {
    $("#resetShipmentGrid").click(function (e) {
        e.preventDefault();
        resetGridView('shipmentGrid', 'initialShipmentGridOptions')
         $("#shipmentGrid").data("kendoGrid").dataSource.read();
    });

    var shipmentGrid = $("#shipmentGrid").data("kendoGrid");
    var totalProjectShipments = shipmentGrid.dataSource.total();
    viewModel.set("totalProjectShipments", totalProjectShipments);
}

function maintenanceRecordCount(equipmentItemId) {
    viewModel.set("selectedEquipmentItemId", equipmentItemId);

    $("#maintenanceRecordWindow").data("kendoWindow").center().open();
}

function maintenanceRecordData() {
    return {
        equipId: viewModel.get("selectedEquipmentItemId")
    }
}

function maintenanceRecordWindowOpened() {
    var maintenanceRecordGrid = $("#maintenanceRecordGrid").data("kendoGrid");
    maintenanceRecordGrid.dataSource.read();
}

function updateBackloadShipmentGrid() {
    $("#resetBackloadShipmentGrid").click(function (e) {
        e.preventDefault();
        resetGridView('backloadShipmentGrid', 'initialBackloadShipmentGridOptions')
         $("#backloadShipmentGrid").data("kendoGrid").dataSource.read();
    });
    
    var shipmentBackloadGrid = $("#backloadShipmentGrid").data("kendoGrid");
    var totalBackloadProjectShipments = shipmentBackloadGrid.dataSource.total();
    viewModel.set("totalBackloadProjectShipments", totalBackloadProjectShipments);
}

function receiveEquipmentShipment(equipmentShipmentId) {
    window.location.href = `/Logistics/EditEquipmentShipment/${equipmentShipmentId}?isReceiving=true`;
}

function shipmentEquipmentItemWindowOpened() {
    var equipmentShipmentItemGrid = $("#equipmentShipmentItemGrid").data("kendoGrid");
    equipmentShipmentItemGrid.dataSource.read();
}

$("#changeToLocationConfirm").click(function () {
    $("#changeToLocationConfirm").disabled = true;
    $.ajax({
        type: 'POST',
        cache: false,
        dataType: 'json',
        url: '/Logistics/AddEquipmentShipmentFromProject',
        data: {
            toCompanyLocationId: viewModel.get("toCompanyLocationId"),
            projectId: viewModel.get("projectId"),
            toProjectId: viewModel.get("toProjectId"),
        },
        success: function (result) {
            window.location.href = `/Logistics/EditEquipmentShipment/${result.equipmentShipmentId}`;
            $("#changeToLocationConfirm").disabled = false;

        },
        error: function () {
            $("#changeToLocationConfirm").disabled = false;

        }
        
    });
});

function onWindowClose(e) {
    let senderElement = e.sender.element[0];
    if(senderElement) {
       let form = senderElement.getElementsByTagName("form")[0];
       form ? form.reset() : ''
       if($(`#${form.id}`).data('kendoValidator')) {
            $(`#${form.id}`).data('kendoValidator').reset();
       }
    }
}

// window submit part
function getValidationCustomMessage(input) {
    const isRequired = "is required";
    switch (input[0].name) {
        case 'companyLocationId':
          return `Location ${isRequired}`
          break;
        case 'FirstName':
          return `First Name ${isRequired}`
          break;
        case 'LastName':
            return `Last Name ${isRequired}`
            break;
        default:
          return `${input[0].name} ${isRequired}`
      }
  }

function addCompanyContact() {
    if($('#customerContact').kendoValidator({ messages: { required: function(input) {
        return getValidationCustomMessage(input);
    }}}).data('kendoValidator').validate()) {
        let data = new FormData($("#customerContact")[0])
        const finalData = Object.fromEntries(data.entries())
        finalData.NotActive = $("#NotActive").data("kendoCheckBox").value();
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: '/Admin/UpdateCompanyContact',
            data: finalData,
            success: function (result) {
                $("#addCompanyContactWindow").data("kendoWindow").close();
                $("#customerContact")[0].reset();
                $("#CompanyContactId").data("kendoDropDownList").dataSource.read();
            },
        })
    } 
}

function addCompanyLocation() {
    if($('#customerLocationForm').kendoValidator().data('kendoValidator').validate()) {
        let data = new FormData($("#customerLocationForm")[0])
        const finalData = Object.fromEntries(data.entries());
        const selectedCustomerId = viewModel.get("selectedCustomerId")
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: `/Admin/UpdateCompanyLocation?cId=${selectedCustomerId}`,
            data: finalData,
            success: function (result) {
                $("#addCompanyLocationWindow").data("kendoWindow").close();
                $("#customerLocationForm")[0].reset();
                $("#CompanyLocationId").data("kendoDropDownList").dataSource.read();
            },
        })
    }
}

function addCompany() {
    if ($('#customerForm').kendoValidator().data('kendoValidator').validate()) {
        let data = new FormData($("#customerForm")[0])
        const finalData = Object.fromEntries(data.entries());
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: `/Admin/EditCompany`,
            data: finalData,
            success: function (result) {
                $("#addCompanyWindow").data("kendoWindow").close();
                $("#customerForm")[0].reset();
                $("#CompanyId").data("kendoDropDownList").dataSource.read();
            },
        })
    }
}
function addCompanyWell() {
    let isValid = $('#companyWellForm').kendoValidator({ messages: { required: function(input) {
        return getcompanyWellValidationCustomMessage(input);
    }}}).data('kendoValidator').validate();

    if(isValid) {
        let data = new FormData($("#companyWellForm")[0])
        const finalData = Object.fromEntries(data.entries());
        finalData.fluidTypeIds = $("#fluidTypes").data("kendoMultiSelect").value();
        
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: '/Admin/AddCompanyWell',
            data: finalData,
            success: function (result) {
                $("#addWellWindow").data("kendoWindow").close();
                $("#companyWellForm")[0].reset();
                $("#CompanyWellIds").data("kendoMultiSelect").dataSource.read();
            },
        });
    }

    function getcompanyWellValidationCustomMessage(input) {
      return  input[0] && input[0].labels[0] && input[0].labels[0].textContent ? input[0].labels[0].textContent + " is required" : "This field is required"
    }
};
function addCompanyField() {
    let isValid = $('#companyFieldForm').kendoValidator({
        messages: {
            required: function (input) {
                return getcompanyFieldValidationCustomMessage(input);
            }
        }
    }).data('kendoValidator').validate();

    if (isValid) {
        let data = new FormData($("#companyFieldForm")[0])
        const finalData = Object.fromEntries(data.entries());
        const companyId = $("#CustomerCompanyId").val();

        if (companyId) {
            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url: `/Admin/UpdateCompanyField?compId=${companyId}`,
                data: finalData,
                success: function (result) {
                    $("#addFieldWindow").data("kendoWindow").close();
                    $("#companyFieldForm")[0].reset();
                    $("#CompanyFieldIds").data("kendoMultiSelect").dataSource.read();
                },
            });
        } else {
          alert("Please select Operator")
        }
    }

    function getcompanyFieldValidationCustomMessage(input) {
        return input[0] && input[0].labels[0] && input[0].labels[0].textContent ? input[0].labels[0].textContent + " is required" : "This field is required"
    }
};

function updateWellDocumentsGrid() {
    var wellDocumentsGrid = $("#wellDocumentsGrid").data("kendoGrid");
    var totalCompanyWellDocuments = wellDocumentsGrid.dataSource.total();

    viewModel.set("totalCompanyWellDocuments", totalCompanyWellDocuments);

}

function divisionData(e) {
    var divisionId = $("#DivisionId").data("kendoDropDownList").value();

    return {
        divisionId: divisionId,
        text: getTextValue(e)
    };
}

function changeDivision() {
    var division = $("#DivisionId").data("kendoDropDownList");
    var dataItem = division.dataItem();
    var divisionId = dataItem.DivisionId;
    var simplifiedMobilisation = dataItem.SimplifiedMobilisation;
    if(simplifiedMobilisation == true){
        $("<div id='dialog'></div>").kendoDialog({
            closable: false,
            title: false,
            close:()=>{
                var dialog = $("#dialog").data("kendoDialog");
                dialog.destroy();
            },
            width: 500,
            buttonLayout: "normal",
            content: "Changing the Division to" + ' ' + '<strong>' + dataItem.Name +'</strong>'+ ' ' + "will make all equipment assigned to that division available to select for this project. Would you like to continue?",
            actions: [
                {
                    text: "Yes",
                    action: function(e){
                        $.ajax({
                            type: 'POST',
                            dataType: 'json',
                            url: "/Operation/AddEquipmentItemsToProject",
                            data: {
                                id: editProjectModel.projectId,
                                divisionId : divisionId
                            },success: function () {
                                refreshEquipmentItemsGrid();
                            },
                        });
                    },
                    cssClass: 'btn-primary'
                },
                {
                    text: "No",
                    action: function(){
                        var oldDivision = $("#DivisionId").data("kendoDropDownList");
                        oldDivision.select(function(dataItem) {
                        return dataItem.Name === editProjectModel.divisionName;
                    });
                    },
                    cssClass: 'btn-primary'
                },
            ]
        }).data("kendoDialog").open().center()
    }

    if (!editProjectModel.OppsProjectId) {
        var equipmentCategoryIds = $("#EquipmentCategoryIds").data("kendoMultiSelect");
            equipmentCategoryIds.dataSource.read();
        }
    }

function dataBoundDivision() {
    var equipmentCategoryIds = $("#OppsEquipmentCategoryIds").data("kendoMultiSelect");
    if(equipmentCategoryIds){
        equipmentCategoryIds.dataSource.read();
    }
}

function equipmentCategoryLoaded() {
    $(".k-treeview-item").click(function (e) {
        var equipmentCategoryTree = $("#equipmentCategoryTreeView").data("kendoTreeView");
        var equipmentCategorySelected = equipmentCategoryTree.select();

        if (equipmentCategorySelected && $(e.currentTarget).attr("date-uid") == $(equipmentCategorySelected.context).attr("data-uid")) {
            equipmentCategoryTree.select($());
        }
    });
}

function equipmentCategorySelected(e) {
    var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
    var node = equipmentCategoryTreeView.select();
    var selectedEquipmentCategory = equipmentCategoryTreeView.dataItem(node);

    if (selectedEquipmentCategory) {
        $.removeCookie('equipmentCategory');
        $.cookie('equipmentCategory', selectedEquipmentCategory.EquipmentCategoryId, { expires: 7, path:'/' });
        viewModel.set("selectedEquipmentCategory", selectedEquipmentCategory);

        var grid = $("#availableEquipmentItemsGrid").data("kendoGrid");
        grid.dataSource.options.endless = null;
        grid._endlessPageSize = grid.dataSource.options.pageSize;
        grid.dataSource.pageSize(grid.dataSource.options.pageSize);
    }
}

function equipmentCategoryDropped(e) {
    var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
    var equipmentCategory = equipmentCategoryTreeView.dataItem(e.sourceNode);

    var parentEquipmentCategory = "";

    if (e.dropPosition == "over") {
        parentEquipmentCategory = equipmentCategoryTreeView.dataItem(e.destinationNode);
    } else {
        parentEquipmentCategory = equipmentCategoryTreeView.dataItem(equipmentCategoryTreeView.parent(e.destinationNode));
    }

    $.ajax({
        type: "POST",
        url: "/Admin/UpdateEquipmentCategoryParent",
        data: {
            equipmentCategoryId: equipmentCategory.EquipmentCategoryId,
            parentEquipmentCategoryId: parentEquipmentCategory ? parentEquipmentCategory.EquipmentCategoryId : ""
        },
        success: function (data) {
            viewModel.set("selectedEquipmentCategory", equipmentCategory);
            console.log(JSON.stringify(data));
        },
        dataType: "json"
    });
}

function equipmentItemData() {
    var equipmentCategory = viewModel.get("selectedEquipmentCategory");
    return {
        equipmentCategoryId: equipmentCategory ? equipmentCategory.EquipmentCategoryId : "",
    };
}


function handleItemGridClick(event) {
    event.preventDefault();
    var equipmentItemGrid = $("#availableEquipmentItemsGrid").data("kendoGrid");

    var equipmentItemIds = [];

    equipmentItemGrid.select().each(function () {
        var equipmentItem = equipmentItemGrid.dataItem($(this));

        if (equipmentItem) {
            equipmentItemIds.push(equipmentItem.EquipmentItemId);
        }
    });
    $.ajax({
       type: 'POST',
       dataType: 'json',
       traditional: true,
        url: "/Operation/LinkEquipmentItemsToProject",
       data: {
           projectId: editProjectModel.projectId,
           equipmentItemIds: equipmentItemIds
       },
       success: function () {
           refreshEquipmentProjectItems();
           refreshEquipmentItems();
       }
    });
};

function receivrProjectEquipmentItem(Id) {
    e.preventDefault();  //prevents postback

    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: "/Operation/ReceiveEquipmentItemsToProject",
        data: {
            equipmentItemId: Id,
        },
        success: function () {
            refreshEquipmentProjectItems();
            refreshEquipmentItems();
        }
    });
}

$("#selectedEquipmentItemsGrid").on("click", "#receiveItemsButton", function (e) {
    e.preventDefault();  //prevents postback
    
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: "/Operation/ReceiveEquipmentItemsToProject",
        data: {
            projectId: editProjectModel.projectId,
        },
        success: function () {
            refreshEquipmentProjectItems();
            refreshEquipmentItems();
        }
    });
});

function refreshEquipmentProjectItems() {
    var equipmentProjectItemGrid = $("#selectedEquipmentItemsGrid").data("kendoGrid");
    equipmentProjectItemGrid.dataSource.read();
}

function refreshEquipmentItems() {
    var equipmentItemGrid = $("#availableEquipmentItemsGrid").data("kendoGrid");

    if(equipmentItemGrid){
        equipmentItemGrid.dataSource.read();
    }
}

function updateSelectedEquipmentItemsGridTotals(e) {

    const data = e.sender.dataSource.data();
    const condition = data.some(i => i.CurrentCompanyLocationId !== Number(viewModel.get('currentLocationId')))

    const reciveButton = document.getElementById("receiveItemsButton")

    if (reciveButton) {
        reciveButton.style.display = condition ? 'block' : 'none'
    }

    $("#resetSelectedEquipmentItemsGrid").click(function (e) {
        e.preventDefault();
        resetGridView('selectedEquipmentItemsGrid', 'initialSelectedEquipmentItemsGridOptions')
        refreshEquipmentProjectItems();
    });

    var equipmentItemGrid = $("#selectedEquipmentItemsGrid").data("kendoGrid");

    var totalProjectEquipmentItems = equipmentItemGrid.dataSource.total();
    viewModel.set("totalProjectEquipmentItems", totalProjectEquipmentItems);

    var equipmentData = equipmentItemGrid.dataSource.data();
    $.each(equipmentData, function (i, item) {
        if (item.MaintenanceScheduleDaysAlert) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(15)').css("color", "#E31E33 !important");
        }
        if (item.MaintenanceSchedulePointsAlert) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(16)').css("color", "#E31E33 !important");
        }
        if (item.Fail) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(19)').css("background-color", "#EA5C6B !important");
        }
    });
}

function expandAllView () {
    let equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
    equipmentCategoryTreeView.expand(".k-treeview-item");
}
function collapseAllView () {
    let equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
    equipmentCategoryTreeView.collapse(".k-treeview-item");
}

function offshoreChanged(e) {
    $(e.container).find(".k-edit-buttons").html("<a class='btn btn-primary btn-sm k-grid-update' href='#'>Update</a> " +
       "<a class='btn btn-primary btn-sm k-grid-cancel' href='#'>Cancel</a>");

    var isOffshore = $("#IsOffshore").data("kendoDropDownList");
    var isOffshoreValue = isOffshore.value();

    if (isOffshoreValue == "true") {
        $("#Street").removeAttr("data-val-required");
        $("#City").removeAttr("data-val-required");
    } else {
        $("#Street").attr("data-val-required", "Street is required");
        $("#City").attr("data-val-required", "City is required");
    }
}

function removeSelectedEquipmentItem(e) {
  if (!e.type || e.type == "destroy") {
    refreshEquipmentProjectItems()
 }
}

function onActivate(e) {
    const tabId = e.item.id;
    function LoadEquipmentItemGridTabData() {
        $("#selectedEquipmentItemsGrid").data("kendoGrid").dataSource.read().then(function () {

            if ($("#equipmentCategoryTreeView").data("kendoTreeView"))
                $("#equipmentCategoryTreeView").data("kendoTreeView").dataSource.read();

            if ($("#availableEquipmentItemsGrid").data("kendoGrid"))
                $("#availableEquipmentItemsGrid").data("kendoGrid").dataSource.read();

            loadSelectedEquipmentItemsGrid()
        });
    }
    function loadShipmentGirdTabData(){
        $("#shipmentGrid").data("kendoGrid").dataSource.read();
    }
    function loadAttachmentsTabData(){
        $("#projectDocumentsGrid").data("kendoGrid").dataSource.read();
    }
    switch (tabId) {
        case "EquipmentItemGridTab":
            $("#equipmentCategoryTreeView").data("kendoTreeView") && $("#equipmentCategoryTreeView").data("kendoTreeView").dataSource.data().length ? true : LoadEquipmentItemGridTabData()
            break;
        case 'ShipmentGirdTab':
            $("#shipmentGrid").data("kendoGrid").dataSource.data().length ? true : loadShipmentGirdTabData()
            break;
        case 'AttachmentsTab':
            $("#projectDocumentsGrid").data("kendoGrid").dataSource.data().length ? true : loadAttachmentsTabData()
            break;            
        default:
            break;
    }
}

function multiselectOpen(e) {
    if (!e.sender.requestFullfield) {
        e.sender.dataSource.read();
        e.sender.requestFullfield = true
    }
}
function oppsFields(e) {

    return {
        companyFieldIdString: $("#OppsFieldIds").data('kendoMultiSelect').value().join(';'),
        text: getTextValue(e)
    }
}
function compFields(e) {
    return {
        companyFieldIdString: $("#CompanyFieldIds").data('kendoMultiSelect').value().join(';'),//$("#CompanyFieldIds").data('kendoMultiSelect').value(),
        text: getTextValue(e)

    }
}

function selectedEquipmentItemsGridRequestEnd(e) {
    if (e.type === "destroy") {
        $("#selectedEquipmentItemsGrid").data("kendoGrid").dataSource.read();
        $("#availableEquipmentItemsGrid").data("kendoGrid").dataSource.read();
    }
}

function scheduleDates(equipmentItemId) {
    viewModel.set("selectedEquipmentItemId", equipmentItemId);

    $("#scheduleDatesWindow").data("kendoWindow").center().open();
}
function equipmentItemMaintenanceScheduleData() {
    return {
        eId: viewModel.get("selectedEquipmentItemId")
    }
}
function scheduleDatesWindowOpened() {
    var equipmentItemMaintenanceScheduleGrid = $("#equipmentItemMaintenanceScheduleGrid").data("kendoGrid");
    equipmentItemMaintenanceScheduleGrid.dataSource.read();
}

function onCrewChange(e) {
    var crewId = this.value();
    $.ajax({
        cache: false,
        dataType: 'json',
        url: '/Operation/CheckCrewExists',
        data: {
            crewId: crewId,
        },
        success: function (result) {
            if (result) {
                alert(result)
            }
        },
    });
}

$("#createProject").click(function () {
    this.disabled = true;
    var validator = $('#editProjectForm').kendoValidator().data('kendoValidator');
    data = $("#editProjectForm").serializeArray();
    setCorrectDateTime(data);

    if (!($("#ProjectTitle").data("kendoTextBox"))) {
        data.push({ name: 'ProjectTitle', value: 'null' });
    }
    if (validator) {
        if (validator.validate()) {
            $.ajax({
                type: "POST",
                url: '/Operation/CreateProject',
                dataType: "json",
                data,
                success: function (result) {
                    window.location.href = `/Operation/EditProject/${result.projectId}`;
                },
            });
        } else {
            scrolToFirstError();
            this.disabled = false;
        }
    } else {
        scrolToFirstError();
        this.disabled = false;
    }
});

$("#projectSaveDetails").click(function () {
    $("#projectSaveDetails").disabled = true;
    this.disabled = true;
    var validator = $('#editProjectForm').kendoValidator().data('kendoValidator');
    let data = $("#editProjectForm").serializeArray();
    setCorrectDateTime(data);

    if (validator) {
        if (validator.validate()) {
            $.ajax({
                type: "POST",
                url: '/Operation/EditProject',
                dataType: "json",
                data: data,
                success: function (result) {
                    updateMessage(editProjectModel.name);
                    $("#projectSaveDetails").removeAttr("disabled");
                    refreshProjectLogs();
                },
                error: function (e) {
                    jqXHRErrors(e);
                    $("#projectSaveDetails").removeAttr("disabled");
                }
            });
        } else {
            scrolToFirstError();
            this.disabled = false;
        }
    } else {
        this.disabled = false;
    }
});

var viewModel = kendo.observable({
    isWellVisible: false,
    totalCompanyWellDocuments: 0,
    jobs: [],
    totalEquipmentItems: 0,
    selectedEquipmentCategory: "",
    otherObjective: editProjectModel.isOther,
    hasJobs :  editProjectModel.hasJobs,
    isTransit: editProjectModel.projectShipmentSent,
    equipmentShipmentId: "",
    // initial counts of async Tab items
    totalProjectEquipmentItems: 0,
    totalProjectShipments: 0,
    totalProjectDocuments: 0,
    totalCrews: 0,

    otherObjectiveVisible: function () {
        var otherObjective = this.get("otherObjective");

        return otherObjective == "True";
    },
    totalJobs: function () {
        var jobs = this.get("jobs");
        return jobs.length;
    },
    projectId: editProjectModel.projectId,
    detailsText: function(){
        return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Details</span>`;   
    },
    totalProjectComments: 0,
    totalProjectCommentsText: function(){
        return `<span class="k-link"><i class="fa fa-comment mr-1"></i> Comments (<span data-bind="text: totalProjectComments"></span>)</span>`;
    },
    totalCrewsText: function(){
        return `<span class="k-link"><i class="fa fa-users mr-1"></i> Crews (<span data-bind="text: totalCrews"></span>)</span>`;
    },
    totalDocuments : function(){
        var totalProjectDocuments = this.get("totalProjectDocuments");
        var totalCompanyWellDocuments = this.get("totalCompanyWellDocuments");
        return totalProjectDocuments + totalCompanyWellDocuments;
    },
    totalDocumentsText: function(){
        return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Attachments (<span data-bind="text: totalDocuments"></span>)</span>`;    
    },
    totalProjectLogs:0,
    totalProjectLogsText:function(){
        return `<span class="k-link"><i class="fa fa-clock mr-1"></i> Project Updates (<span data-bind="text: totalProjectLogs"></span>)</span>`;    
    },
    totalProjectShipmentsText: function(){
        return `<span class="k-link"><i class="fa fa-plane mr-1"></i> Inbound Shipments (<span data-bind="text: totalProjectShipments"></span>)</span>`;    
    },
    totalBackloadProjectShipments: 0,
    totalBackloadProjectShipmentsText: function(){
        return `<span class="k-link"><i class="fa fa-plane mr-1"></i> Backload Shipments (<span data-bind="text: totalBackloadProjectShipments"></span>)</span>`;
    },
    totalEquipmentItemsText: function(){
        return `<span class="k-link"><i class="fa fa-tags mr-1"></i> Equipment Items (<span data-bind="text: totalProjectEquipmentItems"></span>)</span>`;  
    },
    totalEquipmentShipmentNonAssetItems: 0,
    totalEquipmentShipmentNonAssetItemsText: function(){
        return `<span class="k-link"><i class="fa fa-tags mr-1"></i> Non-Asset Items (<span data-bind="text: totalEquipmentShipmentNonAssetItems"></span>)</span>`;
    },
    toCompanyLocationId: "",
    toProjectId : "",
    crewOut: true,
    projectCompanyId: editProjectModel.companyId,
    operatorId: editProjectModel.customerCompanyId,
    partnerCompanyId : editProjectModel.partnerCompanyId,
    companyFieldIds:  editProjectModel.companyFieldIds,
    companyWellIds: editProjectModel.companyWellIds,
    projectCompanyFieldIds: editProjectModel.projectCompanyFieldIds,
    projectCompanyWellIds: editProjectModel.projectCompanyWellIds,
    totalProjectLessons: 0,
    selectedCustomerId: editProjectModel.companyId,
    currentLocationId: editProjectModel.currentCompanyLocationId,
    totalProjectLessonsText: function(){
        return `<span class="k-link"><i class="fa fa-bars mr-1"></i> Lessons (<span data-bind="text: totalProjectLessons"></span>)</span>`;
    },
    totalWells : 0,
    totalWellsText : function(){
        return `<span class="k-link"><i class="fa fa-cogs "></i> Wells (<span data-bind="text: totalWells"></span>)</span>`;
    },
    companyWellId: 0,
    fluidTypes: [],
    deleteProject:function(){
        var confirmDelete = confirm("Are you sure you wish to delete this project");

        if(confirmDelete){
            window.location.href= `/Operation/DeleteProject?id=${editProjectModel.projectId}`;
        }
    },

    showShipmentToLocationWindow: function() {
        $("#toLocationWindow").data("kendoWindow").center().open();
    },

    showAddCompanyLocationWindow: function () {
        $("#addCompanyLocationWindow").data("kendoWindow").center().open();
        var field = $("#CompanyLocationId").data("kendoDropDownList");
            field.dataSource.read();
    },
    showAddCompanyWindow: function () {
        $("#addCompanyWindow").data("kendoWindow").center().open();
        var field = $("#CompanyId").data("kendoDropDownList");
        field.dataSource.read();
    },

    showAddCompanyContactWindow: function () {
        $("#addCompanyContactWindow").data("kendoWindow").center().open();
        var field = $("#companyLocationId").data("kendoDropDownList");
            field.dataSource.read();
    },

    showWellWindow: function () {
        $("#addWellWindow").data("kendoWindow").center().open();
        var field = $("#companyFieldId").data("kendoDropDownList");
            field.dataSource.read();
    },
    showFieldWindow: function () {
        $("#addFieldWindow").data("kendoWindow").center().open();
        //var field = $("#companyFieldId").data("kendoDropDownList");
        //field.dataSource.read();
    },
});

kendo.bind(document.body.children, viewModel);