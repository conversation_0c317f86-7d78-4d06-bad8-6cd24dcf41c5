﻿using Centerpoint.Common.Constants;
using Centerpoint.Common.Enums;
using Centerpoint.Controllers.Extensions.cs;
using Centerpoint.Extensions;
using Centerpoint.Model.Configuration;
using Centerpoint.Model.ViewModels;
using Centerpoint.Service;
using Centerpoint.Service.Interfaces;
using Centerpoint.Service.Services;
using Centerpoint.Services;
using Centerpoint.Utils;
using DocumentFormat.OpenXml.Office2010.Excel;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Net;

namespace Centerpoint.Controllers
{
    [Authorize]
    public class SalesController : Controller
    {
        private readonly Settings _settings;
        private readonly ISalesService _salesService;
        private readonly IIdentityService _identityService;
        private readonly ICurrentUserService _currentUser;
        private readonly ILessonService _lessonService;
        private readonly IProjectService _projectService;
        private readonly ICommentService _commentService;
        private readonly ISifService _sifService;
        private readonly IRiskService _riskService;
        private readonly ICertificateService _certificateService;
        private readonly ICrewService _crewService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly WebContext _webContext;
        private readonly IReportService _reportService;

        public SalesController(IOptions<Settings> settings,
            ISalesService salesService,
                               IIdentityService identityService,
                               ICurrentUserService currentUser,
                               ILessonService lessonService,
                               IProjectService projectService,
                               ICommentService commentService,
                               ISifService sifService,
                               IRiskService riskService,
                               ICertificateService certificateService,
                               ICrewService crewService,
                               IHttpContextAccessor httpContextAccessor,
                               WebContext webContext,
                               IReportService reportService
                               )
        {
            _identityService = identityService;
            _currentUser = currentUser;
            _lessonService = lessonService;
            _projectService = projectService;
            _commentService = commentService;
            _sifService = sifService;
            _riskService = riskService;
            _certificateService = certificateService;
            _crewService = crewService;
            _settings = settings.Value;
            _salesService = salesService;
            _httpContextAccessor = httpContextAccessor;
            _webContext = webContext;
            _reportService = reportService;
        }
        public async Task<IActionResult> Index(string tab)
        {
            var model = await _salesService.GetSalesDashboardModel();

            this.SetTitle("Sales Dashboard");

            ViewBag.Tab = tab;

            return View(model);
        }

        public async Task<IActionResult> Matrix(string tab)
        {
            this.SetTitle("Matrix");

            ViewBag.Tab = tab;

            return View();
        }

        public async Task<IActionResult> GetAllLeads([DataSourceRequest] DataSourceRequest request)
        {
            var opportunities = await _salesService.GetAllLeads();

            return Json(await opportunities.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> GetAllOpportunities([DataSourceRequest] DataSourceRequest request)
        {
            var opportunities = await _salesService.GetAllOpportunities();

            return Json(await opportunities.Response.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> GetAllEvents([DataSourceRequest] DataSourceRequest request)
        {
            var events = await _salesService.GetAllEvents(_settings.EventTime);

            return Json(await events.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> GetAllEventsByOpportunityId([DataSourceRequest] DataSourceRequest request, int opportunityId)
        {
            var events = await _salesService.GetAllEventsByOpportunityId(opportunityId);

            return Json(await events.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> GetAllActions([DataSourceRequest] DataSourceRequest request)
        {
            var actions = await _salesService.GetAllActions();

            return Json(await actions.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> GetAllActionsByOpportunityId([DataSourceRequest] DataSourceRequest request, int opportunityId)
        {
            var actions = await _salesService.GetAllActionsByOpportunityId(opportunityId);

            return Json(await actions.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> GetAllFollowupActionsByOpportunityEventId([DataSourceRequest] DataSourceRequest request, int opportunityEventId)
        {
            var actions = await _salesService.GetAllFollowupActionsByOpportunityEventId(opportunityEventId);

            return Json(await actions.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> GetAllClosedLeadsAndOpportunities([DataSourceRequest] DataSourceRequest request, string from, string to)
        {
            var result = await _salesService.GetAllClosedLeadsAndOpportunities(from, to);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> GetAllClosedLeads([DataSourceRequest] DataSourceRequest request, string from, string to)
        {
            var result = await _salesService.GetAllClosedLeads(from, to);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> GetAllClosedOpportunities([DataSourceRequest] DataSourceRequest request, string from, string to)
        {
            var result = await _salesService.GetAllClosedOpportunities(from, to);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> GetAllClosedLeadOpportunityEvents([DataSourceRequest] DataSourceRequest request)
        {
            var result = await _salesService.GetAllClosedLeadOpportunityEvents(_settings.EventTime);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> GetAllClosedLeadOpportunityActions([DataSourceRequest] DataSourceRequest request)
        {
            var result = await _salesService.GetAllClosedLeadOpportunityActions();

            return Json(await result.ToDataSourceResultAsync(request));
        }

        #region Edit

        public async Task<IActionResult> Add(string type)
        {
            var result = _salesService.Add(type, _currentUser.UserId);

            if (type == OpportunityTypeConstant.Lead)
            {
                this.SetTitle("Add New Lead");
            }
            else
            {
                this.SetTitle("Add New Opportunity");
            }

            return View("Edit", result);
        }

        public async Task<IActionResult> Edit(int id, int revision, string tab)
        {
            var model = await _salesService.Edit(id, revision, tab);
            if (model == null)
                return View(new OpportunityModel { });
            else
            {
                this.SetTitle(string.Format("{0}", model.CompanyName));

                ViewBag.Tab = tab;
                ViewBag.CompanyId = model.CompanyId;
                ViewBag.PartnerCompanyId = model.PartnerCompanyId;
                ViewBag.Revision = revision;
                return View(model);
            }
        }

        public async Task<IActionResult> Revision(int id, int revision)
        {
            var model = await _salesService.Revision(id, revision);

            this.SetTitle(string.Format("{0}", model.CompanyName));

            ViewBag.CompanyId = model.CompanyId;

            return View("Edit", model);
        }

        [HttpPost]
        public async Task<IActionResult> Edit(OpportunityModel model, int revision)
        {
            if (!model.CompanyId.HasValue && !model.PartnerCompanyId.HasValue)
            {
                ModelState.AddModelError("OperatorPartner", "Operator and Partner cannot be empty at least one must have a value.");
            }

            if (model.CompanyId.HasValue && string.IsNullOrWhiteSpace(model.CompanyCountry))
            {
                ModelState.AddModelError("Operator Country", "Operator Country field must have a value");
            }

            if (model.PartnerCompanyId.HasValue && string.IsNullOrWhiteSpace(model.PartnerCountry))
            {
                ModelState.AddModelError("Partner Country", "Partner Country field must have a value");
            }

            if (string.IsNullOrWhiteSpace(model.Description))
            {
                ModelState.AddModelError("Description", "Description field must have a value");
            }

            if (model.CompanyId.HasValue && model.PartnerCompanyId.HasValue && string.IsNullOrWhiteSpace(model.Customer))
            {
                ModelState.AddModelError("Customer", "Customer field must have a value");
            }

            if (model.Type == OpportunityTypeConstant.Opportunity && !model.DivisionId.HasValue)
            {
                ModelState.AddModelError("Division", "Division field must have a value");
            }

            if (model.Type == OpportunityTypeConstant.Opportunity && string.IsNullOrWhiteSpace(model.EngineersRequired))
            {
                ModelState.AddModelError("Engineers Required", "Engineers Required field must have a value");
            }

            if (model.Type == OpportunityTypeConstant.Lead && !model.MobilisationDate.HasValue)
            {
                ModelState.AddModelError("Mobilization Date", "Estimate Start Date must have a value");
            }
            else if (model.Type == OpportunityTypeConstant.Opportunity && !model.MobilisationDate.HasValue)
            {
                ModelState.AddModelError("Mobilization Date", "Mobilization Date must have a value");
            }

            if (model.ServiceIds == null)
            {
                ModelState.AddModelError("Services", "Services must have a value");
            }

            if (!ModelState.IsValid)
            {
                return View(model);
            }

            var result = await _salesService.Edit(model, revision, _currentUser.UserId);

            if (_webContext.IsSalesAdmin || _webContext.IsGlobalAdmin)
                this.SetMessage(MessageType.Success, string.Format("{0} details have been successfully updated", model.CompanyName));
            else
                return RedirectToAction("LeadConfirmation");

            if (result.Model.ParentOpportunityId.HasValue)
                return RedirectToAction("Edit", new { @id = result.Model.ParentOpportunityId, @revision = result.Revision });
            else
                return RedirectToAction("Edit", new { @id = result.Model.OpportunityId, @revision = result.Revision });
        }

        public async Task<IActionResult> LeadConfirmation()
        {
            this.SetTitle("Lead Confirmation");
            return View();
        }

        public async Task<IActionResult> Delete(int id)
        {
            var opportunityType = await _salesService.Delete(id, _currentUser.UserId);

            if (opportunityType == OpportunityTypeConstant.Lead)
            {
                return RedirectToAction("Matrix");
            }
            else
            {
                return RedirectToAction("Matrix", new { @tab = "opportunities" });
            }
        }

        public async Task<IActionResult> DeleteCurrentVersion(int id)
        {
            var result = await _salesService.DeleteCurrentVersion(id, _currentUser.UserId);

            return RedirectToAction("Edit", new { @id = result.ParentOpportunityId, @revision = result.Revision });
        }

        #endregion

        #region Contacts 

        [HttpPost]
        public async Task<IActionResult> AddLeadContact(int companyContactId, int opportunityId, int currentOpportunityId, string contactRole)
        {
            var result = await _salesService.AddLeadContact(companyContactId, opportunityId, currentOpportunityId, contactRole, _currentUser.UserId);

            if (result.HasErrors())
            {
                return Json(new { success = false, responseText = result.Errors });
            }

            return Json(new { success = true });
        }

        public async Task<IActionResult> GetCompanyContactsNotFromOpportunityCompanyContactId([DataSourceRequest] DataSourceRequest request, int companyId, int? partnerCompanyId, int? opportunityId)
        {
            var result = await _salesService.GetCompanyContactsNotFromOpportunityCompanyContactId(companyId, partnerCompanyId, opportunityId);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> GetLeadContacts([DataSourceRequest] DataSourceRequest request, int id)
        {
            var result = await _salesService.GetLeadContacts(id);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> DeleteLeadContacts([DataSourceRequest] DataSourceRequest request, OpportunityCompanyContactModel model)
        {
            await _salesService.DeleteLeadContacts(model, _currentUser.UserId);

            return Json(new[] { model }.ToDataSourceResult(request));
        }

        #endregion

        #region Clone

        public async Task<IActionResult> Clone(int id, bool index = false, string tab = "")
        {
            var opportunityId = await _salesService.Clone(id, tab, _currentUser.UserId, index);

            if (!opportunityId.HasValue || index)
            {
                return RedirectToAction("Matrix", new { @tab = tab });
            }

            return RedirectToAction("Edit", new { @id = opportunityId, @revision = 0 });
        }

        #endregion

        #region Opportunity Documents

        public async Task<IActionResult> GetOpportunityDocuments([DataSourceRequest] DataSourceRequest request, int opportunityId)
        {
            var result = await _salesService.GetOpportunityDocuments(opportunityId);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> AttachOpportunityDocuments(IEnumerable<IFormFile> opportunityDocuments, int id, DocumentModel model)
        {
            await _salesService.AttachOpportunityDocuments(opportunityDocuments, id, model, _currentUser.UserId);

            return Json(true);
        }

        public async Task<IActionResult> DeleteOpportunityDocument(DocumentModel model, int opportunityId)
        {
            await _salesService.DeleteOpportunityDocument(model, opportunityId, _currentUser.UserId);

            return Json(ModelState.ToDataSourceResult());
        }
        #endregion

        #region Opportunity Logs

        public async Task<IActionResult> GetOpportunityLogByOpportunityId([DataSourceRequest] DataSourceRequest request, int opportunityId)
        {
            var result = await _salesService.GetOpportunityLogByOpportunityId(opportunityId);

            return Json(await result.ToDataSourceResultAsync(request));
        }
        #endregion

        #region Events

        public async Task<IActionResult> GetOpportunityEventsByOpportunityId([DataSourceRequest] DataSourceRequest request, int opportunityId)
        {
            var result = await _salesService.GetOpportunityEventsByOpportunityId(opportunityId);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> AddEvent(int? id, int? opportunityActionId, int? revision)
        {
            var model = await _salesService.AddEvent(id, opportunityActionId, revision);

            this.SetTitle("Add New Event");

            return View("EditEvent", model);
        }

        public async Task<IActionResult> EditEvent(int id, int? revision, int? opportunityActionId, string tab)
        {
            var model = await _salesService.EditEvent(id, revision, opportunityActionId, tab);

            this.SetTitle(string.Format("Edit - {0}", model.EventTypeName));

            ViewBag.Tab = tab;

            if (opportunityActionId.HasValue)
                ViewBag.OpportunityActionId = opportunityActionId;

            if (revision.HasValue)
                ViewBag.Revision = revision;

            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> EditEvent(OpportunityEventModel model, int? opportunityActionId, int? revision)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            var result = await _salesService.EditEvent(model, opportunityActionId, revision, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("{0} event details have been successfully updated", model.EventTypeName));

            if (result.Model.OpportunityId.HasValue)
                return RedirectToAction("EditEvent", new { @id = result.Model.OpportunityEventId, @opportunityActionId = opportunityActionId, @revision = revision.HasValue ? revision : result.Revision });
            else
                return RedirectToAction("EditEvent", new { @id = result.Model.OpportunityEventId, @opportunityActionId = opportunityActionId });
        }

        public async Task<IActionResult> DeleteOpportunityEvent(OpportunityEventModel model)
        {
            await _salesService.DeleteOpportunityEvent(model, _currentUser.UserId);

            return RedirectToAction("Edit", new { @id = model.OpportunityId });
        }

        #endregion

        #region Opportunity Event Documents

        public async Task<IActionResult> GetOpportunityEventDocuments([DataSourceRequest] DataSourceRequest request, int opportunityEventId)
        {
            var result = await _salesService.GetOpportunityEventDocuments(opportunityEventId);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> GetOpportunityEventDocumentsByOpportunityId([DataSourceRequest] DataSourceRequest request, int opportunityId)
        {
            var result = await _salesService.GetOpportunityEventDocumentsByOpportunityId(opportunityId);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> AttachOpportunityEventDocuments(IEnumerable<IFormFile> opportunityEventDocuments, int id, DocumentModel model)
        {
            await _salesService.AttachOpportunityEventDocuments(opportunityEventDocuments, id, model, _currentUser.UserId);

            return Json(true);
        }

        public async Task<IActionResult> DeleteOpportunityEventDocument([DataSourceRequest] DataSourceRequest request, DocumentModel model, int opportunityEventId)
        {
            await _salesService.DeleteOpportunityEventDocument(model, opportunityEventId);

            return Json(ModelState.ToDataSourceResult());
        }

        public async Task<IActionResult> DeleteOpportunityEventDocumentByOpportunityId([DataSourceRequest] DataSourceRequest request, DocumentModel model, int opportunityId)
        {
            await _salesService.DeleteOpportunityEventDocumentByOpportunityId(model, opportunityId);

            return Json(ModelState.ToDataSourceResult());
        }
        #endregion

        #region Opportunity Action Documents

        public async Task<IActionResult> GetOpportunityActionDocuments([DataSourceRequest] DataSourceRequest request, int opportunityActionId)
        {
            var result = await _salesService.GetOpportunityActionDocuments(opportunityActionId);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> GetOpportunityActionDocumentsByOpportunityId([DataSourceRequest] DataSourceRequest request, int opportunityId)
        {
            var result = await _salesService.GetOpportunityActionDocumentsByOpportunityId(opportunityId);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> AttachOpportunityActionDocuments(IEnumerable<IFormFile> opportunityActionDocuments, int id, DocumentModel model)
        {
            await _salesService.AttachOpportunityActionDocuments(opportunityActionDocuments, id, model, _currentUser.UserId);

            return Json(true);
        }

        public async Task<IActionResult> DeleteOpportunityActionDocument([DataSourceRequest] DataSourceRequest request, DocumentModel model, int opportunityActionId)
        {
            await _salesService.DeleteOpportunityActionDocument(model, opportunityActionId);

            return Json(ModelState.ToDataSourceResult());
        }

        public async Task<IActionResult> DeleteOpportunityActionDocumentByOpportunityId([DataSourceRequest] DataSourceRequest request, DocumentModel model, int opportunityId)
        {
            await _salesService.DeleteOpportunityActionDocumentByOpportunityId(model, opportunityId);

            return Json(ModelState.ToDataSourceResult());
        }
        #endregion

        #region Actions

        public async Task<IActionResult> GetOpportunityActionsByOpportunityId([DataSourceRequest] DataSourceRequest request, int opportunityId)
        {
            var result = await _salesService.GetOpportunityActionsByOpportunityId(opportunityId);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> AddAction(int? opportunityEventId, int? opportunityId, int? revision)
        {

            var model = await _salesService.AddAction(opportunityEventId, opportunityId, revision);

            if (opportunityEventId.HasValue)
            {
                this.SetTitle("Add New Follow-up Action");
            }
            else
            {
                this.SetTitle("Add New Action");
            }

            if (revision.HasValue)
            {
                ViewBag.Revision = revision;
            }

            return View("EditAction", model);
        }

        public async Task<IActionResult> EditAction(int id, int? revision, string tab)
        {
            var model = await _salesService.EditAction(id, revision, tab);

            if (revision.HasValue)
            {
                ViewBag.Revision = revision; 
            }

            this.SetTitle(string.Format("Edit - {0}", model.ActionTypeName));

            ViewBag.Tab = tab;

            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> EditAction(OpportunityActionModel model, int? revision)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }
            var result = await _salesService.EditAction(model, revision, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("{0} action details have been successfully updated", model.ActionTypeName));

            if (result.Model.OpportunityId.HasValue)
            {
                return RedirectToAction("EditAction", new { @id = result.Model.OpportunityActionId, @opportunityActionId = result.Model.OpportunityActionId, @revision = revision.HasValue ? revision : result.Revision });
            }
            
            return RedirectToAction("EditAction", new { @id = result.Model.OpportunityActionId, @opportunityActionId = result.Model.OpportunityActionId });
        }
        public async Task<IActionResult> DeleteOpportunityAction(OpportunityActionModel model)
        {
            await _salesService.DeleteOpportunityAction(model, _currentUser.UserId);

            return RedirectToAction("Edit", new { @id = model.OpportunityId.Value });
        }

        #endregion

        #region Sales History

        public async Task<IActionResult> SalesHistory(string tab)
        {
            var model = _salesService.SalesHistory();

            ViewBag.Tab = tab;

            this.SetTitle("Sales History Dashboard");

            return View(model);
        }

        #endregion

        #region Other

        public async Task<IActionResult> RefreshContacts(int opportunityId)
        {
            await _salesService.RefreshContacts(opportunityId, _currentUser.UserId);

            return Json(new { success = true });
        }

        public async Task<IActionResult> UpdateStatus(int id)
        {
            await _salesService.UpdateStatus(id, _currentUser.UserId);

            return RedirectToAction("Edit", new { @id = id, @revision = 0 });
        }

        public async Task<IActionResult> RevertToLead(int id)
        {
            await _salesService.RevertToLead(id, _currentUser.UserId);

            return RedirectToAction("Edit", new { @id = id, @revision = 0 });
        }

        public async Task<IActionResult> Reopen(int opportunityId, int revision)
        {
            await _salesService.Reopen(opportunityId, revision, _currentUser.UserId);

            return RedirectToAction("Edit", new { @id = opportunityId, @revision = revision });
        }

        [HttpPost]
        public async Task<IActionResult> ClosureReason(int opportunityId, int reasonId, string reasonText, string comment)
        {
            await _salesService.ClosureReason(opportunityId, reasonId, reasonText, comment, _currentUser.UserId);

            return Json(new { success = "true" });
        }

        public async Task<IActionResult> ConvertToProject(int id, int maxRevision, int oppsId)
        {
            await _salesService.ConvertToProject(id, maxRevision, oppsId, _currentUser.UserId);

            return RedirectToAction("Matrix", new { @tab = "opportunities" });
        }

        public async Task<IActionResult> UpdateRevision(int id)
        {
            var model = await _salesService.UpdateRevision(id, _currentUser.UserId);

            return RedirectToAction("Edit", new { @id = model.ParentOpportunityId, @revision = model.Revision });
        }

        public async Task<IActionResult> GetFromToSummary(string from, string to)
        {
            var model = await _salesService.GetFromToSummary(from, to);

            return Json(model);
        }

        public async Task<IActionResult> GetCompanyWells([DataSourceRequest] DataSourceRequest request, int?[] companyWellIds)
        {
            var result = await _salesService.GetCompanyWells(companyWellIds);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> GetCompanyWellDocuments([DataSourceRequest] DataSourceRequest request, int?[] companyWellIds)
        {
            var result = await _salesService.GetCompanyWellDocuments(companyWellIds);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> GetCompanyWellDocumentByOpportunityId([DataSourceRequest] DataSourceRequest request, int opportunityId)
        {
            var result = await _salesService.GetCompanyWellDocumentByOpportunityId(opportunityId);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> GetCompanyWellDocumentsByWellId([DataSourceRequest] DataSourceRequest request, int cwId)
        {
            var result = await _salesService.GetCompanyWellDocumentsByWellId(cwId);

            return Json(await result.ToDataSourceResultAsync(request));
        }


        #endregion

        #region Sales PDF Report

        [AllowAnonymous]
        public async Task<ActionResult> WeeklySalesReport()
        {
            var cookies = WebContext.GetAuthCookies(Request);
            var resultConverting = _reportService.ExportSaleAsPdf(cookies,
                "Sales Report", PDFType.SalesReport);

            if (resultConverting.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(resultConverting.Errors);
            }

            string fileName = string.Format("{0}.pdf", "Sales Report");

            return File(resultConverting.Response, "application/pdf", fileName);            
        }
        public async Task<ActionResult> ViewSalesHTMLReport()
        {
            var resultModel = await _salesService.WeeklySalesReport(_settings.EventTime);
            return View("PDF_Reports/SalesPdf", resultModel);
        }
        #endregion

        #region Forecast

        public async Task<IActionResult> GetAllForecastOpportunities([DataSourceRequest] DataSourceRequest request, string from, string to)
        {
            var result = await _salesService.GetAllForecastOpportunities(from, to);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> SalesForecast(int year, string tab)
        {
            var model = await _salesService.SalesForecast(year, tab);
            this.SetTitle("Sales Forecast");

            return View(model);
        }

        #endregion

        #region Focused Customer

        public async Task<IActionResult> GetFocusedCustomers([DataSourceRequest] DataSourceRequest request)
        {
            var result = await _salesService.GetFocusedCustomers();

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> AddFocusCustomer(int customerId)
        {
            bool result = false;

            if (customerId > 0)
            {
                result = await _salesService.AddFocusCustomer(customerId);
            }

            return Json(new { success = result });
        }

        public async Task<IActionResult> DeleteFocusCustomer(int customerId)
        {
            await _salesService.DeleteFocusCustomer(customerId);

            return Json(new { success = "true" });
        }

        #endregion

    }
}
