{"Logging": {"LogLevel": {"Default": "Information", "System": "Warning", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Hangfire": "Error"}, "ApplicationInsights": {"LogLevel": {"Default": "Information", "System": "Warning", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Warning", "Azure.Messaging.ServiceBus": "Warning", "Hangfire": "Error"}}}, "ConnectionStrings": {"DefaultConnection": "Server=tcp:centerpointapp.database.windows.net,1433;Initial Catalog=centerpoint;Persist Security Info=False;User ID=centerpoint;Password=**********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"}, "BlobStorage": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=centerpointstorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "Container": "development"}, "EmailSettings": {"Username": "<EMAIL>", "Password": "In$1so_C3nt3r#P0int24!", "SmtpHost": "smtp.office365.com", "SmtpPort": "587", "From": "<EMAIL>", "UseSSL": true}, "Settings": {"webpages:Version": "*******", "webpages:Enabled": false, "ClientValidationEnabled": true, "UnobtrusiveJavaScriptEnabled": true, "EventTime": 30, "LoginAttempts": 5, "DownloadsFolderName": "Downloads", "DepreciationTotalYears": 8}, "TwilioSMS": {"TWILIO_ACCOUNT_SID": "**********************************", "TWILIO_AUTH_TOKEN": "398a000c97d4b7829326b085bb333828", "TWILIO_FROM_PHONE": "+************"}, "AzureAdSettings": {"Enabled": false, "ClientId": "", "TenantId": "", "Authority": "https://login.microsoftonline.com/{0}/"}, "AppUrl": "https://demo.centerpoint.pro"}