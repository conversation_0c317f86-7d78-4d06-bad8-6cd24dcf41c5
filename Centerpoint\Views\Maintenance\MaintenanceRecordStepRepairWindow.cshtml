﻿<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>Is repair required due to the failure of this step?</label>
            <br />
            @(Html.Kendo().DropDownList()
              .Name("stepRepair")              
              .DataValueField("Key")
              .DataTextField("Value")
              .ValuePrimitive(true)
              .Filter("contains")
              .BindTo(new List<KeyValuePair<string, string>> { new KeyValuePair<string, string>("false", "No"), new KeyValuePair<string, string>("true", "Yes"), })
              .HtmlAttributes(new { @data_bind = "value:isRepairRequired" }))
        </div>
    </div>
</div>
<span>
    <button id="repairMaintenanceRecordClick" class="btn btn-primary btn-sm">Confirm</button>
</span>
