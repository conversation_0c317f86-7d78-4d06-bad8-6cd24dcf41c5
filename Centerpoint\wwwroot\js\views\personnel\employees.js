
$(document).ready(function () {
    loadUserGrid();
});

function loadUserGrid() {
    var grid = $("#userGrid").data("kendoGrid");
    var toolBar = $("#userGrid .k-grid-toolbar").html();
    var options = localStorage["userGrid"];
    viewModel.set("initialUserGridOptions", kendo.stringify(grid.getOptions()));
    if (options) {
        grid.setOptions(JSON.parse(options));
        $("#userGrid .k-grid-toolbar").html(toolBar);
        $("#userGrid .k-grid-toolbar").addClass("k-grid-top");
    }
}

function saveUserGrid(e) {
    setTimeout(function () {
        var grid = $("#userGrid").data("kendoGrid");
        localStorage["userGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}

function updateUserTotal() {
    $("#resetUserGrid").click(function (e) {
        e.preventDefault();
        resetGridView('userGrid', 'initialUserGridOptions')
    });

    var userGrid = $("#userGrid").data("kendoGrid");
    var totalUsers = userGrid.dataSource.total();
    viewModel.set("totalUsers", totalUsers);

    saveUserGrid();
}

function baseCompanyLocationClick(baseCompanyLocationName) {
    viewModel.set("baseCompanyLocationName", baseCompanyLocationName);
    refreshUserGrid();
}

function userData() {
    return {
        baseCompanyLocationName: viewModel.get("baseCompanyLocationName")
    }
}

function refreshUserGrid() {
    var userGrid = $("#userGrid").data("kendoGrid");
    userGrid.dataSource.read();
}

var viewModel = new kendo.observable({
    baseCompanyLocationName:"",
    totalUsers: 0,
});

$("#userSearch").keyup(function () {
    var query = $("#userSearch").val();

    var userGrid = $("#userGrid").data("kendoGrid");

    if (query != null && query.length > 0) {
        var filter = {
            logic: "or",
            filters: [
              { field: "Name", operator: "contains", value: query },
              { field: "JobTitle", operator: "contains", value: query },
              { field: "RoleDescription", operator: "contains", value: query },
              { field: "BaseCompanyLocationName", operator: "contains", value: query }
            ]
        };

        userGrid.dataSource.filter(filter);
    } else {
        userGrid.dataSource.filter([]);
    }
});

kendo.bind(document.body.children, viewModel);