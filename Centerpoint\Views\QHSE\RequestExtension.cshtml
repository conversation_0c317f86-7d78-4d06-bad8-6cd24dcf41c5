﻿@model ServiceImprovementModel
<p>In order to the change the Investigation Target Date, please add a new date and fill in the reason for date change</p>
<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>New Investigation Target Date</label>
            <br />
            @(Html.Kendo().DatePicker().Name("NewTargetDate").Min(Model.InvestigationTargetDate.HasValue ? Model.InvestigationTargetDate.Value.AddDays(1) : DateTime.Now.AddDays(1)).HtmlAttributes(new { @data_bind = "value:newInvestigationTargetDate", @style = "width:200px; font-size: 14px;" }))
        </div>
        <div class="form-group">
            <label>Comment</label>
            @(Html.TextArea("NewInvestigationTargetDateComment", null, new { @class = "form-control", @style = "width:100%", @rows = "5", @data_bind = "value:newInvestigationActionComment", @data_value_update = "keyup" }))
        </div>
    </div>
</div>
<button id="dateChangeSelectedConfirm" data-bind="enabled:newInvestigationActionComment" class="btn btn-primary btn-sm">Confirm</button>
@Html.HiddenFor(m => m.InvestigationTargetDate)