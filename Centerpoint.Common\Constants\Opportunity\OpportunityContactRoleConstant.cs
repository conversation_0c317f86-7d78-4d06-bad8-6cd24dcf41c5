﻿namespace Centerpoint.Common.Constants
{
    public static class OpportunityContactRoleConstant
    {

        public const string Primary = "PRI";
        public const string Secondary = "SEC";
        public const string Commercial = "COM";
        public const string GeoScience = "GES";
        public const string Operational = "OPR";
        public const string Other = "OTH";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Primary,"Primary"},
                    {Secondary,"Secondary"},
                    {Commercial,"Commercial"},
                    {GeoScience,"GeoScience"},
                    {Operational,"Operational"},
                    {Other, "Other" }
                };
            }
        }

        public static Dictionary<string, string> WithoutPrimaryValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Secondary,"Secondary"},
                    {Commercial,"Commercial"},
                    {GeoScience,"GeoScience"},
                    {Operational,"Operational"},
                    {Other, "Other" }
                };
            }
        }
    }
}
