﻿using Centerpoint.Service.Interfaces;
using Centerpoint.Storage.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Centerpoint.Controllers
{
    [Authorize]
    public class DocumentController : Controller
    {
        private readonly IDocumentService _documentService;
        private readonly IStorage _storage;
        private readonly IWebHostEnvironment _hostingEnvironment;

        public DocumentController(IDocumentService documentService, IStorage storage, IWebHostEnvironment hostingEnvironment)
        {
            _documentService = documentService;
            _storage = storage;
            _hostingEnvironment = hostingEnvironment;
        }
        public async Task<ActionResult> Index(int id)
        {
            var document = await _documentService.GetDocumentById(id);
            Response.Headers.TryAdd("Content-Disposition", string.Format("inline; filename={0}", document.FileName));

            var filePath = await _storage.GetBlobReferenceUri(document.FilePath, document.FileName);

            if (string.IsNullOrEmpty(filePath))
                return BadRequest("File path is empty");

            return Redirect(filePath);
        }
        public async Task<ActionResult> Info()
        {
            return View();
        }
        public async Task<ActionResult> GetManual()
        {
            var path = Path.Combine(_hostingEnvironment.WebRootPath, "doc", "manual.pptx");
            var stream = System.IO.File.OpenRead(path);
            return File(stream, "application/vnd.ms-powerpoint", "Centerpoint User Manual.pptx");
        }
    }
}