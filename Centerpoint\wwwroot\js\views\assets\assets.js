$(document).ready(function () {
    loadEquipmentItemGrid();
    var assetEquipmentItemGrid = $('#assetEquipmentItemGrid').data("kendoGrid");

    assetEquipmentItemGrid.bind('dataBound', function (e) {
        this.element.find('.k-i-excel').remove();
    });

    var equipmentFilterOptions = $.cookie('equipmentFilterOptions');

    if(equipmentFilterOptions){
        viewModel.set("equipmentFilterOptions", JSON.parse(equipmentFilterOptions));
    }

    equipmentGridFilter();
    kendo.bind(document.body.children, viewModel);
});

$("#confirm").click(async function () {

    const assetEquipmentItemGrid = $("#assetEquipmentItemGrid").data("kendoGrid");

    const selectedEquipmentItemIds = [];

    assetEquipmentItemGrid.select().each(function () {
        const selectedEquipmentItem = assetEquipmentItemGrid.dataItem($(this));

        if (selectedEquipmentItem) {
            selectedEquipmentItemIds.push(selectedEquipmentItem.EquipmentItemId);
        }
    });

    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: "/Assets/UpdateEquipmentItemInfo",
        data: {
            equipmentItemIds: selectedEquipmentItemIds,
            info: $("#info").val()
        },
        success: function () {
            $("#editItemWindowOpen").data("kendoWindow").close();
            viewModel.set("selectedEquipmentItemId", "");
            refreshEquipmentItemGrid();
        },
    });
});

function refreshEquipmentItemGrid(){
    var assetEquipmentItemGrid = $("#assetEquipmentItemGrid").data("kendoGrid");
    assetEquipmentItemGrid.dataSource.read();
}

$(function () {
    $("#reset").click(function (e) {
        e.preventDefault();
        localStorage["assetEquipmentItemGrid"] = "";
        window.location.reload();
    });
});

function refreshEquipmentCategoryMaintenanceStepGrid() {
    var equipmentCategoryMaintenanceStepGrid = $("#equipmentCategoryMaintenanceStepGrid").data("kendoGrid");
    equipmentCategoryMaintenanceStepGrid.dataSource.read();
}

function loadEquipmentItemGrid() {
    var grid = $("#assetEquipmentItemGrid").data("kendoGrid");
    var toolBar = $("#assetEquipmentItemGrid .k-grid-toolbar").html();
    var options = localStorage["assetEquipmentItemGrid"];
    viewModel.set("initialAssetEquipmentItemGridOptions", kendo.stringify(grid.getOptions()));
    if (options) {
        grid.setOptions(JSON.parse(options));
        $("#assetEquipmentItemGrid .k-grid-toolbar").html(toolBar);
        $("#assetEquipmentItemGrid .k-grid-toolbar").addClass("k-grid-top");
    }
}

function saveEquipmentGrid(e) {
    setTimeout(function () {
        var grid = $("#assetEquipmentItemGrid").data("kendoGrid");
        localStorage["assetEquipmentItemGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}

function categoryData() {
    return {
        categoryId: viewModel.get("selectedEquipmentCategory").EquipmentCategoryId
    }
}

function statusFilter(element) {
    element.kendoAutoComplete({
        dataSource: {
            transport: {
                read: "/Lookup/GetEquipmentItemStatuses"
            }
        }
    });
}

function equipmentItemMaintenanceScheduleData() {
    return {
        eId: viewModel.get("selectedEquipmentItemId")
    }
}

function scheduleDates(equipmentItemId) {
    viewModel.set("selectedEquipmentItemId", equipmentItemId);

    $("#scheduleDatesWindow").data("kendoWindow").center().open();
}

function scheduleDatesWindowOpened() {
    var equipmentItemMaintenanceScheduleGrid = $("#equipmentItemMaintenanceScheduleGrid").data("kendoGrid");
    equipmentItemMaintenanceScheduleGrid.dataSource.read();
}

function maintenanceRecordData() {
    return {
        equipId: viewModel.get("selectedEquipmentItemId")
    }
}

function equipmentData() {
    return {
        equipmentItemId: viewModel.get("selectedEquipmentItemId")
    }
}

function maintenanceRecordCount(equipmentItemId) {
    viewModel.set("selectedEquipmentItemId", equipmentItemId);

    $("#maintenanceRecordWindow").data("kendoWindow").center().open();
}

function maintenanceRecordWindowOpened() {
    var maintenanceRecordGrid = $("#maintenanceRecordGrid").data("kendoGrid");
    maintenanceRecordGrid.dataSource.read();
}

function updatedMaintenanceRecordGrid(e) {
    var maintenanceRecordGrid = $("#maintenanceRecordGrid").data("kendoGrid");

    var mrData = maintenanceRecordGrid.dataSource.data();

    var rows = e.sender.tbody.children();

    for (var j = 0; j < rows.length; j++) {
        var row = $(rows[j]);
        var dataItem = e.sender.dataItem(row);

        if (dataItem.get("Fail") == true) {
            row.addClass("fail");
        }
    }
}

function userEquipmentCategorySelected(e) {
    if ($.DirtyForms.isDirty()) {
        var result = confirm("You have made changes to this page which are not saved. If you proceed now you will lose these changes.");

        if (!result) {
            e.preventDefault();
            var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
            var selectedEquipmentCategory = viewModel.get("selectedEquipmentCategory");
            var selectedEquipmentCategoryNode = equipmentCategoryTreeView.findByText(selectedEquipmentCategory.NewName);
            equipmentCategoryTreeView.select(selectedEquipmentCategoryNode);
        } else {
            $('form:dirty').dirtyForms('setClean');
        }
    }
}

function selectedEquipmentItem(e){
    var selectedEquipmentItemRow = this.select();

    if(selectedEquipmentItemRow){
        var selectedItem = this.dataItem(selectedEquipmentItemRow);

        if(selectedItem){
            viewModel.set("selectedEquipmentItemId", selectedItem.EquipmentItemId);
        } else{
            viewModel.set("selectedEquipmentItemId","");
        }
    } else{
        viewModel.set("selectedEquipmentItemId","");
    }
}

$("#moveAssetItemConfirm").click(function () {
    var assetEquipmentItemGrid = $("#assetEquipmentItemGrid").data("kendoGrid");

    var selectedEquipmentItemIds = [];

    assetEquipmentItemGrid.select().each(function () {
        var selectedEquipmentItem = assetEquipmentItemGrid.dataItem($(this));

        if (selectedEquipmentItem) {
            selectedEquipmentItemIds.push(selectedEquipmentItem.EquipmentItemId);
        }
    });

    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url:"/Assets/MoveAssetItem",
        data: {
            equipmentItemIds : selectedEquipmentItemIds,
            equipmentCategoryId : viewModel.get("selectedCategory")
        },
        success: function(){
            $("#equipmentCategory").val("")
            window.location.reload();
        },
    });
});


function selectedCategory(e){
    var assetItemTreeView = $("#assetItemTreeView").data("kendoTreeView");
    var node = assetItemTreeView.select();
    var selectedCategory = assetItemTreeView.dataItem(node);

    if (selectedCategory) {
        viewModel.set("selectedCategory", selectedCategory.EquipmentCategoryId);
    }else {
        viewModel.set("selectedCategory","")
    }
}

function moveAssetItemWindow(equipmentItemId) {
    viewModel.set("selectedEquipmentItemId", equipmentItemId);

    $("#moveAssetItemWindowOpen").data("kendoWindow").center().open();
}

function moveAssetItemWindowOpened() {
    var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
    equipmentCategoryTreeView.dataSource.read();
}
        
function equipmentItemEdit(e) {
    var equipmentItem = e.model;
    var equipmentCategory = viewModel.get("selectedEquipmentCategory");

    $(e.container).find(".k-edit-buttons").html("<a class='btn btn-primary btn-sm k-grid-update' href='#'>Update</a> " +
       "<a class='btn btn-primary btn-sm k-grid-cancel' href='#'>Cancel</a>");

    equipmentItem.set("PointsPerMonth", equipmentCategory.PointsPerMonth);
    equipmentItem.set("PointsPerRun", equipmentCategory.PointsPerRun);
    equipmentItem.set("PointsPerMove", equipmentCategory.PointsPerMove);
}

function equipmentItemData() {
    var equipmentCategory = viewModel.get("selectedEquipmentCategory");

    return {
        equipmentCategoryId: equipmentCategory ? equipmentCategory.EquipmentCategoryId : ""
    };
}

function refreshAllEquipmentItems() {
    var assetEquipmentItemGrid = $("#assetEquipmentItemGrid").data("kendoGrid");
    assetEquipmentItemGrid.dataSource.read();
}

function updateEquipmentTotals() {
    $("#resetAssetEquipmentItemGrid").click(function (e) {
        e.preventDefault();
        viewModel.set("selectedEquipmentItemId", "")
        $('#equipmentQuery').val('');
        resetGridView('assetEquipmentItemGrid', 'initialAssetEquipmentItemGridOptions')
        var assetEquipmentItemGrid = $("#assetEquipmentItemGrid").data("kendoGrid");
        assetEquipmentItemGrid.dataSource.read()
        kendo.bind($("#assetEquipmentItemGrid"), viewModel);
    });

    var assetEquipmentItemGrid = $("#assetEquipmentItemGrid").data("kendoGrid");
    var totalEquipmentItems = assetEquipmentItemGrid.dataSource.total();
    viewModel.set("totalEquipmentItems", totalEquipmentItems);

    var equipmentData = assetEquipmentItemGrid.dataSource.data();

    $.each(equipmentData, function (i, item) {
        if (item.MaintenanceScheduleDaysAlert) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(17)').css("color", "#E31E33 !important");
        }
        if (item.MaintenanceSchedulePointsAlert) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(18)').css("color", "#E31E33 !important");
        }
        if (item.Fail) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(21)').css("background-color", "#EA5C6B !important");
        }
    });

    // Update export button text when grid data changes - pass the actual count
    updateExportButtonText(totalEquipmentItems);
}

// Update export button text with the actual count
function updateExportButtonText(actualCount) {
    var exportBtn = document.getElementById('exportAllAssetsBtn');
    if (exportBtn) {
        var btnText = exportBtn.querySelector('#exportBtnText');
        if (btnText) {
            // Check if there are any filters applied to determine the text
            var grid = $("#assetEquipmentItemGrid").data("kendoGrid");
            var hasFilters = grid && grid.dataSource.filter() && grid.dataSource.filter().filters && grid.dataSource.filter().filters.length > 0;

            if (hasFilters) {
                btnText.textContent = `Export Filtered Assets (${actualCount} items)`;
            } else {
                btnText.textContent = 'Export All Assets';
            }
        }
    }
}

// Common button state management functions
function setButtonLoadingState(element, btnText, btnIcon, loadingText) {
    element.classList.add('disabled');
    element.style.pointerEvents = 'none';
    btnIcon.className = 'fas fa-spinner fa-spin';
    btnText.textContent = loadingText;
}

function createResetButtonFunction(element, btnText, btnIcon, originalText) {
    return function() {
        try {
            element.classList.remove('disabled');
            element.style.pointerEvents = 'auto';
            btnIcon.className = 'fas fa-download';
            btnText.textContent = originalText;
            localStorage.removeItem('activeExportJobId');
        } catch (resetError) {
            console.error('Error resetting button state:', resetError);
        }
    };
}

function refreshEquipmentCategories() {
    var equipmentCategory = $("#equipmentCategoryTreeView").data("kendoTreeView");
    equipmentCategory.dataSource.read();
}

function equipmentCategorySelected(e) {
    var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
    var node = equipmentCategoryTreeView.select();
    var originalEquipmentCategory = viewModel.get("selectedEquipmentCategory");
    var selectedEquipmentCategory = equipmentCategoryTreeView.dataItem(node);
    if (selectedEquipmentCategory) {
       viewModel.set("selectedAssetItemNameWithCount", selectedEquipmentCategory.NewName)
    }
    


    if (originalEquipmentCategory != selectedEquipmentCategory && selectedEquipmentCategory) {
        $.ajax({
            type: "GET",
            url: `/Admin/GetEquipmentCategory?equipmentCategorId=${selectedEquipmentCategory.EquipmentCategoryId}`,
            success: function (data) {
                $.removeCookie('equipmentCategory');
                $.cookie('equipmentCategory', selectedEquipmentCategory.EquipmentCategoryId , { expires: 7, path: '/' });
                viewModel.set("selectedEquipmentCategory", data);

                var grid = $("#assetEquipmentItemGrid").data("kendoGrid");
                grid.dataSource.options.endless = null;
                grid._endlessPageSize = grid.dataSource.options.pageSize;
                grid.dataSource.pageSize(grid.dataSource.options.pageSize);

                $("#equipmentCategoryMaintenanceStepGrid").data("kendoGrid").dataSource.read();
            },
            dataType: "json"
        });

    }
}

function equipmentCategoryLoaded(e) {
    $(".k-treeview-item").click(function (e) {
        var equipmentCategoryTree = $("#equipmentCategoryTreeView").data("kendoTreeView");
        var equipmentCategorySelected = equipmentCategoryTree.select();

        if (equipmentCategorySelected && $(e.currentTarget).attr("date-uid") == $(equipmentCategorySelected.context).attr("data-uid")) {
            equipmentCategoryTree.select($());
        }
    });
}

function equipmentCategoryDropped(e) {
    var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
    var equipmentCategory = equipmentCategoryTreeView.dataItem(e.sourceNode);

    var parentEquipmentCategory = "";

    if (e.dropPosition == "over") {
        parentEquipmentCategory = equipmentCategoryTreeView.dataItem(e.destinationNode);
    } else {
        parentEquipmentCategory = equipmentCategoryTreeView.dataItem(equipmentCategoryTreeView.parent(e.destinationNode));
    }

    $.ajax({
        type: "POST",
        url: "/Admin/UpdateEquipmentCategoryParent",
        data: {
            equipmentCategoryId: equipmentCategory.EquipmentCategoryId,
            parentEquipmentCategoryId: parentEquipmentCategory ? parentEquipmentCategory.EquipmentCategoryId : ""
        },
        success: function (data) {
            viewModel.set("selectedEquipmentCategory", equipmentCategory);
            console.log(JSON.stringify(data));
        },
        dataType: "json"
    });
}

var viewModel = new kendo.observable({
    units: assetsModel.units,
    kg: assetsModel.kg,
    selectedCategory: 0,
    selectedEquipmentCategory: "",
    selectedAssetItemNameWithCount: "All categories (0)",
    selectedEquipmentCategoryPath: [],
    totalEquipmentCategories: 0,
    totalEquipmentItems: 0,
    selectedEquipmentItemId: 0,
    selectedMaintenanceRecordId: 0,
    totalEquipmentCategoryMaintenanceSteps: 0,
    equipmentFilterOptions: assetsModel.equipmentFilterOptions,
    equipmentQuery:"",
    tabStripHeaderConstructorOfAssetWithNumber: function () {
        return `<span class="k-link"> Equipment (<span data-bind="text:totalEquipmentItems"></span>)</span>`;   
    },
    canEditEquipmentItems: function () {
        var selectedEquipmentCategory = this.get("selectedEquipmentCategory");
        return selectedEquipmentCategory && !selectedEquipmentCategory.IsTopLevelOnly && selectedEquipmentCategory.EquipmentCategoryId;
    },
    addEquipmentCategory: function () {
        var parentEquipmentCategory = this.get("selectedEquipmentCategory");

        this.set("selectedEquipmentCategory", {
            ParentEquipmentCategoryId: parentEquipmentCategory ? parentEquipmentCategory.EquipmentCategoryId : "",
            Name: "",
            DivisionId: "",
            Height: parentEquipmentCategory.Height,
            HeightUnit: parentEquipmentCategory.HeightUnit,
            Width: parentEquipmentCategory.Width,
            WidthUnit: parentEquipmentCategory.WidthUnit,
            Depth: parentEquipmentCategory.Depth,
            DepthUnit: parentEquipmentCategory.DepthUnit,
            Weight: parentEquipmentCategory.Weight,
            WeightUnit: parentEquipmentCategory.WeightUnit,
            OuterDiameter: parentEquipmentCategory.OuterDiameter,
            OuterDiameterUnit: parentEquipmentCategory.OuterDiameterUnit,
            PointsPerMonth: parentEquipmentCategory.PointsPerMonth,
            PointsPerMove: parentEquipmentCategory.PointsPerMove,
            PointsPerRun: parentEquipmentCategory.PointsPerRun,
            IsDangerousGoods: parentEquipmentCategory.IsDangerousGoods,
            IsTopLevelOnly: parentEquipmentCategory.IsTopLevelOnly,
        });
    },
    newEquipmentVisible: function (){
        var equipmentCategory = this.get("selectedEquipmentCategory");

        return !equipmentCategory.EquipmentCategoryId;
    },
    canDelete: function () {
        var equipmentCategory = this.get("selectedEquipmentCategory");
        return equipmentCategory && !equipmentCategory.HasChildren && equipmentCategory.EquipmentCategoryId;
    },
    deleteItem: function () {
        var equipmentCategory = this.get("selectedEquipmentCategory");

        if (equipmentCategory) {
            var confirmDelete = confirm("Are you sure you wish to delete Equipment Category '" + equipmentCategory.Name + "'?");

            if (confirmDelete) {
                $.ajax({
                    type: "POST",
                    url: "/Admin/DeleteEquipmentCategory",
                    data: {
                        equipmentCategoryId: equipmentCategory.EquipmentCategoryId,
                    },
                    success: function () {
                        refreshEquipmentCategories();
                    },
                    dataType: "json"
                });
            }
        }
    },

    expandAll: function () {
        var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
        equipmentCategoryTreeView.expand(".k-treeview-item");
    },
    collapseAll: function () {
        var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
        equipmentCategoryTreeView.collapse(".k-treeview-item");
    },
    expandAllItems: function () {
        var assetItemTreeView = $("#assetItemTreeView").data("kendoTreeView");
        assetItemTreeView.expand(".k-treeview-item");
    },
    collapseAllItems: function () {
        var assetItemTreeView = $("#assetItemTreeView").data("kendoTreeView");
        assetItemTreeView.collapse(".k-treeview-item");
    },

    moveAssetItemWindow: function () {
        $("#moveAssetItemWindowOpen").data("kendoWindow").center().open();
    },

    editItemWindow: function () {
        $("#editItemWindowOpen").data("kendoWindow").center().open();
    },

});

$("#equipmentQuery").keyup(function () {
    viewModel.set("equipmentQuery", $("#equipmentQuery").val());
    equipmentGridFilter();
});

function closeEquipmentGridFilter() {
    equipmentGridFilter();
}
function deselectEquipmentGridFilter(e) {
    if (!$("#equipmentStatusFilter").data("kendoMultiSelect").popup.visible()) {
        equipmentGridFilter(viewModel.get("equipmentFilterOptions").filter(x => x.Key !== e.dataItem.Key));
    }
}
function equipmentGridFilter(equipmentFilterStatuses){
    var assetEquipmentItemGrid = $("#assetEquipmentItemGrid").data("kendoGrid");
    var equipmentFilterOptions = equipmentFilterStatuses ? equipmentFilterStatuses : viewModel.get("equipmentFilterOptions");
    var equipmentQuery = viewModel.get("equipmentQuery");

    var filters = [];

    if (equipmentFilterOptions != null && equipmentFilterOptions.length > 0) {
        var statusFilters = [];

        for(var i=0;i<equipmentFilterOptions.length;i++){
            statusFilters.push(getEquipmentFilter(equipmentFilterOptions[i].Key));
        }

        var statusFilter = {
            logic: "or",
            filters:statusFilters
        };

        filters.push(statusFilter);
    }
    if(equipmentQuery){
        var queryFilter = {
            logic: "or",
            filters:[
                { field: "EquipmentNumber", operator: "contains", value: equipmentQuery },
                { field: "EquipmentCategoryName", operator: "contains", value: equipmentQuery }]
        };

        filters.push(queryFilter);
    }

    var filter = {
        logic: "and",
        filters:filters
    };

    assetEquipmentItemGrid.dataSource.filter(filter);

    $.removeCookie('equipmentFilterOptions');
    $.cookie('equipmentFilterOptions', JSON.stringify(equipmentFilterOptions), { expires: 7, path: '/' });
}

function getEquipmentFilter(option){
    var statuses = assetsModel.statuses;
    if(statuses.indexOf(option)>-1){
        return { field: "Status", operator: "contains", value: option };
    } else if(option == "In Transit"){
        return { field: "IsTransit", operator: "eq", value: true };
    } else if(option == "Reserved"){
        return { field: "IsReserved", operator: "eq", value: true };
    } else if(option == "Selected"){
        return { field: "IsSelected", operator: "eq", value: true };
    }else if(option=="Bundle OK"){
        return {logic:"and", filters:[{ field: "IsOkBundle", operator: "eq", value: true }, {field:"HasBundle", operator:"eq",value:true}]};
    } else if(option=="Bundle Check"){
        return {logic:"and", filters:[{ field: "IsOkBundle", operator: "eq", value: false }, {field:"HasBundle", operator:"eq",value:true}]};
    } else if(option == "Bundled Item"){
        return { field: "IsPartOfBundle", operator: "eq", value: true};
    }
}


$("#filterText").keyup(function (e) {

    let treeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
    let filterText = e.target.value;

    if (!filterText) {
        treeView.collapse(".k-treeview-item");
    } else {
        treeView.expand(".k-treeview-item");
        treeView.dataSource.filter(
            { field: "NewName", operator: "contains", value: filterText }
        )
    }
});

function categoriesLoaded() {
    var equipmentCategory = $.cookie('equipmentCategory');

    if (equipmentCategory) {
        var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
        var equipmentCategoryNode = equipmentCategoryTreeView.findByUid(equipmentCategory);

        if (equipmentCategoryNode.length > 0) {
            equipmentCategoryTreeView.select(equipmentCategoryNode);
            equipmentCategoryTreeView.trigger("select", { node: equipmentCategoryNode });
        }
    }
}

// Export functionality
function showExportLoading(element) {
    try {
        var btnText = element.querySelector('#exportBtnText');
        var btnIcon = element.querySelector('i');

        if (!btnText || !btnIcon) {
            console.error('Export button elements not found');
            return true;
        }

        // Check if there's already an active export job
        var activeJobId = localStorage.getItem('activeExportJobId');
        if (activeJobId) {
            resumeExportTracking(activeJobId, btnText, btnIcon, element);
            return false;
        }

        // Check if grid has filters applied
        var grid = $("#assetEquipmentItemGrid").data("kendoGrid");
        var hasFilters = grid && grid.dataSource.filter() && grid.dataSource.filter().filters && grid.dataSource.filter().filters.length > 0;

        // Get the current button text as the original text
        var originalText = btnText.textContent;

        // Set loading state and create reset function
        setButtonLoadingState(element, btnText, btnIcon, 'Starting export...');
        var resetButton = createResetButtonFunction(element, btnText, btnIcon, originalText);

        // Start the export job
        startExportJob(btnText, btnIcon, resetButton, hasFilters, grid);

        return false;
    } catch (error) {
        console.error('Error in showExportLoading:', error);
        return true;
    }
}

function startExportJob(btnText, btnIcon, resetButton, hasFilters, grid) {
    var url = '/Assets/ExportAllAssets';
    var requestData = {};

    // If filters are applied, get the filtered equipment item IDs
    if (hasFilters && grid) {
        var filteredData = grid.dataSource.view();
        var equipmentItemIds = filteredData.map(function(item) {
            return item.EquipmentItemId;
        });

        requestData = {
            equipmentItemIds: equipmentItemIds,
            isFiltered: true
        };
    }

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.jobId) {
            localStorage.setItem('activeExportJobId', data.jobId);
            pollExportJobStatus(data.jobId, btnText, btnIcon, resetButton);
        } else {
            console.error('No job ID received');
            resetButton();
        }
    })
    .catch(error => {
        console.error('Error starting export job:', error);
        resetButton();
    });
}

function pollExportJobStatus(jobId, btnText, btnIcon, resetButton) {
    var pollInterval = setInterval(function() {
        fetch('/Assets/GetExportJobStatus?jobId=' + jobId)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    console.error('Export job error:', data.error);
                    clearInterval(pollInterval);
                    resetButton();
                    return;
                }

                if (data.totalItems > 0) {
                    btnText.textContent = `Exporting... ${data.processedItems}/${data.totalItems} (${data.progressPercentage}%)`;
                }

                if (data.status === 'completed') {
                    clearInterval(pollInterval);
                    localStorage.removeItem('activeExportJobId');
                    btnIcon.className = 'fas fa-check';
                    btnText.textContent = 'Download Ready';
                    if (data.downloadUrl) {
                        window.location.href = data.downloadUrl;
                    }
                    setTimeout(resetButton, 3000);
                } else if (data.status === 'failed') {
                    clearInterval(pollInterval);
                    localStorage.removeItem('activeExportJobId');
                    btnIcon.className = 'fas fa-exclamation-triangle';
                    btnText.textContent = 'Export Failed';
                    console.error('Export failed:', data.errorMessage);
                    setTimeout(resetButton, 5000);
                }
            })
            .catch(error => {
                console.error('Error polling export status:', error);
                clearInterval(pollInterval);
                resetButton();
            });
    }, 2000);

    setTimeout(function() {
        clearInterval(pollInterval);
        resetButton();
    }, 600000);
}

function resumeExportTracking(jobId, btnText, btnIcon, element) {
    element.classList.add('disabled');
    element.style.pointerEvents = 'none';
    btnIcon.className = 'fas fa-spinner fa-spin';
    btnText.textContent = 'Resuming export...';

    function resetButton() {
        try {
            element.classList.remove('disabled');
            element.style.pointerEvents = 'auto';
            btnIcon.className = 'fas fa-download';
            btnText.textContent = 'Export All Assets';
            localStorage.removeItem('activeExportJobId');
        } catch (resetError) {
            console.error('Error resetting button state:', resetError);
        }
    }

    pollExportJobStatus(jobId, btnText, btnIcon, resetButton);
}



// Check for active export job on page load
$(document).ready(function() {
    var activeJobId = localStorage.getItem('activeExportJobId');
    if (activeJobId) {
        var exportBtn = document.getElementById('exportAllAssetsBtn');
        if (exportBtn) {
            var btnText = exportBtn.querySelector('#exportBtnText');
            var btnIcon = exportBtn.querySelector('i');
            if (btnText && btnIcon) {
                resumeExportTracking(activeJobId, btnText, btnIcon, exportBtn);
            }
        }
    }

    // Note: Export button text is now updated directly from updateEquipmentTotals()
    // with the actual count, making event-based detection unnecessary
});

kendo.bind(document.body.children, viewModel);
