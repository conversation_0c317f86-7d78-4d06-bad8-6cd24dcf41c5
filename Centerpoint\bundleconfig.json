[{"outputFileName": "wwwroot/js/views/admin/viewCustomerAssets.min.js", "inputFiles": ["wwwroot/js/views/admin/viewCustomerAssets.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/assets/assets.min.js", "inputFiles": ["wwwroot/js/views/assets/assets.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/admin/archivedAssets.min.js", "inputFiles": ["wwwroot/js/views/admin/archivedAssets.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/assets/assetsAdmin.min.js", "inputFiles": ["wwwroot/js/views/assets/assetsAdmin.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/assets/editAssets.min.js", "inputFiles": ["wwwroot/js/views/assets/editAssets.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/companies/companies.min.js", "inputFiles": ["wwwroot/js/views/companies/companies.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/companies/viewCompany.min.js", "inputFiles": ["wwwroot/js/views/companies/viewCompany.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/companies/viewCompanyWell.min.js", "inputFiles": ["wwwroot/js/views/companies/viewCompanyWell.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/exchangeRate/exchangeRate.min.js", "inputFiles": ["wwwroot/js/views/exchangeRate/exchangeRate.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/home/<USER>", "inputFiles": ["wwwroot/js/views/home/<USER>"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/layout/layout.min.js", "inputFiles": ["wwwroot/js/views/layout/layout.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/logistics/editEquipmentShipment.min.js", "inputFiles": ["wwwroot/js/views/logistics/editEquipmentShipment.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/logistics/logistics.min.js", "inputFiles": ["wwwroot/js/views/logistics/logistics.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/maintenance/editMaintenanceBlueprintAdmin.min.js", "inputFiles": ["wwwroot/js/views/maintenance/editMaintenanceBlueprintAdmin.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/maintenance/editMaintenanceBlueprintStep.min.js", "inputFiles": ["wwwroot/js/views/maintenance/editMaintenanceBlueprintStep.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/maintenance/editMaintenanceRecord.min.js", "inputFiles": ["wwwroot/js/views/maintenance/editMaintenanceRecord.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/maintenance/EditMaintenanceRecordStep.min.js", "inputFiles": ["wwwroot/js/views/maintenance/EditMaintenanceRecordStep.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/maintenance/maintenance.min.js", "inputFiles": ["wwwroot/js/views/maintenance/maintenance.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/maintenance/maintenanceBlueprintsAdmin.min.js", "inputFiles": ["wwwroot/js/views/maintenance/maintenanceBlueprintsAdmin.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/maintenance/maintenanceHistory.min.js", "inputFiles": ["wwwroot/js/views/maintenance/maintenanceHistory.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/maintenance/upcomingMaintenance.min.js", "inputFiles": ["wwwroot/js/views/maintenance/upcomingMaintenance.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/operation/editProject.min.js", "inputFiles": ["wwwroot/js/views/operation/editProject.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/operation/editRun.min.js", "inputFiles": ["wwwroot/js/views/operation/editRun.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/operation/employeeOnSite.min.js", "inputFiles": ["wwwroot/js/views/operation/employeeOnSite.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/operation/operation.min.js", "inputFiles": ["wwwroot/js/views/operation/operation.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/personnel/employees.min.js", "inputFiles": ["wwwroot/js/views/personnel/employees.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/qhse/editLesson.min.js", "inputFiles": ["wwwroot/js/views/qhse/editLesson.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/qhse/editRiskIdentificationSafetyControl.min.js", "inputFiles": ["wwwroot/js/views/qhse/editRiskIdentificationSafetyControl.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/qhse/lesson.min.js", "inputFiles": ["wwwroot/js/views/qhse/lesson.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/qhse/qhse.min.js", "inputFiles": ["wwwroot/js/views/qhse/qhse.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/qhse/riskIdentificationSafetyControl.min.js", "inputFiles": ["wwwroot/js/views/qhse/riskIdentificationSafetyControl.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/qhse/serviceImprovementScripts.min.js", "inputFiles": ["wwwroot/js/views/qhse/serviceImprovementScripts.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/sales/edit.min.js", "inputFiles": ["wwwroot/js/views/sales/edit.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/sales/editAction.min.js", "inputFiles": ["wwwroot/js/views/sales/editAction.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/sales/editEvent.min.js", "inputFiles": ["wwwroot/js/views/sales/editEvent.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/sales/matrix.min.js", "inputFiles": ["wwwroot/js/views/sales/matrix.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/sales/sales.min.js", "inputFiles": ["wwwroot/js/views/sales/sales.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/sales/salesForecast.min.js", "inputFiles": ["wwwroot/js/views/sales/salesForecast.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/sales/salesHistory.min.js", "inputFiles": ["wwwroot/js/views/sales/salesHistory.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}, {"outputFileName": "wwwroot/js/views/statistics/statistics.min.js", "inputFiles": ["wwwroot/js/views/statistics/statistics.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}]