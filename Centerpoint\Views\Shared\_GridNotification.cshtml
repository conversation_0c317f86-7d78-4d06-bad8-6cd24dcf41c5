﻿@model EntityType

@using Centerpoint.Common.Enums

@{
    var messages = NotificationConstants.Values[Model];
    string create = messages.ContainsKey(ActionNotification.Create) ? messages[ActionNotification.Create] : "";
    string update = messages.ContainsKey(ActionNotification.Update) ? messages[ActionNotification.Update] : "";
    string delete = messages.ContainsKey(ActionNotification.Delete) ? messages[ActionNotification.Delete] : ""; 
    string loading = messages.ContainsKey(ActionNotification.Loading) ? messages[ActionNotification.Loading] : "";
}

<script>

    function onRequestEnd(e) {
          if (e.response && e.type){
              const updatedItemsName = e.response.Data[0].Name;
              if(e.type === "update") {
                  updateMessage(updatedItemsName);
              }
              else if(e.type === "create") {
                  createMessage(updatedItemsName);
              }
              else if(e.type === "destroy") {
                  destroyMessage(updatedItemsName);
              }
              else if(e.type === "loading") {
                  loadingMessage(updatedItemsName);
              }
          }
      }

      function updateMessage(updatedItemsName){
          const tempName = '@string.Format(@update, "_updatedItemsName")';
          handleSuccessMessages(tempName, updatedItemsName)
      }

      function createMessage(updatedItemsName){
          const tempName = '@string.Format(@create, "_updatedItemsName")';
          handleSuccessMessages(tempName, updatedItemsName)
      }

      function destroyMessage(updatedItemsName){
          const tempName = '@string.Format(@delete, "_updatedItemsName")';
          handleSuccessMessages(tempName, updatedItemsName)
      }

      function loadingMessage(updatedItemsName){
        const tempName = '@string.Format(@loading, "_updatedItemsName")';
          handleSuccessMessages(tempName, updatedItemsName)
      }

      function submitSuccessHandler(name){
            let params = new URLSearchParams(window.location.search);
            let isCreated = params.get('isCreated');
            if (isCreated != undefined || isCreated != null) {
                if (isCreated.toLowerCase() == 'true') {
                    createMessage(name);
                }
                else if (isCreated.toLowerCase() == 'false') {
                    updateMessage(name);
                }
                window.history.replaceState({}, '', window.location.pathname);
            }
        }

        function onErrorDeleteGridHandler(response, name){
         if(response.xhr != undefined && response.xhr.status == 400){
                var grid = $(name).data("kendoGrid");
                grid.dataSource.cancelChanges();
            }
        }

</script>