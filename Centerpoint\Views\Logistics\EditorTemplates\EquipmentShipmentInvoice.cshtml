﻿@model EquipmentShipmentInvoiceModel

<div id="equipmentShipmentInvoiceForm">
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label>Paperwork Type</label>
                <br />
                    @(Html.Kendo().DropDownListFor(p => p.PaperworkType)
                    .DataValueField("Key")
                    .DataTextField("Value")
                    .Events(e => e.Change("paperworkTypeChange"))
                    .Filter("contains")
                    .OptionLabel("Select Type")
                    .HtmlAttributes(new { @style = "font-size: 14px;" })
                    .BindTo(Centerpoint.Common.Constants.EquipmentShipmentPaperworkConstant.ValuesAndDescriptions.ToList()))
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label>Header From</label>
                <br />
                    @(Html.Kendo()
                    .DropDownListFor(m => m.CompanyLocationId)
                    .DataValueField("CompanyLocationId")
                    .DataTextField("NameCompanyName")
                    .Filter("contains")
                    .OptionLabel("Select Location")
                    .HtmlAttributes(new { @style = "font-size: 14px;", @data_value_primitive = "true" })
                    .DataSource(dataSource => dataSource.Read("GetInternalCompanyLocations", "Lookup")))
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <label id="currencyLabel">Currency</label>
            <br />
            @(Html.Kendo()
            .DropDownListFor(m => m.CurrencyId)
            .DataValueField("Value")
            .DataTextField("Text")
            .Filter("contains")
            .OptionLabel("Select Currency")
            .HtmlAttributes(new { @style = "font-size: 14px;", @data_value_primitive = "true", @id = "currency" })
            .DataSource(dataSource => dataSource.Read("GetCurrencies", "Lookup")))
        </div>
       <div class="col-md-6">
            <label id="valueLabel">Value</label>
            <br />
            @(Html.Kendo()
             .DropDownListFor(m => m.ValueType)
             .DataValueField("Key")
             .DataTextField("Value")
             .Filter("contains")
             .OptionLabel("Select Value")
             .HtmlAttributes(new { @style = "font-size: 14px;", @id = "valueType" })
             .BindTo(Centerpoint.Common.Constants.EquipmentShipmentValueConstant.ValuesAndDescriptions.ToList()))
        </div>
    </div>
    <br />
    <div class="row">
        @if (GlobalSettings.IsWellsense)
        {
            <div class="col-md-6">
                <div class="form-group">
                    <label id="commodityLabel">Commodity Code</label>
                    <br />
                    @(Html.Kendo().DropDownListFor(p => p.CommodityCodeOption)
                        .DataValueField("Key")
                        .DataTextField("Value")
                        .Events(e => e.Change("paperworkTypeChange"))
                        .Filter("contains")
                        .OptionLabel("Select Type")
                        .HtmlAttributes(new { @style = "font-size: 14px;",  @id = "commodityCode" })
                        .BindTo(Centerpoint.Common.Constants.CommodityCodeConstant.ValuesAndDescriptions.ToList()))
                </div>
            </div> 
        }
       
        <div class="col-md-5">
            <div class="form-group">
                <label id="weightLabel" for="netWeight">Net Weight</label>
                <input type="checkbox" id="netWeight" />
            </div>
        </div>
    </div>
     @if (GlobalSettings.IsWellsense)
     {
    <br />
    <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label id="newUsedLabel" for="netWeight">New/Used</label>
                    <input type="checkbox" id="IsNewUsed" />
                </div>
            </div>
        <div class="col-md-5">
            <div class="form-group">
                    <label id="tempPermanentLabel" for="tempPermanent">Temporary/Permanent</label>
                    <input type="checkbox" id="tempPermanent" name="IsTemporaryPermanent" />
            </div>
        </div>
    </div>
    }
</div>
