﻿@model CompanyModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-tags"></i>
        Customer Assets
        (<span data-bind="text:totalCustomerAssetsRecords"></span>)
    </h4>
</div>
<hr />

<div class="grid-container">
    @(Html.Kendo().Grid<CompanyModel>()
        .Name("customerAssetsGrid")
        .Columns(columns => {
            columns.Bound(c => c.Name).ClientTemplate("<a class='text-primary' href='" + @Url.Action("ViewCustomerAssets", "Admin", new { @id = "" }) + "/#=CompanyId#'>#=Name#</a>").Width(200); ;
            columns.Bound(c => c.Categories).Title("Category").Width(200);
            columns.Bound(c => c.IsActive).Title("Active").ClientTemplate("#if(IsActive){#Yes#}else{#No#}#").Width(80);
            columns.Bound(c => c.CompanyLocationCount).Title("Locations").Width(80);
            columns.Bound(c => c.CompanyContactCount).Title("Contacts").Width(80);
            columns.Bound(c => c.CompanyFieldsCount).Title("Fields").Width(80);
            columns.Bound(c => c.CompanyWellsCount).Title("Wells").Width(80);
        })
        .ColumnMenu(c => c.Columns(true))
        .Filterable()
        .Sortable()
        .Groupable()
        .Events(e => e.DataBound("updateCustomerAssetsRecordGrid"))
        .Reorderable(c => c.Columns(true))
        .Scrollable(s => s.Height("auto"))
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(model => {
                model.Id(m => m.CompanyId);
            })
            .Read(read => read.Action("GetCompaniesByCustomerAssets", "Admin"))
        )
    )
</div>



<script>

    function updateCustomerAssetsRecordGrid() {
        var customerAssetsGrid = $("#customerAssetsGrid").data("kendoGrid");
        var totalCustomerAssetsRecords = customerAssetsGrid.dataSource.total();
        viewModel.set("totalCustomerAssetsRecords", totalCustomerAssetsRecords);
    }

    
    var viewModel = kendo.observable({
        totalCustomerAssetsRecords: 0
    })

    kendo.bind(document.body.children, viewModel);
</script>
