{
  "ConnectionStrings": {
    //"DefaultConnection": "Data Source=(local);Initial Catalog=centerpoint1;User ID=sa;password=*********;MultipleActiveResultSets=true",
    "DefaultConnection": "Data Source=insiso-dev;Initial Catalog=REGIISV2_DEV;User ID=sa;password=*********;MultipleActiveResultSets=true"
    //"DefaultConnection": "Data Source=insiso-dev;Initial Catalog=REGIISV2_TEST;User ID=sa;password=*********;MultipleActiveResultSets=true"

  },
  "BlobStorage": {
    "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=centerpointstorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    "Container": "development"
  },
  "EmailSettings": {
    "Username": "<EMAIL>",
    "Password": "In$1so_C3nt3r#P0int24!",
    "SmtpHost": "smtp.office365.com",
    "SmtpPort": "587",
    "From": "<EMAIL>",
    "UseSSL": true
  },
  "Settings": {
    "webpages:Version": "*******",
    "webpages:Enabled": false,
    "ClientValidationEnabled": true,
    "UnobtrusiveJavaScriptEnabled": true,
    "EventTime": 30,
    "LoginAttempts": 5,
    "DownloadsFolderName": "Downloads",
    "DepreciationTotalYears": 8
  },
  "TwilioSMS": {
    "TWILIO_ACCOUNT_SID": "**********************************",
    "TWILIO_AUTH_TOKEN": "398a000c97d4b7829326b085bb333828",
    "TWILIO_FROM_PHONE": "+************"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "System": "Warning",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "Hangfire": "Error"
    },
    "ApplicationInsights": {
      "LogLevel": {
        "Default": "Information",
        "System": "Warning",
        "Microsoft": "Warning",
        "Microsoft.Hosting.Lifetime": "Warning",
        "Azure.Messaging.ServiceBus": "Warning",
        "Hangfire": "Error"
      }
    }
  },
  "AzureAdSettings": {
    "Enabled": false,
    "ClientId": "",
    "TenantId": "",
    "Authority": "https://login.microsoftonline.com/{0}/"
  },
  "AppUrl": "https://dev.centerpoint.pro"
}
