﻿@model MaintenanceBlueprintModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-file-image"></i>
        @(Model.MaintenanceBlueprintId.HasValue ? Model.Name : "Add Maintenance Blueprint")
    </h4>
</div>
<hr />

@using (Html.BeginForm("EditMaintenanceBlueprint", "Admin")) {
    @Html.ValidationSummary(false)
    @(Html.Kendo().TabStrip()
    .Name("adminEquipmentTabStrip")
    .SelectedIndex(0)
    .Animation(false)
    .Items( tabstrip => {

        tabstrip.Add().Text("")
            .HtmlAttributes(new { @data_bind="html: detailsTextWithIcon"})
            .Selected(true)
            .Content(@<text>
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Name</label>
                            @(Html.<PERSON>().TextBoxFor(m => m.Name).HtmlAttributes(new { @class = "form-control" }))
                        </div>
                        <div class="form-group">
                            <label>Division</label>
                            @(Html.Kendo().DropDownListFor(m => m.DivisionId)
                                .Filter("contains")
                                .OptionLabel("Select Division")
                                .DataTextField("Text")
                                .DataValueField("Value")
                                .Events(c => c.Change("divisionChange"))
                                .DataSource(d => d.Read("GetDivisions", "Lookup"))
                                .HtmlAttributes(new { @tabindex = "3" })
                            )
                        </div>
                        <div class="form-group">
                            <label>Expected Time To Complete (hours)</label>
                            @(Html.Kendo().NumericTextBoxFor(m => m.ExpectedTimeToComplete).Spinners(false).Format("n1"))
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Complexity</label>
                            @(Html.Kendo()
                                .DropDownListFor(c => c.Complexity)
                                .Filter("contains")
                                .DataValueField("Key")
                                .DataTextField("Value")
                                .OptionLabel("Select Complexity")
                                .BindTo(Centerpoint.Common.Constants.MaintenanceComplexityConstant.ValuesAndDescriptions.ToList())
                            )
                        </div>
                        <div class="form-group">
                            <label>Related Equipment</label>
                                @(Html.Kendo().MultiSelectFor(m => m.EquipmentCategoryIds)
                                    .DataTextField("Text")
                                    .DataValueField("Value")
                                    .Filter(FilterType.Contains)
                                    .Placeholder("Select Equipment(s)")
                                    .HtmlAttributes(new { @data_value_primitive = "true"@*, @data_bind = "value:equipmentCategoryIds"*@})
                                    .DataSource(source => {
                                        source.Read(read => {
                                            read.Action("GetEquipmentCategoryItemsByDivisionId", "Lookup").Data("divisionData");
                                        })
                                        .ServerFiltering(true);
                                    })
                                )
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Approval Required</label>
                            @(Html.Kendo().DropDownListFor(m => m.ApprovalRequired)
                                .Filter("contains")
                                .DataValueField("Key")
                                .DataTextField("Value")
                                .BindTo(new List<KeyValuePair<string, string>> { new KeyValuePair<string, string>(Boolean.FalseString, "No"), new KeyValuePair<string, string>(Boolean.TrueString, "Yes"), })
                            )
                        </div>
                        <div class="form-group">
                            <label>Reset Maintenance Points</label>
                            @(Html.Kendo().DropDownListFor(m => m.ResetMaintenancePoints)
                                .Filter("contains")
                                .DataValueField("Key")
                                .DataTextField("Value")
                                .BindTo(new List<KeyValuePair<string, string>> { new KeyValuePair<string, string>(Boolean.FalseString, "No"), new KeyValuePair<string, string>(Boolean.TrueString, "Yes"), })
                            )
                        </div>
                    </div>
                </div>
                
                <div class="d-flex actionsContainer">
                    @if (Model.MaintenanceBlueprintId.HasValue && (Html.IsGlobalAdmin() || Html.IsMaintenanceAdmin())) {
                        <a href="@Url.Action("AddMaintenanceBlueprintStep", "Admin", new { @id = Model.MaintenanceBlueprintId } )" class="btn btn-success btn-sm " data-bind="invisible:totalMaintenanceSteps">Add Step</a>
                    }
                    @if (Html.IsGlobalAdmin() || Html.IsMaintenanceAdmin()) {
                        <button type="submit" class="btn btn-primary btn-sm">Save Maintenance Blueprint Details</button>
                        if (Model.MaintenanceBlueprintId.HasValue) {
                            <a class="btn btn-danger btn-sm " data-bind="click:deleteMaintenanceBlueprint">
                                <i class="fa fa-ban"></i>
                                Delete Maintenance Blueprint
                            </a>
                        }
                        <a class="btn btn-info btn-sm" href="@Url.Action("MaintenanceBlueprints", "Admin", new { @id = Model.MaintenanceBlueprintId })">
                            <i class="fa fa-refresh"></i>
                            Return to Blueprint Home
                        </a>
                    }
                    @if (ViewBag != null && ViewBag.MaintenanceRecordId != null) {
                        <a class="btn btn-info btn-sm " href="@Url.Action("EditMaintenanceRecord", "Maintenance", new { @id=ViewBag.MaintenanceRecordId})">
                            <i class="fa fa-refresh"></i>
                            Return to MR
                        </a>
                    }
                </div>

                <hr />

                @if (Model.HasMaintenanceSteps) {
                    <h5 class="text-primary mb-4" >Maintenance Blueprint Steps (<span data-bind="text:totalMaintenanceSteps"></span>)</h5>
                    <ul id="maintenanceStepPanelBar" data-role="panelbar"></ul>
                }
            </text>);

        if (Model.MaintenanceBlueprintId.HasValue) {
            tabstrip.Add().Text("")
            .HtmlAttributes(new {@data_bind="html: totalMaintenanceBlueprintDocumentsText"})
                .Content(@<text>
                    @if (Html.IsGlobalAdmin() || Html.IsMaintenanceAdmin()) {
                        @(Html.Kendo().Upload()
                            .Name("maintenanceBlueprintAttachmentDocuments")
                            .Messages(m => m.Select("Attach Maintenance Blueprint Documents"))
                            .Multiple(true)
                            .Events(e => e.Success("onMaintenanceBlueprintDocumentAttached").Complete("onMaintenanceBlueprintDocumentComplete").Upload("onMaintenanceBlueprintDocumentUpload"))
                            .HtmlAttributes(new { @style = "width:300px" })
                            .Async(async => async.Save("AttachMaintenanceBlueprintDocuments", "Admin", new { @mId = Model.MaintenanceBlueprintId }).Batch(true))
                        )
                    }
                    @(Html.Kendo().Grid<DocumentModel>()
                        .Name("maintenanceBlueprintDocumentsGrid")
                        .Columns(c => {
                            c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                            c.Bound(p => p.Created).Format(DateConstants.DateTimeFormat).Title("Created");
                            c.Bound(p => p.UserName).Title("Created By");
                            c.Command(command => { 
                                command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
                            }).Width(150);
                        })
                        .Events(e => e.DataBound("updateMaintenanceBlueprintDocumentGrid"))
                        .Sortable()
                        .Resizable(r => r.Columns(true))
                        .Filterable()
                        .Groupable()
                        .Editable(e => e.Mode(GridEditMode.InLine))
                        .Scrollable(s => s.Height(300))
                        .DataSource(dataSource => dataSource
                            .Ajax()
                            .ServerOperation(false)
                            .Model(model => model.Id(p => p.DocumentId))
                            .Read(read => read.Action("GetMaintenanceBlueprintDocuments", "Admin", new { @maintenanceBlueprintId = Model.MaintenanceBlueprintId }))
                            .Destroy(destroy => destroy.Action("DeleteMaintenanceBlueprintDocument", "Admin", new { @maintenanceBlueprintId = Model.MaintenanceBlueprintId }))
                        )
                    )
                </text>);
        }

        tabstrip.Add().Text("")
            .HtmlAttributes(new { @data_bind = "html: totalMaintenanceBlueprintLogsText" })
            .Content(@<text>
                <div class="tab-pane">
                    <br />
                    <p>The following table is a log of all changes made to this maintenance blueprint.</p>
                    <hr />
                    @(Html.Kendo().Grid<MaintenanceBlueprintLogModel>()
                    .Name("maintenanceBlueprintLogGrid")
                    .Columns(columns => {
                        columns.Bound(c => c.TypeDescription).Title("Type").Width(125);
                        columns.Bound(c => c.Reference).ClientTemplate("#if(ReferenceId){# <a href='" + Url.Action("EditMaintenanceBlueprintStep", "Admin") + "/#=ReferenceId#' title='View #=TypeDescription#'>#=Reference#</a> #} else if(Reference){# #=Reference# #}#").Width(125);
                        columns.Bound(c => c.Log).ClientTemplate("#=Log#");
                        columns.Bound(c => c.UserName).ClientTemplate("#=UserName ? UserName : 'System'#").Width(125);
                        columns.Bound(c => c.Date).Format(DateConstants.DateTimeFormat).Title("Date").Width(150);
                    })
                    .Events(e => e.DataBound("updateMaintenanceBlueprintTotalLogs"))
                    .Sortable()
                    .Groupable()
                    .Reorderable(reorder => reorder.Columns(true))
                    .Resizable(resize => resize.Columns(true))
                    .Scrollable(s => s.Height(400))
                    .DataSource(dataSource => dataSource
                    .Ajax()
                    .ServerOperation(false)
                    .Read(read => read.Action("GetMaintenanceBlueprintLogByMaintenanceBlueprintId", "Admin").Data("maintenanceBlueprintData"))))
                </div>
            </text>);
    }))
    @Html.HiddenFor(m => m.MaintenanceBlueprintId)
}

 
<script type="text/x-kendo-tmpl" id="maintenanceStepTemplate">
    <div style="margin:20px">
        #if(IsTextBox){#
        <div class="row">
            <div class="col-md-11">
                <p>#=Description#</p>
            </div>
            <div class="col-md-12">
                <label style="color:\\#6eb6b4">Result</label>
                @(Html.TextArea("Results", new { @class = "form-control", @style = "width:100%", @rows = "3", @disabled = "disabled" }))
                
            </div>
            <div class="col-md-12">
                <label style="color:\\#6eb6b4">Note</label>
                @(Html.TextArea("Notes", new { @class = "form-control", @style = "width:100%", @rows = "3", @disabled = "disabled" }))
                
            </div>
        </div>
        #}
        else if(IsCheckbox){#
        <div class="row">
            <div class="col-md-11">
                <p>#=Description#</p>
            </div>
            <div class="col-md-12">
                #if(Tasks){#
                <hr />
                #var tasksSplit = Tasks.split('|~');#
                <label style="color:\\#6eb6b4">Tasks (#=tasksSplit.length#)</label>
                #for(var i = 0;i< tasksSplit.length;i++){#
                <div class="checkbox">
                    <label>
                        <input type="checkbox" disabled="disabled"> #=tasksSplit[i]#
                    </label>
                </div>
                #}#
                <hr />
                #}#
            </div>
            <div class="col-md-12">
                <label style="color:\\#6eb6b4">Note</label>
                @(Html.TextArea("Notes", new { @class = "form-control", @style = "width:100%", @rows = "3", @disabled = "disabled" }))
                
            </div>
        </div>
        #} else if(IsAttachment){#
        <div class="row">
            <div class="col-md-11">
                <p>#=Description#</p>
            </div>
            <div class="col-md-12">
                <label style="color:\\#6eb6b4">Note</label>
                @(Html.TextArea("Notes", new { @class = "form-control", @style = "width:100%", @rows = "3", @disabled = "disabled" }))
                
            </div>
            <div class="col-md-12 mt-2">
                <button class="btn btn-primary btn-sm" disabled="disabled">Upload</button>
            </div>
        </div>
        #}#
        
        
        @if(Html.IsGlobalAdmin() || Html.IsMaintenanceAdmin()) { 
            <text>
                <div class="d-flex actionsContainer justify-content-end mt-4">
                    <a class="btn btn-danger btn-sm" onclick="deleteStep('#=MaintenanceBlueprintStepId#')">
                        <i class="fa fa-ban"></i>
                        Delete Step
                    </a>
                    <a class="btn btn-sm btn-primary" href="@Url.Action("EditMaintenanceBlueprintStep", "Admin")/#=MaintenanceBlueprintStepId#">
                        <i class="fa fa-folder-open"></i> 
                        Edit Step
                    </a>
                    #if (Order == @Model.Steps -1) {#
                        <a href="@Url.Action("AddMaintenanceBlueprintStep", "Admin", new { @id = Model.MaintenanceBlueprintId } )" class="btn btn-success btn-sm">
                            <i class="fa fa-plus"></i>
                            Add Another Step
                        </a>
                    #}#
                    #if(Order!=0){#
                        <a onclick="moveStepUp('#=MaintenanceBlueprintStepId#')" class="btn btn-primary btn-sm">
                            <i class="fa fa-arrow-up"></i>
                            Move Up
                        </a>
                    #}#
                    #if (Order != @Model.Steps -1) {#
                        <a onclick="moveStepDown('#=MaintenanceBlueprintStepId#')" class="btn btn-primary btn-sm">
                            <i class="fa fa-arrow-down"></i>
                            Move Down
                        </a>
                    #}#
               </div>
            </text> 
        }
    </div>
    
</script>
@*<input type="hidden" value="@(Model.EquipmentCategoryIds)" id="equipmentCategoryIdsValue"/>
*@<script>
    const maintenanceModel = {
        maintenanceBlueprintId: @(Model.MaintenanceBlueprintId ?? 0),
        //equipmentCategoryIds: document.getElementById("equipmentCategoryIdsValue").value ?? [0],
    }
</script>

<environment include="Development">
    <script src="~/js/views/maintenance/editMaintenanceBlueprintAdmin.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/maintenance/editMaintenanceBlueprintAdmin.min.js" asp-append-version="true"></script>
</environment>
