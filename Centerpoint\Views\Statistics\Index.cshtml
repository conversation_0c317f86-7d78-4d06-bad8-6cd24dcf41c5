﻿<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-line-chart"></i>
        Statistics
    </h4>
</div>
<hr />

@(Html.Kendo().TabStrip()
   .Name("statisticsTest")
   .SelectedIndex(0)
   .Animation(false)
   .Items( tabStrip => 
   {
         if(!GlobalSettings.IsAisus)
         {
             tabStrip.Add().Text("").HtmlAttributes(new { @data_bind="html:tabStripHeaderJobs"})
             .Selected(true)
             .Content(@<text>
                 <partial name="Jobs"/>
               </text>
             );

             tabStrip.Add().Text("")
           .HtmlAttributes(new { @data_bind = "click:refreshRunChart, html:tabStripHeaderRuns" })
             .Content(@<text>
                 <partial name="Runs"/>
               </text>
             );
        }
          tabStrip.Add().Text("")
         .HtmlAttributes(new { @data_bind="click:refreshRisc<PERSON><PERSON>h<PERSON><PERSON>, html:tabStripHeaderRiscs"})
         .Content(@<text>
             <partial name="Riscs"/>
           </text>
         );

          tabStrip.Add().Text("")
         .HtmlAttributes(new { @data_bind = "click:refreshShipmentChart, html:tabStripHeaderShipments" })
         .Content(@<text>
             <partial name="Shipments"/>
           </text>
         );

          tabStrip.Add().Text("")
         .HtmlAttributes(new { @data_bind = "click:refreshAssetStats, html:tabStripHeaderAssets" })
         .Content(@<text>
             <partial name="Assets"/>
           </text>
         );
   })
)

<script>
    const statisticsModel = {
        statuses:[],
        assetEquipmentFilterOptions:"" @*@Html.Raw(Html.AssetDefaultEquipmentFilterOptions())*@,
        assetStartDate: "@DateTime.Now.AddMonths(-1).ToString("yyyy-MM-dd")",
        assetEndDate: "@DateTime.Now.ToString("yyyy-MM-dd")",
        units: "" @* @Html.Raw(UnitsConstant.EquipmentValuesAndDescriptions.ToList()) *@,
        kg: "" @* @Html.Raw(UnitsConstant.EquipmentWeightValuesAndDescriptions.ToList()) *@,
    }
    @foreach (var status in EquipmentConstant.ValuesAndDescriptions.ToList()){
        @:statisticsModel.statuses.push("@status.Key");
    }
</script>

<environment include="Development">
    <script src="~/js/views/statistics/statistics.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/statistics/statistics.min.js" asp-append-version="true"></script>
</environment>
