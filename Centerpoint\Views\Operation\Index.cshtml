﻿@model OperationDashboardModel

<div class="header-container-between">
    <h4>
        <i class="fa fa-globe"></i>
        Operations
    </h4>
    <div>
        @if (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsLogisticsAdmin() || Html.IsSeniorFieldEngineer() || Html.IsJuniorFieldEngineer() || Html.IsFieldEngineer() || Html.IsSeniorUSEngineer()) {
            <a class="btn btn-primary btn-sm" href="@Url.Action("AddProject", "Operation")"><i class="fa fa-plus"></i> Create New Project</a>
        }
    </div>
</div>
<hr />

<div>
    <div class="row">
        <div class="col-md-4">
            <div class="row align-items-start">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <a href="#projectList" data-bind="click:opportunity">
                            <div class="card-body">
                                <h2><i class="fa fa-lightbulb"></i> @Model.TotalOpportunities</h2>
                            </div>
                            <div class="card-footer" data-bind="style:{backgroundColor:opportunityBackground}">
                                <h6>Upcoming</h6>
                            </div>
                        </a>
                    </div>
                    <div class="card">
                        <a href="#projectList" data-bind="click:closed">
                            <div class="card-body">
                                <h2><i class="fa fa-archive"></i> @Model.TotalClosed</h2>
                            </div>
                            <div class="card-footer" data-bind="style:{backgroundColor:closedBackground}">
                                <h6>Closed</h6>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card mb-4">
                        <a href="#projectList" data-bind="click:active">
                            <div class="card-body">
                                <h2><i class="fa fa-caret-square-up">
                                </i> @Model.TotalActive</h2>
                            </div>
                            <div class="card-footer" data-bind="style:{backgroundColor:activeBackground}">
                                <h6> Active</h6>
                            </div>
                        </a>
                    </div>
                    <div class="card">
                        <a href="#projectList" data-bind="click:lost">
                            <div class="card-body" >
                                <h2><i class="fa fa-caret-square-down">
                                </i> @Model.TotalLost</h2>
                            </div>
                            <div class="card-footer" data-bind="style:{backgroundColor:lostBackground}">
                                <h6>Lost</h6>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-8">
            <div class="card panel-default">
                <div class="card-body">
                    @(Html.Kendo().Chart<ObjectiveModel>(Model.Services)
                    .Name("jobsByServiceGrid")
                    .Theme("Bootstrap")
                    .Series(c => c.Bar(p => p.Jobs))
                    .SeriesColors("#6EB6B4")
                    .ValueAxis(v => v.Numeric().Title("Jobs").Max(5).MajorGridLines(l => l.Visible(false)).MinorGridLines(l => l.Visible(false)))
                    .Legend(l => l.Position(ChartLegendPosition.Right))
                    .CategoryAxis(c => c.Categories(p => p.Name).Title("Ongoing Jobs per Services"))
                    .Tooltip(t => t.Visible(true).Template("#=value# job")))
                </div>
                <div class="card-footer">
                    <h6 class="mb-0">Jobs by Service</h6>
                </div>
            </div>
        </div>
    </div>
    <hr />
    <div>
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0" id="projectList">
                    <i class="fa fa-folder-open"></i> 
                    <span data-bind="text:totalProjects"></span>
                    <span data-bind="text:statusDescription"></span> 
                        Projects
                </h5>
                <div data-bind="visible:isActiveOpportunity" class="mb-0">
                    <button id="expandButton" data-bind="visible:expand,click:expandAll" class="btn btn-secondary margin-r">Expand All</button>
                    <button id="collapseButton" data-bind="invisible:expand,click:collapseAll" class="btn btn-secondary" >Collapse All</button>        
                </div>
            </div>
        </div>
        <div>
            <ul id="projectPanelBar" data-bind="visible:isActiveOpportunity"></ul>
            @(Html.Kendo().Grid<ProjectGridModel>()
                .Name("closedGrid")
                .Columns(c => {
                    if(GlobalSettings.IsRegiis)
                        c.Bound(p => p.ProjectNameandObjectives).ClientTemplate("<a href='" + @Url.Action("EditProject", "Operation") + "/#=ProjectId#'>#=ProjectNameandObjectives#</a>").Title("Project");
                    else
                        c.Bound(p => p.Name).ClientTemplate("<a href='" + @Url.Action("EditProject", "Operation") + "/#=ProjectId#'>#=Name#</a>").Title("Project");
                    c.Bound(p => p.CompanyName);
                    c.Bound(p => p.CompanyLocationName).Title("Company Location");
                    c.Bound(p => p.Objectives).Title("Services");
                    c.Bound(p => p.ProjectCreationDate).Title("Creation Date").Format(DateConstants.DateFormat);
                    c.Bound(p => p.BaseCompanyLocationName).Title("Base");
                    c.Bound(p => p.JobCount).Title("Total Jobs");
                    c.Bound(p => p.JobRunCount).Title("Non-Standby Runs");
                    c.Bound(p => p.ProjectCrews).Title("Engineers");
                })
                .Sortable()
                .Filterable()
                .Groupable()
                .ToolBar(t => {
                    t.Excel().Text("Export");
                }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
                .AutoBind(false)
                .Excel(excel => excel
                    .FileName(string.Format("Centerpoint_Closed_Projects_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                    .Filterable(true)
                    .ProxyURL(Url.Action("Export", "Operation")))
                .Scrollable(s => s.Height(400))
                .Resizable(c => c.Columns(true))
                .ColumnMenu(c => c.Columns(true))
                .Events(e => e.DataBound("updateClosedProjectTotal"))
                .HtmlAttributes(new { @data_bind = "visible:isClosed" })
                .DataSource(dataSource => dataSource
                    .Ajax()
                    .ServerOperation(false)
                    .Read(read => read.Action("GetProjectData", "Operation", new { @status = ProjectStatusConstant.Closed }))
                )
            )
            
            @(Html.Kendo().Grid<ProjectGridModel>()
                .Name("lostGrid")
                .Columns(c => {
                    if (GlobalSettings.IsRegiis)
                        c.Bound(p => p.ProjectNameandObjectives).ClientTemplate("<a href='" + @Url.Action("EditProject", "Operation") + "/#=ProjectId#'>#=ProjectNameandObjectives#</a>").Title("Project");
                    else
                        c.Bound(p => p.Name).ClientTemplate("<a href='" + @Url.Action("EditProject", "Operation") + "/#=ProjectId#'>#=Name#</a>").Title("Project"); c.Bound(p => p.CompanyName);
                    c.Bound(p => p.CompanyName);
                    c.Bound(p => p.CompanyLocationName).Title("Company Location");
                    c.Bound(p => p.Objectives).Title("Services");
                    c.Bound(p => p.FirstNameLastName).Title("Company Contact");
                    c.Bound(p => p.ProjectCreationDate).Title("Creation Date").Format(DateConstants.DateFormat);
                    c.Bound(p => p.BaseCompanyLocationName).Title("Base");
                })
                .AutoBind(false)
                .Sortable()
                .Filterable()
                .Groupable()
                .ToolBar(t => {
                    t.Excel().Text("Export");
                }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
                .Excel(excel => excel
                    .FileName(string.Format("Centerpoint_Lost_Projects_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                    .Filterable(true)
                    .ProxyURL(Url.Action("Export", "Operation")))
                .Scrollable(s => s.Height(400))
                .Resizable(c => c.Columns(true))
                .ColumnMenu(c => c.Columns(true))
                .Events(e => e.DataBound("updateLostProjectTotal"))
                .HtmlAttributes(new {@data_bind="visible:isLost"})
                .DataSource(dataSource => dataSource
                    .Ajax()
                    .ServerOperation(false)
                    .Read(read => read.Action("GetProjectData", "Operation", new { @status = ProjectStatusConstant.Lost }))
                )
            )
        </div>
    </div>
</div>

 <script type="text/x-kendo-tmpl" id="projectTemplateInitial">
 <div class="container-fluid" id="#=ProjectId#">
    <div><div>
 </div>
 </script>

 <script type="text/x-kendo-tmpl" id="projectTemplateDetails">
    <div class="container-fluid" id="#=ProjectId#">    
        <div class="row mt-4">            
            <div class="col-md-5">
                #if(LatestUpdate){#
                    <div class="mb-5">
                        <h5 class="text-primary">
                            <i class="fa fa-clock"></i>
                            Latest Update - #=new Date( LatestUpdate.Date).toLocaleString()# (Centerpoint Time)
                        </h5>
                        <p>#=LatestUpdate.Log# by <span class="text-primary">#=LatestUpdate.UserName#</span></p>
                    </div>    
                #} else {#
                    <div class="mb-5">
                        <h5 class="text-primary"><i class="fa fa-clock"></i>Latest Update</h5>
                        <p>No recent updates found.</p>
                    </div>     
                #}#
                <div class="mb-5">
                    <h5 class="text-primary">
                        <i class="fa fa-users"></i> 
                        Current Crew
                    </h5>
                    <p>#=ProjectCrew ? ProjectCrew : 'No crew assigned'#</p> 
                </div>               
                <div class="text-primary">
                    <h5 class="mb-3">
                        <i class="fa fa-cogs"></i> 
                        #=JobCount# #=JobCount == 1 ? 'Job' : 'Jobs'#
                    </h5>
                    <h5 class="mb-3">
                        <i class="fa fa-usd"></i> 
                        #=JobInvoiceNumberCount# #=JobInvoiceNumberCount == 1 ? 'Job Invoiced' : 'Jobs Invoiced'#
                    </h5>
                    <h5 class="mb-3">
                        <i class="fa fa-cog"></i> 
                        #=JobRunCount# #=JobRunCount == 1 ? 'Run' : 'Runs'#
                    </h5 class="mb-3">
                    <h5 class="mb-3">
                        <i class="fa fa-file-text"></i> 
                        #=AttachmentsCount# #=AttachmentsCount == 1 ? 'Attachment' : 'Attachments'#
                    </h5>
                    <h5 class="mb-3">
                        <i class="fa fa-plane"></i> 
                        #=ShipmentCount# #=ShipmentCount == 1 ? 'Shipment' : 'Shipments'#
                    </h5>
                    <h5 class="mb-3">
                        <i class="fa fa-tags"></i> #=EquipmentItemCount# #=EquipmentItemCount == 1 ? 'Equipment Item' : 'Equipment Items'#
                    </h5>
                    #if(OppsName){#
                        <h5 class="mb-3">
                            <a class="text-primary" href="@Url.Action("Edit", "Sales")/#=LinkOpportunityId#?revision=#=OppsMaxRevision#">
                                <i class="fa fa-usd"></i> 
                                #=OppsName#
                            </a>
                        </h5>
                    #}#
                </div>
            </div>
            <div class="col-md-7">
                <h5 class="text-primary">
                    <a href="@Url.Action("EditProject", "Operation")/#=ProjectId#?tab=comments">
                        Project Comments
                    </a>
                </h5>
                #if(RecentProjectComments && RecentProjectComments.length > 0){
                    for (var i =0;i < RecentProjectComments.length;i++){#
                        <div class="mb-5">   
                            <p class="mb-2">
                                <a class="text-primary" href="@Url.Action("EditProject", "Operation")/#=RecentProjectComments[i].ProjectId#?tab=comments">
                                    <i class="fa fa-comment"></i> 
                                    #=RecentProjectComments[i].ProjectTitle#
                                </a>
                                <span>#=new Date(RecentProjectComments[i].Date).toLocaleString()#</span>

                            </p>
                            <p class="mb-2" data-toggle="tooltip" title="#=RecentProjectComments[i].Comment#">
                                <i>"#=RecentProjectComments[i].TrimmedProjectComment#"</i> 
                                - 
                                <span class="text-primary">
                                    <i class="fa fa-user"></i> 
                                    #=RecentProjectComments[i].UserName#
                                </span>
                            </p>
                        </div>    
                    #}
                } else{#
                    <p class="mb-5">No project comments found.</p>
                #}# 
                <h5 class="text-primary mt-1">Job Comments</h5>
                #if(RecentComments && RecentComments.length > 0){
                    for (var i =0;i < RecentComments.length;i++){#
                    <div class="mb-5">            
                        <p class="mb-2">
                            <a class="text-primary" href="@Url.Action("EditJob", "Operation")/#=RecentComments[i].JobId#?tab=comments">
                                <i class="fa fa-comment"></i> 
                                #=RecentComments[i].NewName#
                            </a> 
                            <span>#=new Date(RecentComments[i].Date).toLocaleString()#</span>
                        </p>
                        <p class="mb-2" data-toggle="tooltip" title="#=RecentComments[i].Comment#">
                            <i>"#=RecentComments[i].TrimmedComment#"</i>
                            - 
                            <span class="text-primary">
                                <i class="fa fa-user"></i>
                                #=RecentComments[i].UserName#
                            </span>
                    </p>           
                    </div>        
                #}
                } else {#
                    <p class="mb-5">No job comments found.</p>   
                #}#
            </div>
        </div>              
        <div class="text-right">
            <a class="btn btn-sm btn-primary mt-2 mb-2" href="@Url.Action("EditProject", "Operation")/#=ProjectId#">
                <i class="fa fa-folder-open"></i> 
                Open Project
            </a>
        </div>
    </div>
</script>

<script>
    const projectStatusConstant = { 
        opportunity: "@ProjectStatusConstant.Opportunity",
        active: "@ProjectStatusConstant.Active",
        closed: "@ProjectStatusConstant.Closed",
        lost: "@ProjectStatusConstant.Lost",
    }
    const statusDescription = {
        opportunity: "@ProjectStatusConstant.GetDescription(ProjectStatusConstant.Opportunity)",
        active: "@ProjectStatusConstant.GetDescription(ProjectStatusConstant.Active)",
        closed: "@ProjectStatusConstant.GetDescription(ProjectStatusConstant.Closed)",
        lost: "@ProjectStatusConstant.GetDescription(ProjectStatusConstant.Lost)"
    }
</script>

<style>
    .k-grid-content {
        min-height: 400px;
    }
</style>

<environment include="Development">
    <script src="~/js/views/operation/operation.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/operation/operation.min.js" asp-append-version="true"></script>
</environment>

