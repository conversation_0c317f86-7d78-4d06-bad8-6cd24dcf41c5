﻿<p>Please select a location for the project equipment items to be shipped to.</p>
<form id="shipmentBackloadForm">
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label>Backload Client</label>
                <br />
                @(Html.Kendo().DropDownList()
                .Name("currentClient")
                .Filter(FilterType.Contains)
                .OptionLabel("Select Client")
                .DataTextField("Text")
                .DataValueField("Value")
                .Events(x => x.Change("cascadeDropdownFilterHelper"))
                .DataSource(d => d.Read("GetCompanies", "Lookup")).AutoBind(false)
                .HtmlAttributes(new { @style = "font-size: 14px", @data_bind = "value:toCompanyId", @data_cascade_to="currentLocation" }))
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label>Backload Location</label>
                <br />
                @(Html.Kendo().DropDownList()
                .Name("currentLocation")
                .Filter(FilterType.Contains)
                .OptionLabel("Select Client Location")
                .DataTextField("Text")
                .DataValueField("Value")
                .Events(x => x.Change("cascadeDropdownFilterHelper"))
                .DataSource(source => {
                    source.Read(read => {
                        read.Action("GetLocationsByCompanyId", "Lookup").Data("filterBackLoadCompanyLocations");
                    });
                }).AutoBind(false)
                .HtmlAttributes(new { @style = "font-size: 14px", @data_bind = "value:toCompanyLocationId", @data_cascade_to="currentproject" }))
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <div class="form-group">
                <label>Backload Project</label>
                <br />
                @(Html.Kendo().DropDownList()
                .Name("currentproject")
                .Filter(FilterType.Contains)
                .OptionLabel("Select Project")
                .DataValueField("ProjectId")
                .DataTextField("ProjectNameandObjectives")
                .DataSource(source => {
                    source.Read(read => {
                        read.Action("GetProjectsByCompanyLocationId", "Lookup").Data("filterByCurrentCompanyLocation");
                    });
                }).AutoBind(false)
                .HtmlAttributes(new { @style = "width:100%;font-size: 14px", @data_bind = "value:toProjectId" }))
            </div>
        </div>
    </div>
</form>
<div class="d-flex justify-content-end">
    <button id="changeToLocationConfirm" data-bind="enabled:toCompanyLocationId" class="btn btn-primary btn-sm">Confirm</button>
</div>

