﻿namespace Centerpoint.Common.Constants
{
    public static class EquipmentLogTypeConstant
    {

        public const string Job = "JOB";
        public const string Shipment = "SHI";
        public const string PackingList = "PCL";
        public const string Maintenance = "MTN";
        public const string Update = "UPD";
        public const string Project = "PRJ";
        public const string Run = "RUN";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Job,"Job"},
                    {Shipment,"Shipment"},
                    {PackingList,"Packing List"},
                    {Maintenance,"Maintenance"},
                    {Update, "Update" },
                    {Project, "Project" },
                    {Run, "Run" },
                };
            }
        }
    }
}
