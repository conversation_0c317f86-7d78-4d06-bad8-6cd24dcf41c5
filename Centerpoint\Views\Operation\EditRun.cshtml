﻿@model RunModel

@if (Model.RunId.HasValue && !Model.RunFinish.HasValue) {
    <div class="alert alert-dismissible alert-warning">
        <button type="button" class="close" data-bs-dismiss="alert">×</button>
        <strong>Warning!</strong> Run cannot be closed without a finish time and atleast one run equipment item.
    </div>
}

@if (Model.RunId.HasValue && string.IsNullOrEmpty(Model.JobCrews)) {
    <div class="alert alert-dismissible alert-warning">
        <button type="button" class="close" data-bs-dismiss="alert">×</button>
        <strong>Warning!</strong> Run cannot be closed until crew members are selected for this job.
    </div>    
}

<div class="header-container-between">
    <h4>
        <i class="fa fa-building"></i>
        @(Model.RunId.HasValue ? Model.RunName : "Create New Run")
    </h4>
    <div class="d-flex actionsContainer">
            <a class="btn btn-info btn-sm ml-1" href="@Url.Action("EditJob", "Operation", new { @id = Model.JobId })"><i class="fa fa-refresh"></i>Return to Job</a>
        @if (Model.RunFinish.HasValue && !Model.IsClosed && (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Model.IsJobEngineer)) {
            <a class="btn btn-success btn-sm ml-1" data-bind="visible:totalRunEquipmentItems" href="@Url.Action("CloseRun", "Operation", new { @id = Model.RunId})"><i class="fa fa-check"></i>Mark as Closed</a>
        }
        @if (Model.IsClosed && (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Model.IsJobEngineer)) {
            <a class="btn btn-primary btn-sm ml-1" href="@Url.Action("ReOpenRun", "Operation", new { @id = Model.RunId})"><i class="fa fa-thumbs-up"></i>Re-Open Run</a>
        }
        @if (!Model.IsClosed && (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsSeniorFieldEngineer() || Html.IsJuniorFieldEngineer() || Html.IsFieldEngineer())) {
            <a class="btn btn-danger btn-sm ml-1"  href="#" data-bind="click:deleteRun, invisible:totalRunEquipmentItems"><i class="fa fa-ban"></i>Delete Run</a>
        }
    </div>
</div>
<hr />




@(Html.Kendo().TabStrip()
    .Name("assetsStrips")
    .SelectedIndex(0)
    .Animation(false)
    .Items( tabstrip => {

    tabstrip.Add().Text("")
        .HtmlAttributes(new { @data_bind = "html:tabStripHeaderDetails" })
        .Selected(true)
        .Content(@<text>
    <form id="editRunForm">
        @Html.ValidationSummary(false)

            <div id="details">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            @if (Model.IsClosed) {
                            @Html.LabelFor(m => m.IsStandBy)
                            <br />
                            @(Html.Kendo().DropDownListFor(m => m.IsStandBy)
                            .DataValueField("Key")
                            .DataTextField("Value")
                            .Filter(FilterType.Contains)
                            .Events(e => {
                                e.Change("standByChanged");
                            })
                            .BindTo(new List<KeyValuePair<string, string>> { new KeyValuePair<string, string>(Boolean.FalseString, "No"), new KeyValuePair<string, string>(Boolean.TrueString, "Yes"), })
                            .HtmlAttributes(new { @tabindex = "1", @data_bind = "value:isStandBy", @disabled="disabled" }))
                            } else {
                            @Html.LabelFor(m => m.IsStandBy)
                            <br />
                            @(Html.Kendo().DropDownListFor(m => m.IsStandBy)
                            .DataValueField("Key")
                            .DataTextField("Value")
                            .Filter(FilterType.Contains)
                            .Events(e => {
                                e.Change("standByChanged");
                            })
                            .BindTo(new List<KeyValuePair<string, string>> { new KeyValuePair<string, string>(Boolean.FalseString, "No"), new KeyValuePair<string, string>(Boolean.TrueString, "Yes"), })
                            .HtmlAttributes(new { @tabindex = "1", @data_bind = "value:isStandBy"}))
                            }
                        </div>
                    </div>
                    @if (Model.RunId.HasValue) { 
                    <div class="col-md-4">
                        <label> Crew </label>
                        <br />
                        @if (!string.IsNullOrEmpty(Model.JobCrews)) {
                            @Html.TextArea("CrewDisabled", Model.JobCrews, new { @class = "form-control", @style = "width:100%;font-size: 14px;", @disabled = "disabled" })
                        } else {
                            <p> No Crew members are selected for this job</p>
                        }
                    </div>
                    }
                    @if (!Html.IsUser()) {
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Job Creation Date</label>
                            <br />
                            @Html.Kendo().DateTimePickerFor(m => m.JobCreationDate).Enable(false).HtmlAttributes(new { @class = "k-textbox utcTimePicker" }))
                        </div>
                    </div>
                    }
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Well</label>
                            <br />
                            @Html.Kendo().TextBoxFor(m => m.JobCompanyWellName).Enable(false).HtmlAttributes(new { @class = "form-control"})
                        </div>
                    </div>
                </div>
                <hr />
                <div class="row">
                    <div class="col-md-4">
                        @if (Model.FirstRun) {
                        <div class="form-group">
                            @Html.LabelFor(m => m.RunStart)
                            <br />
                            @(Html.Kendo().DateTimePickerFor(m => m.RunStart).HtmlAttributes(new { @tabindex = "2", @class = "utcTimePicker" }))
                        </div>
                            }
                        @if(!Model.FirstRun && !Html.IsGlobalAdmin()) {
                        <div class="form-group">
                            @Html.LabelFor(m => m.RunStart)
                            <br />
                            @(Html.Kendo().DateTimePickerFor(m => m.RunStart).HtmlAttributes(new { @tabindex = "2" , @disabled="disabled", @class = "utcTimePicker" }))
                        </div>
                        @Html.HiddenFor(m => m.RunStart)
                        } else if (!Model.FirstRun && Html.IsGlobalAdmin()) {
                            <div class="form-group">
                                @Html.LabelFor(m => m.RunStart)
                                <br />
                                @(Html.Kendo().DateTimePickerFor(m => m.RunStart).HtmlAttributes(new { @tabindex = "2", @class = "utcTimePicker" }))
                            </div>
                        }
                        @Html.HiddenFor(m => m.RunStart)
                        <div class="form-group d-inline-block">
                            @if (Model.IsClosed) {
                            @Html.LabelFor(m => m.LostHrs)
                            <br />
                            @(Html.Kendo().IntegerTextBoxFor(p => p.LostHrs)
                            .Min(0)
                            .HtmlAttributes(new { @style = "width:100px", @tabindex = "5" ,@disabled="disabled"})
                            .Spinners(false))
                            } else {
                            @Html.LabelFor(m => m.LostHrs)
                            <br />
                            @(Html.Kendo().IntegerTextBoxFor(p => p.LostHrs)
                            .Min(0)
                            .HtmlAttributes(new { @style = "width:100px", @tabindex = "5" })
                            .Spinners(false))
                            }
                        </div>
                        <div class="form-group d-inline-block">
                            @if (Model.IsClosed) {
                            @Html.LabelFor(m => m.LostMin)
                            <br />
                            @(Html.Kendo().IntegerTextBoxFor(p => p.LostMin)
                                .Max(59)
                                .Min(0)
                                .HtmlAttributes(new {  @style = "width:100px", @tabindex = "6", @disabled = "disabled" })
                                .Spinners(false))
                        } else {
                            @Html.LabelFor(m => m.LostMin)
                            <br />
                            @(Html.Kendo().IntegerTextBoxFor(p => p.LostMin)
                                .Max(59)
                                .Min(0)
                                .HtmlAttributes(new {@style = "width:100px", @tabindex = "6" })
                                .Spinners(false))
                        }
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            @if (Model.IsClosed) {
                            @Html.LabelFor(m => m.RunFinish)
                            <br />
                                @(Html.Kendo().DateTimePickerFor(m => m.RunFinish).Enable(false).HtmlAttributes(new { @tabindex = "3", @class = "utcTimePicker" }))
                            } else {
                            @Html.LabelFor(m => m.RunFinish)
                            <br />
                                @(Html.Kendo().DateTimePickerFor(m => m.RunFinish).HtmlAttributes(new { @tabindex = "3", @class = "utcTimePicker" }))
                            }
                        </div>
                        <div class="form-group d-inline-block">
                            @if (Model.IsClosed) {
                            @Html.LabelFor(m => m.Bottom)
                            <br />
                            @(Html.Kendo().NumericTextBoxFor(p => p.Bottom)
                                        .HtmlAttributes(new { @style = "width:100px", @tabindex = "7", @data_bind = "invisible:otherRunFieldsVisible", @disabled = "disabled"})
                                        .Spinners(false)
                                        .Format("n2"))
                            @Html.TextBox("BottomDiabled", Model.Bottom, new {@style = "width:100px;", @disabled = "disabled", @data_bind = "visible:otherRunFieldsVisible" })
                            } else {
                            @Html.LabelFor(m => m.Bottom)
                            <br />
                            @(Html.Kendo().NumericTextBoxFor(p => p.Bottom)
                                        .HtmlAttributes(new { @style = "width:100px", @tabindex = "7", @data_bind = "invisible:otherRunFieldsVisible"})
                                        .Spinners(false)
                                        .Format("n2"))
                            @Html.TextBox("BottomDiabled", Model.Bottom, new { @class = "k-textbox", @style = "width:100px;", @disabled = "disabled", @data_bind = "visible:otherRunFieldsVisible" })
                            }
                        </div>
                        <div class="form-group d-inline-block">
                            @if (Model.IsClosed) {
                            @Html.LabelFor(m => m.BottomUnits)
                            <br />
                            @(Html.Kendo().DropDownListFor(m => m.BottomUnits)
                                .DataTextField("Value")
                                .DataValueField("Key")
                                .Filter(FilterType.Contains)
                                .Events(e => e.Change("onBottomUnitsChange"))
                                .BindTo(Centerpoint.Common.Constants.UnitsConstant.RunValuesAndDescriptions.ToList())
                                .HtmlAttributes(new {@style = "width:100px", @tabindex = "8", @data_bind = "invisible:otherRunFieldsVisible", @disabled = "disabled" }))
                            @Html.TextBox("BottomUnitsDiabled", Model.BottomUnitsDescription, new { @style = "width:100px;", @disabled = "disabled", @data_bind = "visible:otherRunFieldsVisible" })
                        } else {
                            @Html.LabelFor(m => m.BottomUnits)
                            <br />
                            @(Html.Kendo().DropDownListFor(m => m.BottomUnits)
                                .DataTextField("Value")
                                .DataValueField("Key")
                                .Filter(FilterType.Contains)
                                .Events(e => e.Change("onBottomUnitsChange"))
                                .BindTo(Centerpoint.Common.Constants.UnitsConstant.RunValuesAndDescriptions.ToList())
                                .HtmlAttributes(new { @style = "width:100px", @tabindex = "8", @data_bind = "invisible:otherRunFieldsVisible" }))
                            @Html.TextBox("BottomUnitsDiabled", Model.BottomUnitsDescription, new { @class = "k-textbox", @style = "width:100px;", @disabled = "disabled", @data_bind = "visible:otherRunFieldsVisible" })
                        }
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            @if (Model.IsClosed) {
                            <label>Services</label>
                            <br />
                            @(Html.Kendo().MultiSelectFor(m => m.ObjectiveIds)
                            .DataTextField("Name")
                            .DataValueField("ObjectiveId")
                            .Placeholder("Select Services...")
                            .HtmlAttributes(new { @style = "font-size: 14px;", @tabindex = "4", @data_bind = "invisible:otherRunFieldsVisible", @disabled = "disabled" })
                            .DataSource(source => {
                                                source.Read(read => {
                                                     read.Action("GetObjectivesbyJobId", "Lookup", new { @jobId = Model.JobId });
                                                }).ServerFiltering(true);
                                            }))
                            @Html.TextBox("ServiceDiabled", Model.Objectives, new { @style = "font-size: 14px;", @disabled = "disabled", @data_bind = "visible:otherRunFieldsVisible" })
                            } else {
                            <label>Services</label>
                            <br />
                            @(Html.Kendo().MultiSelectFor(m => m.ObjectiveIds)
                            .DataTextField("Name")
                            .DataValueField("ObjectiveId")
                            .Placeholder("Select Services...")
                            .HtmlAttributes(new { @style = "font-size: 14px;", @tabindex = "4", @data_bind = "invisible:otherRunFieldsVisible"})
                            .DataSource(source => {
                                                source.Read(read => {
                                                     read.Action("GetObjectivesbyJobId", "Lookup", new { @jobId = Model.JobId });
                                                }).ServerFiltering(true);
                                            }))
                            @Html.TextBox("ServiceDiabled", Model.Objectives, new { @class = "k-textbox", @style = "font-size: 14px;", @disabled = "disabled", @data_bind = "visible:otherRunFieldsVisible" })
                            }
                        </div>
                        <div class="form-group d-inline-block">
                            @if (Model.IsClosed) {
                            @Html.LabelFor(m => m.Top)
                            <br />
                            @(Html.Kendo().NumericTextBoxFor(p => p.Top)
                            .HtmlAttributes(new { @style = "width:100px", @tabindex = "9", @data_bind = "invisible:otherRunFieldsVisible", @disabled = "disabled" })
                            .Spinners(false)
                            .Format("n2"))
                            @Html.TextBox("TopDiabled", Model.Top, new { @style = "width:100px;", @disabled = "disabled", @data_bind = "visible:otherRunFieldsVisible", @tabindex = "10" })
                            } else {
                            @Html.LabelFor(m => m.Top)
                            <br />
                            @(Html.Kendo().NumericTextBoxFor(p => p.Top)
                                .HtmlAttributes(new { @style = "width:100px", @tabindex = "9", @data_bind = "invisible:otherRunFieldsVisible" })
                                .Spinners(false)
                                .Format("n2"))
                            @Html.TextBox("TopDiabled", Model.Top, new { @class = "k-textbox", @style = "width:100px;", @disabled = "disabled", @data_bind = "visible:otherRunFieldsVisible", @tabindex = "10" })
                            }
                        </div>
                        <div class="form-group d-inline-block">
                            @if (Model.IsClosed) {
                            @Html.LabelFor(m => m.TopUnits)
                            <br />
                            @(Html.Kendo().DropDownListFor(m => m.TopUnits)
                            .DataTextField("Value")
                            .DataValueField("Key")
                            .Filter(FilterType.Contains)
                            .Events(e => e.Change("onTopUnitsChange"))
                            .BindTo(Centerpoint.Common.Constants.UnitsConstant.RunValuesAndDescriptions.ToList())
                            .HtmlAttributes(new { @style = "width:100px", @tabindex = "11", @data_bind = "invisible:otherRunFieldsVisible", @disabled = "disabled" }))
                            @Html.TextBox("TopUnitsDiabled", Model.TopUnitsDescription, new { @class = "k-textbox", @style = "width:100px;", @disabled = "disabled", @data_bind = "visible:otherRunFieldsVisible" })
                            } else {
                            @Html.LabelFor(m => m.TopUnits)
                            <br />
                            @(Html.Kendo().DropDownListFor(m => m.TopUnits)
                                .DataTextField("Value")
                                .DataValueField("Key")
                                .Filter(FilterType.Contains)
                                .Events(e => e.Change("onTopUnitsChange"))
                                .BindTo(Centerpoint.Common.Constants.UnitsConstant.RunValuesAndDescriptions.ToList())
                                .HtmlAttributes(new { @style = "width:100px", @tabindex = "11", @data_bind = "invisible:otherRunFieldsVisible" }))
                            @Html.TextBox("TopUnitsDiabled", Model.TopUnitsDescription, new { @class = "k-textbox", @style = "width:100px;", @disabled = "disabled", @data_bind = "visible:otherRunFieldsVisible" })
                            }
                        </div>
                    </div>
                </div>
                <hr />
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            @if (Model.IsClosed) {
                            <label>Notes</label>
                            <br />
                                @Html.TextAreaFor(p => p.EngineerNotes, new { @class = "form-control", @style = "width:100%", @rows = "5", @disabled = "disabled" })
                            } else {
                            <label>Notes</label>
                            <br />
                                @Html.TextAreaFor(p => p.EngineerNotes, new { @class = "form-control", @style = "width:100%", @rows = "5" })
                            }
                        </div>
                    </div>
                </div>

            @Html.HiddenFor(m => m.RunId)
            @Html.HiddenFor(m => m.JobId)
            @Html.HiddenFor(m => m.JobProjectId)
            @Html.HiddenFor(m => m.OpportunityId)



            @if (!Model.IsClosed &&(Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsSeniorFieldEngineer() || Html.IsJuniorFieldEngineer() || Html.IsFieldEngineer())) {
            <button id="runSaveDetails" type="button" class="btn btn-sm btn-primary"> Save Run Details </button>
            }
            </div>
            </form>

        </text>);

        if (Model.RunId.HasValue && !Model.IsStandBy) {
            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind="html:runEquipmentItemsStripText"})
                .Content(@<text>
                    @if (Model.RunId.HasValue && !Model.IsStandBy && !Model.IsClosed) { 
                        <div id="equipmentItems">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fa fa-tags"></i> Equipment Items (<span data-bind="text: totalEquipmentItems"></span>)</h6>
                                </div>
                                @(Html.Kendo().Grid<EquipmentItemModel>()
                                    .Name("equipmentItemGrid")
                                    .Columns(columns => {
                                        columns.Bound(c => c.EquipmentItemName).Title("Item Number").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#?runId=" + Model.RunId + "'>#=EquipmentItemName#</a>");
                                        columns.Bound(c => c.ReceivedDate).Title("Manufacture Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                        columns.Bound(c => c.PurchasedDate).Title("Purchased Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                        columns.Bound(c => c.CurrencyName).Title("Currency").Hidden(true);
                                        columns.Bound(c => c.Price).Format("{0:n2}").Hidden(true);
                                        columns.Bound(c => c.DepreciatedPrice).Title("Net Book Value").Hidden(true).Format("{0:n2}").Width(100);
                                        columns.Bound(c => c.PointsPerMonth).Title("Points Per Month").Hidden(true);
                                        columns.Bound(c => c.PointsPerRun).Title("Points Per Run").Hidden(true);
                                        columns.Bound(c => c.PointsPerMove).Title("Points Per Move").Hidden(true);
                                        columns.Bound(c => c.DivisionName).Title("Division").Hidden(true);
                                        columns.Bound(c => c.TrackedNonAssetItem).Title("Tracked Non-Asset Item").ClientTemplate("#if(TrackedNonAssetItem){#Yes#}else{#No#}#").Hidden(true);
                                        columns.Bound(c => c.CurrentClientLocationName).Title("Current Location");
                                        columns.Bound(c => c.CustomStatusCode).Title("Custom Status").Hidden(true);
                                        columns.Bound(c => c.Points).Title("Current Points");
                                        columns.Bound(c => c.MaintenanceScheduleDates).Title("Maintenance Schedule Date(s)").ClientTemplate("#=MaintenanceScheduleDetail#");
                                        columns.Bound(c => c.EquipmentInfo).Title("Info");
                                        columns.Bound(c => c.AllStatusDescription).Title("Status").Encoded(false).Filterable(f => f.Operators(o => o.ForString(str => str.Clear().Contains("Contains").DoesNotContain("Does not contain")))).Width(150);
                                    })
                                        .ColumnMenu(c => c.Columns(true))
                                        .Events(e => e.DataBound("updateEquipmentTotals"))
                                        .ToolBar(toolbar => { toolbar.ClientTemplateId("equipmentItemGridHeader");})
                                        .Filterable()
                                        .Selectable(selectable => selectable.Mode(GridSelectionMode.Multiple))
                                        .Search(s => { s.Field(o => o.EquipmentItemName, "contains"); })
                                        .Sortable()
                                        .Groupable()
                                        .Scrollable(s => s.Height(532))
                                        .Resizable(resize => resize.Columns(true))
                                        .Reorderable(reorder => reorder.Columns(true))
                                    .DataSource(dataSource => dataSource
                                    .Ajax()
                                    .ServerOperation(false)
                                    .Model(model => {
                                        model.Id(m => m.EquipmentItemId);
                                    })
                                    .Read(read => read.Action("GetEquipmentItemsByProjectId", "Operation").Data("equipmentItemData"))))
                                <br />
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fa fa-tags"></i> Run Equipment Items (<span data-bind="text: totalRunEquipmentItems"></span>)</h6>
                                </div>
                                @(Html.Kendo().Grid<RunEquipmentItemModel>()
                                    .Name("runEquipmentItemGrid")
                                    .Columns(columns => {
                                    columns.Bound(c => c.EquipmentItem.EquipmentItemName).Title("Item Number").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#?runId=" + Model.RunId + "'>#=EquipmentItem.EquipmentItemName#</a>");
                                    columns.Bound(c => c.EquipmentItemReceivedDate).Title("Manufacture Date").Format(DateConstants.DateFormat).Hidden(true);
                                    columns.Bound(c => c.EquipmentItemPurchasedDate).Title("Purchased Date").Format(DateConstants.DateFormat).Hidden(true);
                                    columns.Bound(c => c.EquipmentItemCurrencyName).Title("Currency").Hidden(true);
                                    columns.Bound(c => c.EquipmentItemPrice).Format("{0:n2}").Hidden(true);
                                    columns.Bound(c => c.EquipmentItemDepreciatedPrice).Title("Net Book Value").Hidden(true).Format("{0:n2}").Width(100);
                                    columns.Bound(c => c.EquipmentItemPointsPerMonth).Title("Points Per Month").Hidden(true);
                                    columns.Bound(c => c.EquipmentItemPointsPerRun).Title("Points Per Run").Hidden(true);
                                    columns.Bound(c => c.EquipmentItemPointsPerMove).Title("Points Per Move").Hidden(true);
                                    columns.Bound(c => c.EquipmentItemDivisionName).Title("Division").Hidden(true);
                                    columns.Bound(c => c.EquipmentItemTrackedNonAssetItem).Title("Tracked Non-Asset Item").ClientTemplate("#if(EquipmentItemTrackedNonAssetItem){#Yes#}else{#No#}#").Hidden(true);
                                    columns.Bound(c => c.CurrentClientLocationName).Title("Current Location");
                                    columns.Bound(c => c.EquipmentItem.CustomStatusCode).Title("Custom Status").Hidden(true);
                                    columns.Bound(c => c.EquipmentItemPoints).Title("Current Points");
                                    columns.Bound(c => c.MaintenanceScheduleDetail).Title("Maintenance Schedule Date(s)").ClientTemplate("#=MaintenanceScheduleDetail#");
                                    columns.Bound(c => c.EquipmentItemEquipmentInfo).Title("Info");
                                    columns.Bound(c => c.AllStatusDescription).Title("Status").Encoded(false).Filterable(f => f.Operators(o => o.ForString(str => str.Clear().Contains("Contains").DoesNotContain("Does not contain")))).Width(150);
                                    columns.Command(c => { 
                                        c.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
                                    }).Width(100);
                                    })
                                    .ColumnMenu(c => c.Columns(true))
                                    .Events(e => e.DataBound("updateRunEquipmentItemsTotals"))
                                    .Filterable()
                                    .Groupable()
                                    .ToolBar(t => t.Search())
                                    .Search(s => { s.Field(o => o.EquipmentItem.EquipmentItemName, "contains"); })
                                    .Editable(e => e.Mode(GridEditMode.InLine))
                                    .Scrollable(s => s.Height(500))
                                    .Resizable(resize => resize.Columns(true))
                                    .Reorderable(reorder => reorder.Columns(true))
                                    .DataSource(dataSource => dataSource
                                    .Ajax()
                                    .ServerOperation(false)
                                    .Events(e => e.RequestEnd("runEquipmentItemsRequestEnd"))
                                    .Model(model => {
                                        model.Id(m => m.RunEquipmentItemId);
                                    })
                                    .Read(read => read.Action("GetRunEquipmentItems", "Operation", new { @runId = Model.RunId, @projectId = Model.JobProjectId }))
                                    .Destroy(destroy => destroy.Action("DeleteRunEquipmentItem", "Operation"))))
                        </div>
                    }  
                    else if (Model.RunId.HasValue && !Model.IsStandBy && Model.IsClosed) { 
                        <div id="equipmentItems">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fa fa-tags"></i> Equipment Items (<span data-bind="text: totalEquipmentItems"></span>)</h6>
                            </div>
                            @(Html.Kendo().Grid<EquipmentItemModel>()
                                .Name("equipmentItemGrid")
                                .Columns(columns => {
                                columns.Bound(c => c.EquipmentItemName).Title("Item Number").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#?runId=" + Model.RunId + "'>#=EquipmentItemName#</a>");
                                columns.Bound(c => c.ReceivedDate).Title("Manufacture Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                columns.Bound(c => c.PurchasedDate).Title("Purchased Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                columns.Bound(c => c.CurrencyName).Title("Currency").Hidden(true);
                                columns.Bound(c => c.Price).Format("{0:n2}").Hidden(true);
                                columns.Bound(c => c.DepreciatedPrice).Title("Net Book Value").Hidden(true).Format("{0:n2}").Width(100);
                                columns.Bound(c => c.PointsPerMonth).Title("Points Per Month").Hidden(true);
                                columns.Bound(c => c.PointsPerRun).Title("Points Per Run").Hidden(true);
                                columns.Bound(c => c.PointsPerMove).Title("Points Per Move").Hidden(true);
                                columns.Bound(c => c.DivisionName).Title("Division").Hidden(true);
                                columns.Bound(c => c.TrackedNonAssetItem).Title("Tracked Non-Asset Item").ClientTemplate("#if(TrackedNonAssetItem){#Yes#}else{#No#}#").Hidden(true);
                                columns.Bound(c => c.CurrentClientLocationName).Title("Current Location").Hidden(true);
                                columns.Bound(c => c.CustomStatusCode).Title("Custom Status").Hidden(true);
                                columns.Bound(c => c.Points).Title("Current Points");
                                columns.Bound(c => c.MaintenanceScheduleDetail).Title("Maintenance Schedule Date(s)").ClientTemplate("#=MaintenanceScheduleDetail#");
                                columns.Bound(c => c.EquipmentInfo).Title("Info");
                                columns.Bound(c => c.AllStatusDescription).Title("Status").Encoded(false).Filterable(f => f.Operators(o => o.ForString(str => str.Clear().Contains("Contains").DoesNotContain("Does not contain")))).Width(150);
                                    })
                                .ColumnMenu(c => c.Columns(true))
                                .Events(e => e.DataBound("updateEquipmentTotals"))
                                .Filterable()
                                .Selectable(selectable => selectable.Mode(GridSelectionMode.Single))
                                .Sortable()
                                .Groupable()
                                .ToolBar(t => t.Search())
                                .Search(s => { s.Field(o => o.EquipmentItemName, "contains"); })
                                .Scrollable(s => s.Height(532))
                                .Resizable(resize => resize.Columns(true))
                                .Reorderable(reorder => reorder.Columns(true))
                                .DataSource(dataSource => dataSource
                                .Ajax()
                                .ServerOperation(false)
                                .Model(model => {
                                    model.Id(m => m.EquipmentItemId);
                                })
                                .Read(read => read.Action("GetEquipmentItemsByProjectId", "Operation").Data("equipmentItemData"))))
                            <br />
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fa fa-tags"></i> Run Equipment Items (<span data-bind="text: totalRunEquipmentItems"></span>)</h6>
                            </div>
                            @(Html.Kendo().Grid<RunEquipmentItemModel>()
                                .Name("runEquipmentItemGrid")
                                .Columns(columns => {
                                columns.Bound(c => c.EquipmentItem.EquipmentItemName).Title("Item Number 22").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#?runId=" + Model.RunId + "'>#=EquipmentItem.EquipmentItemName#</a>");
                                columns.Bound(c => c.EquipmentItemReceivedDate).Title("Manufacture Date").Format(DateConstants.DateFormat).Hidden(true);
                                columns.Bound(c => c.EquipmentItemPurchasedDate).Title("Purchased Date").Format(DateConstants.DateFormat).Hidden(true);
                                columns.Bound(c => c.EquipmentItemCurrencyName).Title("Currency").Hidden(true);
                                columns.Bound(c => c.EquipmentItemPrice).Format("{0:n2}").Hidden(true);
                                columns.Bound(c => c.EquipmentItemDepreciatedPrice).Title("Net Book Value").Hidden(true).Format("{0:n2}").Width(100);
                                columns.Bound(c => c.EquipmentItemPointsPerMonth).Title("Points Per Month").Hidden(true);
                                columns.Bound(c => c.EquipmentItemPointsPerRun).Title("Points Per Run").Hidden(true);
                                columns.Bound(c => c.EquipmentItemPointsPerMove).Title("Points Per Move").Hidden(true);
                                columns.Bound(c => c.EquipmentItemDivisionName).Title("Division").Hidden(true);
                                columns.Bound(c => c.EquipmentItemTrackedNonAssetItem).Title("Tracked Non-Asset Item").ClientTemplate("#if(EquipmentItemTrackedNonAssetItem){#Yes#}else{#No#}#").Hidden(true);
                                columns.Bound(c => c.CurrentClientLocationName).Title("Current Location");
                                columns.Bound(c => c.EquipmentItemCustomStatusCode).Title("Custom Status").Hidden(true);
                                columns.Bound(c => c.EquipmentItemPoints).Title("Current Points");
                                columns.Bound(c => c.MaintenanceScheduleDetail).Title("Maintenance Schedule Date(s)").ClientTemplate("#=MaintenanceScheduleDetail#");
                                columns.Bound(c => c.EquipmentItemEquipmentInfo).Title("Info");
                                columns.Bound(c => c.AllStatusDescription).Title("Status").Encoded(false).Filterable(f => f.Operators(o => o.ForString(str => str.Clear().Contains("Contains").DoesNotContain("Does not contain")))).Width(150);
                                columns.Command(c => { 
                                    c.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
                                }).Width(100);
                                })
                                .ColumnMenu(c => c.Columns(true))
                                .Events(e => e.DataBound("updateRunEquipmentItemsTotals"))
                                .Filterable()
                                .Groupable()
                                .ToolBar(t => t.Search())
                                .Search(s => { s.Field(o => o.EquipmentItemName, "contains"); })
                                .Editable(e => e.Mode(GridEditMode.InLine))
                                .Scrollable(s => s.Height(500))
                                .Resizable(resize => resize.Columns(true))
                                .Reorderable(reorder => reorder.Columns(true))
                                .DataSource(dataSource => dataSource
                                .Ajax()
                                .ServerOperation(false)
                                .Events(e => e.RequestEnd("runEquipmentItemsRequestEnd"))
                                .Model(model => {
                                    model.Id(m => m.RunEquipmentItemId);
                                })
                                .Read(read => read.Action("GetRunEquipmentItems", "Operation", new { @runId = Model.RunId, @projectId = Model.JobProjectId }))
                                .Destroy(destroy => destroy.Action("DeleteRunEquipmentItem", "Operation"))))
                        </div>              
                    }
                </text>);

            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind="html:runTriggeredMRsStripText"})
                .Content(@<text>
                    @if (Model.IsJobEngineer || Html.IsOperationAdmin() || Html.IsGlobalAdmin()) {
                        <div id="runTriggeredMRs" data-bind="visible:totalRunTriggeredMRs">
                            <br />
                            @(Html.Kendo().Grid<MaintenanceRecordModel>()
                            .Name("runTriggeredMRGrid")
                            .Columns(columns => {
                                if(!GlobalSettings.IsRegiis)
                                {
                                    columns.Template("#if(IsUnAssigned){#<a class='btn btn-sm btn-primary' onclick = 'startMR(#=MaintenanceRecordId#)'><i class='fa fa-thumbs-up'></i>Start Now</a>#} if(!Pass && !IsClosed && !IsUnAssigned && HasMaintenanceRecordSteps){#<a class='btn btn-sm btn-success' onclick = 'passMR(#=MaintenanceRecordId#)'>Pass</a>#} if(!Fail && !IsClosed && !IsUnAssigned && HasMaintenanceRecordSteps){#<a class='btn btn-sm btn-danger' onclick = 'failMR(#=MaintenanceRecordId#)'>Fail</a>|#} if(!IsClosed && Pass){#<a class='btn btn-sm btn-primary' onclick = 'closeMR(#=MaintenanceRecordId#)'>Close</a>#}#");
                                }
                                columns.Bound(c => c.Number).ClientTemplate("<a href='" + @Url.Action("EditMaintenanceRecord", "Maintenance", new { @id = "" }) + "/#=MaintenanceRecordId#'>#=Number#</a>");
                                columns.Bound(c => c.MaintenanceBlueprintName).Title("Maintenance Blueprint");
                                columns.Bound(c => c.EquipmentItemName).Title("Equipment Item").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#'>#=EquipmentItemName#</a>");
                                columns.Bound(c => c.ServiceImprovementId).Title("Sif").ClientTemplate("<a href='" + @Url.Action("EditServiceImprovement", "QHSE", new { @id = "" }) + "/#=ServiceImprovementId#'>#=ServiceImprovementName ? ServiceImprovementName : ''#</a>").Hidden(true);
                                columns.Bound(c => c.PriorityDescription).Title("Priority").Hidden(true);
                                columns.Bound(c => c.UserName).Title("Created By").Hidden(true);
                                columns.Bound(c => c.EngineerUserName).Title("Assigned Engineer").Hidden(true);
                                columns.Bound(c => c.Modified).Title("Modified").Format(DateConstants.DateTimeFormat).Hidden(true);
                                columns.Bound(c => c.StartDate).Format(DateConstants.DateTimeFormat).Title("Start Date").EditorTemplateName("StartDate");
                                columns.Bound(c => c.CompletedDate).Format(DateConstants.DateTimeFormat).Title("Complete Date").EditorTemplateName("CompletedDate");
                                columns.Bound(c => c.ComplexityDescription).Title("Complexity").Hidden(true);
                                columns.Bound(c => c.OnHoldHours).Title("On Hold Hours").Hidden(true);
                                columns.Bound(c => c.MaintenanceTimeString).Title("Maintenance Time").Hidden(true);
                                columns.Bound(c => c.TotalMaintenanceTimeString).Title("Total Maintenance Time").Hidden(true);
                                columns.Bound(c => c.StatusDescription).Title("Status").ClientTemplate("#if(Pass){#<span class='badge' style='background:#=StatusColour#;color:#=StatustextColour#'>#=NewPassedName#</span>#} else if(Fail){#<span class='badge' style='background:\\#FF0000;color:\\#fff'>#=NewFailedName#</span>#} else{#<span class='badge' style='background:#=StatusColour#;color:#=StatustextColour#'>#=StatusDescription#</span>#}#");
                                columns.Command(command => { 
                                    command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                                }).Width(175);
                                })
                                .ColumnMenu(c => c.Columns(true))
                                .Editable(editable => editable.Mode(GridEditMode.InLine))
                        .Events(e => e.DataBound("updatedMaintenanceRecordGrid").Edit("onEdit"))
                                .Filterable()
                                .Sortable()
                                .Groupable()
                                .Scrollable(s => s.Height(532))
                                .Resizable(resize => resize.Columns(true))
                                .Reorderable(reorder => reorder.Columns(true))
                                .DataSource(dataSource => dataSource
                                .Ajax()
                                .ServerOperation(false)
                                .Model(model => {
                                    model.Id(m => m.MaintenanceRecordId);
                                    model.Field(m => m.Number).Editable(false);
                                    model.Field(m => m.MaintenanceBlueprintName).Editable(false);
                                    model.Field(m => m.EquipmentItemName).Editable(false);
                                    model.Field(m => m.ServiceImprovementId).Editable(false);
                                    model.Field(m => m.PriorityDescription).Editable(false);
                                    model.Field(m => m.UserName).Editable(false);
                                    model.Field(m => m.EngineerUserName).Editable(false);
                                    model.Field(m => m.Modified).Editable(false);
                                    model.Field(m => m.ComplexityDescription).Editable(false);
                                    model.Field(m => m.OnHoldHours).Editable(false);
                                    model.Field(m => m.MaintenanceTimeString).Editable(false);
                                    model.Field(m => m.TotalMaintenanceTimeString).Editable(false);
                                })
                                .Read(read => read.Action("GetMaintenanceRecordsByRunId", "Maintenance").Data("runData"))
                                .Update(update => update.Action("UpdateMaintenanceRecord", "Maintenance").Data("mrData"))))
                        </div>
                     } 
                    else {
                        <div id="runTriggeredMRs" data-bind="visible:totalRunTriggeredMRs">
                            <br />
                            @(Html.Kendo().Grid<MaintenanceRecordModel>()
                            .Name("runTriggeredMRGrid")
                            .Columns(columns => {
                                columns.Bound(c => c.Number).ClientTemplate("<a href='" + @Url.Action("EditMaintenanceRecord", "Maintenance", new { @id = "" }) + "/#=MaintenanceRecordId#'>#=Number#</a>");
                                columns.Bound(c => c.MaintenanceBlueprintName).Title("Maintenance Blueprint");
                                columns.Bound(c => c.EquipmentItemName).Title("Equipment Item").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#'>#=EquipmentItemName#</a>");
                                columns.Bound(c => c.ServiceImprovementId).Title("Sif").ClientTemplate("<a href='" + @Url.Action("EditServiceImprovement", "QHSE", new { @id = "" }) + "/#=ServiceImprovementId#'>#=ServiceImprovementName ? ServiceImprovementName : ''#</a>").Hidden(true);
                                columns.Bound(c => c.PriorityDescription).Title("Priority").Hidden(true);
                                columns.Bound(c => c.UserName).Title("Created By").Hidden(true);
                                columns.Bound(c => c.EngineerUserName).Title("Assigned Engineer").Hidden(true);
                                columns.Bound(c => c.Modified).Title("Modified").Hidden(true).Format(DateConstants.DateTimeFormat);
                                columns.Bound(c => c.StartDate).Title("Start Date").Format(DateConstants.DateTimeFormat);
                                columns.Bound(c => c.CompletedDate).Title("Complete Date").Format(DateConstants.DateTimeFormat);
                                columns.Bound(c => c.ComplexityDescription).Title("Complexity").Hidden(true);
                                columns.Bound(c => c.OnHoldHours).Title("On Hold Hours").Hidden(true);
                                columns.Bound(c => c.MaintenanceTimeString).Title("Maintenance Time").Hidden(true);
                                columns.Bound(c => c.TotalMaintenanceTimeString).Title("Total Maintenance Time").Hidden(true);
                                columns.Bound(c => c.StatusDescription).Title("Status").ClientTemplate("#if(Pass){#<span class='badge' style='background:#=StatusColour#;color:#=StatustextColour#'>#=NewPassedName#</span>#} else if(Fail){#<span class='badge' style='background:\\#FF0000;color:\\#fff'>#=NewFailedName#</span>#} else{#<span class='badge' style='background:#=StatusColour#;color:#=StatustextColour#'>#=StatusDescription#</span>#}#");
                            })
                                .ColumnMenu(c => c.Columns(true))
                                .Events(e => e.DataBound("updatedMaintenanceRecordGrid"))
                                .Filterable()
                                .Sortable()
                                .Groupable()
                                .Scrollable(s => s.Height(532))
                                .Resizable(resize => resize.Columns(true))
                                .Reorderable(reorder => reorder.Columns(true))
                            .DataSource(dataSource => dataSource
                            .Ajax()
                            .ServerOperation(false)
                            .Model(model => {
                                model.Id(m => m.MaintenanceRecordId);
                            })
                            .Read(read => read.Action("GetMaintenanceRecordsByRunId", "Maintenance").Data("runData"))))
                        </div>                         
                    }
                </text>);   
        }
    }
    ))
        @Html.HiddenFor(m => m.RunId)
        @Html.HiddenFor(m => m.JobId)
        @Html.HiddenFor(m => m.JobCreationDate)



<script>
    $(document).ready(function () {
        var tabStrip = $("#assetsStrips").data("kendoTabStrip");

        switch ("@ViewBag.Tab") {
            case "runTriggeredMRs":
                tabStrip.select(2);
                break;
            case "equipmentItems":
                tabStrip.select(1);
                break;
            default:
                tabStrip.select(0);
        }
    });

    function isRunDeleteVisible() {
       return OnProject && !IsTransit && !HasRunClosed ? true : false
    }

    function canDeleteRunTriggeredMR() {
        return data.IsClosed
    }

    const editRunModel = {
        projectId: "@Model.JobProjectId",
        runId: "@Model.RunId",
        booleanFalseString: "@Boolean.FalseString",
        modelIsStandBy: "@Model.IsStandBy",
        name: "@Model.Name",
    }
</script>

<script type="text/x-kendo-tmpl" id="equipmentItemGridHeader">
    <div style="display: flow; width: 100%;">
    <button type="button" class="btn btn-primary" onClick="populateRunItemGridClick()">
        Add to Run
    </button>
    <span class="k-searchbox k-input k-input-md k-rounded-md k-input-solid k-grid-search" style="float:right;width:250px">
        <span class="k-input-icon k-icon k-i-search"></span>
        <input autocomplete="off" placeholder="Search..." title="Search..." class="k-input-inner">
    </span>
    </div>
</script>

<environment include="Development">
    <script src="~/js/views/operation/editRun.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/operation/editRun.min.js" asp-append-version="true"></script>
</environment>