<div class="grid-container-standard">
    @(Html.<PERSON>().Grid<LessonModel>()
        .Name("lessonGrid")
        .Columns(columns => {
            columns.Bound(c => c.Number).ClientTemplate("<a href='" + @Url.Action("EditLesson", "Qhse") + "/#=LessonId#'>#=Number#</a>").Title("Lesson ID");
            columns.Bound(c => c.Name).Title("Title");
            columns.Bound(c => c.LessonCategoryName).Title("Lesson Category");
            columns.Bound(c => c.EquipmentCategories).Title("Equipment Category").ClientTemplate("#=EquipmentCategories ? EquipmentCategories : 'N/A'#");
            columns.Bound(c => c.CompanyName).Title("Client").ClientTemplate("#=CompanyName ? CompanyName : 'N/A'#");
            columns.Bound(c => c.Objectives).Title("Service").ClientTemplate("#=Objectives ? Objectives : 'N/A'#"); ;
            columns.Bound(c => c.StatusDescription).Title("Status");
            columns.Bound(c => c.RaisedByUserName).Title("Raised By").Hidden(true);
        })
        .Sortable()
        .Filterable()
        .Events(e => e.DataBound("updateLessonGrid"))
        .Excel(excel => excel
            .FileName(string.Format("Centerpoint_Lessons_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Qhse"))
        )
        .Groupable()
        .Scrollable()
        .Resizable(c => c.Columns(true))
            .Reorderable(r => r.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Read(read => read.Action("GetLessonsByProjectId", "Operation").Data("lessonData"))
        )
    )
</div>