﻿@model LessonCategoryModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fas fa-money-bill"></i>
        Lesson Category
        (<span data-bind="text:totalLessonCategories"></span>)
    </h4>
</div>
<hr />

@(Html.Kendo().Grid<LessonCategoryModel>()
    .Name("lessonCategoryGrid")
    .Columns(c =>{
        c.Bound(p => p.Name);
        c.Command(command => { 
            command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
            command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
        }).Width(200);
    })
    .Editable(editable => editable.Mode(GridEditMode.InLine))
    .ToolBar(t => {
        t.Create().Text("Add Lesson Category");
    })
    .Sortable()
    .Filterable()
    .Scrollable(s => s.Height(500))
    .Resizable(c => c.Columns(true))
    .ColumnMenu(c => c.Columns(true))
    .Events(e => e.DataBound("updateLessonCategoryTotal"))
    .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Model(m => m.Id(p => p.LessonCategoryId))
            .Events(e => e.Error("onError"))
            .Read(read => read.Action("GetLessonCategories", "Admin"))
            .Create(create => create.Action("UpdateLessonCategories", "Admin"))
            .Update(update => update.Action("UpdateLessonCategories", "Admin"))
            .Destroy(destroy => destroy.Action("DeleteLessonCategory", "Admin"))))

    <script>
        function updateLessonCategoryTotal() {
            var lessonCategoryGrid = $("#lessonCategoryGrid").data("kendoGrid");
            var totalLessonCategories = lessonCategoryGrid.dataSource.total();
            viewModel.set("totalLessonCategories", totalLessonCategories);
        }

        function onError(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var lessonCategoryGrid = $("#lessonCategoryGrid").data("kendoGrid");
                lessonCategoryGrid.dataSource.cancelChanges();
            }
        }

        var viewModel = new kendo.observable({
            totalLessonCategories: 0
        });

        kendo.bind(document.body.children, viewModel);
    </script>
