﻿@model SalesDashboardModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-clock"></i>
        Sales History
    </h4>
</div>
<hr />

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Summary</h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between flex-wrap">
                    <div class="form-group">
                        <label>Closed From</label>
                        <br/>
                        @(Html.Kendo().DatePickerFor(m => m.FromDate).Events(e => e.Change("refreshClosedSalesGrid")).HtmlAttributes(new { @data_bind = "value:fromDate", @style = "width:120px; font-size: 14px" }))
                    </div>                        
                    <div class="form-group">
                        <label>Closed To</label>
                        <br/>
                        @(Html.Kendo().DatePickerFor(m => m.ToDate).Events(e => e.Change("refreshClosedSalesGrid")).HtmlAttributes(new { @data_bind = "value:toDate", @style = "width:120px; font-size: 14px" }))
                    </div>
                </div>
                <div class="d-flex justify-content-between m-1">
                    <span>Total</span>
                    <button data-bind="text:totalClosedSales" class="btn card-list-item-count disabled" style="background: black; color: #fff">
                    </button>
                </div>
                <div class="d-flex justify-content-between m-1">
                    <span>Lead</span>
                    <button data-bind="text:totalClosedLeads" class="btn card-list-item-count disabled" style="background: #BA55D3; color: #fff">
                    </button>
                </div>
                <div class="d-flex justify-content-between m-1">
                    <span>Opportunity</span>
                    <button data-bind="text:totalClosedOpportunities" class="btn card-list-item-count disabled" style="background: #55ADD3; color: #fff">
                    </button>
                </div>                
                <div class="d-flex justify-content-between m-1">
                    <span>Event</span>
                    <button data-bind="text:totalClosedEvents" class="btn card-list-item-count disabled" style="background: #7CBB00; color: #fff">
                    </button>
                </div>   
                <div class="d-flex justify-content-between m-1">
                    <span>Action</span>
                    <button data-bind="text:totalClosedActions" class="btn card-list-item-count disabled" style="background: #4A0000; color: #fff">
                    </button>
                </div>   
            </div>
        </div>
    </div>
</div>
<br />


@(Html.Kendo().TabStrip()
    .Name("salesHistoryStrips")
    .SelectedIndex(0)
    .Animation(false)
    .Items( tabstrip => {

    tabstrip.Add().Text("")
        .HtmlAttributes(new { @data_bind="html:totalStripText"})
        .Selected(true)
        .Content(@<text>

            <div id="closedSales">
                        @(Html.Kendo().Grid<OpportunityModel>()
                    .Name("closedSalesGrid")
                    .Columns(columns => {
                        columns.Bound(c => c.GridName).Title("GridName").ClientTemplate("<a href='" + @Url.Action("Edit", "Sales") + "/#=LinkOpportunityId#?revision=#=MaxRevision#'>#=GridName#</a>");
                        columns.Bound(c => c.ProjectName).Title("Project").ClientTemplate("#if(ProjectName){#<a href='" + @Url.Action("EditProject", "Operation") + "/#=ProjectId#'>#=ProjectName#</a>#} else{#N/A#}#");
                        columns.Bound(c => c.CompanyName).Title("Operator").ClientTemplate("#if(CompanyName){#<a href='" + @Url.Action("EditCompany", "Admin") + "/#=CompanyId#'>#=CompanyName#</a>#}else{#N/A#}#").Hidden(true);
                        columns.Bound(c => c.PartnerCompanyName).Title("Partner").ClientTemplate("#if(PartnerCompanyName){#<a href='" + @Url.Action("EditCompany", "Admin") + "/#=PartnerCompanyId#'>#=PartnerCompanyName#</a>#}else{#N/A#}#").Hidden(true);
                        columns.Bound(c => c.CustomerCompanyName).Title("Customer").ClientTemplate("<a href='" + @Url.Action("EditCompany", "Admin") + "/#=CustomerCompanyId#'>#=CustomerCompanyName#</a>");
                        columns.Bound(c => c.Services).Title("Service").Hidden(true);
                        columns.Bound(c => c.Description).Title("Description").Hidden(true);
                        columns.Bound(c => c.CompanyFields).Title("Fields").ClientTemplate("#=CompanyFields ? CompanyFields : 'N/A'#").Hidden(true);
                        columns.Bound(c => c.CompanyWells).Title("Wells").ClientTemplate("#=CompanyWells ? CompanyWells : 'N/A'#").Hidden(true);
                        columns.Bound(c => c.RigTypeDescription).Title("Rig Type").ClientTemplate("#=RigTypeDescription ? RigTypeDescription : 'N/A'#").Hidden(true);
                        columns.Bound(c => c.CurrencyValue).Title("Currency").ClientTemplate("#=CurrencyValue ? CurrencyValue : 'N/A'#").Hidden(true);
                        columns.Bound(c => c.Value).Title("Value").Format("{0:n2}").Hidden(true);
                        columns.Bound(c => c.Probability).Title("Probability Value").Format("{0:n2}").Hidden(true);
                        columns.Bound(c => c.Conveyances).Title("Conveyance").ClientTemplate("#=Conveyances ? Conveyances : 'N/A'#").Hidden(true);
                        columns.Bound(c => c.CloseoutDate).Title("Closeout").Format(DateConstants.DateFormat).Hidden(true);
                        columns.Bound(c => c.MobilisationDate).Title("Mobilisation").Format(DateConstants.DateFormat).Hidden(true);
                        columns.Bound(c => c.Revision).Title("Revision").Hidden(true);
                        columns.Bound(c => c.Contacts).Title("Contacts").Hidden(true);
                        columns.Bound(c => c.StageDescription).Title("Stage").ClientTemplate("<span class='badge' style='background:#=StageColour#;color:#=StagetextColour#'>#=StageDescription#</span>");
                        columns.Bound(c => c.TypeDescription).Title("Type").ClientTemplate("<span class='badge' style='background:#=TypeColour#;color:#=TypeTextColour#'>#=TypeDescription#</span>").Hidden(true);
                        columns.Bound(c => c.TotalOpportunityAttachments).Title("Attachments").ClientTemplate("#if(TotalOpportunityAttachments){#<a class='badge' style='background:\\#0073D0;color:\\#fff' href='\\#' onclick='opportunityAttachmentCount(#=LinkOpportunityId#)'>#=TotalOpportunityAttachments#</a>#} else {##=TotalOpportunityAttachments##}#").Hidden(true);
                        columns.Bound(c => c.OpportunityEventCount).Title("Events").ClientTemplate("#if(OpportunityEventCount){#<a class='badge' style='background:\\#0073D0;color:\\#fff' href='\\#' onclick='opportunityEventCount(#=LinkOpportunityId#)'>#=OpportunityEventCount#</a>#} else {##=OpportunityEventCount##}#");
                        columns.Bound(c => c.OpportunityActionCount).Title("Actions").ClientTemplate("#if(OpportunityActionCount && IsActionUpcoming){#<a class='badge' style='background:\\#F7A54A;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else if(OpportunityActionCount && IsActionRegistered){#<a class='badge' style='background:\\#7CBB00;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else if(OpportunityActionCount && IsActionOverdue){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else if(OpportunityActionCount && IsActionCompleted) {#<a class='badge' style='background:\\#7CBB00;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else {##=OpportunityActionCount##}#");
                        columns.Bound(c => c.Closed).Title("Closed Date").Format(DateConstants.DateFormat);
                        columns.Bound(c => c.OpportunityClosedReasonName).Title("Closure Reason").Hidden(true);
                        @* columns.Bound(c => c.ClosedReasonComment).Title("Closure Reason Comment").Hidden(true); *@
                        columns.Bound(c => c.TrimmedClosedReason).Title("Closure Reason Comment").ClientTemplate("#if(IsClosedReasonLength){#<a href='\\#' onclick=\"showClosedReason()\">#=TrimmedClosedReason#</a>#} else {##=TrimmedClosedReason##}#").Hidden(true);
                        columns.Bound(c => c.Created).Title("Created Date").Format(DateConstants.DateTimeFormat);
                        columns.Bound(c => c.CreatedBy).Title("Created By");
                    })
                    .ToolBar(t => {
                        t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetClosedSalesGrid", @class="bg-danger text-white"});
                        t.Excel().Text("Export");
                    }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
                    .ColumnMenu(c => c.Columns(true))
                    .Sortable()
                    .Groupable()
                    .Filterable()
                    .Scrollable(s => s.Height("auto"))
                    .Events(e => e.DataBound("updateClosedSalesGrid").ColumnReorder("saveClosedSalesGrid").ColumnResize("saveClosedSalesGrid").ColumnShow("saveClosedSalesGrid").ColumnHide("saveClosedSalesGrid"))
                    .Excel(excel => excel
                    .FileName(string.Format("Centerpoint_Closed_Sales_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                    .Filterable(true)
                    .ProxyURL(Url.Action("Export", "Admin")))
                    .DataSource(dataSource => dataSource
                    .Ajax()
                    .ServerOperation(false)
                    .Model(model => {
                        model.Id(m => m.OpportunityId);
                    })
                    .Read(read => read.Action("GetAllClosedLeadsAndOpportunities", "Sales").Data("closedLeadOpportunityData"))))
            </div>
        </text>);

    tabstrip.Add().Text("")
        .HtmlAttributes(new { @data_bind="html:leadsStripText"})
        .Content(@<text>

            <div id="closedLeads">
                        @(Html.Kendo().Grid<OpportunityModel>()
                    .Name("closedLeadsGrid")
                    .Columns(columns => {
                        columns.Bound(c => c.Name).Title("Lead ID").ClientTemplate("<a href='" + @Url.Action("Edit", "Sales") + "/#=LinkOpportunityId#?revision=#=MaxRevision#'>#=GridName#</a>");
                        columns.Bound(c => c.CompanyName).Title("Operator").ClientTemplate("#if(CompanyName){#<a href='" + @Url.Action("EditCompany", "Admin") + "/#=CompanyId#'>#=CompanyName#</a>#}else{#N/A#}#").Hidden(true);
                        columns.Bound(c => c.PartnerCompanyName).Title("Partner").ClientTemplate("#if(PartnerCompanyName){#<a href='" + @Url.Action("EditCompany", "Admin") + "/#=PartnerCompanyId#'>#=PartnerCompanyName#</a>#}else{#N/A#}#").Hidden(true);
                        columns.Bound(c => c.CustomerCompanyName).Title("Customer").ClientTemplate("<a href='" + @Url.Action("EditCompany", "Admin") + "/#=CustomerCompanyId#'>#=CustomerCompanyName#</a>").Hidden(true);
                        columns.Bound(c => c.Services).Title("Service").Hidden(true);
                        columns.Bound(c => c.Description).Title("Description").Hidden(true);
                        columns.Bound(c => c.Revision).Title("Revision").Hidden(true);
                        columns.Bound(c => c.Created).Title("Created Date").Format(DateConstants.DateTimeFormat);
                        columns.Bound(c => c.Modified).Title("Last Updated").Format(DateConstants.DateFormat);
                        columns.Bound(c => c.Contacts).Title("Contacts");
                        columns.Bound(c => c.TotalLeadAttachments).Title("Attachments").ClientTemplate("#if(TotalLeadAttachments){#<a class='badge' style='background:\\#0073D0;color:\\#fff' href='\\#' onclick='opportunityAttachmentCount(#=LinkOpportunityId#)'>#=TotalLeadAttachments#</a>#} else {##=TotalLeadAttachments##}#");
                        columns.Bound(c => c.OpportunityEventCount).Title("Events").ClientTemplate("#if(OpportunityEventCount){#<a class='badge' style='background:\\#0073D0;color:\\#fff' href='\\#' onclick='opportunityEventCount(#=LinkOpportunityId#)'>#=OpportunityEventCount#</a>#} else {##=OpportunityEventCount##}#");
                        columns.Bound(c => c.OpportunityActionCount).Title("Actions").ClientTemplate("#if(OpportunityActionCount && IsActionUpcoming){#<a class='badge' style='background:\\#F7A54A;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else if(OpportunityActionCount && IsActionRegistered){#<a class='badge' style='background:\\#e5e500 ;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else if(OpportunityActionCount && IsActionOverdue){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else if(OpportunityActionCount && IsActionCompleted) {#<a class='badge' style='background:\\#7CBB00;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=OpportunityId#)'>#=OpportunityActionCount#</a>#} else {##=OpportunityActionCount##}#");
                        columns.Bound(c => c.CreatedBy).Title("Created By");
                        columns.Bound(c => c.Closed).Title("Closed Date").Format(DateConstants.DateFormat);
                    })
                    .ToolBar(t => {
                        t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetClosedLeadsGrid", @class="bg-danger text-white"});
                        t.Excel().Text("Export");
                    }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
                    .ColumnMenu(c => c.Columns(true))
                    .Sortable()
                    .Groupable()
                    .Filterable()
                    .Scrollable(s => s.Height("auto"))
                    .Events(e => e.DataBound("updateClosedLeadsGrid").ColumnReorder("saveClosedLeadsGrid").ColumnResize("saveClosedLeadsGrid").ColumnShow("saveClosedLeadsGrid").ColumnHide("saveClosedLeadsGrid"))
                    .Excel(excel => excel
                    .FileName(string.Format("Centerpoint_Closed_Leads_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                    .Filterable(true)
                    .ProxyURL(Url.Action("Export", "Admin")))
                    .DataSource(dataSource => dataSource
                    .Ajax()
                    .ServerOperation(false)
                    .Model(model => {
                        model.Id(m => m.OpportunityId);
                    })
                    .Read(read => read.Action("GetAllClosedLeads", "Sales").Data("closedLeadOpportunityData"))))
            </div>
        </text>);

    tabstrip.Add().Text("")
        .HtmlAttributes(new { @data_bind="html:opportunitiesStripText"})
        .Content(@<text>

            <div id="closedOpportunities">
                        @(Html.Kendo().Grid<OpportunityModel>()
                    .Name("closedOpportunitiesGrid")
                    .Columns(columns => {
                        columns.Bound(c => c.GridName).Title("Opportunity ID").ClientTemplate("<a href='" + @Url.Action("Edit", "Sales") + "/#=LinkOpportunityId#?revision=#=MaxRevision#'>#=GridName#</a>");
                        columns.Bound(c => c.ProjectName).Title("Project").ClientTemplate("#if(ProjectName){#<a href='" + @Url.Action("EditProject", "Operation") + "/#=ProjectId#'>#=ProjectName#</a>#}else{#N/A#}#");
                        columns.Bound(c => c.CompanyName).Title("Operator").ClientTemplate("#if(CompanyName){#<a href='" + @Url.Action("EditCompany", "Admin") + "/#=CompanyId#'>#=CompanyName#</a>#}else{#N/A#}#").Hidden(true);
                        columns.Bound(c => c.PartnerCompanyName).Title("Partner").ClientTemplate("#if(PartnerCompanyName){#<a href='" + @Url.Action("EditCompany", "Admin") + "/#=PartnerCompanyId#'>#=PartnerCompanyName#</a>#}else{#N/A#}#").Hidden(true);
                        columns.Bound(c => c.CustomerCompanyName).Title("Customer").ClientTemplate("<a href='" + @Url.Action("EditCompany", "Admin") + "/#=CustomerCompanyId#'>#=CustomerCompanyName#</a>");
                        columns.Bound(c => c.Services).Title("Service").Hidden(true);
                        columns.Bound(c => c.Description).Title("Description").Hidden(true);
                        columns.Bound(c => c.CompanyFields).Title("Fields").ClientTemplate("#=CompanyFields ? CompanyFields : 'N/A'#");
                        columns.Bound(c => c.CompanyWells).Title("Wells").ClientTemplate("#=CompanyWells ? CompanyWells : 'N/A'#");
                        columns.Bound(c => c.RigTypeDescription).Title("Rig Type").ClientTemplate("#=RigTypeDescription ? RigTypeDescription : 'N/A'#").Hidden(true);
                        columns.Bound(c => c.CurrencyValue).Title("Currency").ClientTemplate("#=CurrencyValue ? CurrencyValue : 'N/A'#").Hidden(true);
                        columns.Bound(c => c.Value).Title("Value").Format("{0:n2}").Hidden(true);
                        columns.Bound(c => c.Probability).Title("Probability Value").Format("{0:n2}").Hidden(true);
                        columns.Bound(c => c.Conveyances).Title("Conveyance").ClientTemplate("#=Conveyances ? Conveyances : 'N/A'#");
                        columns.Bound(c => c.CloseoutDate).Title("Closeout").Format(DateConstants.DateFormat);
                        columns.Bound(c => c.MobilisationDate).Title("Mobilisation").Format(DateConstants.DateFormat);
                        columns.Bound(c => c.Revision).Title("Revision").Hidden(true);
                        columns.Bound(c => c.Contacts).Title("Contacts");
                        columns.Bound(c => c.TotalOpportunityAttachments).Title("Attachments").ClientTemplate("#if(TotalOpportunityAttachments){#<a class='badge' style='background:\\#0073D0;color:\\#fff' href='\\#' onclick='opportunityAttachmentCount(#=LinkOpportunityId#)'>#=TotalOpportunityAttachments#</a>#} else {##=TotalOpportunityAttachments##}#");
                        columns.Bound(c => c.OpportunityEventCount).Title("Events").ClientTemplate("#if(OpportunityEventCount){#<a class='badge' style='background:\\#0073D0;color:\\#fff' href='\\#' onclick='opportunityEventCount(#=LinkOpportunityId#)'>#=OpportunityEventCount#</a>#} else {##=OpportunityEventCount##}#");
                        columns.Bound(c => c.OpportunityActionCount).Title("Actions").ClientTemplate("#if(OpportunityActionCount && IsActionUpcoming){#<a class='badge' style='background:\\#F7A54A;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else if(OpportunityActionCount && IsActionRegistered){#<a class='badge' style='background:\\#e5e500 ;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else if(OpportunityActionCount && IsActionOverdue){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else if(OpportunityActionCount && IsActionCompleted) {#<a class='badge' style='background:\\#7CBB00;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else {##=OpportunityActionCount##}#");
                        columns.Bound(c => c.Closed).Title("Closed Date").Format(DateConstants.DateFormat);
                        columns.Bound(c => c.OpportunityClosedReasonName).Title("Closure Reason").Hidden(true);
                        //columns.Bound(c => c.ClosedReasonComment).Title("Closure Reason Comment").Hidden(true);
                        columns.Bound(c => c.TrimmedClosedReason).Title("Closure Reason Comment").ClientTemplate("#if(IsClosedReasonLength){#<a href='\\#' onclick=\"showReason()\">#=TrimmedClosedReason#</a>#} else {##=TrimmedClosedReason##}#").Hidden(true);
                        columns.Bound(c => c.Created).Title("Created Date").Format(DateConstants.DateTimeFormat).Hidden(true);
                        columns.Bound(c => c.CreatedBy).Title("Created By").Hidden(true);
                    })
                    .ToolBar(t => {
                        t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetClosedOpportunitiesGrid", @class="bg-danger text-white"});
                        t.Excel().Text("Export");
                    }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
                    .ColumnMenu(c => c.Columns(true))
                    .Sortable()
                    .Groupable()
                    .Filterable()
                    .Reorderable(c => c.Columns(true))
                    .Scrollable(s => s.Height("auto"))
                    .Events(e => e.DataBound("updateClosedOpportunityGrid").ColumnReorder("saveClosedOpportunityGrid").ColumnResize("saveClosedOpportunityGrid").ColumnShow("saveClosedOpportunityGrid").ColumnHide("saveClosedOpportunityGrid"))
                    .Excel(excel => excel
                    .FileName(string.Format("Centerpoint_Sales_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                    .Filterable(true)
                    .ProxyURL(Url.Action("Export", "Admin")))
                    .DataSource(dataSource => dataSource
                    .Ajax()
                    .ServerOperation(false)
                    .Model(model => {
                        model.Id(m => m.OpportunityId);
                    })
                    .Read(read => read.Action("GetAllClosedOpportunities", "Sales").Data("closedLeadOpportunityData"))))
            </div>
        </text>);

    tabstrip.Add().Text("")
        .HtmlAttributes(new { @data_bind="html:eventsStripText"})
        .Content(@<text>
            <div id="closedEvents">
                @(Html.Kendo().Grid<OpportunityEventModel>()
                    .Name("closedEventsGrid")
                    .Columns(c => {
                        c.Bound(p => p.NewName).Title("Event ID").ClientTemplate("<a href='" + @Url.Action("EditEvent", "Sales") + "/#=OpportunityEventId#'>#=NewName#</a>");
                        c.Bound(p => p.OpportunityName).Title("Lead / Opportunity ID").ClientTemplate("#if(OpportunityName){#<a href='" + @Url.Action("Edit", "Sales") + "/#=LinkOpportunityId#?revision=#=MaxRevision#'>#=GridName#</a>#} else{#N/A#}#");
                        c.Bound(p => p.GridCompanyName).Title("Company");
                        c.Bound(p => p.CompanyContacts).Title("Contact");
                        c.Bound(p => p.Topic).Title("Topic").ClientTemplate("#if(IsTopicLength){#<a href='\\#' onclick=\"showTopic()\">#=TrimmedTopic#</a>#} else {##=Topic##}#");
                        c.Bound(p => p.EventDate).Title("Event Date").Format(DateConstants.DateTimeFormat);
                        c.Bound(p => p.TotalEventActionCount).Title("Follow-up Actions").ClientTemplate("#if(TotalEventActionCount && IsActionUpcoming){#<a class='badge' style='background:\\#F7A54A;color:\\#fff' href='\\#' onclick='followUpActionCount(#=OpportunityEventId#)'>#=TotalEventActionCount#</a>#} else if(TotalEventActionCount && IsActionRegistered){#<a class='badge' style='background:\\#e5e500 ;color:\\#fff' href='\\#' onclick='followUpActionCount(#=OpportunityEventId#)'>#=TotalEventActionCount#</a>#} else if(TotalEventActionCount && IsActionOverdue){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='followUpActionCount(#=OpportunityEventId#)'>#=TotalEventActionCount#</a>#} else if(TotalEventActionCount && IsActionCompleted) {#<a class='badge' style='background:\\#7CBB00;color:\\#fff' href='\\#' onclick='followUpActionCount(#=OpportunityEventId#)'>#=TotalEventActionCount#</a>#} else {##=TotalEventActionCount##}#");
                        c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
                        c.Bound(p => p.CreatedBy).Title("Created By");
                    })
                    .ToolBar(t => {
                        t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetClosedEventsGrid", @class="bg-danger text-white"});
                        t.Excel().Text("Export");
                    }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
                    .Events(e => e.DataBound("updateClosedEventsGrid").ColumnReorder("saveClosedEventGrid").ColumnResize("saveClosedEventGrid").ColumnShow("saveClosedEventGrid").ColumnHide("saveClosedEventGrid"))
                    .Sortable()
                    .Resizable(r => r.Columns(true))
                    .ColumnMenu(c => c.Columns(true))
                    .Filterable()
                    .Groupable()
                    .Scrollable(s => s.Height(300))
                    .DataSource(dataSource => dataSource
                        .Ajax()
                        .ServerOperation(false)
                        .Model(model => model.Id(p => p.OpportunityEventId))
                        .Read(read => read.Action("GetAllClosedLeadOpportunityEvents", "Sales"))))
            </div>

        </text>);

    tabstrip.Add().Text("")
        .HtmlAttributes(new { @data_bind="html:actionsStripText"})
        .Content(@<text>
            <div id="closedActions">
                @(Html.Kendo().Grid<OpportunityActionModel>()
                    .Name("closedActionsGrid")
                    .Columns(c => {
                        c.Bound(p => p.NewName).Title("Action ID").ClientTemplate("<a href='" + @Url.Action("EditAction", "Sales", new { @id = "" }) + "/#=OpportunityActionId#?revision=#=MaxRevision#'>#=NewName#</a>"); ;
                        c.Bound(p => p.OpportunityName).Title("Lead / Opportunity ID").ClientTemplate("#if(OpportunityName){#<a href='" + @Url.Action("Edit", "Sales") + "/#=LinkOpportunityId#?revision=#=MaxRevision#'>#=GridName#</a>#} else{#N/A#}#");
                        c.Bound(p => p.GridCompanyName).Title("Company");
                        c.Bound(p => p.AssignedUserName).Title("Assignee");
                        c.Bound(p => p.TargetDate).Title("Target Date").Format(DateConstants.DateFormat);
                        c.Bound(p => p.CompletedDate).Title("Completed Date").Format(DateConstants.DateFormat);
                        c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
                        c.Bound(p => p.CreatedBy).Title("Created By");
                        c.Bound(p => p.Status).Title("Status").ClientTemplate("<span class='badge' style='background:#=StatusColor#;color:#=StatusTextColor#'>#=Status#</span>");
                    })
                    .ToolBar(t => {
                        t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetClosedActionsGrid", @class="bg-danger text-white"});
                        t.Excel().Text("Export");
                    }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
                    .Events(e => e.DataBound("updateClosedActionsGrid").ColumnReorder("saveClosedActionGrid").ColumnResize("saveClosedActionGrid").ColumnShow("saveClosedActionGrid").ColumnHide("saveClosedActionGrid"))
                    .Sortable()
                    .Resizable(r => r.Columns(true))
                    .ColumnMenu(c => c.Columns(true))
                    .Filterable()
                    .Groupable()
                    .Scrollable(s => s.Height(300))
                    .DataSource(dataSource => dataSource
                        .Ajax()
                        .ServerOperation(false)
                        .Model(model => model.Id(p => p.OpportunityActionId))
                        .Read(read => read.Action("GetAllClosedLeadOpportunityActions", "Sales"))))
            </div>
        </text>);
    }))



@(Html.Kendo().Window().Name("opportunityEventWindow")
.Width(1000)
.Height(500)
.Title("Events")
.Visible(false)
.Modal(true)
.Events(e => e.Open("opportunityEventWindowOpened"))
.Content(@<text>
        @(Html.Kendo().Grid<OpportunityEventModel>()
            .Name("opportunityEventsGrid")
            .Columns(c => {
                c.Bound(p => p.NewName).Title("Event ID").ClientTemplate("<a href='" + @Url.Action("EditEvent", "Sales") + "/#=OpportunityEventId#'>#=NewName#</a>"); ;
                c.Bound(p => p.OpportunityName).Title("Lead / Opportunity ID").ClientTemplate("<a href='" + @Url.Action("Edit", "Sales") + "/#=LinkOpportunityId#'>#=GridName#</a>");
                c.Bound(p => p.CompanyContacts).Title("Contact");
                c.Bound(p => p.Topic).Title("Topic").ClientTemplate("#if(IsTopicLength){#<a href='\\#' onclick=\"showOppTopic()\">#=TrimmedTopic#</a>#} else {##=Topic##}#"); ;
                c.Bound(p => p.EventDate).Title("Event Date").Format(DateConstants.DateTimeFormat);
                c.Bound(p => p.TotalEventActionCount).Title("Total Actions").ClientTemplate("#if(TotalEventActionCount && IsActionUpcoming){#<a class='badge' style='background:\\#F7A54A;color:\\#fff' href='\\#' onclick='followUpActionCount(#=OpportunityId#)'>#=TotalEventActionCount#</a>#} else if(TotalEventActionCount && IsActionRegistered){#<a class='badge' style='background:\\#e5e500;color:\\#fff' href='\\#' onclick='followUpActionCount(#=OpportunityId#)'>#=TotalEventActionCount#</a>#} else if(TotalEventActionCount && IsActionOverdue){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='followUpActionCount(#=OpportunityId#)'>#=TotalEventActionCount#</a>#} else if(TotalEventActionCount && IsActionCompleted) {#<a class='badge' style='background:\\#7CBB00;color:\\#fff' href='\\#' onclick='followUpActionCount(#=OpportunityId#)'>#=TotalEventActionCount#</a>#} else {##=TotalEventActionCount##}#");
                c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
                c.Bound(p => p.CreatedBy).Title("Created By");
            })
             .Events(e => e.DataBound("updateOpportunityEventsGrid"))
             .Sortable()
             .Resizable(r => r.Columns(true))
              .ColumnMenu(c => c.Columns(true))
             .Filterable()
             .Groupable()
             .Scrollable(s => s.Height(400))
             .DataSource(dataSource => dataSource
                 .Ajax()
                 .ServerOperation(false)
                 .Model(model => model.Id(p => p.OpportunityEventId))
                 .Read(read => read.Action("GetAllEventsByOpportunityId", "Sales").Data("opportunityData")))) </text>
         ))

@(Html.Kendo().Window().Name("opportunityActionWindow")
.Width(1000)
.Height(500)
.Title("Actions")
.Visible(false)
.Modal(true)
.Events(e => e.Open("opportunityActionWindowOpened"))
.Content(@<text>
        @(Html.Kendo().Grid<OpportunityActionModel>()
            .Name("opportunityActionsGrid")
            .Columns(c => {
                c.Bound(p => p.NewName).Title("Action ID").ClientTemplate("<a href='" + @Url.Action("EditAction", "Sales", new { @id = "" }) + "/#=OpportunityActionId#'>#=NewName#</a>"); ;
                c.Bound(p => p.OpportunityName).Title("Lead / Opportunity ID").ClientTemplate("<a href='" + @Url.Action("Edit", "Sales") + "/#=LinkOpportunityId#'>#=GridName#</a>");
                c.Bound(p => p.AssignedUserName).Title("Assignee");
                c.Bound(p => p.TargetDate).Title("Target Date").Format(DateConstants.DateFormat);
                c.Bound(p => p.CompletedDate).Title("Completed Date").Format(DateConstants.DateFormat);
                c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
                c.Bound(p => p.CreatedBy).Title("Created By");
                c.Bound(p => p.Status).Title("Status").ClientTemplate("<span class='badge' style='background:#=StatusColor#;color:#=StatusTextColor#'>#=Status#</span>");
            })
             .Events(e => e.DataBound("updateOpportunityActionsGrid"))
             .Sortable()
             .Resizable(r => r.Columns(true))
              .ColumnMenu(c => c.Columns(true))
             .Filterable()
             .Groupable()
             .Scrollable(s => s.Height(400))
             .DataSource(dataSource => dataSource
                 .Ajax()
                 .ServerOperation(false)
                 .Model(model => model.Id(p => p.OpportunityActionId))
                 .Read(read => read.Action("GetAllActionsByOpportunityId", "Sales").Data("opportunityData"))))</text>
         ))

@(Html.Kendo().Window().Name("opportunityAttachmentWindow")
.Width(1000)
.Title("Attachments")
.Visible(false)
.Modal(true)
.Events(e => e.Open("opportunityAttachmentWindowOpened"))
.Content(@<text>
<ul class="nav nav-tabs" id="opportunityAttachmentWindowTab" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="pill1-tab" data-bs-toggle="tab" data-bs-target="#pill1" type="button" role="tab" aria-controls="pill1" aria-selected="true">
            General (<span data-bind="text:totalOpportunityDocuments"></span>)
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="pill2-tab" data-bs-toggle="tab" data-bs-target="#pill2" type="button" role="tab" aria-controls="pill2" aria-selected="false">
            Events (<span data-bind="text:totalOpportunityEventDocuments"></span>)
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="pill3-tab" data-bs-toggle="tab" data-bs-target="#pill3" type="button" role="tab" aria-controls="pill3" aria-selected="false">
            Actions (<span data-bind="text:totalOpportunityActionDocuments"></span>)
        </button>        
    </li>
    <li class="nav-item" role="presentation" data-bind="visible:totalOpportunityWellDocuments">
        <button class="nav-link" id="pill4-tab" data-bs-toggle="tab" data-bs-target="#pill4" type="button" role="tab" aria-controls="pill4" aria-selected="false">
            Wells (<span data-bind="text:totalOpportunityWellDocuments"></span>)
        </button>  
    </li>
</ul>
<div class="tab-content" id="opportunityAttachmentWindowTab">
    <div class="tab-pane show active" id="pill1" role="tabpanel" aria-labelledby="pill1-tab">
        <br />
        <div class="card">
            <div class="card-header">
                <h6 class=""></h6>
            </div>
            <div class="card-body">
                @(Html.Kendo().Grid<DocumentModel>()
                    .Name("opportunityDocumentsGrid")
                    .Columns(c => {
                        c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                        c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
                        c.Bound(p => p.UserName).Title("Created By");
                    })
                    .Sortable()
                    .Events(e => e.DataBound("updateOpportunityDocumentsGrid"))
                    .Resizable(r => r.Columns(true))
                    .ColumnMenu(c => c.Columns(true))
                    .Filterable()
                    .Groupable()
                    .Scrollable(s => s.Height(300))
                    .DataSource(dataSource => dataSource
                    .Ajax()
                    .ServerOperation(false)
                    .Model(model => model.Id(p => p.DocumentId))
                    .Read(read => read.Action("GetOpportunityDocuments", "Sales").Data("opportunityData"))))
            </div>
        </div>
    </div>
    <div class="tab-pane fade" id="pill2" role="tabpanel" aria-labelledby="pill2-tab">
        <br />
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"></h6>
            </div>
            <div class="card-body">
                @(Html.Kendo().Grid<OpportunityEventDocumentModel>()
                    .Name("opportunityEventDocumentsGrid")
                    .Columns(c => {
                        c.Bound(p => p.Event).Title("Event").ClientGroupHeaderTemplate("Event : #= value # (#= count#)").Width(200).Hidden(true);
                        c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                        c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateFormat);
                        c.Bound(p => p.Username).Title("Created By");
                    })
                    .Sortable()
                    .Events(e => e.DataBound("updateOpportunityEventDocumentsGrid"))
                    .Resizable(r => r.Columns(true))
                    .ColumnMenu(c => c.Columns(true))
                    .Editable(e => e.Mode(GridEditMode.InLine))
                    .Filterable()
                    .Groupable()
                    .Scrollable(s => s.Height(300))
                    .DataSource(dataSource => dataSource
                    .Ajax()
                    .ServerOperation(false)
                    .Aggregates(aggregates => {
                        aggregates.Add(p => p.Event).Min().Max().Count();
                    })
                    .Group(group => group.Add(p => p.Event))
                    .Model(model => model.Id(p => p.DocumentId))
                    .Read(read => read.Action("GetOpportunityEventDocumentsByOpportunityId", "Sales").Data("opportunityData"))))
            </div>
        </div>
    </div>
    <div class="tab-pane fade" id="pill3" role="tabpanel" aria-labelledby="pill3-tab">
        <br />
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"></h6>
            </div>
            <div class="card-body">
                @(Html.Kendo().Grid<OpportunityActionDocumentModel>()
            .Name("opportunityActionDocumentsGrid")
            .Columns(c => {
                c.Bound(p => p.Action).Title("Action").ClientGroupHeaderTemplate("Action : #= value # (#= count#)").Width(200).Hidden(true);
                c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateFormat);
                c.Bound(p => p.Username).Title("Created By");
            })
             .Events(e => e.DataBound("updateOpportunityActionDocumentsGrid"))
             .Editable(e => e.Mode(GridEditMode.InLine))
             .Sortable()
             .Resizable(r => r.Columns(true))
             .ColumnMenu(c => c.Columns(true))
             .Filterable()
             .Groupable()
             .Scrollable(s => s.Height(300))
             .DataSource(dataSource => dataSource
                 .Ajax()
                 .ServerOperation(false)
                 .Aggregates(aggregates => {
                     aggregates.Add(p => p.Action).Min().Max().Count();
                 })
                 .Group(group => group.Add(p => p.Action))
                 .Model(model => model.Id(p => p.DocumentId))
                 .Read(read => read.Action("GetOpportunityActionDocumentsByOpportunityId", "Sales").Data("opportunityData"))))
            </div>
        </div>
    </div>
    <div class="tab-pane fade" id="pill4" data-bind="visible:totalOpportunityWellDocuments" role="tabpanel" aria-labelledby="pill4-tab">
        <br />
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"></h6>
            </div>
            <div class="card-body">
                @(Html.Kendo().Grid<CompanyWellDocumentModel>()
             .Name("wellDocumentsGrid")
             .Columns(c => {
                 c.Bound(p => p.CompanyWellName).Title("Well").ClientGroupHeaderTemplate("Well : #= value # (#= count#)").Width(200).Hidden(true);
                 c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                 c.Bound(p => p.CompanyWellDocumentTypeDescription).Title("Type").ClientTemplate("#=CompanyWellDocumentTypeDescription ? CompanyWellDocumentTypeDescription : 'N/A'#");
                 c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateFormat);
                 c.Bound(p => p.Username).Title("Created By");
             })
              .Events(e => e.DataBound("updateWellDocumentsGrid"))
              .Sortable()
              .Resizable(r => r.Columns(true))
              .ColumnMenu(c => c.Columns(true))
              .Filterable()
              .Groupable()
              .Scrollable(s => s.Height(300))
              .DataSource(dataSource => dataSource
              .Ajax()
              .ServerOperation(false)
              .Aggregates(aggregates => {
                  aggregates.Add(p => p.CompanyWellName).Min().Max().Count();
              })
              .Group(group => group.Add(p => p.CompanyWellName))
              .Model(model => model.Id(p => p.CompanyWellDocumentId))
              .Read(read => read.Action("GetCompanyWellDocumentByOpportunityId", "Sales").Data("opportunityData"))))
            </div>
        </div>
    </div>
</div>
</text>
         ))

@(Html.Kendo().Window().Name("followUpActionWindow")
.Width(1000)
.Height(500)
.Title("Follow-up Actions")
.Visible(false)
.Modal(true)
.Events(e => e.Open("followUpActionWindowOpened"))
.Content(@<text>
        @(Html.Kendo().Grid<OpportunityActionModel>()
            .Name("followUpActionsGrid")
            .Columns(c => {
                c.Bound(p => p.NewName).Title("Action ID").ClientTemplate("<a href='" + @Url.Action("EditAction", "Sales", new { @id = "" }) + "/#=OpportunityActionId#'>#=NewName#</a>"); ;
                c.Bound(p => p.OpportunityName).Title("Lead / Opportunity ID").ClientTemplate("<a href='" + @Url.Action("Edit", "Sales") + "/#=LinkOpportunityId#'>#=GridName#</a>");
                c.Bound(p => p.AssignedUserName).Title("Assignee");
                c.Bound(p => p.TargetDate).Title("Target Date").Format(DateConstants.DateFormat);
                c.Bound(p => p.CompletedDate).Title("Completed Date").Format(DateConstants.DateFormat);
                c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
                c.Bound(p => p.CreatedBy).Title("Created By");
                c.Bound(p => p.Status).Title("Status").ClientTemplate("<span class='badge' style='background:#=StatusColor#;color:#=StatusTextColor#'>#=Status#</span>");
            })
             .Events(e => e.DataBound("updateFollowUpActionsGrid"))
             .Sortable()
             .Resizable(r => r.Columns(true))
             .Filterable()
             .Groupable()
             .Scrollable(s => s.Height(400))
             .DataSource(dataSource => dataSource
                 .Ajax()
                 .ServerOperation(false)
                 .Model(model => model.Id(p => p.OpportunityActionId))
                 .Read(read => read.Action("GetAllFollowupActionsByOpportunityEventId", "Sales").Data("opportunityEventData"))))</text>
         ))

<script>
    const salesHistoryModel = {
        modelFromDate: "@Model.FromDate",
        modelToDate: "@Model.ToDate",
    }
</script>

<environment include="Development">
    <script src="~/js/views/sales/salesHistory.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/sales/salesHistory.min.js" asp-append-version="true"></script>
</environment>