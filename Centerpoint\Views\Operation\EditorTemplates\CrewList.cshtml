﻿@model int

@(Html.<PERSON>()
 .DropDownListFor(m => m)
 .DataValueField("UserId")
 .DataTextField("Name")
 .Filter(FilterType.Contains)
 .OptionLabel("Select Engineer")
  .Events(e =>
  {
      e.Change("onCrewChange");
  })
 .HtmlAttributes(new {@id = "crewUserId", @style = "width:100%", @data_value_primitive = "true" })
 .DataSource(dataSource => dataSource.Read("GetFieldEngineers", "Lookup")))
    