<form id="customerLocationForm">
    <div>
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label>Offshore</label>
                    <br />
                    @(Html.Kendo().DropDownList()
                    .Name("IsOffshore")
                    .DataValueField("Key")
                    .Filter("contains")                 
                    .DataTextField("Value")                 
                    .Events(e => {
                        e.Change("offshoreChanged");
                    })                 
                    .BindTo(new List<KeyValuePair<string, string>> {
                        new KeyValuePair<string,string>("false", "No"),
                        new KeyValuePair<string,string>("true", "Yes")
                    })
                    .Value("false")
                    .HtmlAttributes(new { @data_value_primitive="true"}))
                </div>
            </div>
        </div>
        <hr />
        <br />
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label>Name</label>
                    <br />
                    @(Html.Kendo().TextBox().Name("Name").HtmlAttributes(new { @class = "form-control", @required="required", @style = "font-size: 14px;", tabindex = 1 }))
                </div>
                <div class="form-group">
                    <label>Address Line 1 (Number)</label>
                    <br />
                    @(Html.Kendo().TextBox().Name("HouseNumber").HtmlAttributes(new { @class = "form-control", @style = "font-size: 14px;", tabindex = 2 }))
                </div>
                    <div class="form-group">
                        <label>Address Line 2 (Street)</label>
                        <br />
                        @(Html.Kendo().TextBox().Name("Street").HtmlAttributes(new { @class = "form-control", @required="required", @style = "font-size: 14px;", tabindex = 3 }))
                    </div>
                <div class="form-group">
                    <label>Address Line 3 (City)</label>
                    <br />
                    @(Html.Kendo().TextBox().Name("City").HtmlAttributes(new { @class = "form-control", @required="required", @style = "font-size: 14px;", tabindex = 4 }))
                </div>
                <div class="form-group">
                    <label>Address Line 4 (Postcode)</label>
                    <br />
                    @(Html.Kendo().TextBox().Name("Postcode").HtmlAttributes(new { @class = "form-control", @style = "font-size: 14px;", tabindex = 5 }))
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label>Telephone</label>
                    <br />
                    @(Html.Kendo().TextBox().Name("Telephone").HtmlAttributes(new { @class = "form-control", @style = "font-size: 14px;", tabindex = 6 }))
                </div>
                <div class="form-group">
                    <label>Country</label>
                    <br/>
                    @(Html.Kendo().AutoComplete()
                    .Name("Country")
                    .Filter("startswith")                 
                    .Placeholder("Select country...")
                    .DataSource(d => d.Read("GetCountries", "Lookup"))
                    .HtmlAttributes(new { @class = "form-control", @required="required", @style = "font-size: 14px;", tabindex = 7}))
                </div>
                <div class="form-group">
                    <label>Active</label>
                    <br />
                    @(Html.Kendo().DropDownList()
                    .Name("IsLocationActive")
                    .DataValueField("Key")
                    .DataTextField("Value")
                    .Filter("contains")
                    .BindTo(new List<KeyValuePair<string, string>> {
                        new KeyValuePair<string,string>("false", "No"),
                        new KeyValuePair<string,string>("true", "Yes")
                    })
                    .Value("true")
                    .HtmlAttributes(new { @data_value_primitive = "true", @tabindex = 8}))
                </div>
            </div>
        </div>
        <hr />
        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label>Comment</label>
                    @(Html.Kendo().TextArea().Name("Comment").HtmlAttributes(new { @class = "form-control", @style = "width:100%", @rows = "5", tabindex = 9 }))
                </div>
            </div>
        </div>
    </div>
    <hr/>
    <button type="button" onclick="addCompanyLocation()" class="btn btn-sm btn-primary">save</button>
</form>
