@if (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsLogisticsAdmin() || Html.IsSeniorFieldEngineer() || Html.IsJuniorFieldEngineer() || Html.IsFieldEngineer()) {
    @(Html.Kendo().Upload()
        .Name("projectAttachmentDocuments")
        .Messages(m => m.Select("Attach Project Documents"))
        .Multiple(true)
        .Events(e => e
            .Success("onProjectDocumentAttached")
            .Complete("onProjectDocumentComplete")
            .Upload("onProjectDocumentUpload")
        )
        .Async(async => async
            .Save("AttachProjectDocuments", "Operation", new { @projectId = Model.ProjectId })
            .Batch(true)
        )
    )
}
@(Html.Kendo().Grid<DocumentModel>()
    .Name("projectDocumentsGrid")
    .Columns(c => {
        c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
        c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat).Width(150);
        c.Bound(p => p.UserName).Title("Created By").Width(200);
        c.Command(command => { 
            command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
        });
    })
    .Events(e => e.DataBound("updateProjectDocumentGrid"))
    .Sortable()
    .Resizable(r => r.Columns(true))
    .Filterable()
    .Groupable()
    .Editable(e => e.Mode(GridEditMode.InLine))
    .Scrollable()
    .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Model(model => model.Id(p => p.DocumentId))
        .Read(read => read.Action("GetProjectDocuments", "Operation", new { @projectId = Model.ProjectId }))
        .Destroy(destroy => destroy.Action("DeleteProjectDocument", "Operation", new { @projectId = Model.ProjectId }))
    )
)
@if (Model.HasJobWellAttachments) {
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0">Well </h6>
        </div>
        <div class="card-body">
            @(Html.Kendo().Grid<CompanyWellDocumentModel>()
                .Name("wellDocumentsGrid")
                .Columns(c => {
                    c.Bound(p => p.CompanyWellName).Title("Well").ClientGroupHeaderTemplate("Well : #= value # (#= count#)").Width(200).Hidden(true);
                    c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                    c.Bound(p => p.CompanyWellDocumentTypeDescription).Title("Type").ClientTemplate("#=CompanyWellDocumentTypeDescription ? CompanyWellDocumentTypeDescription : 'N/A'#");
                    c.Bound(p => p.Created).Format(DateConstants.DateFormat).Title("Created");
                    c.Bound(p => p.Username).Title("Created By");
                })
                .Events(e => e.DataBound("updateWellDocumentsGrid"))
                .Sortable()
                .Resizable(r => r.Columns(true))
                .ColumnMenu(c => c.Columns(true))
                .Filterable()
                .Groupable()
                .Scrollable()
                .DataSource(dataSource => dataSource
                    .Ajax()
                    .ServerOperation(false)
                    .Aggregates(aggregates => {
                        aggregates.Add(p => p.CompanyWellName).Min().Max().Count();
                    })
                    .Group(group => group.Add(p => p.CompanyWellName))
                    .Model(model => model.Id(p => p.CompanyWellDocumentId))
                    .Read(read => read.Action("GetCompanyWellDocumentsByProjectId", "Operation").Data("projectData"))
                )
            )
        </div>
    </div>
}