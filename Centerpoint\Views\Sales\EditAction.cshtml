﻿@model OpportunityActionModel

<div class="header-container-between">
    <h4>
        <i class="fa fa-check"></i>
        @(Model.OpportunityActionId.HasValue ? string.Format("Edit - {0} - {1}", Model.Name, Model.ActionTypeName) : "Add New Action")
    </h4>
    <div class="actionsContainer">
        @if (Model.OpportunityId.HasValue) { 
            <a class="btn btn-info btn-sm" href="@Url.Action("Edit","Sales",new { @id = Model.OpportunityId, @revision = ViewBag.Revision} )"><i class="fa fa-refresh"></i>Go to Lead / Opportunity</a>
        }
        @if (Model.OpportunityActionId.HasValue && !Model.CompletedOpportunityEventId.HasValue) {
            <a class="btn btn-primary btn-sm" href="@Url.Action("AddEvent", "Sales", new { @id = Model.OpportunityId, @opportunityActionId = Model.OpportunityActionId , @revision = ViewBag.Revision })"><i class="fa fa-plus"></i>Create Completion Event</a>
        }
    </div>
</div>
<hr />

    @{Html.Kendo().TabStrip()
        .Name("salesHistoryStrips")
        .SelectedIndex(0)
        .Animation(false)
        .Items( tabstrip => {

        tabstrip.Add().Text("")
            .HtmlAttributes(new { @data_bind = "html:tabStripHeaderDetails" })
            .Selected(true)
            .Content(@<text>

                        <div id="details">
                            @using (Html.BeginForm("EditAction", "Sales", FormMethod.Post, new { @revision = ViewBag.Revision, @id="editActionForm" })) {
                                @Html.ValidationSummary(false)
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Lead / Opportunity</label>
                                        <br />
                                        @(Html.Kendo().DropDownListFor(a => a.OpportunityId)
                                        .Filter(FilterType.Contains)
                                        .OptionLabel("Select Lead / Opportunity")
                                        .DataTextField("Text")
                                        .DataValueField("Value")
                                        .HtmlAttributes(new { @data_bind="value:opportunityId" })
                                        .Events(m => m.Change("opportunityChange").DataBound("opportunityDatabound"))
                                        .DataSource(source => {
                                            source.Read(read => {
                                                read.Action("GetAllOpportunities", "Lookup").Data("opportunityData");
                                            });
                                        }))
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Company</label>
                                        <br />
                                        @(Html.Kendo().DropDownListFor(a => a.CompanyId)
                                        .Filter(FilterType.Contains)
                                        .OptionLabel("Select Company")
                                        .DataTextField("Text")
                                        .DataValueField("Value")
                                        .Events(m => m.Change("companyChange").DataBound("companyDatabound"))
                                        .HtmlAttributes(new { @data_bind = "value:companyId" })
                                        .DataSource(source => {
                                            source.Read(read => {
                                                read.Action("GetActionEventCompanies", "Lookup");
                                            });
                                        }))
                                    </div>
                                </div>
                            </div>
                                <hr />
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>Type</label>
                                            <br />
                                            @(Html.Kendo().DropDownListFor(a => a.ActionTypeId)
                                            .OptionLabel("Select Action Type")
                                            .DataTextField("Name")
                                            .DataValueField("ActionTypeId")
                                            .Filter(FilterType.Contains)
                                            .DataSource(d => d.Read("GetActionTypes", "Lookup"))
                                            .HtmlAttributes(new { @tabindex = "1" }))
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>Assignee</label>
                                            <br />
                                            @(Html.Kendo().DropDownListFor(a => a.AssignedUserId)
                                            .OptionLabel("Select Assignee")
                                            .DataTextField("Name")
                                            .DataValueField("UserId")
                                            .Filter(FilterType.Contains)
                                            .DataSource(d => d.Read("GetSalesUsers", "Lookup", new { @excludeUser = false }))
                                            .HtmlAttributes(new { @tabindex = "2" }))
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>Target Date</label>
                                            <br />
                                            @(Html.Kendo().DatePickerFor(m => m.TargetDate).Min(DateTime.Now).HtmlAttributes(new { @style = "font-size: 14px;", @tabindex = "3" }))
                                        </div>
                                    </div>
                                    @if (Model.CompletedOpportunityEventId.HasValue) {
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>Completed Date</label>
                                                <br />
                                                @(Html.Kendo().DatePickerFor(m => m.CompletedDate).Min(DateTime.Now).HtmlAttributes(new { @style = "font-size: 14px;", @tabindex = "4" }))
                                            </div>
                                        </div>
                                }
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label>Objective</label>
                                            @(Html.TextAreaFor(p => p.Objective, new { @class = "k-textbox", @style = "width:100%", @rows = "5", @tabindex = "5" }))
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-sm btn-primary" onclick="validateEditActionForm(event)">Save Details</button>
                                @Html.HiddenFor(m => m.OpportunityEventId)
                                @Html.HiddenFor(m => m.OpportunityActionId)
                                @Html.HiddenFor(m => m.OpportunityType)
                                @Html.HiddenFor(m => m.CompletedOpportunityEventId)
                                @Html.HiddenFor(m => m.OpportunityId)
                                @Html.HiddenFor(m => m.Created)
                                @Html.HiddenFor(m => m.Name)
                                @Html.HiddenFor(m => m.CreatedBy)
                        }
                            <hr />
                            @if (Model.CompletedOpportunityEventId.HasValue) {
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Completed Event - @Model.CompletedOpportunityEvent.Name</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <h4>Event Type</h4>
                                                <p>@Model.CompletedOpportunityEvent.EventTypeName</p>
                                            </div>
                                            <div class="col-md-4">
                                                <h4>Contact</h4>
                                                <p>@Model.CompletedOpportunityEvent.CompanyContacts</p>
                                            </div>
                                            <div class="col-md-4">
                                                <h4>Event Date</h4>
                                                <p class="utcTimePicker">@Model.CompletedOpportunityEvent.EventDate</p>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <h4>Topic</h4>
                                                <p>@Model.CompletedOpportunityEvent.Topic</p>
                                            </div>
                                            <div class="col-md-4">
                                                <h4>Created</h4>
                                                <p class="utcTimePicker">@Model.CompletedOpportunityEvent.Created</p>
                                            </div>
                                            <div class="col-md-4">
                                                <h4>Created By</h4>
                                                <p>@Model.CompletedOpportunityEvent.CreatedBy</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        }
                        </div>

                </text>);

        if (Model.OpportunityActionId.HasValue) {
            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind="html:attachmentsStripText"})
                .Content(@<text>

                        <div id="documents">
                            @if (!Model.IsLeadOpportunityClosed) {
                                <p>Click the link below to attach documents</p>
                                @(Html.Kendo().Upload()
                            .Name("opportunityActionDocuments")
                            .Messages(m => m.Select("Attach Action Documents"))
                            .Multiple(true)
                            .Events(e => e.Success("onOpportunityActionDocumentAttached").Complete("onOpportunityActionDocumentComplete").Upload("onOpportunityActionDocumentUpload"))
                            .HtmlAttributes(new { @style = "width:300px" })
                            .Async(async => async.Save("AttachOpportunityActionDocuments", "Sales", new { @id = Model.OpportunityActionId }).Batch(true)))
                                <br />
                        }
                            @(Html.Kendo().Grid<DocumentModel>()
                            .Name("opportunityActionDocumentsGrid")
                            .Columns(c => {
                            c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                            c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
                            c.Bound(p => p.UserName).Title("Created By");
                            c.Command(command => { 
                                    command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
                                });
                            })
                            .Events(e => e.DataBound("updateOpportunityActionDocumentsGrid"))
                            .Sortable()
                            .Resizable(r => r.Columns(true))
                            .ColumnMenu(c => c.Columns(true))
                            .Filterable()
                            .Groupable()
                            .Editable(e => e.Mode(GridEditMode.InLine))
                            .Scrollable(s => s.Height(300))
                            .DataSource(dataSource => dataSource
                                .Ajax()
                                .ServerOperation(false)
                                .Model(model => model.Id(p => p.DocumentId))
                                .Read(read => read.Action("GetOpportunityActionDocuments", "Sales", new { @opportunityActionId = Model.OpportunityActionId }))
                                .Destroy(destroy => destroy.Action("DeleteOpportunityActionDocument", "Sales", new { @opportunityActionId = Model.OpportunityActionId }))))
                        </div>

                    </text>);
            }
        }).Render();
    }

<script>
    function validateEditActionForm(e){
    e.preventDefault();
    if($('#editActionForm').kendoValidator().data('kendoValidator').validate()){
        $('#editActionForm').submit();
    }
}
    const editActionModel = {
        modelOpportunityId: "@Model.OpportunityId",
        modelCompanyId: "@Model.CompanyId"
    }
</script>    

<environment include="Development">
    <script src="~/js/views/sales/editAction.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/sales/editAction.min.js" asp-append-version="true"></script>
</environment>