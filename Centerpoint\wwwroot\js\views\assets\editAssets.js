$("#saveEquipmentItem").click(function () {
    if ($('#editAssetsDetails').kendoValidator().data('kendoValidator').validate()) {
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: '/Assets/EditEquipmentItem',
            data: {
                equipmentNumber: $("#EquipmentNumber").val(),
                equipmentItemId: $("#EquipmentItemId").val(),
                equipmentInfo: $("#EquipmentInfo").val(),
                divisionId: $("#DivisionId").val(),
                currentCompanyId: $("#CurrentCompanyId").val(),
                currentClientLocationName: $("#CurrentClientLocationName").val(),
                currentCompanyName: $("#CurrentCompanyName").val(),
                currentCompanyLocationId: $("#CurrentCompanyLocationId").val(),
                currentCompanyLocationCompanyName: $("#currentCompanyLocationCompanyName").val(),
                currentCompanyLocationName: $("#CurrentCompanyLocationName").val(),
                countryOfOrigin: $("#CountryOfOrigin").val(),
                commodityCode: $("#CommodityCode").val(),
                exportcommodityCode: $("#ExportCommodityCode").val(),
                importcommodityCode: $("#ImportCommodityCode").val(),
                ushtsCommodityCode: $("#USHTSCommodityCode").val(),
                waiverCode: $("#WaiverCode").val(),
                waiverRequirement: $("#WaiverRequirement").val(),
                manufacturerCompanyId: $("#ManufacturerCompanyId").val(),
                manufacturerCompanyLocationId: $("#ManufacturerCompanyLocationId").val(),
                depreciatedPrice: $("#DepreciatedPrice").val(),
                currencyId: $("#CurrencyId").val(),
                retestDate: $("#RetestDate").val(),
                price: $("#Price").val(),
                trackedNonAssetItem: $("#TrackedNonAssetItem").val(),
                purchasedDate: $("#PurchasedDate").val(),
                internalInvoiceNumber: $("#InternalInvoiceNumber").val(),
                receivedDate: $("#ReceivedDate").val(),
                externalInvoiceNumber: $("#ExternalInvoiceNumber").val(),
                height: $("#Height").val(),
                heightUnit: $("#HeightUnit").val(),
                outerDiameter: $("#OuterDiameter").val(),
                outerDiameterUnit: $("#OuterDiameterUnit").val(),
                width: $("#Width").val(),
                widthUnit: $("#WidthUnit").val(),
                weight: $("#Weight").val(),
                weightUnit: $("#WeightUnit").val(),
                depth: $("#Depth").val(),
                depthUnit: $("#DepthUnit").val(),
                pointsPerMonth: $("#PointsPerMonth").val(),
                points: $("#Points").val(),
                pointsPerMove: $("#PointsPerMove").val(),
                pointsPerRun: $("#PointsPerRun").val(),
                h2sCo20to5: $("#H2sCo20to5").val(),
                h2sCo25to10: $("#H2sCo25to10").val(),
                h2sCo210to15: $("#H2sCo210to15").val(),
                h2sCo215Greater: $("#H2sCo215Greater").val(),
                status: $("#Status").val(),
                equipmentCategoryId: $("#EquipmentCategoryId").val(),
                equipmentCategoryName: $("#EquipmentCategoryName").val(),
                equipmentItemName: $("#EquipmentItemName").val(),
                acceptanceUser: $("#AcceptanceUser").val(),
                acceptanceCreated: $("#AcceptanceCreated").val(),
                comments: $("#Comments").val(),
                depreciationYears: $("#DepreciationYears").val(),
                currentEquipmentItemLocation: $("#CurrentEquipmentItemLocation").val(),
                isTransit: $("#IsTransit").val(),
                isReserved: $("#IsReserved").val(),
                shipmentTransit: $("#ShipmentTransit").val(),
                points: $("#Points").val(),
                projectId: $("#ProjectId").val(),
                isSelected: $("#IsSelected").val(),
                pointsUpdated: $("#PointsUpdated").val(),
                equipmentPackingListProjectId: $("#EquipmentPackingListProjectId").val(),
                serialNumber: $("#SerialNumber").val()
            },
            success: function (e) {
                updateMessage($("#EquipmentNumber").val());
                updateEquipmentItemLogGrid();
                window.location.reload();
            },
            error: function (e) {
                jqXHRErrors(e);
            }
        });
    } else {
        scrolToFirstError();
    }

})

function mRCountData() {
    return {
        equipId: viewModel.get("selectedEquipmentItemId")
    }
}

function maintenanceRecordCount(equipmentItemId) {
    viewModel.set("selectedEquipmentItemId", equipmentItemId);

    $("#maintenanceRecordWindow").data("kendoWindow").center().open();
}

function maintenanceRecordWindowOpened() {
    var mRGrid = $("#mRGrid").data("kendoGrid");
    mRGrid.dataSource.read();
}
function filterDivision() {
    return {
        divisionId: $("#DivisionId").data("kendoDropDownList").value()
    };
} 

function maintenanceBlueprintData(){
    return {
        equipmentCategoryId: editEquipmentItemModel.equipmentCategoryId
    }
}

function equipmentCategoryData() {
    return {
        equipmentCategoryId: editEquipmentItemModel.EquipmentCategoryId
    }
}

function maintenanceRecordData() {
    return {
        equipmentItemId: editEquipmentItemModel.equipmentItemId
    }
}

function filterCompanyLocations() {
    return {
        companyId: $("#currentClient").data("kendoDropDownList").value()
    };
}

function filterByCurrentCompanyLocation() {
    return {
        companyLocationId: $("#currentLocation").data("kendoDropDownList").value()
    };
}

function equipmentData() {
    return {
        equipmentItemId: editEquipmentItemModel.eId
    };
}
function noteData(data) {
    data.Created = toUTCString(data.Created);
}

function maintenanceScheduleData(data) {
    data.Created = toUTCString(data.Created);
}


function equipmentItemLogData() {
    return {
        eId: editEquipmentItemModel.eId
    };
}

function updateTotalLogs() {
    var equipmentItemLogGrid = $("#equipmentItemLogGrid").data("kendoGrid");
    var totalEquipmentItemLogs = equipmentItemLogGrid.dataSource.total();
    viewModel.set("totalEquipmentItemLogs", totalEquipmentItemLogs);
}

function updateEquipmentItemLogGrid() {
    let grid = $("#equipmentItemLogGrid").data("kendoGrid");
    if (grid) {
        grid.dataSource.read();
    }
}

function equipmentItemCertificatesData(data) {
    data.ExpiryDate = toUTCString(data.ExpiryDate);
    data.Created = toUTCString(data.Created);
}

function updateCustomStatusTotals() {
    var customStatusGrid = $("#customStatusGrid").data("kendoGrid");
    var totalCustomStatusCodes = customStatusGrid.dataSource.total();
    viewModel.set("totalCustomStatusCodes", totalCustomStatusCodes);

    var customStatusData = customStatusGrid.dataSource.data();
    $.each(customStatusData, function (i ,item){
        if (item.IsExpired) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(1)').css("color", "#E31E33");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(2)').css("color", "#E31E33");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(3)').css("color", "#E31E33");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(4)').css("color", "#E31E33");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(5)').css("color", "#E31E33");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(6)').css("color", "#E31E33");
        }
        if (item.IsActive) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(1)').css("background", "#98fb98");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(2)').css("background", "#98fb98");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(3)').css("background", "#98fb98");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(4)').css("background", "#98fb98");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(5)').css("background", "#98fb98");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(6)').css("background", "#98fb98");
        }
    });
}

function onBundleTabActivate() {
    var availableItemsGrid = $("#equipmentItemGrid").data("kendoGrid");
    availableItemsGrid.dataSource.read();
}

$(document).ready(function () {
    var customStatusGrid = $('#customStatusGrid').data("kendoGrid");
    if(customStatusGrid) {
          customStatusGrid.bind('dataBound', function (e) {
        this.element.find('.k-add').remove();
        this.element.find('.k-i-excel').remove();
    });  
    }
});
function onEdit(e) {
    //on row edit replace the Delete and Edit buttons with Update and Cancel
    $(e.container).find(".k-edit-buttons").html("<a class='btn btn-success btn-sm k-grid-update' href='#'><i class='fa fa-check-circle'></i>Update</a> " +
       "<a class='btn btn-warning btn-sm k-grid-cancel' href='#'><i class='fa fa-ban'></i>Cancel</a>");
}
function updateEquipmentTotals() {
    var equipmentItemGrid = $("#equipmentItemGrid").data("kendoGrid");
    var totalEquipmentItems = equipmentItemGrid.dataSource.total();
    viewModel.set("totalEquipmentItems", totalEquipmentItems);
    var equipmentData = equipmentItemGrid.dataSource.data();

    $.each(equipmentData, function (i, item) {
        if (item.MaintenanceScheduleDaysAlert) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(12)').css("color", "#E31E33");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(13)').css("color", "#E31E33");
        }
    });
}

function updateEquipmentItemBundlesTotals() {
    var equipmentItemBundleGrid = $("#equipmentItemBundleGrid").data("kendoGrid");
    var totalBundledEquipmentItems = equipmentItemBundleGrid.dataSource.total();
    viewModel.set("totalBundledEquipmentItems", totalBundledEquipmentItems);
    $("#archivedBtn").show();

    var equipmentData = equipmentItemBundleGrid.dataSource.data();
    $.each(equipmentData, function (i, item) {
        if (item.EquipmentItem.MaintenanceScheduleDaysAlert) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(12)').css("color", "#E31E33");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(13)').css("color", "#E31E33");
        }
    });
}

function equipmentItemNoteEdit(e) {
    $(e.container).find(".k-edit-buttons").html("<a class='btn btn-primary btn-sm k-grid-update' href='#'>Update</a> " +
       "<a class='btn btn-primary btn-sm k-grid-cancel' href='#'>Cancel</a>");
}
function edit(e) {

    $(e.container).find(".k-edit-buttons").html("<a class='btn btn-primary btn-sm k-grid-update' href='#'>Update</a> " +
       "<a class='btn btn-primary btn-sm k-grid-cancel' href='#'>Cancel</a>");
}
function equipmentItemData() {
    var equipmentItemBundleId = viewModel.get("equipmentItemBundleId");
    var equipmentItemId = editEquipmentItemModel.equipmentItemId;
    

    return {
        equipmentItemBundleId: equipmentItemBundleId,
        equipmentItemId: equipmentItemId
    };
}

function bundleEquipmentItemData() {
    var equipmentCategoryId = editEquipmentItemModel.equipmentCategoryId;
    var equipmentItemId = editEquipmentItemModel.equipmentItemId;
    var companyLocationId = editEquipmentItemModel.companyLocationId;
    return {
        equipmentCategoryId : equipmentCategoryId,
        equipmentItemId: equipmentItemId,
        companyLocationId: companyLocationId
    };
}
function refreshEquipmentCategories() {
    var equipmentCategory = $("#equipmentCategoryTreeView").data("kendoTreeView");
    equipmentCategory.dataSource.read();

    var path = viewModel.get("selectedEquipmentCategoryPath");
    equipmentCategory.expandPath(path);
}

function equipmentCategorySelected(e) {
    var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
    var equipmentCategory = equipmentCategoryTreeView.dataItem(e.node);

    $.cookie('equipmentCategory', equipmentCategory.EquipmentCategoryId , { expires: 7, path: '/' });
    viewModel.set("selectedEquipmentCategoryPath", []);

    var path = [];
    var node = e.node;
    path.push(equipmentCategoryTreeView.dataItem(node).EquipmentCategoryId);

    while (equipmentCategoryTreeView.dataItem(equipmentCategoryTreeView.parent(node))) {
        node = equipmentCategoryTreeView.parent(node);
        var parentEquipmentCategory = equipmentCategoryTreeView.dataItem(node);

        if (parentEquipmentCategory) {
            path.push(parentEquipmentCategory.EquipmentCategoryId);
        }
    }

    viewModel.set("selectedEquipmentCategoryPath", path);

    if (equipmentCategory) {
        viewModel.set("selectedEquipmentCategory", equipmentCategory);
        $("#equipmentItemGrid").data("kendoGrid").dataSource.read();
    }
}

function updateEquipmentItemMaintenanceScheduleTotals() {
    var equipmentItemMaintenanceScheduleGrid = $("#equipmentItemMaintenanceScheduleGrid").data("kendoGrid");
    var totalEquipmentItemMaintenanceSchedules = equipmentItemMaintenanceScheduleGrid.dataSource.total();
    viewModel.set("totalEquipmentItemMaintenanceSchedules", totalEquipmentItemMaintenanceSchedules);
    
    //updateEquipmentItemLogGrid();
}

function filterManufacturerCompanyLocations() {
    return {
        companyId: $("#ManufacturerCompanyId").data("kendoDropDownList").value()
    };
}

function equipmentItemMaintenanceScheduleData(e) {
    e.LastDate = toUTCString(e.LastDate);
    e.StartDate = toUTCString(e.StartDate);
}

function customStatusData(e) {
    e.Created = toUTCString(e.Created);
    e.ExpiryDate = toUTCString(e.ExpiryDate);
}

function filterCompanies() {
    return {
        projectId: $("#ProjectId").data("kendoDropDownList").value()
    };
}
function refreshBundledEquipmentItems() {
    var equipmentItemBundleGrid = $("#equipmentItemBundleGrid").data("kendoGrid");
    equipmentItemBundleGrid.dataSource.read();
}

function refreshEquipmentItems() {
    var equipmentItemGrid = $("#equipmentItemGrid").data("kendoGrid");
    equipmentItemGrid.dataSource.read();
}

function refreshEquipmentHistory() {
    var equipmentPackingListItemGridInDetails = $("#equipmentItemLogGrid").data("kendoGrid");
    equipmentPackingListItemGridInDetails.dataSource.read();
}

function updateEquipmentItemNoteGrid() {
    var equipmentItemNoteGrid = $("#equipmentItemNoteGrid").data("kendoGrid");
    var totalEquipmentItemNotes = equipmentItemNoteGrid.dataSource.total();
    viewModel.set("totalEquipmentItemNotes", totalEquipmentItemNotes);
}

function updateMaintenanceRecordAttachmentsGrid() {
    var maintenanceRecordAttachmentsGrid = $("#maintenanceRecordAttachmentsGrid").data("kendoGrid");
    var totalMRAttachments = maintenanceRecordAttachmentsGrid.dataSource.total();
    viewModel.set("totalMRAttachments", totalMRAttachments);
}

function updateMaintenanceRecordGrid() {
    var maintenanceRecordGrid = $("#maintenanceRecordGrid").data("kendoGrid");
    var maintenanceRecordData = maintenanceRecordGrid.dataSource.data();

    var totalActiveMRs = 0;
    var totalClosedMRs = 0;


    if(maintenanceRecordData){
        for(var i = 0;i<maintenanceRecordData.length;i++){
            if(maintenanceRecordData[i].Status == editEquipmentItemModel.maintenanceConstantClosed){
                totalClosedMRs++;
            } else{
                totalActiveMRs++;
            }
        }
        var totalMRs = totalActiveMRs + totalClosedMRs;

        viewModel.set("totalMRs", totalMRs);
        viewModel.set("totalActiveMRs", totalActiveMRs);
        viewModel.set("totalClosedMRs", totalClosedMRs);
    }
}

function startScheduleMaintenance(maintenanceBlueprintId) {
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: '/Maintenance/StartMaintenanceRecord',
        data: {
            equipmentItemId: editEquipmentItemModel.equipmentItemId,
            maintenanceBlueprintId: maintenanceBlueprintId
        },
        success: function (result) {
            window.location.href = "/Maintenance/EditMaintenanceRecord/" + result.maintenanceRecordId;
        }
    });
}
function equipmentCategoryMaintenanceStepData() {
    return {
        equipmentCategoryId: editEquipmentItemModel.equipmentCategoryId
    }
}

$("#acceptEquipmentItemConfirm").click(function () {
    $.ajax({
        type: 'POST',
        cache: false,
        dataType: 'json',
        url: '/Assets/AcceptEquipmentItem',
        data: {
            equipmentItemId: viewModel.get("acceptEquipmentItemId"),
            currentCompanyLocationId: viewModel.get("acceptCurrentCompanyLocationId"),
            acceptanceNote: viewModel.get("equipmentItemAcceptanceNote"),
            customStatusId: viewModel.get("acceptCustomStatusId"),
            acceptCustomStatusComment: viewModel.get("equipmentCustomStatusComment"),
            expiryDate: kendo.toString(viewModel.get("customStatusExpiryDate"), 'dd/MM/yyyy')
        },
        success: function () {
            $("#acceptanceWindow").data("kendoWindow").close();
            viewModel.set("acceptCurrentCompanyLocationId","");
            viewModel.set("equipmentItemAcceptanceNote","");
            viewModel.set("acceptCustomStatusId","");
            viewModel.set("equipmentCustomStatusComment","");
            viewModel.set("customStatusExpiryDate","");
            var equipmentItemGrid = $("#equipmentItemGrid").data("kendoGrid");
            if(equipmentItemGrid) {
              refreshEquipmentItems();  
            }
            window.location.reload();
        },
    });
});

$("#changeCurrentLocationConfirm").click(function () {
    $.ajax({
        type: 'POST',
        cache: false,
        dataType: 'json',
        url: '/Assets/ChangeCurrentLocation',
        data: {
            equipmentItemId: viewModel.get("equipmentItemId"),
            currentCompanyLocationId: viewModel.get("currentCompanyLocationId"),
            currentProjectId: viewModel.get("currentProjectId")
        },
        success: function () {
            window.location.reload();
        },
    });
});

$("#startMaintenanceRecordConfirm").click(function () {
    $.ajax({
        type: 'POST',
        cache: false,
        dataType: 'json',
        url: '/Maintenance/StartMaintenanceRecord',
        data: {
            equipmentItemId: viewModel.get("equipmentItemId"),
            maintenanceBlueprintId: viewModel.get("maintenanceBlueprintId"),
        },
        success: function (result) {
            window.location.href = "/Maintenance/EditMaintenanceRecord/" + result.maintenanceRecordId;
            $("#toMaintenanceRecordWindow").data("kendoWindow").close();
        },
    });
});

function onEquipmentItemDocumentAttached() {
    var equipmentItemDocumentsGrid = $("#equipmentItemDocumentsGrid").data("kendoGrid");
    equipmentItemDocumentsGrid.dataSource.read();
}

function onEquipmentItemCertificatesAttached() {
    var equipmentItemCertificatesGrid = $("#equipmentItemCertificatesGrid").data("kendoGrid");
    equipmentItemCertificatesGrid.dataSource.read();
}

function onEquipmentItemDocumentUpload(e) {
    uploadValidation(e);

    $(".k-upload-files.k-reset").show();
}

function onEquipmentItemDocumentComplete(e) {
    $(".k-upload-files.k-reset").find("li").remove();
    $(".k-upload-files.k-reset").slideUp();

    updateEquipmentItemLogGrid();
}

function updateEquipmentItemDocumentGrid() {
    var equipmentItemDocumentsGrid = $("#equipmentItemDocumentsGrid").data("kendoGrid");
    var totalAttachments = equipmentItemDocumentsGrid.dataSource.total();
    viewModel.set("totalAttachments", totalAttachments);
}

function updateEquipmentItemCertificateGrid() {
    let equipmentItemCertificatesGrid = $("#equipmentItemCertificatesGrid").data("kendoGrid");
    let totalCertificates = equipmentItemCertificatesGrid.dataSource.total();
    viewModel.set("totalCertificates", totalCertificates);
}

var viewModel = new kendo.observable({
    equipmentItemBundleId: editEquipmentItemModel.equipmentItemId,
    selectedEquipmentCategory: "",
    maintenanceBlueprintId : "",
    selectedEquipmentCategoryPath: [],
    totalEquipmentItemMaintenanceSchedules: 0,
    totalEquipmentItemNotes: 0,
    totalEquipmentItems: 0,
    totalBundledEquipmentItems: 0,
    acceptCurrentCompanyLocationId: 0,
    currentCompanyId: 0 ,
    currentCompanyLocationId: 0,
    currentProjectId: 0,
    acceptEquipmentItemId: 0,
    equipmentItemId: 0,
    selectedEquipmentItemId : "",
    acceptCustomStatusId: 0,
    totalEquipmentItemLogs: 0,
    totalMRAttachments: 0,
    totalCertificates: 0,
    totalActiveMRs: 0,
    totalClosedMRs: 0,
    totalMRs: 0,
    totalAttachments: 0,
    maintenanceRecordId: "",
    equipmentCustomStatusComment:"",
    equipmentItemAcceptanceNote: "",
    isDatesHidden: false,

    MRsTotalActiveMRsWithNumber: function () {
        return `MRs (<span data-bind="text: totalActiveMRs" style="margin-right: 2px;"></span>Open / <span data-bind="text: totalClosedMRs" style="margin-right: 2px;margin-left: 2px;"></span> Closed)`;
    },

    maintenanceScheduleWithNumber: function() {
        return `Maintenance Schedule (<span data-bind="text: totalEquipmentItemMaintenanceSchedules"></span>)`
    },
    customStatusWithNumber: function() {
        return `Custom Status (<span data-bind="text: totalCustomStatusCodes"></span>)`
    },
    bundleItemsWithNumber: function() {
        return `Bundle Items (<span data-bind="text: totalBundledEquipmentItems"></span>)`
    },
    attachmentsWithNumber: function() {
        return `Attachments (<span data-bind="text: totalAttachments"></span>)`
    },
    certificatesWithNumber: function () {
        return `Certificates (<span data-bind="text: totalCertificates"></span>)`
    },
    mrAttachmentsWithNumber: function() {
        return `MR Attachments (<span data-bind="text: totalMRAttachments"></span>)`
    },
    hasMRAttachments: function () {
        return this.get("totalMRAttachments") > 0;
    },
    equipmentItemHistoryWithNumber: function() {
        return `Equipment Item History (<span data-bind="text: totalEquipmentItemLogs"></span>)`
    },




    canAcceptEquipmentItem:function(){
        return this.get("acceptCurrentCompanyLocationId")
            && this.get("acceptCustomStatusId")
            && this.get("customStatusExpiryDate");

    },
    customStatusExpiryDate:"",
    expandAll: function () {
        var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
        equipmentCategoryTreeView.expand(".k-treeview-item");

    },
    collapseAll: function () {
        var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
        equipmentCategoryTreeView.collapse(".k-treeview-item");
    },


    showAcceptEquipmentItemWindow: function () {
        viewModel.set("acceptEquipmentItemId", editEquipmentItemModel.equipmentItemId);
        $("#acceptanceWindow").data("kendoWindow").center().open();
    },

    lostClick:function() {
        var confirmation = confirm ("Are you sure you wish to mark the equipment item as lost?");

        if(confirmation) {
            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url:`/Assets/UpdateStatus?status=${editEquipmentItemModel.equipmentConstantLost}`,
                data: {
                    id: editEquipmentItemModel.equipmentItemIdNumber,
                },
                success: function () {
                    dialogAssetMessage(`${$("#EquipmentItemName").val() }`,
                        `Equipment item \'${$("#EquipmentItemName").val()}\' status been successfully updated to \'Lost\'`);                    
                },
            });
        }
    },

    inActiveClick:function() {
        var confirmation = confirm ("Are you sure you wish to mark the equipment item as inactive?");

        if(confirmation) {
            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url:`/Assets/UpdateStatus/?status=${editEquipmentItemModel.equipmentConstantInactive}`,
                data: {
                    id: editEquipmentItemModel.equipmentItemIdNumber,
                },
                success: function () {
                    dialogAssetMessage(`${$("#EquipmentItemName").val()}`,
                        `Equipment item \'${$("#EquipmentItemName").val() }\' status been successfully updated to \'Inactive\'`);
                },
            });
        }
    },
    archivedClick: function () {
        var confirmation = confirm("Are you sure you wish to mark the equipment item as archived?");

        if (confirmation) {
            var totalBundledEquipmentItems = viewModel.get("totalBundledEquipmentItems");

            if (totalBundledEquipmentItems > 0)  {
                dialogAssetMessage(`${$("#EquipmentItemName").val()}`, `First you need to archive or remove Bundle Items`);
                return;
            }

            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url: `/Assets/UpdateStatus/?status=${editEquipmentItemModel.equipmentConstantArchived}`,
                data: {
                    id: editEquipmentItemModel.equipmentItemIdNumber
                },
                success: function () {
                    dialogAssetMessage(
                        `${$("#EquipmentItemName").val()}`,
                        `Equipment item '${$("#EquipmentItemName").val()}' status has been successfully updated to 'Archived'`
                    );
                }
            });
        }
    },
    operationalClick:function() {
        var confirmation = confirm ("Are you sure you wish to mark the equipment item as operational?");

        if(confirmation) {
            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url:`/Assets/UpdateStatus?status=${editEquipmentItemModel.equipmentConstantOperational}`,
                data: {
                    id: editEquipmentItemModel.equipmentItemId,
                },
                success: function () {
                    dialogAssetMessage(`${$("#EquipmentItemName").val()}`,
                        `Equipment item \'${$("#EquipmentItemName").val()}\' status been successfully updated to \'Operational\'`);
                },
            });
        }
    },

    unReserveItemClick:function() {
        var confirmation = confirm ("Are you sure you wish to mark this item as not reserved?");

        if(confirmation) {
            $.ajax({
                type: 'POST',
                dataType: 'json',
                url:`/Logistics/RemoveReservedBadge?id=${editEquipmentItemModel.equipmentItemIdNumber}`,
                data: {
                    id: editEquipmentItemModel.equipmentItemId,
                },
                success: function () {
                    window.location.reload();
                },
            });
        }
    },

    quarantineItemClick:function() {
        var confirmation = confirm ("Are you sure you wish to mark this item as quarantine?");

        if(confirmation) {
            $.ajax({
                type: 'POST',
                dataType: 'json',
                url:`/Assets/UpdateStatus?status=${editEquipmentItemModel.equipmentConstantQuarantined}`,
                data: {
                    id: editEquipmentItemModel.equipmentItemId,
                },
                success: function () {
                    dialogAssetMessage(`${$("#EquipmentItemName").val()}`,
                        `Equipment item \'${$("#EquipmentItemName").val()}\' status been successfully updated to \'Quarantine\'`);
                },
            });
        }
    },

    deleteEquipmentItem:function(){
        var confirmDelete = confirm("Are you sure you wish to delete this Equipment Item");

        if (confirmDelete) {
            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url: '/Assets/DeleteEquipmentItem',
                data: { id: editEquipmentItemModel.equipmentItemId },
                success: function (result) {
                    window.location.href = `/Assets`;
                },
            });
        }
    },

    showAttachmentPopup: function () {
        $("#attachDocumentWindow").data("kendoWindow").center().open();
    },

    showCurrentLocationChangeWindow: function() {
        viewModel.set("equipmentItemId", editEquipmentItemModel.equipmentItemIdNumber);
        $("#currentLocationChangeWindow").data("kendoWindow").center().open();
    },

    showMaintenanceRecordWindow: function() {
        viewModel.set("equipmentItemId", editEquipmentItemModel.equipmentItemIdNumber);
        $("#maintenanceBlueprint").data("kendoDropDownList").dataSource.read();
        $("#toMaintenanceRecordWindow").data("kendoWindow").center().open();
    },
});

function populateItemGridClick() {
    $('#addBundleBtn').prop('disabled', true);
    var equipmentItemGrid = $("#equipmentItemGrid").data("kendoGrid");
    var equipmentItemIds = [];
    equipmentItemGrid.select().each(function () {
        var equipmentItem = equipmentItemGrid.dataItem($(this));

        if (equipmentItem) {
            equipmentItemIds.push(equipmentItem.EquipmentItemId);
        }
    });

    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: "/Assets/UpdateBundledEquipmentItems",
        data: {
            parentEquipmentItemId: editEquipmentItemModel.equipmentItemId,
            equipmentItemIds: equipmentItemIds
        },
        success: function () {
            $('#addBundleBtn').prop('disabled', false);
            refreshBundledEquipmentItems();
            refreshEquipmentItems();
            refreshEquipmentHistory();
        },
        dataType: "json"
    });
}

function onDocumenetsGridSave(e) {
    if (e.type === "destroy") {
        updateEquipmentItemLogGrid();
    }
}

function onCertificatesGridSave(e) {
    if (e.type === "update") {
        var grid = $("#equipmentItemCertificatesGrid").data("kendoGrid");
        grid.dataSource.read();
        updateEquipmentItemLogGrid();
    }
    if (e.type === "destroy") {
        updateEquipmentItemLogGrid();
    }
}

function onCompanyLocationIdChange() {
    var dropdownList = $("#CompanyLocationId").data("kendoDropDownList");
    if (dropdownList.value()) {
        $("#maintenanceScheduleDates").addClass("invisible")
    } else {
        $("#maintenanceScheduleDates").removeClass("invisible")
    }
}

function kendoDropdownOpen(e) {
    if (!e.sender.requestFullfield) {
        e.sender.dataSource.read();
        e.sender.requestFullfield = true
    }
}

function dialogAssetMessage(titleMes, contentMes, hrefMes) {
    $("<div id='dialog'></div>").kendoDialog({
        closable: false,
        title: titleMes,
        close: () => {
            var dialog = $("#dialog").data("kendoDialog");
            dialog.destroy();
        },
        width: 500,
        buttonLayout: "normal",
        content: contentMes,
        actions: [
            {
                text: "OK",
                action: function () {
                    if (hrefMes != null)
                        window.location.href = hrefMes;
                    else
                        window.location.reload();
                },
                cssClass: 'btn-primary'
            }]
    }).data("kendoDialog").open().center()
}
kendo.bind(document.body.children, viewModel);
