﻿namespace Centerpoint.Common.Constants
{
    public static class MaintenanceComplexityConstant
    {

        public const string High = "HIG";
        public const string Medium = "MED";
        public const string Low = "LOW";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {High,"High"},
                    {Medium,"Medium"},
                    {Low,"Low"}
                };
            }
        }
    }
}
