﻿body {
    color: #676a6c !important;
    font-size: 14px !important
}

h1, h2, h3, h4, h5 {
    font-family: 'Raleway', sans-serif !important;
}

h2{
    font-size: 22px !important;
}

hr{
    margin-top: 20px !important;
    margin-bottom: 20px !important;
}

p {
    margin: 0 0 4px 0px !important;
}

.panel{
    width:100%;
}

.panel-heading h1,
.panel-heading h2 {
    padding: 12px 18px;
    border-bottom: 1px solid transparent;
    margin-bottom: 5px;
}

main {
    margin: 0px;
    padding: 0px 0px;
}

.table {
    table-layout: fixed;
    color: #676a6c !important;
    width: 100%;
}

.container{
    margin-left: 0px;
}

table > thead > tr > th > p {
    font-weight: bold
}

.table-bordered {
    border: 1px solid #EBEBEB;
}

.table-bordered > thead > tr > th,
.table-bordered > thead > tr > td {
    background-color: #F5F5F6;
    border-bottom-width: 1px;
}

.table-bordered > thead > tr > th,
.table-bordered > tbody > tr > th,
.table-bordered > tfoot > tr > th,
.table-bordered > thead > tr > td,
.table-bordered > tbody > tr > td,
.table-bordered > tfoot > tr > td {
    border: 1px solid #e7e7e7;
}

.table > thead > tr > th {
    border-bottom: 1px solid #DDDDDD;
    vertical-align: bottom;
}

.table > thead > tr > th,
.table > tbody > tr > th,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > tbody > tr > td,
.table > tfoot > tr > td {
    border-top: 1px solid #e7eaec;
    line-height: 1.42857;
    padding: 8px;
    vertical-align: top;
}