﻿@{
    Layout = null;
}
@using Centerpoint.Common
@model EquipmentShipmentInvoiceModel

@using Centerpoint.Model.Configuration;
@using Microsoft.Extensions.Options;
<!DOCTYPE html>
<html lang="en-GB">
<head>
    <base href="/" />

    <meta charset="UTF-8">
    <link rel="stylesheet" href="style/invocePDF.css" />
    <link rel="stylesheet" href="style/bootstrap.min.css" />
    <link href='http://fonts.googleapis.com/css?family=Raleway' rel='stylesheet' type='text/css'>

    <title>Document</title>
</head>
<body style="margin: 5px;">
    <div>
        <table style="width:100%">
            <tbody>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p><strong>@Model.CompanyLocationCompanyName</strong></p></td>
                </tr>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p>@Model.CompanyLocationName</p></td>
                </tr>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p> @Model.CompanyLocationHouseNumber</p></td>
                </tr>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p>@Model.CompanyLocationStreet</p></td>
                </tr>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p>@Model.CompanyLocationCity, @Model.CompanyLocationPostcode</p></td>
                </tr>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p>@Model.CompanyLocationCountry</p></td>
                </tr>
                <tr>
                    <td class="td-info td-assign"></td>
                    <td class="td-info-center td-assign"></td>
                    <td class="td-info td-assign"><p>Tel: @Model.CompanyLocationTelephone</p></td>
                </tr>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p>@(Html.Raw(!string.IsNullOrEmpty(Model.CompanyLocationComment) ? Model.CompanyLocationComment.Replace(System.Environment.NewLine, "<br />").Replace("\n", @"<br />") : string.Empty))</p></td>
                </tr>
            </tbody>
        </table>
    </div>
    <hr />
    <div>
        @if (GlobalSettings.IsWellsense && !string.IsNullOrEmpty(@Model.EquipmentShipment.DeliveryAddress))
        {
            <div style="display: flex">
                <table id="info" style="width:66%">
                    <tbody>
                        <tr>
                            <td class="td-info title-padding"><p class="title-shipment-details">@Model.EquipmentShipment.FromTitlePDF</p></td>
                            <td class="td-info-center title-padding"><p class="title-shipment-details">@Model.EquipmentShipment.ToTitlePDF</p></td>
                        </tr>
                        <tr>
                            <td class="td-info"><p><strong>@Model.EquipmentShipment.FromCompanyPDF</strong></p></td>
                            <td class="td-info-center"><p><strong>@Model.EquipmentShipment.ToCompanyPDF</strong></p></td>
                        </tr>
                        <tr>
                            <td class="td-info"><p>@(Html.Raw(@Model.EquipmentShipment.FromCompanyLocationHousePDF.Replace(System.Environment.NewLine, "<br />").Replace("\n", @"<br />")))</p></td>
                            <td class="td-info"><p>@(Html.Raw(@Model.EquipmentShipment.ToCompanyLocationHousePDF.Replace(System.Environment.NewLine, "<br />").Replace("\n", @"<br />")))</p></td>
                        </tr>
                    </tbody>
                </table>
                <table style="width:33%; height:100%;">
                    <tr>
                        <td class="td-info title-padding"><p class="title-shipment-details" id="deliveryAddress"> Delivery Address:</p></td>
                    </tr>
                    @foreach (var item in Model.EquipmentShipment.DeliveryAddress.Split("\r\n"))
                    {
                        <tr>
                            <td class="td-info"><p>@item</p></td>
                        </tr>
                    }
                </table>
            </div>
            <br>
            <table id="shipment-details" style="width:100%">
                <tbody>
                    <tr><td class="td-info title-padding"><p class="title-shipment-details">Shipment Details:</p></td></tr>
                    <tr><td class="td-info"><p><strong>@Model.PaperworkTypeDescription:</strong> @Model.EquipmentShipment.Number</p></td></tr>
                    <tr><td class="td-info"><p><strong>Description:</strong> @Model.EquipmentShipment.Description</p></td></tr>
                    <tr><td class="td-info"><p><strong>Shipment Method:</strong> @Model.EquipmentShipment.ShipmentMethodName</p></td></tr>
                    <tr><td class="td-info"><p><strong>Project:</strong> @Model.EquipmentShipment.ProjectName</p></td></tr>
                    <tr><td class="td-info"><p><strong>Date:</strong> @Model.EquipmentShipment.SentDateOnly</p></td></tr>
                    <tr><td class="td-info"><p><strong>Custom Status:</strong> @Model.EquipmentShipment.CustomStatus</p></td></tr>
                </tbody>
            </table>
        }
        else
        {
            <div style="display: flex; justify-content: space-between;">
                <table id="info" style="width:65%">
                    <tbody>
                        <tr>
                            <td class="td-info title-padding"><p class="title-shipment-details">@Model.EquipmentShipment.FromTitlePDF</p></td>
                            <td class="td-info-center title-padding"><p class="title-shipment-details">@Model.EquipmentShipment.ToTitlePDF</p></td>
                        </tr>
                        <tr>
                            <td class="td-info"><p><strong>@Model.EquipmentShipment.FromCompanyPDF</strong></p></td>
                            <td class="td-info-center"><p><strong>@Model.EquipmentShipment.ToCompanyPDF</strong></p></td>
                        </tr>
                        <tr>
                            <td class="td-info"><p>@(Html.Raw(@Model.EquipmentShipment.FromCompanyLocationHousePDF.Replace(System.Environment.NewLine, "<br />").Replace("\n", @"<br />")))</p></td>
                            <td class="td-info"><p>@(Html.Raw(@Model.EquipmentShipment.ToCompanyLocationHousePDF.Replace(System.Environment.NewLine, "<br />").Replace("\n", @"<br />")))</p></td>
                        </tr>
                    </tbody>
                </table>

                <table id="shipment-details" style="width:30%">
                    <tbody>
                        <tr>
                            <td class="td-info title-padding"><p class="title-shipment-details">Shipment Details:</p></td>
                        </tr>
                        <tr>
                            <td class="td-info"><p><strong>@Model.PaperworkTypeDescription:</strong> @Model.EquipmentShipment.Number</p></td>
                        </tr>
                        <tr>
                            <td class="td-info"><p><strong>Description:</strong> @Model.EquipmentShipment.Description</p></td>
                        </tr>
                        <tr>
                            <td class="td-info"><p><strong>Shipment Method:</strong> @Model.EquipmentShipment.ShipmentMethodName</p></td>
                        </tr>
                        <tr>
                            <td class="td-info"><p><strong>Project:</strong> @Model.EquipmentShipment.ProjectName</p></td>
                        </tr>
                        <tr>
                            <td class="td-info"><p><strong>Date:</strong> @Model.EquipmentShipment.SentDateOnly</p></td>
                        </tr>
                        <tr>
                            @if (GlobalSettings.IsWellsense)
                            {
                                <td class="td-info"><p><strong>Incoterm:</strong> @Model.EquipmentShipment.CustomStatus</p></td>
                            } else
                            {
                                <td class="td-info"><p><strong>Custom Status:</strong> @Model.EquipmentShipment.CustomStatus</p></td>
                            }
                        </tr>
                    </tbody>
                </table>
            </div>
            
        }
    </div>
    <hr />
    @{
        var totalShipmentValue = 0.0M;
    }
    @if (Model.EquipmentShipment.EquipmentShipmentItems != null && Model.EquipmentShipment.EquipmentShipmentItems.Any())
    {
        <div class="panel panel-default" style="border:none">
            <div class="panel-heading" style="background: #6eb6b4; color: #fff">
                <h2 class="panel-title" style="font-size:20px">Assets [@Model.EquipmentShipment.EquipmentShipmentItems.Count] </h2>
            </div>
            <br />
            <div class="panel-body" style="padding:0px;border-bottom:none">
                <table class="table table-bordered">
                    <thead>
                        <tr>


                            <th style="text-align:center; width:5%;font-size:20px">Item</th>
                            <th style="text-align:center; width:5%;font-size:20px">Quantity</th>
                            <th style="text-align:center; width:10%;font-size:20px">Item Number</th>

                            @if(GlobalSettings.IsRegiis)
                            {
                                <th style="text-align:center; width:35%;font-size:20px">Description</th>
                            }
                            else
                            {
                                <th style="text-align:center; width:35%;font-size:20px">Info</th>
                            }
                            @if (GlobalSettings.IsWellsense)
                            {
                                <th style="text-align:center; width:10%;font-size:20px">Commodity Code</th>
                                <th style="text-align:center; width:10%;font-size:20px">Country Of Origin</th>
                            }
                            @if (Model.IsNetWeight)
                            {
                                <th style="text-align:center; width:10%;font-size:20px">Net Weight</th>
                            }
                            @if (Model.PaperworkType == EquipmentShipmentPaperworkConstant.CommercialInvoice)
                            {
                                <th style="text-align:center; width:15%;font-size:20px">Unit Price</th>
                            }
                        </tr>
                    </thead>
                    <tbody>
                        @for (var i = 0; i < Model.EquipmentShipment.EquipmentShipmentItems.Count; i++)
                        {
                            var value = 0.0M;
                            <tr>
                                <td style="text-align:center; width:5%;font-size:20px">@((i + 1).ToString())</td>
                                <td style="text-align:center; width:5%;font-size:20px">1</td>
                                <td style="text-align:center; width:10%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentItems[i].EquipmentItemName</td>
                                @if(GlobalSettings.IsRegiis)
                                {
                                    <td style="text-align:left; width:40%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentItems[i].DescriptionName</td>
                                }
                                else
                                {
                                    <td style="text-align:left; width:40%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentItems[i].EquipmentItemEquipmentInfo</td>
                                }

                                @if (GlobalSettings.IsWellsense)
                                {
                                    if(Model.CommodityCodeOption == CommodityCodeConstant.ImportCommodityCode)
                                    {
                                        <td style="text-align:left; width:10%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentItems[i].EquipmentItemImportCommodityCode</td>
                                    }
                                    else if (Model.CommodityCodeOption == CommodityCodeConstant.ExportCommodityCode)
                                    {
                                        <td style="text-align:left; width:10%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentItems[i].EquipmentItemExportCommodityCode</td>
                                    }
                                    else if (Model.CommodityCodeOption == CommodityCodeConstant.USHTSCommodityCode)
                                    {
                                        <td style="text-align:left; width:10%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentItems[i].EquipmentItemUSHTSCommodityCode</td>
                                    }
                                    <td style="text-align:left; width:10%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentItems[i].EquipmentItemCountryOfOrigin</td>

                                }
                                @if (Model.IsNetWeight)
                                {
                                    <td style="text-align:center; width:10%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentItems[i].NetWeight</td>
                                }
                                @if (Model.PaperworkType == EquipmentShipmentPaperworkConstant.CommercialInvoice)
                                {
                                    if (Model.ValueType == EquipmentShipmentValueConstant.NetBookValue)
                                    {
                                        value = Math.Round(Model.EquipmentShipment.EquipmentShipmentItems[i].EquipmentItemDepreciatedPricePounds / Model.CurrencyMultiple, 2);
                                    }
                                    else if (Model.ValueType == EquipmentShipmentValueConstant.PurchasePrice)
                                    {
                                        value = Math.Round(Model.EquipmentShipment.EquipmentShipmentItems[i].EquipmentItemPricePounds / Model.CurrencyMultiple, 2);
                                    }
                                    <td style="text-align:right ; width:10%;font-size:20px">@value.ToString("n2") (@Model.CurrencyFormat)</td>
                                }
                                

                            </tr>
                            totalShipmentValue += value;
                           
                        }
                    </tbody>



                </table>
            </div>
        </div>
    }
    <br />
    @if (Model.EquipmentShipment.EquipmentShipmentNonAssetItems != null && Model.EquipmentShipment.EquipmentShipmentNonAssetItems.Any())
    {
        <div class="panel panel-default" style="border:none">
            <div class="panel-heading" style="background: #6eb6b4; color: #fff">
                <h2 class="panel-title" style="font-size:20px">Non-Assets [@Model.EquipmentShipment.EquipmentShipmentNonAssetItems.Count] </h2>
            </div>
            <br />
            <div class="panel-body" style="padding:0px;border-bottom:none">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th style="text-align:center; width:5%;font-size:20px">Item</th>
                            <th style="text-align:center; width:5%;font-size:20px">Quantity</th>
                            <th style="text-align:center; width:50%;font-size:20px">Description</th>
                            @if (GlobalSettings.IsWellsense)
                            {
                                <th style="text-align:center; width:10%;font-size:20px">Commodity</th>
                                @if (Model.IsNonAssetNetWeight)
                                {
                                    <th style="text-align:center; width:10%;font-size:20px">Net Weight</th>
                                }
                                <th style="text-align:center; width:10%;font-size:20px">Country Of Origin</th>
                            }
                            @if (Model.PaperworkType == EquipmentShipmentPaperworkConstant.ProformaInvoice || Model.PaperworkType == EquipmentShipmentPaperworkConstant.CustomsInvoice || Model.PaperworkType == EquipmentShipmentPaperworkConstant.CommercialInvoice)
                            {
                                <th style="text-align:center; width:10%;font-size:20px">Unit Price</th>
                                <th style="text-align:center; width:10%;font-size:20px">Amount</th>
                            }
                        </tr>
                    </thead>
                    <tbody>
                        @for (var i = 0; i < Model.EquipmentShipment.EquipmentShipmentNonAssetItems.Count; i++)
                        {
                            var value = 0.0M;
                            var totalValue = 0.0M;
                            <tr>
                                <td style="text-align:center ; width:5%;font-size:20px">@((i + 1).ToString())</td>
                                <td style="text-align:center ; width:5%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentNonAssetItems[i].Quantity</td>
                                <td style="text-align:left; width:50%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentNonAssetItems[i].Description</td>
                                @if (GlobalSettings.IsWellsense)
                                {
                                    <td style="text-align:left; width:10%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentNonAssetItems[i].Commodity</td>
                                    @if (Model.IsNonAssetNetWeight)
                                    {
                                        <td style="text-align:left; width:10%;font-size:20px"> @(Model.EquipmentShipment.EquipmentShipmentNonAssetItems[i].NetWeight.HasValue ? Model.EquipmentShipment.EquipmentShipmentNonAssetItems[i].NetWeight.Value : 0)</td>
                                    }
                                    <td style="text-align:left; width:10%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentNonAssetItems[i].CountryOfOrigin</td>
                                }

                                @if (Model.PaperworkType == EquipmentShipmentPaperworkConstant.ProformaInvoice || Model.PaperworkType == EquipmentShipmentPaperworkConstant.CustomsInvoice || Model.PaperworkType == EquipmentShipmentPaperworkConstant.CommercialInvoice)
                                {
                                    value = Math.Round(Model.EquipmentShipment.EquipmentShipmentNonAssetItems[i].UnitPricePounds / Model.CurrencyMultiple, 2);
                                    totalValue = value * (decimal)@Model.EquipmentShipment.EquipmentShipmentNonAssetItems[i].Quantity;

                                    <td style="text-align:right; width:20%;font-size:20px">@value.ToString("n2") (@Model.CurrencyFormat)</td>
                                    <td style="text-align:right; width:10%;font-size:20px">@totalValue.ToString("n2") (@Model.CurrencyFormat)</td>
                                }
                            </tr>

                            totalShipmentValue += totalValue;
                        }
                    </tbody>
                </table>
            </div>
        </div>
    }
    @if (Model.PaperworkType == EquipmentShipmentPaperworkConstant.ProformaInvoice || Model.PaperworkType == EquipmentShipmentPaperworkConstant.CustomsInvoice || Model.PaperworkType == EquipmentShipmentPaperworkConstant.CommercialInvoice)
    {
        <table class="table table-bordered total">
            <tbody>
                <tr>
                    <td colspan="5" style="text-align:right"><strong>Total Shipment Value</strong></td>
                    <td style="text-align:right;width:20%">@totalShipmentValue.ToString("n2") (@Model.CurrencyFormat)</td>
                </tr>
            </tbody>
        </table>
    }
    <br />
    @if (Model.EquipmentShipment.EquipmentShipmentPackages != null && Model.EquipmentShipment.EquipmentShipmentPackages.Any())
    {
        <div>
            <div class="panel-heading" style="background: #6eb6b4; color: #fff">
                <h2 class="panel-title" style="font-size:20px">Packages</h2>
            </div>
            <br />
            <div class="panel-body" style="padding:0px;border-bottom:none">
                <table class="table table-bordered packing-list-table">
                    <thead>
                        <tr>
                            <th style="text-align:center; width:17%;font-size:20px">Package Number / ID </th>
                            <th style="text-align:center; width:17%;font-size:20px">Type of Package</th>
                            <th style="text-align:center; width:17%;font-size:20px">Length (cm)</th>
                            <th style="text-align:center; width:17%;font-size:20px">Width (cm)</th>
                            <th style="text-align:center; width:16%;font-size:20px">Height (cm)</th>
                            <th style="text-align:center; width:16%;font-size:20px">Weight (kg)</th>
                        </tr>
                    </thead>
                    <tbody>
                        @for (var i = 0; i < Model.EquipmentShipment.EquipmentShipmentPackages.Count; i++)
                        {
                            <tr style="height: 35px">
                                <td style="text-align:center; width:17%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentPackages[i].PackageNumber</td>
                                <td style="text-align:center; width:17%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentPackages[i].TypeOfPackage</td>
                                <td style="text-align:center; width:17%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentPackages[i].Length</td>
                                <td style="text-align:center; width:17%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentPackages[i].Width</td>
                                <td style="text-align:center; width:16%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentPackages[i].Height</td>
                                <td style="text-align:center; width:16%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentPackages[i].Weight</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
            <table class="table table-bordered">
                <tbody>
                    <tr>
                        <td colspan="5" style="text-align:right"><strong>Total Weight</strong></td>
                        <td style="text-align:center;width:16%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentPackages.Select(s => s.Weight).Sum()</td>
                    </tr>
                </tbody>
            </table>
        </div>
    }
   
    @if (!string.IsNullOrEmpty(Model.EquipmentShipment.ShipmentNotes))
    {
        <div style="font-size:20px !important">
            <p class="title-shipment-details">Shipment Notes:</p><p> @(Html.Raw(@Model.EquipmentShipment.ShipmentNotes.Replace(System.Environment.NewLine, "<br />").Replace("\n", @"<br />")))</p>
        </div>
    }
    @if (!string.IsNullOrEmpty(Model.EquipmentShipment.PackingNotes))
    {
        <div style="font-size:20px !important">
            <p class="title-shipment-details">Packing Notes:</p><p> @(Html.Raw(Model.EquipmentShipment.PackingNotes.Replace(System.Environment.NewLine, "<br />").Replace("\n", @"<br />")))</p>
        </div>
    }
    <hr />
    <div>
        <table style="width:100%">
            <tbody>
                <tr>
                    <td class="td-info-label td-assign"><p><label><strong> Sent By: </strong></label></p></td>
                    <td class="td-info-sign td-assign"><p>______________________________________________</p></td>
                    <td class="td-info-label td-assign"><p></p></td>
                    <td class="td-info-label td-assign"><p><label><strong> Received By: </strong></label></p></td>
                    <td class="td-info-sign td-assign">______________________________________________<p></p></td>

                </tr>
                <tr>
                    <td class="td-info-label td-assign"><p><label><strong>Print:</strong></label></p></td>
                    <td class="td-info-sign td-assign"><p>______________________________________________</p></td>
                    <td class="td-info-label td-assign"><p></p></td>
                    <td class="td-info-label td-assign"><p><label><strong>Print:</strong></label></p></td>
                    <td class="td-info-sign td-assign"><p>______________________________________________</p></td>

                </tr>
                <tr class="table-assign">
                    <td class="td-info-label td-assign"><p><label><strong>Sign:</strong></label> </p></td>
                    <td class="td-info-sing td-assign"><p>______________________________________________</p></td>
                    <td class="td-info-label td-assign"><p></p></td>
                    <td class="td-info-label td-assign"><p><label><strong>Sign:</strong></label> </p></td>
                    <td class="td-info-sing td-assign"><p>______________________________________________</p></td>
                </tr>
                <tr>
                    <td class="td-info-label td-assign"><p><label><strong>Date:</strong></label> </p></td>
                    <td class="td-info-sing td-assign"><p>______________________________________________</p></td>
                    <td class="td-info-label td-assign"><p></p></td>
                    <td class="td-info-label td-assign"><p><label><strong>Date:</strong></label> </p></td>
                    <td class="td-info-sing td-assign"><p>______________________________________________</p></td>
                </tr>
            </tbody>
        </table>
    </div>
</body>
</html>