<div class="grid-container-standard">
    @(Html.Ken<PERSON>().Grid<EquipmentShipmentNonAssetItemModel>()
        .Name("equipmentShipmentNonAssetItemGrid")
        .Columns(c => {
            c.Bound(p => p.EquipmentShipmentNumber).Title("Shipment ID").ClientTemplate("<a href='" + @Url.Action("EditEquipmentShipment", "Logistics", new { @id = "" }) + "/#=EquipmentShipmentId#'>#=EquipmentShipmentNumber#</a>").Width(100);
            c.<PERSON>und(p => p.Quantity).Width(100);
            c.<PERSON>und(p => p.Description).Width(175);
            c.<PERSON>und(p => p.CustomStatusCodeId).Title("Custom Status").EditorTemplateName("CustomStatusCode").Title("Custom Status").ClientTemplate("#=CustomStatusName ? CustomStatusName : ''#").Width(150);
            c.<PERSON>und(p => p.UnitPrice).Title("Unit Price").Width(100).Format("{0:n2}");
            c.Bound(p => p.CurrencyId).Title("Currency").EditorTemplateName("Currency").Title("Currency").ClientTemplate("#=CurrencyName ? CurrencyName : ''#").Width(150);
            c.Bound(p => p.Commodity).Title("Commodity").Width(175);
            c.Bound(p => p.CountryOfOrigin).Title("Country Of Origin").Width(175).Visible(GlobalSettings.IsWellsense);
        })
        .ToolBar(t => {
            t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetEquipmentShipmentNonAssetItemGrid", @class="bg-danger text-white"});
            t.Excel().Text("Export");
        }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
        .Sortable()
        .Filterable()
        .Groupable()
        .Scrollable(s => s.Height(500))
        .Resizable(resize => resize.Columns(true))
        .Reorderable(reorder => reorder.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .Excel(excel => excel
            .FileName(string.Format("Centerpoint_NonAssetItems_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Logistics")))
        .Events(e => e.DataBound("updateEquipmentShipmentNonAssetItemTotals").ColumnReorder("saveEquipmentShipmentNonAssetItemGrid").ColumnResize("saveEquipmentShipmentNonAssetItemGrid").ColumnShow("saveEquipmentShipmentNonAssetItemGrid").ColumnHide("saveEquipmentShipmentNonAssetItemGrid"))
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(m => m.Id(p => p.EquipmentShipmentNonAssetItemId))
            .Read(read => read.Action("GetAllNonAssetItemsByProjectId", "Operation").Data("projectData"))
        )
    )
</div>