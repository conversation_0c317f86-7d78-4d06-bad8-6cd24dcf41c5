<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Centerpoint</title>

    <link rel="stylesheet" href="~/style/globalRules.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/style/bootstrap.min.css"/>
    <link rel="stylesheet" href="~/style/kendo-theme.css"/>
    <link rel="stylesheet" href="~/style/login.css" asp-append-version="true">
    <script src="https://code.jquery.com/jquery-3.6.1.min.js" integrity="sha256-o88AwQnZB+VDvE9tvIXrMQaPlFFSUTR+nldQm1LuPXQ=" crossorigin="anonymous"></script>
    <script src="~/lib/kendo/kendo.all.min.js"></script>
</head>

<body>
    @RenderBody()
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p" crossorigin="anonymous"></script>
    <script src="~/js/helpers/inputHelpers.js" asp-append-version="true"></script>
    <script src="~/js/views/layout/messageInterceptor.js" asp-append-version="true"></script>
</body>

</html>
