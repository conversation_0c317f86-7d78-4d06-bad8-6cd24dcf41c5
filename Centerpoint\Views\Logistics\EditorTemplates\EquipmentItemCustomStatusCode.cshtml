﻿@model int?

@(Html.<PERSON>()
 .DropDownListFor(m => m)
 .DataValueField("EquipmentItemCustomStatusCodeId")
 .DataTextField("CustomStatusName")
 .Filter("contains")
 .OptionLabel("Select Custom Status")
 .HtmlAttributes(new {@style = "width:100%", @data_value_primitive = "true" })
 .DataSource(dataSource => dataSource.Read(read => read.Action("GetEquipmentItemNotExpiredCustomStatusCodeByEquipmentItemId", "Lookup").Data("equipmentItemCustomStatusCodeData"))))
    