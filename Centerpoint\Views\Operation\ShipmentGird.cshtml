<div class="grid-container-standard">
    @(Html.Ken<PERSON>().Grid<EquipmentShipmentModel>()
        .Name("shipmentGrid")
        .Columns(columns => {
            columns.Bound(c => c.Number).Title("Shipment ID").ClientTemplate("<a href='" + @Url.Action("EditEquipmentShipment", "Logistics", new { @id = "" }) + "/#=EquipmentShipmentId#'>#=Number#</a>");
            columns.Bound(c => c.EquipmentShipmentStatusDescription).ClientTemplate("#=IsSent ? 'Sent' : EquipmentShipmentStatusDescription#").Title("Status");
            columns.Bound(c => c.ProjectNameandObjectives).Title("Project").ClientTemplate("#=ProjectId ? ProjectNameandObjectives : 'N/A'#").Hidden(true);
            columns.Bound(c => c.FromCompanyId).Title("From Client").ClientTemplate("#=FromCompanyName ? FromCompanyName : 'N/A'#");
            columns.Bound(c => c.FromCompanyLocationId).Title(" Client Location").ClientTemplate("#=FromCompanyLocationName ? FromCompanyLocationName : 'N/A'#");
            columns.Bound(c => c.ToCompanyId).Title("To Client").ClientTemplate("#=ToCompanyName ? ToCompanyName : 'N/A'#");
            columns.Bound(c => c.ToCompanyLocationId).Title(" Client Location").ClientTemplate("#=ToCompanyLocationName ? ToCompanyLocationName : 'N/A'#");
            columns.Bound(c => c.ShipmentMethodName).Title("Shipment Method").Hidden(true);
            columns.Bound(c => c.CustomStatus).Title(!GlobalSettings.IsWellsense ? "Custom Status" : "Incoterm");
            columns.Bound(c => c.CreatedDate).Format(DateConstants.DateFormat);
            columns.Bound(c => c.SentDate).Format(DateConstants.DateFormat);
            columns.Bound(c => c.ReceivedDate).Format(DateConstants.DateFormat);
            columns.Bound(c => c.EquipmentItemsCount).Title("Items Sent");
            columns.Bound(c => c.EquipmentItemsReceivedCount).Title("Items Received");
            if (Html.IsAssetAdmin() || Html.IsLogisticsAdmin() || Html.IsOperationAdmin() || Html.IsGlobalAdmin() || Html.IsFieldEngineer() || Html.IsJuniorFieldEngineer() || Html.IsSeniorFieldEngineer()) {
                columns.Command(c=>c.Custom("").Template("#=shipmentGridTemplate(data)#"));
            }
        })
        .ToolBar(t => {
            t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetShipmentGrid", @class="bg-danger text-white"});
            t.Excel().Text("Export");
        }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
        .Groupable()
        .Filterable()
        .ColumnMenu(c => c.Columns(true))
        .Resizable(r => r.Columns(true))
        .Reorderable(r => r.Columns(true))
        .Events(e => e.DataBound("updateShipmentGrid").ColumnReorder("saveShipmentGrid").ColumnResize("saveShipmentGrid").ColumnShow("saveShipmentGrid").ColumnHide("saveShipmentGrid"))
        .Excel(excel => excel
            .FileName(string.Format("Centerpoint_Shipments_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Logistics")))
        .Scrollable(s => s.Height(500))
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(model => {
                model.Id(m => m.EquipmentShipmentId);
            })
            .Events(e => e.Error("onError"))
            .Read(read => read.Action("GetEquipmentShipmentsByProjectId", "Logistics").Data("logisticsData"))
        )
    )
</div>

<script id="equipmentShipmentTemplate" type="text/x-kendo-template">
    #if(data.IsShipped){#
        <button class='btn btn-primary btn-sm' onclick='receiveEquipmentShipment(#=data.EquipmentShipmentId#)'>Receive Shipment</button>
    #}#
</script>

<script>
        function shipmentGridTemplate(data) {
           let template = kendo.template($("#equipmentShipmentTemplate").html())
           let result = template(data)
           return result
        }
</script>