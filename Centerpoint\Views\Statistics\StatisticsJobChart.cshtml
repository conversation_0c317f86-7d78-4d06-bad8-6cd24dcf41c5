<div class="card">
    <div class="card-header">
        <h6 class="mb-0">Jobs (per <span data-bind="text:jobChartShowBy"></span>)</h6>
    </div>
    <div class="card-body">
        <div class="d-flex">
            <div class="form-group w-50 mr-5">
                <label>Show By</label>
                <input 
                    data-role="dropdownlist" 
                    data-spinners="false" 
                    data-bind="value:jobChartShowBy, source:showByOptions, events:{change:refreshJobChart}" 
                    data-format="0 months" 
                    data-max="12" 
                    data-min="3" 
                />
            </div>
            <div class="form-group w-50">
                <label>Period</label>
                <input 
                    data-role="numerictextbox" 
                    data-spinners="false" 
                    data-bind="value:jobChartMonths, events:{change:refreshJobChart}" 
                    data-format="Last 0 months" 
                    data-min="3" 
                />
            </div>
        </div>
        @(Html.Kendo().Chart<StatisticsBaseModel>()
            .Name("jobChart")
            .Series(c => {
                c.Column(p => p.Count);
            })
            .ValueAxis(v => v.Numeric().Title("Count").MajorGridLines(l => l.Visible(false)).MinorGridLines(l => l.Visible(false)))
            .Legend(l => l.Visible(false))
            .CategoryAxis(c => c.Categories(p => p.Category))
            .Tooltip(t => t.Visible(true).Template("#=category# - #=value# jobs"))
            .DataSource(d => d.Read(read => read.Action("GetJobChartData", "Statistics").Data("jobChartData")))
        )
    </div>
</div>