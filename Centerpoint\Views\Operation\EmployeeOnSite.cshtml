﻿@model ProjectModel

<div class="header-container-between">
    <h4>
        <i class="fa fa-users"></i>
        Employee On-Site
        (<span data-bind="text: totalEmployeeOnSite"></span>)
    </h4>
    <div>
       <a class="btn btn-info btn-sm" href="@Url.Action("Index","Operation")"><i class="fa fa-refresh"></i>Return to Operation Dashboard</a> 
    </div>
</div>
<hr />

<div>
    @(Html.Kendo().Grid<CrewModel>()
       .Name("employeeOnSiteGrid")
       .Columns(c => {
           c.Bound(p => p.UserName).Title("Engineer Name");
           c.Bound(p => p.NewName).ClientTemplate("<a href='" + @Url.Action("EditProject", "Operation") + "/#=ProjectId#'>#=NewName#</a>").Title("Project Name");
           c.Bound(p => p.DateIn).Title("In").Format(DateConstants.DateTimeFormat);
           c.Bound(p => p.TotalDays).Title("Time On-Site");
       })
          .Events(e => e.DataBound("updateEmployeeOnSiteGrid"))
          .ToolBar(t => {
              t.Excel().Text("Export");
          }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
           .ColumnMenu(c => c.Columns(true))
          .Sortable()
          .Resizable(r => r.Columns(true))
          .Filterable()
          .Groupable()
          .Scrollable(s => s.Height("auto"))
          .Excel(excel => excel
                .FileName(string.Format("Centerpoint_Employees_On_Site_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                .Filterable(true)
                .ProxyURL(Url.Action("Export", "Qhse")))
          .DataSource(dataSource => dataSource
              .Ajax()
              .ServerOperation(false)
              .Model(model => model.Id(p => p.CrewId))
        .Read(read => read.Action("GetCrewsOnsite", "Operation"))))
</div>

<environment include="Development">
    <script src="~/js/views/operation/employeeOnSite.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/operation/employeeOnSite.min.js" asp-append-version="true"></script>
</environment>