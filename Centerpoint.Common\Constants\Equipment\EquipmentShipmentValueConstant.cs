﻿namespace Centerpoint.Common.Constants
{
    public static class EquipmentShipmentValueConstant
    {

        public const string NetBookValue = "NBV";
        public const string PurchasePrice = "PUP";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {NetBookValue,"Net Book Value"},
                    {PurchasePrice,"Purchase Price"},
                };
            }
        }
    }
}
