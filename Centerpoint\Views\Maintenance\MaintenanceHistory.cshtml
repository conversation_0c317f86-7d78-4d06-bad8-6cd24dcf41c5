﻿@model MaintenanceRecordDashboardModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-wrench"></i>
        Maintenance History
        (<span data-bind="text: totalMaintenanceRecords"></span>)
    </h4>
</div>
<hr />


    <div class="row mb-2">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fa fa-list"></i> Summary</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between flex-wrap">
                        <div class="form-group">
                            <label>Closed From</label>
                            <br/>
                            @(Html.Kendo().DatePickerFor(m => m.FromDate).Events(e => e.Change("refreshMaintenanceRecordHistoryGrid")).HtmlAttributes(new { @data_bind = "value:fromDate", @style = "width:200px; font-size: 14px" }))
                        </div>                        
                        <div class="form-group">
                            <label>Closed To</label>
                            <br/>
                            @(Html.Kendo().DatePickerFor(m => m.ToDate).Events(e => e.Change("refreshMaintenanceRecordHistoryGrid")).HtmlAttributes(new { @data_bind = "value:toDate", @style = "width:200px; font-size: 14px" }))
                        </div>
                    </div>
                    <div class="d-flex justify-content-between m-1">
                        <span>Passed</span>
                        <span>
                            <a data-bind="text:totalClosedPassed, click:totalClosedPassedClick" class="btn card-list-item-count" style="background: #7CBB00; color: #fff">
                            </a>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between m-1">
                        <span>Failed - Inactive</span>
                        <span>
                            <a data-bind="text:totalClosedFailed, click:totalClosedFailedClick" class="btn card-list-item-count" style="background: #FF0000; color: #fff"></a>
                        </span>
                    </div>

                    <div class="d-flex justify-content-between m-1">
                        <span>Failed - Active</span>
                        <span>
                            <a data-bind="text:totalClosedFailedActive, click:totalClosedFailedActiveClick" class="btn card-list-item-count" style="background: #E99002; color: #fff"></a>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between m-1">
                        <span>Repaired</span>
                        <span>
                            <a data-bind="text:totalClosedRepaired, click:totalClosedRepairedClick" class="btn card-list-item-count" style="background: #7ACCE4; color: #fff"></a>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between m-1">
                        <span>Not Repaired</span>
                        <span>
                            <a data-bind="text:totalClosedNotRepaired, click:totalClosedNotRepairedClick" class="btn card-list-item-count" style="background: #F2B660; color: #fff"></a>
                        </span>
                    </div>                            
                </div>
            </div>    
        </div>
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fa fa-pie-chart"></i> Maintenance by Status</h6>
                </div>
                <div class="chart-wrapper">
                    <div class="card-body" style="height:370px">
                        <div style="height:350px"
                                data-role="chart"
                                data-theme="Bootstrap"
                                data-legend="{visible:true, position:'bottom'}"
                                data-series-defaults="{ type: 'pie', labels:{visible:true, font:'12px Open Sans, sans-serif', template:'#=category# #=value# (#=kendo.format(\'{0:P}\', percentage)#)'} }"
                                data-series="[{field: 'Count', categoryField: 'Name', colorField:'Color'}]"
                                data-tooltip="{visible:true, template:'#=category# - #=value# (#=kendo.format(\'{0:P}\', percentage)#)'}"
                                data-bind="visible:hasMaintenance, source: maintenanceChartData">
                        </div>
                        <div data-bind="invisible:hasMaintenance">
                            <span>No maintenance data found</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@(Html.Kendo().Grid<MaintenanceRecordModel>()
        .Name("maintenanceRecordHistoryGrid")
        .Columns(columns => {
            columns.Bound(c => c.Number).ClientTemplate("<a href='" + @Url.Action("EditMaintenanceRecord", "Maintenance", new { @id = "" }) + "/#=MaintenanceRecordId#'>#=Number#</a>");
            columns.Bound(c => c.MaintenanceBlueprintName).Title("Blueprint").ClientTemplate("<a href='" + @Url.Action("EditMaintenanceBlueprint", "Admin", new { @id = "" }) + "/#=MaintenanceBlueprintId#'>#=MaintenanceBlueprintName#</a>");
            columns.Bound(c => c.EquipmentItemName).Title("Equipment Item").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#'>#=EquipmentItemName#</a>");
            columns.Bound(c => c.RunName).Title("Run").ClientTemplate("<a href='" + @Url.Action("EditRun", "Operation", new { @id = "" }) + "/#=RunId#?tab=runTriggeredMRs'>#=RunName? RunName:''#</a>");
            columns.Bound(c => c.StatusDescription).Title("Status").ClientTemplate("#if(IsClosed && IsRepair && IsRepairFailed){#<span class='badge' style='background:\\#F2B660;color:\\#fff'>#=NewRepairFailedName#</span>#}else if(IsClosed && IsRepair && Pass){#<span class='badge' style='background:\\#7ACCE4;color:\\#fff'>#=NewRepairedName#</span>#}else if(Pass){#<span class='badge' style='background:#=StatusColour#;color:#=StatustextColour#'>#=NewPassedName#</span>#}else if(Fail){#<span class='badge' style='background:\\#FF0000;color:\\#fff'>#=NewFailedName#</span>#}#");
            columns.Bound(c => c.PriorityDescription).Title("Priority");
            columns.Bound(c => c.UserName).Title("Created By").ClientTemplate("#=UserName ? UserName : 'System Generated'#").Hidden(true);
            columns.Bound(c => c.ClosedBy).Title("Closed By").Hidden(true);
            columns.Bound(c => c.Created).Title("Created").Format(DateConstants.DateTimeFormat);
            columns.Bound(c => c.StartDate).Title("Started").Format(DateConstants.DateTimeFormat);
            columns.Bound(c => c.CompletedDate).Title("Completed").Format(DateConstants.DateTimeFormat);
            columns.Bound(c => c.ClosedDate).Title("Closed").Format(DateConstants.DateFormat).Hidden(true);
        })
        .ToolBar(t => {
            if (Html.IsMaintenanceAdmin() || Html.IsGlobalAdmin())
            {
                t.Custom().HtmlAttributes(new { @class = "text-white bg-primary", @data_bind = "visible: selectedMaintenanceRecordId, click: editMaintenanceRecordWindow" }).Text("Edit Dates");
            }
            t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetMaintenanceRecordHistoryGrid", @class="bg-danger text-white"});
            t.Excel().Text("Export");
        }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
       .ColumnMenu(c => c.Columns(true))
       .Events(e => e.DataBound("updatedMaintenanceRecordGrid").Change("selectedMaintenanceHistoryRecord").ColumnReorder("saveMaintenanceRecordHistoryGrid").ColumnResize("saveMaintenanceRecordHistoryGrid").ColumnShow("saveMaintenanceRecordHistoryGrid").ColumnHide("saveMaintenanceRecordHistoryGrid"))
       .Filterable()
       .Excel(excel => excel
            .FileName(string.Format("Centerpoint_MR_History_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Qhse")))
       .Sortable()
       .Selectable(selectable => selectable.Mode(GridSelectionMode.Multiple).Type(GridSelectionType.Row))
       .Groupable()
       .Scrollable(s => s.Height("auto"))
       .Resizable(resize => resize.Columns(true))
       .Reorderable(reorder => reorder.Columns(true))
       .AutoBind(false)
    .DataSource(dataSource => dataSource
    .Ajax()
    .ServerOperation(false)
    .Model(model => {
        model.Id(m => m.MaintenanceRecordId);
        model.Field(m => m.RunId);
    })
    .Read(read => read.Action("GetAllMaintenanceRecordsByClosed", "Maintenance").Data("maintenanceRecordData"))))

@(Html.Kendo().Window()
 .Name("editMaintenanceRecordWindowOpen")
 .Title("Edit Item")
 .Content(@<text>
               <partial name="EditSelectedMaintenanceRecord"/>
 </text>)
 .Width(800)
 .Modal(true)
 .Visible(false))

    <script>
        const maintenanceHistoryModel = {
            fromDate: "@Model.FromDate",
            toDate: "@Model.ToDate",
        }
    </script>

    <script id="maintenanceRecordHistoryGridActions" type="text/x-kendo-tmpl">
         <a href='" + @Url.Action("EditRun", "Operation", new { @id = "" }) + "/#=RunId#?tab=runTriggeredMRs'>#=RunName? RunName:''#</a>
    </script> 

    <environment include="Development">
        <script src="~/js/views/maintenance/maintenanceHistory.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script src="~/js/views/maintenance/maintenanceHistory.min.js" asp-append-version="true"></script>
    </environment>