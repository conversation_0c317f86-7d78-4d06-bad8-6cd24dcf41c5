﻿@model UserModel

@Html.Partial( "_GridNotification", EntityType.User)
<div class="header-container-between">
    <h4>
        <i class="fa fa-users"></i>
        Employees
        (<span data-bind="text:totalUsers"></span>)
    </h4>
    <div class="form-group d-flex align-items-center mb-0">
        <label for="userSearch" class="col-lg-5 control-label mb-0">Quick Search</label>
        <input id="userSearch" type="text" class="form-control" />
    </div>
</div>
<hr />


<script type="text/kendo" id="rolesTemplate">
    <ul class="rolesTemplateUl">
        #for(var i = 0; i< data.length; i++){#
                 <li>#:data[i].Text#</li>
        #}#
    </ul>
</script>

<script type="text/javascript">
    var rolesTemplate = kendo.template($("#rolesTemplate").html(), { useWithBlock: false });
</script>

<div class="grid-container-big">
    @(Html.Kendo().Grid<UserModel>()
        .Name("adminUserGrid")
        .Columns(c => {
            c.Bound(p => p.Name).Title("Name").ClientTemplate("<a href='" + @Url.Action("EditUser", "Admin", new { @id = "" }) + "/#=UserId#'>#=Name#</a>").Width(200);
            c.Bound(p => p.JobTitle).Width(150);
            c.Bound(p => p.RolesList).ClientTemplate("#=rolesTemplate(RolesList)#").Width(260);
            c.Bound(p => p.WorkTelephone).Width(150);
            c.Bound(p => p.EmailAddress).ClientTemplate("<a href='mailto:#=EmailAddress#'>#=EmailAddress#</a>").Width(260);
            c.Bound(p => p.LastLogin).Format(DateConstants.DateTimeFormat).Width(150).Hidden(true);
            c.Bound(p => p.BaseCompanyLocationId).EditorTemplateName("BaseLocationCountry").Title("Base Location").ClientTemplate("#=BaseCompanyLocationName ? BaseCompanyLocationName : ''#").Width(150);
            c.Bound(p => p.IsEnabled).Title("Enabled").ClientTemplate("#if(IsEnabled){#Yes#}else{#No#}#").Width(80);
            c.Bound(p => p.IsNonEmployeeAccount).Title("Non-Employee Account").ClientTemplate("#if(IsNonEmployeeAccount){#Yes#}else{#No#}#").Width(80);
            if (Html.IsGlobalAdmin()) {
                c.Command(command=> { 
                    command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"});
                }).Width(200);
            }
        })
        .Sortable()
        .Filterable()
        .Groupable()
        .ToolBar(t => {
            t.Custom().HtmlAttributes(new{@class="text-white bg-primary ml-2", @onclick="window.location.href='/Admin/AddUser'"}).Text("Add New Employee");
            t.Excel().Text("Export").HtmlAttributes(new{@class="float-right ml-1"});
            t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetAdminUser", @class="text-white bg-danger float-right"});
        }).HtmlAttributes( new { @class="gridWithThreeToolbarButtons"})
        .Scrollable()
        .Resizable(c => c.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .Events(e => e.DataBound("updateUserTotal").ColumnReorder("saveUserGrid").ColumnResize("saveUserGrid").ColumnShow("saveUserGrid").ColumnHide("saveUserGrid").ExcelExport("excelExport"))
        .Excel(excel => excel
            .FileName(string.Format("Centerpoint_Personnel_Employees_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Admin"))
        )
        .DataSource(dataSource => dataSource
            .Ajax()
            .Model(m => {
                m.Id(p => p.UserId);
            m.Field(e => e.RolesList).DefaultValue(new List<SelectListItem> ());
            })
            .Events(e => e.Error("onError").RequestEnd("onRequestEnd"))
            .Read(read => read.Action("GetAllUsers", "Admin"))
            .Update(update => update.Action("EditUser", "Admin").Data("serialize"))
            .Destroy(destroy => destroy.Action("DeleteUser", "Admin"))
        )
    )
</div>


    <script>
        $(document).ready(function () {
            loadUserGrid();
            var adminUserGrid = $('#adminUserGrid').data("kendoGrid");
            adminUserGrid.bind('dataBound', function (e) {
                this.element.find('.k-i-excel').remove();
            });
        });
        $(function () {
            var grid = $("#adminUserGrid").data("kendoGrid");

            $("#resetAdminUser").click(function (e) {
                e.preventDefault();
                localStorage["adminUserGrid"] = "";
                window.location.reload();
            });
        });

        function loadUserGrid() {
            var grid = $("#adminUserGrid").data("kendoGrid");
            var options = localStorage["adminUserGrid"];
            var toolBar = $("#adminUserGrid .k-grid-toolbar").html();
            if (options) {
                grid.setOptions(JSON.parse(options));
                $("#adminUserGrid .k-grid-toolbar").html(toolBar);
                $("#adminUserGrid .k-grid-toolbar").addClass("k-grid-top");
            }
        }

        function saveUserGrid(e) {
            @* this part makes development hard so must stay commented *@
            @* setTimeout(function () {
                var grid = $("#adminUserGrid").data("kendoGrid");
                localStorage["adminUserGrid"] = kendo.stringify(grid.getOptions());
            }, 10); *@
        }

        function onError(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var adminUserGrid = $("#adminUserGrid").data("kendoGrid");
                adminUserGrid.dataSource.cancelChanges();
            }

                onErrorDeleteGridHandler(e, "#adminUserGrid");
        }

        function updateUserTotal() {
            var adminUserGrid = $("#adminUserGrid").data("kendoGrid");
            var totalUsers = adminUserGrid.dataSource.total();
            viewModel.set("totalUsers", totalUsers);
        }

        $("#userSearch").keyup(function () {
            var query = $("#userSearch").val();

            var adminUserGrid = $("#adminUserGrid").data("kendoGrid");

            if (query != null && query.length > 0) {
                var filter = {
                    logic: "or",
                    filters: [
                      { field: "Name", operator: "contains", value: query },
                      { field: "JobTitle", operator: "contains", value: query },
                      { field: "RoleDescription", operator: "contains", value: query },
                      { field: "BaseCompanyLocationName", operator: "contains", value: query }
                    ]
                };

                adminUserGrid.dataSource.filter(filter);
            } else {
                adminUserGrid.dataSource.filter([]);
            }
        });

        var viewModel = new kendo.observable({
            totalUsers: 0
        });

        kendo.bind(document.body.children, viewModel);

    </script>


    <script type="text/javascript">

        function serialize(data) {
            const selectedBaseCompanyLocation = $("#baseLocationId").data("kendoDropDownList").dataSource.data().find(i=>i.CompanyLocationId === data.BaseCompanyLocationId)
            data.BaseCompanyLocationName = selectedBaseCompanyLocation.Name;
            for (var property in data) {
                if ($.isArray(data[property])) {
                    serializeArray(property, data[property], data);
                }
            }
        }

        function serializeArray(prefix, array, result) {
            for (var i = 0; i < array.length; i++) {
                if ($.isPlainObject(array[i])) {
                    for (var property in array[i]) {
                        result[prefix + "[" + i + "]." + property] = array[i][property];
                    }
                }
                else {
                    result[prefix + "[" + i + "]"] = array[i];
                }
            }
        }

    </script>
    <script>
    function excelExport(e) {
        var sheet = e.workbook.sheets[0];
        var template = kendo.template(this.columns[6].template);


        let name;
        let jobTitle;
        let roles;
        let workTel;
        let email;
        let lastLogin;
        let baseLocation;
        let enabled;
        let nonEmployee;

        headersRow = sheet.rows[0];

        for (var i = 0; i < headersRow.cells.length; i++) {
            let currentCell = headersRow.cells[i].value;
            if (currentCell === 'Name')
                name = i;
            else if (currentCell === 'Job Title')
                jobTitle = i;
            else if (currentCell === 'Roles')
                roles = i;
            else if (currentCell === 'Work Telephone Number')
                workTel = i;
            else if (currentCell === 'E-mail Address')
                email = i;
            else if (currentCell === 'Base Location')
                baseLocation = i;
            else if (currentCell === 'Enabled')
                enabled = i;
            else if (currentCell === 'Non-Employee Account')
                nonEmployee = i;
        }


        for (var i = 1; i < sheet.rows.length; i++) {
            var row = sheet.rows[i];

            let cells = [...sheet.rows[i].cells];
            
            if (roles) {

                var dataItem2 = {
                    UnitPrice: cells[roles].value.map(x => x.Text).join()
                };
               
                if (row.cells[roles].value.length > 1) {
                    row.height = row.cells[2].value.length * 24;
                }

                row.cells[roles].value = dataItem2.UnitPrice.replaceAll(',', '\n');
                
                row.cells[roles].wrap = true;
            }
            if (baseLocation) {

                var dataItem5 = {
                    UnitPrice: cells[baseLocation].value
                };

                row.cells[baseLocation].value = e.data.toJSON()[i - 1].BaseCompanyLocationName;
            }

            if(enabled)
                row.cells[enabled].value = row.cells[enabled].value ? "yes" : "no";
            if(nonEmployee)
                row.cells[nonEmployee].value = row.cells[nonEmployee].value ? "yes" : "no";


                row.cells.map(x => x.verticalAlign = "center")
        }
    }
    </script>


