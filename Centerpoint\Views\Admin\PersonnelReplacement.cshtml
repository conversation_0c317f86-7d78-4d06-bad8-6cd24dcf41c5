﻿<div class="d-flex flex-column">
    <div>
        <div class="form-group">
            <label>Replacement User</label>
            <br />
            <p>Select the user to replace. If the current user is associated with a specific area of work, it will be replaced by the user you select.</p>
            @(Html.Kendo().DropDownList()
                .Name("ReplacementUser")
                .DataTextField("Text")
                .DataValueField("Value")
                .Filter("contains")
                .DataSource(d => d.Read("GetUsers", "Lookup"))
                .HtmlAttributes(new { @style = "width:100%" }))
            <br />
        </div>
        <div>
            <btn id="replaceUser" class="btn btn-primary btn-sm">Replace</btn>
            <btn type="button" id="cancelReplaceUser" style="float: right" class="k-grid-delete bg-danger text-white grid-action-button k-button k-button-md k-rounded-md k-button-solid k-button-solid-base"><span class="k-icon k-i-close k-button-icon"></span><span class="k-button-text">Cancel</span></btn>
        </div>
    </div>
</div>


<script type="text/javascript">
    $(document).ready(function () {
        $('#replaceUser').click(function () {
            $('#IsEnabled').prop('disabled', true);
            $.ajax({
                type: 'POST',
                url: '/Admin/ReplaceUser',
                dataType: "json",
                data: {
                    userId: $("#UserId").val(),
                    replacementUserId: $("#ReplacementUser").data("kendoDropDownList").value()
                },
                success: function (e) {
                    $("#personnelReplacementWindow").data("kendoWindow").center().close()
                    $('#IsEnabled').prop('checked', false);
                },
                error: function (e) {
                    jqXHRErrors(e);
                    $('#IsEnabled').val(true);                    
                    $('#IsEnabled').prop('disabled', false);
                }
            })
        });
        $('#cancelReplaceUser').click(function () {
            cancelReplace();
        });
    });

    function cancelReplace() {
        $('#IsEnabled').prop("checked", true);
        var personnelReplacementWindow = $("#personnelReplacementWindow").data("kendoWindow");
        personnelReplacementWindow.unbind("close").close();
        personnelReplacementWindow.bind("close", cancelReplace);
    }
</script>

