﻿@model MaintenanceBlueprintStepModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-file-image"></i>
        Edit Maintenance Blueprint Step
    </h4>
</div>
<hr />

@using (Html.BeginForm("EditMaintenanceBlueprintStep", "Admin")) {
    @Html.ValidationSummary(false)
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label>Type</label>
                    @(Html.Kendo()
                        .DropDownListFor(m => m.Type)
                        .Filter("contains")
                        .OptionLabel("Select Type")
                        .DataValueField("Key")
                        .DataTextField("Value")
                        .HtmlAttributes(new { @data_value_primitive = "true", @data_bind = "value:stepType" })
                        .BindTo(Centerpoint.Common.Constants.MaintenanceBlueprintStepTypeConstant.ValuesAndDescriptions.ToList())
                    )
                </div>
            </div>
        </div>
        <div class="row" data-bind="visible:freeTextVisible">
            <div class="col-md-6">
                <div class="form-group">
                    <label>Step Name</label>
                    @(Html.Kendo().TextBoxFor(m => m.Name))
                </div>
            </div>
        </div>
        <div class="row" data-bind="visible:freeTextVisible">
            <div class="col-md-12">
                <div class="form-group">
                    <label>Step Description</label>
                    @(Html.Kendo().EditorFor(m => m.Description)
                        .Tools(t => t
                            .Clear()
                            .Bold()
                            .Italic()
                            .Underline()
                            .Strikethrough()
                            .ForeColor()
                            .FontSize()
                            .JustifyCenter().JustifyFull().JustifyLeft().JustifyRight()
                            .InsertUnorderedList().InsertOrderedList().Indent().Outdent()
                            .CreateLink().Unlink()
                            .SubScript()
                            .SuperScript()
                            .ViewHtml()
                        )
                        .Resizable(resizable => resizable
                            .Content(true)
                            .Toolbar(true)
                        )
                        .HtmlAttributes(new { @rows = "10" })
                        .Encoded(false)
                        )
                </div>
                <div class="form-group">
                    <label>Repair Step ?</label>
                    @(Html.Kendo().DropDownListFor(m => m.IsRepair)
                        .DataValueField("Key")
                        .Filter("contains")
                        .DataTextField("Value")
                        .BindTo(new List<KeyValuePair<string, string>> { 
                            new KeyValuePair<string, string>(Boolean.FalseString, "No"), 
                            new KeyValuePair<string, string>(Boolean.TrueString, "Yes"), 
                        })
                    )
                </div>
            </div>
        </div>
        <div class="row" data-bind="visible:checkBoxVisible">
            <div class="col-md-8">
                <div class="form-group">
                    <label class="mr-2 d-inline-block">Task:</label>
                    @(Html.Kendo().TextBox()
                        .Name("taskInput")
                        .HtmlAttributes(new { 
                            @class = "form-control d-inline-block", 
                            @data_bind = "value:task", 
                            @data_value_update = "keyup",
                            @style="width:90%",
                            @autocomplete = "off" }
                        )
                    )
                    @Html.HiddenFor(m => m.Tasks, new { @data_bind = "value:stepTasks" })
                </div>
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-info btn-sm " data-bind="click:addtaskClick, enabled:canAddTask">Add Task</button>
            </div>
        </div>
        <div class="box-col" data-bind="visible:checkBoxVisible">
            <div id="taskListView"
                    data-template="taskTemplate" 
                    data-bind="source: tasks"
                    style="width: 100%; height: 25vh; overflow:auto">
            </div>
        </div>
        @(Html.Kendo().Sortable()
            .For("#taskListView")
            .CursorOffset(offset => offset.Left(0).Top(0))
            .HintHandler("hint")
            .PlaceholderHandler("placeholder")
            .Ignore("input")
            .Events(e => e.Change("onDragChange")))
        <div class="row">
            <div class="col-md-10">
                <input type="submit" onclick="refreshListViewTasks('#:data#')" class="btn btn-primary btn-sm " value="Save Maintenance Blueprint Step Details" style="margin-top: 10px" />
                <a class="btn btn-info btn-sm " style="margin-right: 10px; margin-top: 10px" href="@Url.Action("EditMaintenanceBlueprint","Admin",new { @id = Model.MaintenanceBlueprintId} )"><i class="fa fa-refresh"></i>Go to Maintenance Blueprint</a>
            </div>
        </div>
        @Html.HiddenFor(m => m.MaintenanceBlueprintId)
        @Html.HiddenFor(m => m.MaintenanceBlueprintStepId)
    </div>

}

<script type="text/x-kendo-tmpl" id="taskTemplate">
    <div class="task-template-item m-1">
        <span class="btn btn-danger btn-xs" onclick="deleteTaskClick('#:data#');" title="Delete Task">
            <i class="fa fa-trash mr-0"></i>
        </span>
        <span class="btn btn-primary btn-xs mr-1 ml-1" onclick="saveTaskClick('#:data#');" title="Save Task">
            <i class="fa fa-floppy-disk mr-0"></i>
        </span>
        <span class="btn btn-success btn-xs disabled mr-1" title="Drag Task" role="option" style="background-color: darkgreen;border-color: darkgreen;">
            <i class="fa fa-arrows-v mr-0"></i>
        </span>
        <input autocomplete="off" value="#= kendo.toString(replaceString(data))#" name="taskBox" id="task_box" type="text" class="form-control task" onclick="$(this).focus();" />
    </div>
</script>
    
<script>

    const editMaintenanceBlueprintStepModel = {
        tasks: "@Model.Tasks",
        type: "@Model.Type",
        maintenanceBlueprintStepTypeConstantBoolean :"@Centerpoint.Common.Constants.MaintenanceBlueprintStepTypeConstant.Boolean",
        maintenanceBlueprintStepTypeConstantText :"@Centerpoint.Common.Constants.MaintenanceBlueprintStepTypeConstant.Text",
        maintenanceBlueprintStepTypeConstantDocument:"@Centerpoint.Common.Constants.MaintenanceBlueprintStepTypeConstant.Document"
    }

</script> 

<environment include="Development">
    <script src="~/js/views/maintenance/editMaintenanceBlueprintStep.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/maintenance/editMaintenanceBlueprintStep.min.js" asp-append-version="true"></script>
</environment>



