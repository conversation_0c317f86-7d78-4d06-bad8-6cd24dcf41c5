$(document).ready(function () {
    var joinedTask = editMaintenanceBlueprintStepModel.tasks;

    if (joinedTask) {
        viewModel.set("tasks", joinedTask.split("|~"));
    }

    $("#taskInput").keyup(function (event) {
        if(event.target.value.trim()) {
            viewModel.set("canAddTask", true)
        } else {
            viewModel.set("canAddTask", false)
        }
     });

    document.onkeypress = stopRKey;
});

function stopRKey(evt) {
    var evt = (evt) ? evt : ((event) ? event : null);
    var node = (evt.target) ? evt.target : ((evt.srcElement) ? evt.srcElement : null);
    if ((evt.keyCode == 13) && (node.type=="text"))  {return false;}
}

function replaceString(value) {
    return value.replace(/&lt;/g, "<").replace(/&gt;/g, ">").replace(/&amp;/g, "&");
}

function saveTaskClick() {
    refreshListViewTasks();
    updateMaintenanceBlueprintStepTasks();
}

function refreshListViewTasks() {
    var listView = $("#taskListView").data("kendoSortable");
    var taskData = listView.items();

    var tasks = [];

    for (var i = 0; i < taskData.length; i++) {
        tasks.push($(taskData[i]).find(".task").val());
    }
    viewModel.set("tasks", tasks); 
}

function updateMaintenanceBlueprintStepTasks() {
    var maintenanceBlueprintStepId = $("#MaintenanceBlueprintStepId").val();
    if (maintenanceBlueprintStepId) {
        $.ajax({
            type: "POST",
            url: "/Admin/UpdateMaintenanceBlueprintStepTasks",
            dataType: "json",
            data: {
                maintenanceBlueprintStepId: maintenanceBlueprintStepId,
                tasks: viewModel.stepTasks()
            },
            success: function (response) {},
            error: function (e) { jqXHRErrors(e); }
        });
    }
}
function hint(element) {
    return element.clone().addClass("hint");
}

function placeholder(element) {
    return element.clone().addClass("placeholder").text("drop here");
}

function deleteTaskClick(task) {
    var tasks = viewModel.get("tasks");
    tasks.splice(tasks.indexOf(task), 1);
    viewModel.set("tasks", tasks);
    refreshListViewTasks();
    updateMaintenanceBlueprintStepTasks();
}

function onDragChange(e) {
    refreshListViewTasks();
    updateMaintenanceBlueprintStepTasks();
}

var viewModel = new kendo.observable({
    stepType: editMaintenanceBlueprintStepModel.type,
    task: "",
    tasks:[],
    canAddTask: false,

    addtaskClick: function () {
        refreshListViewTasks();
        var task = this.get("task");
        var tasks = this.get("tasks");

        tasks.push(task);
        this.set("tasks", tasks);
        this.set("task", "");
        updateMaintenanceBlueprintStepTasks();
    },

    stepTasks: function () {
        var tasks = this.get("tasks");
        return tasks.join("|~");
    },

    checkBoxVisible: function () {
        var stepType = this.get("stepType");

        if (stepType == editMaintenanceBlueprintStepModel.maintenanceBlueprintStepTypeConstantBoolean) {
            return true;
        } else {
            return false;
        }
    },

    freeTextVisible: function () {
        var stepType = this.get("stepType");

        if (
            stepType == editMaintenanceBlueprintStepModel.maintenanceBlueprintStepTypeConstantText || 
            stepType == editMaintenanceBlueprintStepModel.maintenanceBlueprintStepTypeConstantBoolean || 
            stepType == editMaintenanceBlueprintStepModel.maintenanceBlueprintStepTypeConstantDocument
        ) {
            return true;
        } else {
            return false;
        }
    },
});

kendo.bind(document.body.children, viewModel);