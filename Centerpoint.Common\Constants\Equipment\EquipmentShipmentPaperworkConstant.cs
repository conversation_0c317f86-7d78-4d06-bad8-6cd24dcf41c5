﻿using Microsoft.Extensions.Configuration;

namespace Centerpoint.Common.Constants
{
    public static class EquipmentShipmentPaperworkConstant
    {
        public const string ShippingDocket = "SHD";
        public const string ProformaInvoice = "PFI";
        public const string CustomsInvoice = "CUI";
        public const string CommercialInvoice = "COI";
        public const string DeliveryNote = "DEN";
        public const string MaintenanceSchedule = "MAS";


        private static string _client;
        public static void Initialize(IConfiguration configuration)
        {
            _client = configuration["Settings:Client"];
        }

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetTitle(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndTitles.ContainsKey(value) ? ValuesAndTitles[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string>
                {
                    {CommercialInvoice,"Commercial Invoice"},
                    {DeliveryNote,  GlobalSettings.IsWellsense ? "Packing List" : "Delivery Note" },
                    {MaintenanceSchedule,"Maintenance Schedule"}
                };
            }
        }

        public static Dictionary<string, string> ValuesAndTitles
        {
            get
            {
                return new Dictionary<string, string>
                {
                    {ShippingDocket,"ShippingDocket"},
                    {ProformaInvoice,"ProformaInvoice"},
                    {CustomsInvoice,"CustomsInvoice"},
                    {CommercialInvoice,"CommercialInvoice"},
                    {DeliveryNote,  GlobalSettings.IsWellsense ? "PackingList" : "DeliveryNote" },
                    {MaintenanceSchedule,"MaintenanceSchedule"}
                };
            }
        }
    }
}
