﻿using Centerpoint.Extensions;
using Centerpoint.Model.ViewModels;
using Centerpoint.Service.Interfaces;
using Centerpoint.Services;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net;

namespace Centerpoint.Controllers
{
    [Authorize]
    public class LogisticsController : Controller
    {
        private readonly ILogisticService _logisticService;
        private readonly ICurrentUserService _currentUserService;

        public LogisticsController(ILogisticService logisticService,
            ICurrentUserService currentUserService)
        {
            _logisticService = logisticService;
            _currentUserService = currentUserService;
        }

        public async Task<ActionResult> Index()
        {
            var model = await _logisticService.GetLogisticsDashboardModel();
            this.SetTitle("Logistics");
            return View(model);
        }

        public async Task<ActionResult> GetEquipmentShipmentMonthlySummary(int? month)
        {
            var model = await _logisticService.GetEquipmentShipmentMonthlySummary(month);

            return Json(model);
        }

        public async Task<ActionResult> GetEquipmentShipmentYearlySummary(int? year)
        {
            var model = await _logisticService.GetEquipmentShipmentYearlySummary(year);

            return Json(model);
        }

        public async Task<ActionResult> PackingList()
        {
            this.SetTitle("Packing List");

            return View();
        }

        #region Equipment Shipment

        public async Task<ActionResult> GetCurrentEquipmentShipments([DataSourceRequest] DataSourceRequest request, string type, int? month, int? year, string shipmentMethod)
        {
            var result = await _logisticService.GetCurrentEquipmentShipments(type, month, year, shipmentMethod);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetReceivedEquipmentShipments([DataSourceRequest] DataSourceRequest request, string type, int? month, int? year, string shipmentMethod)
        {
            var result = await _logisticService.GetReceivedEquipmentShipments(type, month, year, shipmentMethod);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetEquipmentShipmentsByProjectId([DataSourceRequest] DataSourceRequest request, int projectId)
        {
            var result = await _logisticService.GetEquipmentShipmentsByProjectId(projectId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> AddEquipmentShipment(string shipmentType)
        {
            var model = new EquipmentShipmentModel
            {
                CreatedDate = DateTime.UtcNow,
                CreatedByUserId = _currentUserService.UserId,
            };

            this.SetTitle("Add New Shipment");

            return View("EditEquipmentShipment", model);
        }

        [HttpPost]
        public async Task<ActionResult> AddEquipmentShipmentFromProject(int projectId, int toCompanyLocationId, int? toProjectId)
        {
            var result = await _logisticService.AddEquipmentShipmentFromProject(projectId, toCompanyLocationId, toProjectId, _currentUserService.UserId);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new { equipmentShipmentId = result.Response });
        }

        [HttpPost]
        public async Task<ActionResult> CreateBackloadShipment(int id)
        {
            var result = await _logisticService.CreateBackloadShipment(id, _currentUserService.UserId);

            return Json(new { equipmentShipmentId = result.Response });
        }

        public async Task<ActionResult> EditEquipmentShipment(int id, string tab, string returnUrl, bool isReceiving = false)
        {
            var model = await _logisticService.GetEquipmentShipmentById(id);

            this.SetTitle(string.Format("{0}", model.Number));

            ViewBag.ReturnUrl = string.IsNullOrWhiteSpace(returnUrl) ? (Request.GetTypedHeaders().Referer != null ? Request.GetTypedHeaders().Referer.ToString() : null) : returnUrl;
            ViewBag.Tab = tab;

            if (isReceiving)
            {
                ViewBag.IsReceiving = true;
            }

            return View(model);
        }

        [HttpPost]
        public async Task<ActionResult> EditEquipmentShipment(EquipmentShipmentModel model)
        {
            if (!model.ProjectId.HasValue && model.IsProjectRelated)
                return BadRequest(new { message = "Select Project to make equipment available is required" });

            if (model.SentDate < model.CreatedDate)
                return BadRequest(new { message = "Shipped Date\", \"Shipped Date cannot be set before the Creation date" });

            if (!ModelState.IsValid)
                return BadRequest(new { message = "An error occurred while updating the MR" });

            var result = await _logisticService.EditEquipmentShipment(model);

            return Json(new { shipmentNumber = result.Number, shipmentId = result.EquipmentShipmentId });
        }
        [HttpDelete]
        public async Task<ActionResult> DeleteEquipmentShipment(int id)
        {
            var equipmentShipment = await _logisticService.DeleteEquipmentShipment(id, _currentUserService.UserId);
            this.SetMessage(MessageType.Success, string.Format("'{0}' Shipment has been successfully deleted", equipmentShipment.Number));

            return Json("true");
        }

        #endregion

        #region Shipment Invoice

        public async Task<ActionResult> GetEquipmentShipmentInvoices([DataSourceRequest] DataSourceRequest request, int eId)
        {
            var result = await _logisticService.GetEquipmentShipmentInvoices(eId);

            return Json(result.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> CreateEquipmentShipmentInvoice([DataSourceRequest] DataSourceRequest request, EquipmentShipmentInvoiceModel model, int eId)
        {
            var result = await _logisticService.CreateEquipmentShipmentInvoice(model, eId, _currentUserService.UserId);

            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteEquipmentShipmentInvoice([DataSourceRequest] DataSourceRequest request, EquipmentShipmentInvoiceModel model)
        {
            var result = await _logisticService.DeleteEquipmentShipmentInvoice(model);

            return Json(result.ToDataSourceResult(request));
        }

        #endregion

        public async Task<ActionResult> GetEquipmentShipmentItems([DataSourceRequest] DataSourceRequest request, int equipmentShipmentId)
        {
            var result = await _logisticService.GetEquipmentShipmentItems(equipmentShipmentId);

            return Json(result.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateShipmentEquipmentItems(int[] equipmentItemIds, int equipmentShipmentId)
        {
            await _logisticService.UpdateShipmentEquipmentItems(equipmentItemIds, equipmentShipmentId, _currentUserService.UserId);

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<ActionResult> UpdateEquipmentShipmentItem([DataSourceRequest] DataSourceRequest request, EquipmentShipmentItemModel model, int equipmentShipmentItemId)
        {
            var result = await _logisticService.UpdateEquipmentShipmentItem(model, equipmentShipmentItemId);

            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteEquipmentShipmentItem([DataSourceRequest] DataSourceRequest request, EquipmentShipmentItemModel model)
        {
            var result = await _logisticService.DeleteEquipmentShipmentItem(model, _currentUserService.UserId);

            return Json(result.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> RemoveReservedBadge(int id)
        {
            await _logisticService.RemoveReservedBadge(id, _currentUserService.UserId);

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<ActionResult> ReceiveEquipmentShipmentItem(int equipmentShipmentItemId)
        {
            await _logisticService.ReceiveEquipmentShipmentItem(equipmentShipmentItemId);

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<ActionResult> ReceiveEquipmentShipmentNonAssetItem(int equipmentShipmentNonAssetItemId)
        {
            await _logisticService.ReceiveEquipmentShipmentNonAssetItem(equipmentShipmentNonAssetItemId);

            return Json(new { success = "true" });
        }

        public async Task<ActionResult> GetEquipmentItemsNotInEquipmentShipmentId([DataSourceRequest] DataSourceRequest request, int? equipmentCategoryId, int equipmentShipmentId)
        {
            var result = await _logisticService.GetEquipmentItemsNotInEquipmentShipmentId(equipmentCategoryId, equipmentShipmentId, _currentUserService.UserId);

            return Json(result.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> AddEquipmentPackingListToEquipmentShipment(int equipmentShipmentId, int equipmentPackingListId)
        {
            await _logisticService.AddEquipmentPackingListToEquipmentShipment(equipmentShipmentId, equipmentPackingListId, _currentUserService.UserId);

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<ActionResult> RemoveEquipmentPackingListFromEquipmentShipment(int equipmentShipmentId, int equipmentPackingListId)
        {
            await _logisticService.RemoveEquipmentPackingListFromEquipmentShipment(equipmentShipmentId, equipmentPackingListId, _currentUserService.UserId);

            return Json(new { success = "true" });
        }
        #region Equipment Packing List

        public async Task<ActionResult> GetEquipmentPackingLists([DataSourceRequest] DataSourceRequest request)
        {
            var result = await _logisticService.GetEquipmentPackingLists();
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetEquipmentPackingListsShipped([DataSourceRequest] DataSourceRequest request)
        {
            var result = await _logisticService.GetEquipmentPackingListsShipped();
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetEquipmentPackingListsPending([DataSourceRequest] DataSourceRequest request)
        {
            var result = await _logisticService.GetEquipmentPackingListsPending();
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetEquipmentPackingListsByEquimentShipment([DataSourceRequest] DataSourceRequest request, int equipmentShipmentId)
        {
            var result = await _logisticService.GetEquipmentPackingListsByEquimentShipment(equipmentShipmentId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetEquipmentPackingListsWithoutShipmentId([DataSourceRequest] DataSourceRequest request)
        {
            var result = await _logisticService.GetEquipmentPackingListsWithoutShipmentId();
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetEquipmentPackingListItems([DataSourceRequest] DataSourceRequest request, int equipmentPackinglistId)
        {
            var result = await _logisticService.GetEquipmentPackingListItems(equipmentPackinglistId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetEquipmentItemsNotInPackingListId([DataSourceRequest] DataSourceRequest request, int? equipmentCategoryId, int equipmentPackingListId)
        {
            var result = await _logisticService.GetEquipmentItemsNotInPackingListId(equipmentCategoryId, equipmentPackingListId, _currentUserService.UserId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> AddEquipmentPackingList()
        {
            var model = new EquipmentPackingListModel
            {
                Created = DateTime.UtcNow,
                CreatedByUserId = _currentUserService.UserId,
            };

            this.SetTitle("Add New Packing List");

            return View("EditEquipmentPackingList", model);
        }

        public async Task<ActionResult> CloneEquipmentPackingList(int id)
        {
            var equipmentPackingListId = await _logisticService.CloneEquipmentPackingList(id, _currentUserService.UserId);

            return RedirectToAction("EditEquipmentPackingList", new { @id = equipmentPackingListId });
        }

        public async Task<ActionResult> EditEquipmentPackingList(int id, string tab)
        {
            var model = await _logisticService.GetEquipmentPackingListById(id);

            this.SetTitle(string.Format("{0}", model.Number));

            ViewBag.Tab = tab;

            return View(model);
        }

        [HttpPost]
        public async Task<ActionResult> EditEquipmentPackingList(EquipmentPackingListModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            var EquipmentPackingListId = await _logisticService.EditEquipmentPackingList(model);

            this.SetMessage(MessageType.Success, string.Format("{0} packing list details have been successfully updated", model.Number));

            return RedirectToAction("EditEquipmentPackingList", new { @id = EquipmentPackingListId });
        }

        [HttpPost]
        public async Task<ActionResult> UpdatePackingListEquipmentItems(int[] equipmentItemIds, int equipmentPackingListId, int? equipmentShipmentId)
        {
            await _logisticService.AddPackingListEquipmentItems(equipmentItemIds, equipmentPackingListId, equipmentShipmentId, _currentUserService.UserId);

            return Json(new { success = "true" });
        }

        public async Task<ActionResult> DeleteEquipmentPackingListItem([DataSourceRequest] DataSourceRequest request, EquipmentPackingListItemModel model, int? equipmentShipmentId)
        {
            await _logisticService.DeleteEquipmentPackingListItem(model, _currentUserService.UserId);

            return Json(new[] { model }.ToDataSourceResult(request));
        }

        [HttpGet]
        public async Task<ActionResult> DeleteEquipmentPackingList(int id)
        {
            var equipmentPackingList = await _logisticService.DeleteEquipmentPackingList(id, _currentUserService.UserId);
            this.SetMessage(MessageType.Success, string.Format("'{0}' Packing List has been successfully deleted", equipmentPackingList.Number));

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<ActionResult> SignOffPackingList(int id)
        {
            await _logisticService.SignOffPackingList(id, _currentUserService.UserId);

            return Json(new { success = "true" });
        }

        #endregion

        #region Export

        [HttpPost]
        public async Task<ActionResult> Export(string contentType, string base64, string fileName)
        {
            var fileContents = Convert.FromBase64String(base64);

            return File(fileContents, contentType, fileName);
        }
        #endregion

        #region Equipment Shipment Non Asset Item

        public async Task<ActionResult> GetEquipmentShipmentNonAssetItems([DataSourceRequest] DataSourceRequest request, int equipmentShipmentId)
        {
            var result = await _logisticService.GetEquipmentShipmentNonAssetItems(equipmentShipmentId);
            return Json(result.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateEquipmentShipmentNonAssetItem([DataSourceRequest] DataSourceRequest request, EquipmentShipmentNonAssetItemModel model, int sId)
        {
            var result = await _logisticService.UpdateEquipmentShipmentNonAssetItem(model, sId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteEquipmentShipmentNonAssetItem([DataSourceRequest] DataSourceRequest request, EquipmentShipmentNonAssetItemModel model)
        {
            await _logisticService.DeleteEquipmentShipmentNonAssetItem(model);

            return Json(new[] { model }.ToDataSourceResult(request));
        }

        #endregion

        #region Equipment Shipment Status

        [HttpPost]
        public async Task<ActionResult> UpdateStatus(int id, string status)
        {
            var result = await _logisticService.UpdateStatus(id, status, _currentUserService.UserId);

            return Json(result);
        }

        [HttpPost]
        public async Task<ActionResult> UndoShipmentReceived(int id)
        {
            await _logisticService.UndoShipmentReceived(id, _currentUserService.UserId);

            return Json(new { success = "true" });
        }

        #endregion

        [HttpPost]
        public async Task<ActionResult> CheckMaintenancesCreatedByShipmentId(int id)
        {
            var result = await _logisticService.CheckMaintenancesCreatedByShipmentId(id);

            return Json(result);
        }

        #region Equipment Shipment Package
        public async Task<ActionResult> GetEquipmentShipmentPackages([DataSourceRequest] DataSourceRequest request, int equipmentShipmentId)
        {
            var result = await _logisticService.GetEquipmentShipmentPackages(equipmentShipmentId);
            return Json(result.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateEquipmentShipmentPackage([DataSourceRequest] DataSourceRequest request, EquipmentShipmentPackageModel model, int sId)
        {
            var result = await _logisticService.UpdateEquipmentShipmentPackage(model, sId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteEquipmentShipmentPackage([DataSourceRequest] DataSourceRequest request, EquipmentShipmentPackageModel model)
        {
            await _logisticService.DeleteEquipmentShipmentPackage(model);

            return Json(new[] { model }.ToDataSourceResult(request));
        }
        #endregion

        [HttpPost]
        public async Task<ActionResult> ReceiveAllShipmentsItems(int id)
        {
            await _logisticService.ReceiveAllShipmentsItems(id);

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<ActionResult> ReceiveAllNonAssetShipmentsItems(int id)
        {
            await _logisticService.ReceiveAllNonAssetShipmentsItems(id);
            return Json(new { success = "true" });
        }

        #region PdfExport

        [HttpPost]
        public async Task<ActionResult> PdfExport(string contentType, string base64, string fileName)
        {
            var fileContents = Convert.FromBase64String(base64);

            return File(fileContents, contentType, fileName);
        }

        #endregion

        #region Packing List PDF Report

        public async Task<ActionResult> PackingListPdf(EquipmentPackingListModel model)
        {
            this.SetTitle("Packing List Pdf Report");
            return View(model);
        }

        #endregion

        #region Equipment Item Maintenance Schedule

        public async Task<ActionResult> GetEquipmentItemMaintenanceSchedules([DataSourceRequest] DataSourceRequest request, int eId)
        {
            var result = await _logisticService.GetEquipmentItemMaintenanceSchedules(eId);
            return Json(result.ToDataSourceResult(request));
        }
        #endregion

        public async Task<ActionResult> GetShipmentDropdownData(int id)
        {
            var model = await _logisticService.GetShipmentDropdownData(id);

            return Json(model);
        }
        public async Task<ActionResult> GetAddData()
        {
            var model = await _logisticService.GetAddData(_currentUserService.UserId);

            return Json(model);
        }
        public async Task<ActionResult> GetEquipmentShipmentDocuments([DataSourceRequest] DataSourceRequest request, int equipmentShipmentId)
        {
            var result = await _logisticService.GetEquipmentShipmentDocuments(equipmentShipmentId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> AttachEquipmentShipmentDocuments(IEnumerable<IFormFile> equipmentShipmentAttachmentDocuments, int equipmentShipmentId)
        {
            await _logisticService.AttachEquipmentShipmentDocuments(equipmentShipmentAttachmentDocuments, equipmentShipmentId, _currentUserService.UserId);
            return Json(true);
        }
        public async Task<ActionResult> DeleteEquipmentShipmentDocument([DataSourceRequest] DataSourceRequest request, DocumentModel model, int equipmentShipmentId)
        {
            await _logisticService.DeleteEquipmentShipmentDocument(model, equipmentShipmentId, _currentUserService.UserId);
            return Json(ModelState.ToDataSourceResult());
        }
    }
}