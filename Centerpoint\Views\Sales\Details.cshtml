﻿@model OpportunityModel

<div>
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                @if (Model.OpportunityId.HasValue && Model.Stage != OpportunityStageConstant.Closed && Model.Stage != OpportunityStageConstant.Realised && Model.Type == OpportunityTypeConstant.Opportunity) {
                    <label>Stage</label>
                    @(Html.Kendo().DropDownListFor(m => m.Stage)
                        .DataTextField("Value")
                        .DataValueField("Key")
                        .Filter(FilterType.Contains)
                        .BindTo(Centerpoint.Common.Constants.OpportunityStageConstant.ValuesAndDescriptionsWithoutClosed.ToList())
                        .HtmlAttributes(new { @data_bind = "value:stage"})
                    )
                }
                @if (Model.OpportunityId.HasValue && Model.Stage != OpportunityStageConstant.Closed && Model.Type == OpportunityTypeConstant.Lead) {
                    <label>Stage</label>
                    @(Html.Kendo().DropDownListFor(m => m.LeadStage)
                        .DataTextField("Value")
                        .DataValueField("Key")
                        .Filter(FilterType.Contains)
                        .BindTo(Centerpoint.Common.Constants.LeadStageConstant.ValuesAndDescriptions.ToList())
                    )
                }
                @if (Model.OpportunityId.HasValue && Model.Stage == OpportunityStageConstant.Realised) {
                    <label>Stage</label>
                    @(Html.Kendo().DropDownListFor(m => m.LeadStage)
                        .DataTextField("Value")
                        .DataValueField("Key")
                        .Filter(FilterType.Contains)
                        .BindTo(Centerpoint.Common.Constants.OpportunityStageConstant.ValuesAndDescriptionsWithRealised.ToList())
                    )
                    @Html.HiddenFor(m => m.Stage)
                }
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-3">
            <div class="form-group">
                @if (Html.IsSalesAdmin() || Html.IsGlobalAdmin()) {
                    <label>
                        Operator
                        <a href="/Admin/AddCompany?opportunityId=@Model.LinkOpportunityId&revision=@Model.Revision">
                            <i class="fa fa-plus"></i>
                        </a>
                    </label>
                } else {
                    <label>Operator</label>
                }
                @(Html.Kendo().DropDownListFor(m => m.CompanyId)
                    .Filter(FilterType.Contains)
                    .OptionLabel("Select Operator")
                    .DataTextField("Text")
                    .DataValueField("Value")
                    .Events(m => m.Change("companyChange"))
                    .DataSource(d => d.Read("GetOperatorCompanies", "Lookup"))
                    .HtmlAttributes(new { @tabindex = "1", @data_bind = "value:companyId", @data_cascade_to="CompanyCountry" }))
            </div>
            <div class="form-group">
                <label>Services <span class="text-danger">*</span></label>
                @(Html.Kendo().MultiSelectFor(m => m.ServiceIds)
                    .Filter(FilterType.Contains)
                    .DataTextField("Text")
                    .DataValueField("Value")
                    .Placeholder("Select Services")
                    .HtmlAttributes(new {@tabindex = "4" })
                    .DataSource(source => {
                        source.Read(read => {
                            read.Action("GetObjectivesByCompanyId", "Lookup").Data("clientDataObjectives");
                        })
                        .ServerFiltering(true);
                    })
                )
            </div>
            <div class="form-group">
                <label>Division <span class="text-danger">*</span></label>
                @(Html.Kendo().DropDownListFor(m => m.DivisionId)
                    .OptionLabel("Select Division")
                    .DataTextField("Text")
                    .DataValueField("Value")
                    .Filter(FilterType.Contains)
                    .Events(c => c.Change("divisionChange"))
                    .DataSource(d => d.Read("GetDivisions", "Lookup"))
                    .HtmlAttributes(new {@tabindex = "3" })
                )
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label>Operator Country</label>
                @(Html.Kendo().DropDownListFor(m => m.CompanyCountry)
                    .OptionLabel("Select Operator Country")
                    .DataTextField("Key")
                    .DataValueField("Value")
                    .Filter(FilterType.Contains)
                    .DataSource(source => {
                        source.Read(read => {
                            read.Action("GetCountriesByFromCompanyId", "Lookup").Data("clientData");
                        });
                    })
                    .HtmlAttributes(new {@tabindex = "2" })
                )
            </div>
            <div class="form-group">
                <label>Owner</label>
                @(Html.Kendo().DropDownListFor(m => m.CreatedByUserId)
                    .DataTextField("Name")
                    .DataValueField("UserId")
                    .Filter(FilterType.Contains)
                    .DataSource(source => {
                        source.Read(read => {
                            read.Action("GetSalesUsers", "Lookup");
                        });
                    })
                )
            </div>
            <div class="form-group">
                <label>Base</label>
                @(Html.Kendo().DropDownListFor(m => m.BaseCompanyLocationId)
                    .OptionLabel("Select Base")
                    .DataTextField("Text")
                    .DataValueField("Value")
                    .Filter(FilterType.Contains)
                    .DataSource(d => d.Read("GetBaseCompanyLocations", "Lookup"))
                    .HtmlAttributes(new {@tabindex = "9" })
                )
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label>Partner</label>
                @(Html.Kendo().DropDownListFor(m => m.PartnerCompanyId)
                    .Filter(FilterType.Contains)
                    .OptionLabel("Select Partner")
                    .DataTextField("Text")
                    .DataValueField("Value")
                    .Events(m => m.Change("partnerCompanyChange"))
                    .DataSource(d => d.Read("GetPartnerCompanies", "Lookup"))
                    .HtmlAttributes(new {@tabindex = "3", @data_cascade_to="PartnerCountry"})
                )
            </div>
            @if (Model.Type == OpportunityTypeConstant.Opportunity) {
                <div class="form-group">
                    <label>Engineers Required <span class="text-danger">*</span></label>
                    @(Html.Kendo().DropDownListFor(m => m.EngineersRequired)
                        .OptionLabel("Select Engineers Required")
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .Filter(FilterType.Contains)
                        .BindTo(new List<SelectListItem>() {
                            new SelectListItem() {
                                Text = "0",
                                Value = "0"
                            },
                            new SelectListItem() {
                                Text = "1",
                                Value = "1"
                            },
                            new SelectListItem() {
                                Text = "2",
                                Value = "2"
                            },
                            new SelectListItem() {
                                Text = "3",
                                Value = "3"
                            },
                            new SelectListItem() {
                                Text = "4",
                                Value = "4"
                            }
                        })
                    )
                </div>
            }

            @if (Html.IsSalesAdmin() || Html.IsGlobalAdmin()) {
                if (Model.Type == OpportunityTypeConstant.Lead) {
                    <div class="d-flex">
                        <div class="form-group col-md-5 pl-0">
                            <label>Currency</label>
                            @(Html.Kendo().DropDownListFor(m => m.CurrencyId)
                                .DataTextField("Text")
                                .DataValueField("Value")
                                .Filter(FilterType.Contains)
                                .DataSource(d => d.Read("GetCurrencies", "Lookup"))
                                .HtmlAttributes(new {@id = "leadCurrency" })
                            )
                        </div>
                        <div class="form-group col-md-7 p-0">
                            <label>Value</label>
                            @(Html.Kendo().NumericTextBoxFor(p => p.Value)
                                .HtmlAttributes(new { @data_bind = "value:value", @id = "leadValue" })
                                .Spinners(false)
                                .Format("n2")
                            )
                        </div>
                    </div>
                }
            }
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label>Partner Country</label>
                @(Html.Kendo().DropDownListFor(m => m.PartnerCountry)
                    .OptionLabel("Select Partner Country")
                    .DataTextField("Key")
                    .DataValueField("Value")
                    .Filter(FilterType.Contains)
                    .DataSource(source => {
                        source.Read(read => {
                            read.Action("GetCountriesByPartnerCompanyId", "Lookup").Data("partnerData");
                        });
                    })
                    .HtmlAttributes(new {@tabindex = "4" })
                )

            </div>
            <div class="form-group" data-bind="visible:customerVisible">
                <label>Customer <span class="text-danger">*</span></label>
                @(Html.Kendo().DropDownListFor(m => m.Customer)
                    .OptionLabel("Select Customer")
                    .DataTextField("Value")
                    .DataValueField("Key")
                    .Filter(FilterType.Contains)
                    .Events(c => c.Change("customerChange"))
                    .BindTo(Centerpoint.Common.Constants.CustomerTypeConstant.ValuesAndDescriptions.ToList())
                )
                @Html.HiddenFor(m => m.Customer)
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <div class="form-group">
                <label>Description <span class="text-danger">*</span></label>
                @(Html.TextAreaFor(p => p.Description, new { @class = "form-control w-100", @rows = "5", @tabindex = "3" }))
            </div>
        </div>
    </div>
    @if (Model.Type == OpportunityTypeConstant.Lead) {
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label>Estimate Start Date <span class="text-danger">*</span></label>
                    @(Html.Kendo().DatePickerFor(m => m.MobilisationDate)
                        .Min(DateTime.Now)
                        .HtmlAttributes(new { @id = "leadMobilisationDate" })
                    )
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex">
                    <div class="form-group col-md-4 pl-0">
                        <label>Duration</label>
                        @(Html.Kendo().DropDownListFor(m => m.Duration)
                            .HtmlAttributes(new { @id = "leadDuration" })
                            .DataTextField("Value")
                            .DataValueField("key")
                            .Filter(FilterType.Contains)
                            .DataSource(d => d.Read(read => read.Action("GetDays", "Lookup")))
                        )
                    </div>
                    <div class="form-group col-md-8 p-0">
                        <label>Time Period</label>
                        @(Html.Kendo().DropDownListFor(m => m.TimePeriod)
                            .DataTextField("Value")
                            .DataValueField("Key")
                            .Filter(FilterType.Contains)
                            .BindTo(Centerpoint.Common.Constants.OpportunityTimePeriodConstant.ValuesAndDescriptions.ToList())
                            .HtmlAttributes(new { @id = "leadTimePeriod" })
                        )
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label>Expected Closeout Date</label>
                    @(Html.Kendo().DatePickerFor(m => m.CloseoutDate)
                        .Min(DateTime.Now)
                        .HtmlAttributes(new { @disabled = "true", @id = "leadCloseOut" })
                    )
                </div>
            </div>
        </div>
    }
    @if (Model.Stage == OpportunityStageConstant.Closed && Model.Type == OpportunityTypeConstant.Lead) {
        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label>Closure Reason</label>
                    @(Html.Kendo().TextBoxFor(p => p.OpportunityClosedReasonName)
                        .HtmlAttributes(new { @readonly = "readonly" })
                )
                </div>
                <div class="form-group">
                    <label>Closure Reason Comment</label>
                    @(Html.TextAreaFor(p => p.ClosedReasonComment, new { @class = "form-control w-100", @rows = "5", @readonly = "readonly" }))
                </div>
            </div>
        </div>
    }
    @if (Model.Type == OpportunityTypeConstant.Opportunity) {
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    @if (Model.CompanyId.HasValue) {
                        <label>
                            Field
                            <a href="@Url.Action("ViewCustomerAssets","Admin", new {@id = Model.CompanyId, @opportunityId= Model.ParentOpportunityId.HasValue ? Model.ParentOpportunityId:Model.OpportunityId, @revision=Model.Revision })">
                                <i class="fa fa-plus"></i>
                            </a>
                        </label>
                    } else {
                        <label>Field</label>
                    }
                    @(Html.Kendo().MultiSelectFor(m => m.CompanyFieldIds)
                        .Placeholder("Select Field(s)")
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .Events(m => m.Change("companyFieldChange"))
                        .Filter(FilterType.Contains)
                        .DataSource(source => {
                            source.Read(read => {
                                read.Action("GetFieldsByCompanyOrPartnerId", "Lookup").Data("customerData");
                                })
                            .ServerFiltering(true);
                        })
                        .HtmlAttributes(new {@data_bind = "value:companyFieldIds", @data_value_primitive = "true" })
                    )
                </div>
                <div class="form-group">
                    <label>Rig Type</label>
                    @(Html.Kendo().DropDownListFor(m => m.RigType)
                        .DataTextField("Value")
                        .DataValueField("Key")
                        .OptionLabel("Select Rig Type")
                        .Filter(FilterType.Contains)
                        .BindTo(Centerpoint.Common.Constants.RigTypeConstant.ValuesAndDescriptions.ToList())
                    )
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    @if (Model.CompanyId.HasValue) {
                        <label>
                            Well
                            <a href="@Url.Action("ViewCustomerAssets","Admin", new {@id = Model.CompanyId, @opportunityId= Model.ParentOpportunityId.HasValue ? Model.ParentOpportunityId:Model.OpportunityId, @revision=Model.Revision })">
                                <i class="fa fa-plus"></i>
                            </a>
                        </label>
                    } else {
                        <label>Well</label>
                    }
                    @(Html.Kendo().MultiSelectFor(m => m.CompanyWellIds)
                        .Placeholder("Select Well(s)")
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .Events(m => m.Change("companyWellChange"))
                        .Filter(FilterType.Contains)
                        .DataSource(source => { source
                            .Read(read => {
                            read.Action("GetCompanyWellsByCompanyFieldIds", "Lookup").Data("filterCompanyWells");
                                })
                                .ServerFiltering(true);
                                })
                        .HtmlAttributes(new { @data_bind = "value:companyWellIds" })
                    )
                </div>
                <div class="d-flex">
                    <div class="form-group col-md-4 pl-0">
                        <label>Currency</label>
                        @(Html.Kendo().DropDownListFor(m => m.CurrencyId)
                            .DataTextField("CurrencyFormat")
                            .DataValueField("CurrencyId")
                            .Filter(FilterType.Contains)
                            .DataSource(d => d.Read("GetFullCurrencies", "Lookup"))
                            .HtmlAttributes(new {@id = "oppsCurrency" })
                        )
                    </div>
                    <div class="form-group col-md-8 p-0">
                        <label>Value</label>
                        @(Html.Kendo().NumericTextBoxFor(p => p.Value)
                            .HtmlAttributes(new { @data_bind = "value:value", @id = "oppsValue" })
                            .Spinners(false).Format("n2")
                        )
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label>Conveyance Method</label>
                    @(Html.Kendo().MultiSelectFor(m => m.OpportunityConveyanceValues)
                        .DataTextField("Value")
                        .Filter(FilterType.Contains)
                        .DataValueField("Key")
                        .Placeholder("Select Conveyance Method")
                        .BindTo(Centerpoint.Common.Constants.ConveyanceConstant.ValuesAndDescriptions.ToList())
                    )
                </div>
                @if (Model.OpportunityId.HasValue && Model.Type == OpportunityTypeConstant.Opportunity) {
                    <div class="form-group">
                        <label>Probability Value</label>
                        @(Html.Kendo().NumericTextBoxFor(p => p.Probability)
                            .Spinners(false)
                            .Format("n2")
                            .Decimals(2)
                            .HtmlAttributes(new { @data_bind = "value:probabilityValue", @disabled = "disabled" })
                        )
                    </div>
                    <div class="d-flex">
                        <div class="form-group col-md-6 pl-0">
                            <label>Probability (%)</label>
                            @(Html.Kendo().NumericTextBox()
                                .Name("probablityPercentage")
                                .Spinners(false)
                                .Decimals(0)
                                .Format("n0")
                                .HtmlAttributes(new { @data_bind = "value:probablityPercentage", @disabled = "disabled" })
                            )
                        </div>
                         <div class="form-group col-md-6 p-0">
                            <label>Probability Override (%)</label>
                            @(Html.Kendo().NumericTextBoxFor(p => p.ProbabilityOverride)
                                .Spinners(false)
                                .Decimals(0)
                                .Min(0)
                                .Max(100)
                                .Format("n0")
                                .HtmlAttributes(new { @data_bind = "value:probabilityOverridePercentage" })
                            )
                        </div>
                    </div>
                }
            </div>
        </div>
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label>FTA Required ?</label>
                    @(Html.Kendo().DropDownListFor(m => m.FtaRequired)
                        .OptionLabel("Select FTA Required")
                        .DataValueField("Key")
                        .DataTextField("Value")
                        .Filter(FilterType.Contains)
                        .BindTo(Html.BooleanListValues().ToList())
                    )
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label>New Personnel Visa Required ?</label>
                    @(Html.Kendo().DropDownListFor(m => m.NewPersonnelVisaRequired)
                        .OptionLabel("Select Personnel Visa Required")
                        .DataValueField("Key")
                        .DataTextField("Value")
                        .Filter(FilterType.Contains)
                        .BindTo(Html.BooleanListValues().ToList())
                    )
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label>Main Equipment Required</label>
                    @(Html.Kendo().MultiSelectFor(m => m.EquipmentCategoryIds)
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .Filter(FilterType.Contains)
                        .Placeholder("Select Equipment(s)")
                        .HtmlAttributes(new { @data_value_primitive = "true", @data_bind = "value:equipmentCategoryIds" })
                        .DataSource(source => {
                            source.Read(read => {
                                read.Action("GetEquipmentCategoriesByDivisionId", "Lookup").Data("divisionData");
                            })
                            .ServerFiltering(true);
                        })
                    )
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label>Objectives</label>
                    @(Html.TextAreaFor(p => p.OpportunityObjectives, new { @class = "form-control w-100", @rows = "5" }))
                </div>
                <div class="form-group">
                    <label>Special Requirements</label>
                    @(Html.TextAreaFor(p => p.ValueProposition, new { @class = "form-control w-100", @rows = "5" }))
                </div>
                <div class="form-group">
                    <label>QHSE Consideration</label>
                    @(Html.TextAreaFor(p => p.QhseConsideration, new { @class = "form-control w-100", @rows = "5" }))
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3">
                <div class="form-group">
                    <label>Mobilisation Date <span class="text-danger">*</span></label>
                    @Html.Kendo().DatePickerFor(m => m.MobilisationDate).Min(DateTime.Now)
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-flex">
                    <div class="form-group col-md-4 pl-0">
                        <label>Duration</label>
                        @(Html.Kendo().DropDownListFor(m => m.Duration)
                            .DataTextField("Value")
                            .DataValueField("Key")
                            .Filter(FilterType.Contains)
                            .DataSource(d => d.Read(read => read.Action("GetDays", "Lookup")))
                        )
                    </div>
                    <div class="form-group col-md-8 p-0">
                        <label>Time Period</label>
                        <br />
                        @(Html.Kendo().DropDownListFor(m => m.TimePeriod)
                            .DataTextField("Value")
                            .DataValueField("Key")
                            .Filter(FilterType.Contains)
                            .BindTo(Centerpoint.Common.Constants.OpportunityTimePeriodConstant.ValuesAndDescriptions.ToList())
                        )
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label>Expected Closeout Date</label>
                    @(Html.Kendo().DatePickerFor(m => m.CloseoutDate).Min(DateTime.Now).HtmlAttributes(new { @disabled = "true" }))
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label>System Integration Testing Required ?</label>
                        @(Html.Kendo().DropDownListFor(m => m.SystemIntegrationTestingRequired)
                            .DataValueField("Key")
                            .DataTextField("Value")
                            .Filter(FilterType.Contains)
                            .BindTo(Html.BooleanListValues().ToList())
                        )
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label>Comments</label>
                    @(Html.TextAreaFor(p => p.Comments, new { @class = "form-control w-100", @rows = "5" }))
                </div>
            </div>
        </div>
        if (Model.Stage == OpportunityStageConstant.Closed && Model.Type == OpportunityTypeConstant.Opportunity) {
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label>Closure Reason</label>
                        @(Html.Kendo().TextBoxFor(p => p.OpportunityClosedReasonName).HtmlAttributes(new {@readonly = "readonly" }))
                    </div>
                    <div class="form-group">
                        <label>Closure Reason Comment</label>
                        @(Html.TextAreaFor(p => p.ClosedReasonComment, new { @class = "form-control w-100", @rows = "5", @readonly = "readonly" }))
                    </div>
                </div>
            </div>
        }
    }
    @Html.HiddenFor(m => m.OpportunityId)
    @Html.HiddenFor(m => m.ParentOpportunityId)
    @Html.HiddenFor(m => m.Revision)
    @Html.HiddenFor(m => m.Type)
    @Html.HiddenFor(m => m.Created)
    @Html.HiddenFor(m => m.CreatedBy)
    @Html.HiddenFor(m => m.Modified)
    @Html.HiddenFor(m => m.ModifiedBy)
    @Html.HiddenFor(m => m.Name)
    @Html.HiddenFor(m => m.ProjectId)
    @Html.HiddenFor(m => m.ProjectName)
    @Html.HiddenFor(m => m.PoundValue)
    @Html.HiddenFor(m => m.PoundProbability)
    @Html.HiddenFor(m => m.CustomerCompanyId)
    @Html.HiddenFor(m => m.CustomerCountry)
    @Html.HiddenFor(m => m.Probability, new { @data_bind = "value:probabilityValue" })
    @Html.HiddenFor(m => m.GridName)
    @Html.HiddenFor(m => m.CustomerName)
    @Html.HiddenFor(m => m.ServiceAlias)
    @Html.HiddenFor(m => m.Wells)


    <div class="d-flex w-100 justify-content-start actionsContainer">
        @if (Html.IsSalesAdmin() || Html.IsGlobalAdmin()) {
            if (Model.Type == OpportunityTypeConstant.Lead && Model.Stage != OpportunityStageConstant.Closed) {
                <button type="submit" class="btn btn-sm btn-primary mt-3">Save Lead Details</button>
            }
        } else {
            if (Model.Type == OpportunityTypeConstant.Lead && Model.Stage != OpportunityStageConstant.Closed) {
                <button type="submit" class="btn btn-sm btn-primary mt-3">Submit New Lead</button>
            }
        }
        @if (Model.Type == OpportunityTypeConstant.Opportunity && Model.Stage != OpportunityStageConstant.Closed) {
            <button type="submit" class="btn btn-sm btn-primary mt-3">Save Opportunity Details</button>
        }
    </div>
</div>