﻿@model Centerpoint.Model.ViewModels.SendSifAdditionEmail
<html>
<body>
    <p style="font-family:Calib<PERSON>,<PERSON><PERSON>,sans-serif; font-size:14px">Hi @Model.Name,</p>
    <p style="font-family:Cal<PERSON><PERSON>,<PERSON><PERSON>,sans-serif; font-size:14px">A new SIF has been created and is waiting approval</p>
    <p style="font-family:Calibri,Arial,sans-serif; font-size:14px">SIF : @Model.SifName - @Model.Title</p>
    <p style="font-family:Calibri,Arial,sans-serif; font-size:14px">Created By : @Model.CreatedBy</p>
    <p style="font-family:Calibri,Arial,sans-serif; font-size:14px">Category : @Model.Category</p>
    <p style="font-family:Calibri,Arial,sans-serif; font-size:14px">Sub Categories : @string.Join(",", Model.SubCategories)</p>
    <p style="font-family:<PERSON><PERSON><PERSON>,<PERSON><PERSON>,sans-serif; font-size:14px">Please <a href="@Model.Link"> click here</a> to view the SIF.</p>
</body>
</html>