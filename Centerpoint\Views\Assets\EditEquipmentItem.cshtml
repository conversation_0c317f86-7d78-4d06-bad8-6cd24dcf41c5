﻿@using Centerpoint.Model.ViewModels.Lookup
@model EquipmentItemModel

@Html.Partial( "_GridNotification", EntityType.EquipmentItem)
@if (Model.Depth > 0 && string.IsNullOrEmpty(Model.DepthUnit)
                          || (Model.Width > 0 && string.IsNullOrEmpty(Model.WidthUnit))
                          || (Model.Height > 0 && string.IsNullOrEmpty(Model.HeightUnit))
                          || (Model.OuterDiameter > 0 && string.IsNullOrEmpty(Model.OuterDiameterUnit))
                          || (Model.Weight > 0 && string.IsNullOrEmpty(Model.WeightUnit)))
{
    <div class="alert alert-dismissible alert-warning">
        <button type="button" class="close" data-bs-dismiss="alert">×</button>
        <strong>Warning!</strong> Units for dimensions must have a value.
    </div>
}
<i class="fa fa-exclamation-circle text-warning" data-bind="visible:totalActiveMRs" title="There are MRs in the system for this item"></i>

<div class="header-container-between">
    @if (Model.EquipmentItemId.HasValue)
    {
        <h4><i class="fa fa-tags"></i> @Model.NewEquipmentItemName</h4>
    }
</div>
<br />
<div class="row">
    <div class="col-md-12">
        @if (ViewBag.ReturnUrl != null && ViewBag.ReturnUrl.Contains("Assets"))
        {
            <a class="btn btn-info btn-sm" href="@Url.Action("Index", "Assets", new {@equipmentCategory=Model.EquipmentCategoryName})"><i class="fa fa-refresh"></i>Return to Asset Viewer</a>
        }
        @if (ViewBag.ReturnUrl != null && ViewBag.ReturnUrl.Contains("Admin"))
        {
            <a class="btn btn-info btn-sm" href="@Url.Action("Equipment", "Admin", new {@equipmentCategory=Model.EquipmentCategoryName})"><i class="fa fa-refresh"></i>Return to Asset Admin</a>
        }
        @if (ViewBag != null && ViewBag.ProjectId != null)
        {
            <a class="btn btn-info btn-sm" href="@Url.Action("EditProject", "Operation", new { @id=ViewBag.ProjectId, @tab="equipmentItems"})"><i class="fa fa-refresh"></i>Go to Project</a>
        }

        @if (ViewBag != null && ViewBag.RunId != null)
        {
            <a class="btn btn-info btn-sm" href="@Url.Action("EditRun", "Operation", new { @id=ViewBag.RunId, @tab="equipmentItems"})"><i class="fa fa-refresh"></i>Return to Run</a>
        }

        @if (ViewBag != null && ViewBag.MaintenanceRecordId != null)
        {
            <a class="btn btn-info btn-sm" href="@Url.Action("EditMaintenanceRecord", "Maintenance", new { @id=ViewBag.MaintenanceRecordId})"><i class="fa fa-refresh"></i>Return to MR</a>
        }

        @if (ViewBag != null && ViewBag.ShipmentId != null)
        {
            <a class="btn btn-info btn-sm" href="@Url.Action("EditEquipmentShipment", "Logistics", new { @id=ViewBag.ShipmentId, @tab="equipmentItems"})"><i class="fa fa-refresh"></i>Return to Shipment</a>
        }

        @if (ViewBag != null && ViewBag.PackingListId != null)
        {
            <a class="btn btn-info btn-sm" href="@Url.Action("EditEquipmentPackingList", "Logistics", new { @id=ViewBag.PackingListId, @tab="packingListItems"})"><i class="fa fa-refresh"></i>Return to Packing List</a>
        }
        @if ((Html.IsJuniorFieldEngineer() || Html.IsFieldEngineer() || Html.IsSeniorFieldEngineer() || Html.IsAssetAdmin() || Html.IsMaintenanceAdmin() || Html.IsMaintenanceEngineer() || Html.IsGlobalAdmin()) && (!Html.IsAnsaAnalyst() && !Html.IsAnsaAdministrator()))
        {
            if (Model.EquipmentItemId.HasValue && Model.Status != EquipmentConstant.Inactive && Model.Status != EquipmentConstant.AcceptancePending && Model.Status != EquipmentConstant.Lost && Model.Status != EquipmentConstant.Archived)
            {
                <a class="btn btn-primary btn-sm" href="#" data-bind="click:showMaintenanceRecordWindow"><i class="fa fa-plus"></i>Create MR</a>
            }
        }

        @if (!Html.IsAnsaAdministrator() && !Html.IsAnsaAnalyst())
        {
            if (Model.EquipmentItemId.HasValue && (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) && Model.Status != Centerpoint.Common.Constants.EquipmentConstant.AcceptancePending && Model.Status != Centerpoint.Common.Constants.EquipmentConstant.Lost && Model.Status != Centerpoint.Common.Constants.EquipmentConstant.Inactive && Model.Status != EquipmentConstant.Archived)
            {
                <a class="btn btn-danger btn-sm" data-bind="click:lostClick"><i class="fa fa-thumbs-down"></i>Mark as Lost</a>
                <a class="btn btn-warning btn-sm" data-bind="click:inActiveClick"><i class="far fa-thumbs-down"></i>Mark as Inactive</a>
            }
            if (Model.EquipmentItemId.HasValue && (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) && (Model.Status == Centerpoint.Common.Constants.EquipmentConstant.Lost || Model.Status == Centerpoint.Common.Constants.EquipmentConstant.Inactive || Model.Status == Centerpoint.Common.Constants.EquipmentConstant.Quarantined))
            {
                <a class="btn btn-success btn-sm" data-bind="click:operationalClick"><i class="fa fa-thumbs-up"></i>Mark as Operational</a>
            }
            if (Model.AcceptancePending && (Html.IsGlobalAdmin() || Html.IsAssetAdmin()))
            {
                <a class="btn btn-primary btn-sm" href="#" data-bind="click:showAcceptEquipmentItemWindow"><i class="fa fa-thumbs-up"></i>Accept Equipment Item</a>
            }
            if (Model.ParentEquipmentItemId.HasValue)
            {
                <a class="btn btn-info btn-sm" href=@Url.Action("EditEquipmentItem", "Assets", new { @id = @Model.ParentEquipmentItemId, @returnurl = ViewBag.ReturnUrl })><i class="fa fa-refresh"></i>Bundle Parent Item</a>
            }
            if (Model.IsReserved && Html.IsGlobalAdmin())
            {
                <a class="btn btn-success btn-sm " data-bind="click:unReserveItemClick"><i class="fa fa-check"></i>Mark as Not Reserved</a>
            }
            if (Model.EquipmentItemId.HasValue && (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) && Model.Status != Centerpoint.Common.Constants.EquipmentConstant.AcceptancePending && Model.Status != Centerpoint.Common.Constants.EquipmentConstant.Lost && Model.Status != Centerpoint.Common.Constants.EquipmentConstant.Inactive && Model.Status != Centerpoint.Common.Constants.EquipmentConstant.Quarantined && Model.Status != EquipmentConstant.Archived)
            {
                <a class="btn btn-dark btn-sm" data-bind="click:quarantineItemClick"><i class="fa fa-ban"></i>Mark as Quarantine</a>
            }
            if (Model.EquipmentItemId.HasValue && (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) && Model.Status == Centerpoint.Common.Constants.EquipmentConstant.Inactive)
            {
                <a class="btn btn-warning btn-sm" id="archivedBtn" data-bind="click:archivedClick" style="display: none;"><i class="far fa-thumbs-down"></i>Mark as Archived</a>
                <a class="btn btn-danger btn-sm" href="#" data-bind="click:deleteEquipmentItem"><i class="fa fa-ban"></i>Delete Equipment Item</a>
            }
            if (Model.EquipmentItemId.HasValue && (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) && Model.Status == Centerpoint.Common.Constants.EquipmentConstant.Archived)
            {
                <a class="btn btn-warning btn-sm" data-bind="click:inActiveClick"><i class="far fa-thumbs-down"></i>Mark as Inactive</a>
                <a class="btn btn-danger btn-sm" href="#" data-bind="click:deleteEquipmentItem"><i class="fa fa-ban"></i>Delete Equipment Item</a>
            }
            if (GlobalSettings.IsWellsense && Model.EquipmentItemId.HasValue && (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) && Model.Status != Centerpoint.Common.Constants.EquipmentConstant.AcceptancePending && Model.Status != Centerpoint.Common.Constants.EquipmentConstant.Lost && Model.Status != Centerpoint.Common.Constants.EquipmentConstant.Inactive && Model.Status != Centerpoint.Common.Constants.EquipmentConstant.Quarantined && Model.Status != EquipmentConstant.Archived)
            {
                <a class="btn btn-primary" href="@Url.Action("CollectEquipmentItemsData", "Assets", new { id = Model.EquipmentItemId })">
                    <i class="fas fa-download"></i> Export
                </a>
            }
        }
    </div>
  </div>
<hr />

@(Html.Kendo().TabStrip()
            .Name("editEquipmentStrips")
            .SelectedIndex(0)
            .Animation(false)
            .Events(e=>e.Activate("onBundleTabActivate"))
            .Items( tabstrip => {

            tabstrip.Add().Text("Details")
                .Selected(true)
                .Content(@<text>
        <div id="editAssetsDetails">
            @{
                <div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Equipment Number</label>
                                @if (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) {
                                @(Html.Kendo().TextBoxFor(m => m.EquipmentNumber))
                        } else {
                                @(Html.Kendo().TextBoxFor(m => m.EquipmentNumber))
                                @Html.HiddenFor(u => u.EquipmentNumber)
                        }
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Info</label>
                                @if (Html.IsGlobalAdmin() || Html.IsAssetAdmin() || Html.IsAssetEngineers()) {
                                @(Html.Kendo().TextBoxFor(m => m.EquipmentInfo))
                        } else {
                                @(Html.Kendo().TextBoxFor(m => m.EquipmentInfo)
                               .HtmlAttributes(new { @readonly = "readonly" }))
                                @Html.HiddenFor(u => u.EquipmentInfo)
                        }
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Division</label>
                                @if (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) {
                                @(Html.Kendo().DropDownListFor(m => m.DivisionId)
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .Filter("contains")
                        .AutoBind(false)
                        .Events(e => e.Open("kendoDropdownOpen"))
                        .DataSource(d => d.Read("GetDivisions", "Lookup")))
                        } else {
                                @(Html.Kendo().DropDownListFor(m => m.DivisionId)
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .Filter("contains")
                        .Events(e => e.Open("kendoDropdownOpen"))
                        .HtmlAttributes(new { @readonly = "readonly" })
                        .AutoBind(false)
                        .DataSource(d => d.Read("GetDivisions", "Lookup")))
                                @Html.HiddenFor(u => u.DivisionId)

                        }
                            </div>
                        </div>
                    </div>
                    <div class="row d-flex align-items-end">
                        @if (Model.Status != EquipmentConstant.AcceptancePending) {
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Current Client</label>
                                @(Html.Kendo().TextBoxFor(m => m.CurrentCompanyLocationCompanyName)
                        .HtmlAttributes(new { @readonly = "readonly" }))
                            </div>
                        </div>
                }
                        @if (Model.Status != EquipmentConstant.AcceptancePending) {
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Current Location</label>
                                @(Html.Kendo().TextBoxFor(m => m.CurrentCompanyLocationName)
                        .HtmlAttributes(new { @readonly = "readonly" }))
                            </div>
                        </div>
                }

                        @if ((Html.IsLogisticsAdmin() || Html.IsMaintenanceAdmin() || Html.IsAssetAdmin() || Html.IsGlobalAdmin()) && (!Html.IsAnsaAnalyst() && !Html.IsAnsaAdministrator())) {
                if ((Model.EquipmentItemId.HasValue && Model.Status != EquipmentConstant.AcceptancePending && !Model.IsTransit && !Model.IsPartofBundle) || Model.IsParentLocationChanged) {
                        <div class="col-md-3">
                            <div class="form-group">
                                <a id="currentLocationChangeButton" href="#" data-bind="click: showCurrentLocationChangeWindow" class="btn btn-primary btn-sm p-2">Change Current Location</a>
                            </div>
                        </div>
                }
                }
                    </div>
                    <div class="row">
                        @if (GlobalSettings.IsWellsense)
                        {
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Country of Origin</label>
                                    @(Html.Kendo().TextBoxFor(m => m.CountryOfOrigin))
                                </div>
                                <div class="form-group">
                                    <label>Waiver Code</label>
                                    @(Html.Kendo().TextBoxFor(m => m.WaiverCode))
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Import Commodity Code</label>
                                    @(Html.Kendo().TextBoxFor(m => m.ImportCommodityCode))
                                </div>
                                <div class="form-group">
                                    <label>Waiver Requirement</label>
                                    @(Html.Kendo().TextBoxFor(m => m.WaiverRequirement))
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Export Commodity Code</label>
                                    @(Html.Kendo().TextBoxFor(m => m.ExportCommodityCode))
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>US HTS Commodity Code</label>
                                    @(Html.Kendo().TextBoxFor(m => m.USHTSCommodityCode))
                                </div>
                            </div>
                            @Html.HiddenFor(u => u.SerialNumber)
                        }
                        else
                        {
                            @Html.HiddenFor(u => u.CommodityCode)
                            @Html.HiddenFor(u => u.CountryOfOrigin)
                            @Html.HiddenFor(u => u.WaiverCode)
                            @Html.HiddenFor(u => u.ImportCommodityCode)
                            @Html.HiddenFor(u => u.WaiverRequirement)
                            @Html.HiddenFor(u => u.ExportCommodityCode)
                            @Html.HiddenFor(u => u.USHTSCommodityCode)

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Serial Number</label>
                                    @(Html.Kendo().TextBoxFor(m => m.SerialNumber))
                                </div>
                            </div>         
                        }
                    </div>
                </div>

                <div class="card mt-2">
                    <div class="card-header">
                        <h6 class="mb-0">Details</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Manufacturer</label>
                                    @if (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) {
                                    @(Html.Kendo().DropDownListFor(m => m.ManufacturerCompanyId)
                            .Filter("contains")
                            .OptionLabel("Select Manufacturer")
                            .DataTextField("Text")
                            .DataValueField("Value")
                            .Events(x=>x.Change("cascadeDropdownFilterHelper"))
                            .AutoBind(false)
                            .Events(e => e.Open("kendoDropdownOpen"))
                            .DataSource(d => d.Read(r=>r.Action("GetCompanies", "Lookup")))
                            .HtmlAttributes(new{ @data_cascade_to="ManufacturerCompanyLocationId"}))
                            } else {
                                    @(Html.Kendo().TextBoxFor(m => m.ManufacturerCompanyName)
                            .HtmlAttributes(new { @readonly = "readonly" }))
                            }
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Manufacturer Location</label>
                                    @if (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) {
                                    @(Html.Kendo().DropDownListFor(m => m.ManufacturerCompanyLocationId)
                            .Filter("contains")
                            .OptionLabel("Select Location")
                            .DataTextField("Text")
                            .DataValueField("Value")
                            .AutoBind(false)
                            .Events(e => e.Open("kendoDropdownOpen"))
                            .DataSource(source => {
                            source.Read(read => {
                            read.Action("GetLocationsByCompanyId", "Lookup").Data("filterManufacturerCompanyLocations");
                            });
                            }))
                            } else {
                                    @(Html.Kendo().TextBoxFor(m => m.ManufacturerCompanyLocationName)
                            .HtmlAttributes(new { @readonly = "readonly" }))
                                    @Html.HiddenFor(u => u.ManufacturerCompanyLocationId)
                            }
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Net Book Value</label>
                                    @(Html.Kendo().NumericTextBoxFor<decimal>(m => m.DepreciatedPrice)
                                        .Spinners(false)
                                        .HtmlAttributes(new { @readonly = "readonly" }))
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Currency</label>
                                    @if (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) {
                                    @(Html.Kendo().DropDownListFor(m => m.CurrencyId)
                            .DataTextField("Text")
                            .DataValueField("Value")
                            .Filter("contains")
                            .OptionLabel("Select Currency")
                            .HtmlAttributes(new { @data_value_primitive = "true" })
                            .AutoBind(false)
                            .Events(e => e.Open("kendoDropdownOpen"))
                            .DataSource(dataSource => dataSource.Read("GetCurrencies", "Lookup")))
                            } else {
                                    @(Html.Kendo().DropDownListFor(m => m.CurrencyId)
                            .DataTextField("Text")
                            .DataValueField("Value")
                            .Filter("contains")
                            .OptionLabel("Select Currency")
                            .HtmlAttributes(new { @data_value_primitive = "true", @readonly = "readonly" })
                            .AutoBind(false)
                            .Events(e => e.Open("kendoDropdownOpen"))
                            .DataSource(dataSource => dataSource.Read("GetCurrencies", "Lookup")))
                                    @Html.HiddenFor(u => u.CurrencyId)
                            }
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Retest Date</label>
                                    @if (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) {
                                    @(Html.Kendo().DatePickerFor(m => m.RetestDate)
                            .Max(DateTime.Now))
                            } else {
                                    @(Html.Kendo().DatePickerFor(m => m.RetestDate)
                            .Max(DateTime.Now)
                            .HtmlAttributes(new { @readonly = "readonly" }))
                                    @Html.HiddenFor(u => u.RetestDate)
                            }
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Purchase Price</label>
                                    @if (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) {
                                    @(Html.Kendo().NumericTextBoxFor<decimal>
                            (m => m.Price)
                            .Spinners(false))
                            } else {
                                    @(Html.Kendo().NumericTextBoxFor<decimal>
                            (m => m.Price)
                            .Spinners(false)
                            .HtmlAttributes(new { @readonly = "readonly" }))
                                    @Html.HiddenFor(u => u.Price)
                            }
                                </div>
                                <div class="form-group">
                                    <label>Tracked Non-Asset Item</label>
                                    @if (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) {
                                    @(Html.Kendo().DropDownListFor(m => m.TrackedNonAssetItem)
                            .DataValueField("Key")
                            .DataTextField("Value")
                            .Filter("contains")
                            .BindTo(new List<KeyValuePair<string, string>> { new KeyValuePair<string, string>(Boolean.FalseString, "No"), new KeyValuePair<string, string>(Boolean.TrueString, "Yes"), }))
                            } else {
                                    @(Html.Kendo().DropDownListFor(m => m.TrackedNonAssetItem)
                            .DataValueField("Key")
                            .DataTextField("Value")
                            .Filter("contains")
                            .BindTo(new List<KeyValuePair<string, string>> { new KeyValuePair<string, string>(Boolean.FalseString, "No"), new KeyValuePair<string, string>(Boolean.TrueString, "Yes"), })
                            .HtmlAttributes(new { @readonly = "readonly" }))
                                    @Html.HiddenFor(u => u.TrackedNonAssetItem)
                            }
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Purchased Date</label>
                                    @if (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) {
                                    @(Html.Kendo().DatePickerFor(m => m.PurchasedDate)
                            .Max(DateTime.Now))
                            } else {
                                    @(Html.Kendo().DatePickerFor(m => m.PurchasedDate)
                            .Max(DateTime.Now)
                            .HtmlAttributes(new { @readonly = "readonly" }))
                                    @Html.HiddenFor(u => u.PurchasedDate)
                            }
                                </div>
                                <div class="form-group">
                                    <label>Internal Invoice Number</label>
                                    @if (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) {
                                    @(Html.Kendo().TextBoxFor(m => m.InternalInvoiceNumber))
                            } else {
                                    @(Html.Kendo().TextBoxFor(m => m.InternalInvoiceNumber)
                            .HtmlAttributes(new { @readonly = "readonly" }))
                                    @Html.HiddenFor(u => u.InternalInvoiceNumber)
                            }
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Received Date</label>
                                    @if (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) {
                                    @(Html.Kendo().DatePickerFor(m => m.ReceivedDate)
                            .Max(DateTime.Now))
                            } else {
                                    @(Html.Kendo().DatePickerFor(m => m.ReceivedDate)
                            .Max(DateTime.Now)
                            .HtmlAttributes(new { @readonly = "readonly" }))
                                    @Html.HiddenFor(u => u.ReceivedDate)
                            }
                                </div>
                                <div class="form-group">
                                    <label>External Invoice Number</label>
                                    @if (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) {
                                    @(Html.Kendo().TextBoxFor(m => m.ExternalInvoiceNumber))
                            } else {
                                    @(Html.Kendo().TextBoxFor(m => m.ExternalInvoiceNumber)
                            .HtmlAttributes(new { @readonly = "readonly" }))
                                    @Html.HiddenFor(u => u.ExternalInvoiceNumber)
                            }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header">
                        <h6 class="mb-0">Dimensions</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="editEquipmentLabelSmall">Length</label>
                                    <div class="d-inline-block">
                                        @(Html.Kendo().NumericTextBoxFor(m => m.Height).Spinners(false).Min(0).Format("n3").Decimals(3))
                                    </div>
                                    <div class="d-inline-block">
                                        @(Html.Kendo().DropDownListFor(m => m.HeightUnit)
                                .ValuePrimitive(true)
                                .Filter("contains")
                                .OptionLabel("Select")
                                .DataValueField("Key")
                                .DataTextField("Value")
                                .HtmlAttributes(new { @style = "width:100px" })
                                .BindTo(Centerpoint.Common.Constants.UnitsConstant.EquipmentValuesAndDescriptions.ToList()))
                                    </div>
                                </div>

                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="editEquipmentLabelSmall">OD</label>
                                    <div class="d-inline-block">
                                        @Html.Kendo().NumericTextBoxFor(m => m.OuterDiameter).Spinners(false).Min(0).Format("n3").Decimals(3)
                                    </div>
                                    <div class="d-inline-block">
                                        @(Html.Kendo().DropDownListFor(m => m.OuterDiameterUnit)
                                .ValuePrimitive(true)
                                .Filter("contains")
                                .OptionLabel("Select")
                                .DataValueField("Key")
                                .DataTextField("Value")
                                .HtmlAttributes(new { @style = "width:100px" })
                                .BindTo(Centerpoint.Common.Constants.UnitsConstant.EquipmentValuesAndDescriptions.ToList()))
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="editEquipmentLabelSmall">Width</label>
                                    <div class="d-inline-block">
                                        @Html.Kendo().NumericTextBoxFor(m => m.Width).Spinners(false).Min(0).Format("n3").Decimals(3)
                                    </div>
                                    <div class="d-inline-block">
                                        @(Html.Kendo().DropDownListFor(m => m.WidthUnit)
                                .ValuePrimitive(true)
                                .Filter("contains")
                                .OptionLabel("Select")
                                .DataValueField("Key")
                                .DataTextField("Value")
                                .HtmlAttributes(new { @style = "width:100px" })
                                .BindTo(Centerpoint.Common.Constants.UnitsConstant.EquipmentValuesAndDescriptions.ToList()))
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="editEquipmentLabelSmall">Weight</label>
                                    <div class="d-inline-block">
                                        @Html.Kendo().NumericTextBoxFor(m => m.Weight).Spinners(false).Min(0).Format("n3").Decimals(3)
                                    </div>
                                    <div class="d-inline-block">
                                        @(Html.Kendo().DropDownListFor(m => m.WeightUnit)
                                .ValuePrimitive(true)
                                .Filter("contains")
                                .OptionLabel("Select")
                                .DataValueField("Key")
                                .DataTextField("Value")
                                .HtmlAttributes(new { @style = "width:100px" })
                                .BindTo(Centerpoint.Common.Constants.UnitsConstant.EquipmentWeightValuesAndDescriptions.ToList()))
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="editEquipmentLabelSmall">Depth</label>
                                    <div class="d-inline-block">
                                        @Html.Kendo().NumericTextBoxFor(m => m.Depth).Spinners(false).Min(0).Format("n3").Decimals(3)
                                    </div>
                                    <div class="d-inline-block">
                                        @(Html.Kendo().DropDownListFor(m => m.DepthUnit)
                                .ValuePrimitive(true)
                                .Filter("contains")
                                .OptionLabel("Select")
                                .DataValueField("Key")
                                .DataTextField("Value")
                                .HtmlAttributes(new { @style = "width:100px" })
                                .BindTo(Centerpoint.Common.Constants.UnitsConstant.EquipmentValuesAndDescriptions.ToList()))
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            @if (GlobalSettings.IsRegiis){
            <div class="card mt-2">
                <div class="card-header">
                    <h6 class="mb-0">Maintenance</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="editEquipmentLabelMedium">Points Per Month</label>
                                @if (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) {
                                @(Html.Kendo().IntegerTextBoxFor(m => m.PointsPerMonth)
                            .Spinners(false)
                            .Min(0)
                            .Max(100))
                            } else {
                                @(Html.Kendo().IntegerTextBoxFor(m => m.PointsPerMonth)
                            .Spinners(false)
                            .Min(0)
                            .Max(100)
                            .HtmlAttributes(new { @readonly = "readonly" }))
                                @Html.HiddenFor(u => u.ManufacturerCompanyId)
                            }
                            </div>
                            <div class="form-group">
                                @if (Html.IsGlobalAdmin() || Html.IsMaintenanceAdmin() || Html.IsMaintenanceEngineer() || Html.IsAssetAdmin()) {
                                <label class="editEquipmentLabelMedium">Current Points</label>
                                @(Html.Kendo().IntegerTextBoxFor(m => m.Points)
                            .Spinners(false))
                            } else {
                                <label>Current Points</label>
                                @(Html.Kendo().IntegerTextBoxFor(m => m.Points)
                            .Spinners(false)
                            .HtmlAttributes(new { @readonly = "readonly" }))
                                @Html.HiddenFor(u => u.Points)
                            }
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="editEquipmentLabelMedium">Points Per Move</label>
                                @if (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) {
                                @(Html.Kendo().IntegerTextBoxFor(m => m.PointsPerMove)
                            .Spinners(false)
                            .Min(0)
                            .Max(100))
                            } else {
                                @(Html.Kendo().IntegerTextBoxFor(m => m.PointsPerMove)
                            .Spinners(false)
                            .Min(0)
                            .Max(100)
                            .HtmlAttributes(new { @readonly = "readonly" }))
                                @Html.HiddenFor(u => u.PointsPerMove)
                            }
                            </div>
                            <div class="form-group">
                                <label class="editEquipmentLabelMedium">Points Per Run</label>
                                @if (Html.IsGlobalAdmin() || Html.IsAssetAdmin()) {
                                @(Html.Kendo().IntegerTextBoxFor(m => m.PointsPerRun)
                            .Spinners(false)
                            .Min(0)
                            .Max(100))
                            } else {
                                @(Html.Kendo().IntegerTextBoxFor(m => m.PointsPerRun)
                            .Spinners(false)
                            .Min(0)
                            .Max(100)
                            .HtmlAttributes(new { @readonly = "readonly" }))
                                @Html.HiddenFor(u => u.PointsPerRun)
                            }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card mt-2 mb-2">
                <div class="card-header">
                    <h6 class="mb-0">H<sub>2</sub>S or CO<sub>2</sub> concentration modifier </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>0% @(Html.Raw("<")) Concentration @(Html.Raw("<")) 5%</label>
                                @(Html.Kendo().IntegerTextBoxFor(m => m.H2sCo20to5)
                            .Spinners(false)
                            .Min(0)
                            .Max(100))
                            </div>
                            <div class="form-group">
                                <label>5% @(Html.Raw("<")) Concentration @(Html.Raw("<")) 10%</label>
                                @(Html.Kendo().IntegerTextBoxFor(m => m.H2sCo25to10)
                            .Spinners(false)
                            .Min(0)
                            .Max(100))
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>10% @(Html.Raw("<")) Concentration @(Html.Raw("<")) 15%</label>
                                @(Html.Kendo().IntegerTextBoxFor(m => m.H2sCo210to15)
                            .Spinners(false)
                            .Min(0)
                            .Max(100))
                            </div>
                            <div class="form-group">
                                <label>Concentration > 15% </label>
                                @(Html.Kendo().IntegerTextBoxFor(m => m.H2sCo215Greater)
                            .Spinners(false)
                            .Min(0)
                            .Max(100))
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            }

        @(Html.Kendo().Grid<EquipmentItemNoteModel>()
            .Name("equipmentItemNoteGrid")
            .Columns(c => {
                c.Bound(p => p.Comment).Title("Note");
                c.Bound(p => p.UserName).Width(125);
                c.Bound(p => p.Created).Format(DateConstants.DateTimeFormat).Width(125);
                c.Command(command => { 
                    command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                    command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
                }).Width(200);
            })
            .Editable(editable => editable.Mode(GridEditMode.PopUp).DisplayDeleteConfirmation("Are you sure you want to delete this Comment?").TemplateName("EquipmentItemNoteWindow")
            .Window(w => w.Name("EquipmentItemNoteWindow").Title("Equipment Item Comment").Width(850).Draggable(false)))
            .ToolBar(t => t.ClientTemplateId("equipmentItemNoteGridToolbar"))
            .Sortable()
            .Filterable()
            .Groupable()
            .Events(e => e.DataBound("updateEquipmentItemNoteGrid"))
            .Events(e => e.Edit("equipmentItemNoteEdit"))
            .Scrollable(s => s.Height(300))
            .Resizable(c => c.Columns(true))
            .ColumnMenu(c => c.Columns(true))
            .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(m => m.Id(p => p.EquipmentItemNoteId))
            .Read(read => read.Action("GetEquipmentItemNotes", "Assets", new { @equipmentItemId = Model.EquipmentItemId }))
            .Create(create => create.Action("UpdateEquipmentItemNote", "Assets", new { @eId = Model.EquipmentItemId }).Data("noteData"))
        .Update(update => update.Action("UpdateEquipmentItemNote", "Assets", new { @eId = Model.EquipmentItemId }).Data("noteData"))
            .Destroy(destroy => destroy.Action("DeleteEquipmentItemNote", "Assets"))))
            <br />
        if (Model.Status != EquipmentConstant.AcceptancePending) {
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Acceptance Note</label>
                            @(Html.TextAreaFor(p => p.CommentsandUser, new { @class = "k-textbox", @style = "width:100%", @rows = "5", @disabled = "disabled" }))
                        </div>
                    </div>
                </div>
        }
        if ((Html.IsGlobalAdmin() || Html.IsAssetAdmin() || Html.IsAssetEngineers()) && (!Html.IsAnsaAdministrator() && !Html.IsAnsaAnalyst())) {
                <input id="saveEquipmentItem" type="submit" class="btn btn-sm btn-primary" value="Save Details" />
        }
                @Html.HiddenFor(m => m.Status)
                @Html.HiddenFor(m => m.EquipmentItemId)
                @Html.HiddenFor(m => m.EquipmentCategoryId)
                @Html.HiddenFor(m => m.EquipmentCategoryName)
                @Html.HiddenFor(m => m.EquipmentItemName)
                @Html.HiddenFor(m => m.AcceptanceUser)
                @Html.HiddenFor(m => m.AcceptanceCreated)
                @Html.HiddenFor(m => m.CurrentCompanyId)
                @Html.HiddenFor(m => m.CurrentCompanyLocationId)
                @Html.HiddenFor(m => m.CurrentClientLocationName)
                @Html.HiddenFor(m => m.CurrentCompanyLocationCompanyName)
                @Html.HiddenFor(m => m.CurrentCompanyName)
                @Html.HiddenFor(m => m.Comments)
                @Html.HiddenFor(m => m.DepreciationYears)
                @Html.HiddenFor(m => m.CurrentEquipmentItemLocation)
                @Html.HiddenFor(m => m.IsTransit)
                @Html.HiddenFor(m => m.IsReserved)
                @Html.HiddenFor(m => m.ShipmentTransit)
                @Html.HiddenFor(m => m.Points)
                @Html.HiddenFor(m => m.ProjectId)
                @Html.HiddenFor(m => m.IsSelected)
                @Html.HiddenFor(m => m.PointsUpdated)
                @Html.HiddenFor(m => m.EquipmentPackingListProjectId)
            }
        </div>
    </text>);

            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind="html:MRsTotalActiveMRsWithNumber, visible:totalMRs"})
                .Content(@<text>
        <div>
            @(Html.Kendo().Grid<MaintenanceRecordModel>()
        .Name("maintenanceRecordGrid")
        .Columns(columns => {
        columns.Bound(c => c.Number).Title("Maintenance Record").ClientTemplate("<a href='" + @Url.Action("EditMaintenanceRecord", "Maintenance", new { @id = "" }) + "/#=MaintenanceRecordId#'>#=Number#</a>");
        columns.Bound(c => c.MaintenanceBlueprintId).Title("Maintenance Blueprint").ClientTemplate("<a href='" + @Url.Action("EditMaintenanceBlueprint", "Admin", new { @id = "" }) + "/#=MaintenanceBlueprintId#'>#=MaintenanceBlueprintName#</a>");
        columns.Bound(c => c.PriorityDescription).Title("Priority");
        columns.Bound(c => c.UserName).Title("Created By").Hidden(true);
        columns.Bound(c => c.Created).Title("Created").Format(DateConstants.DateTimeFormat);
        columns.Bound(c => c.MaintenanceTimeString).Title("Time On-Going").Hidden(true);
        columns.Bound(c => c.OnHoldHoursString).Title("Time On Hold").Hidden(true);
        columns.Bound(c => c.TotalMaintenanceTimeString).Title("Total Time");
        columns.Bound(c => c.CompletedDate).Title("Completed").Format(DateConstants.DateTimeFormat);
        columns.Bound(c => c.Modified).Title("Modified").Format(DateConstants.DateTimeFormat).Hidden(true);
        columns.Bound(c => c.StatusDescription).Title("Status").ClientTemplate("<span class='badge' style='background:#=StatusColour#;color:#=StatustextColour#'>#=StatusDescription#</span>");
        })
        .Events(e => e.DataBound("updateMaintenanceRecordGrid"))
        .ColumnMenu(c => c.Columns(true))
        .Filterable()
        .Sortable()
        .Groupable()
        .Scrollable(s => s.Height(532))
        .Resizable(resize => resize.Columns(true))
        .Reorderable(reorder => reorder.Columns(true))
        .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Model(model => {
        model.Id(m => m.MaintenanceRecordId);
        })
        .Read(read => read.Action("GetMaintenanceRecordByEquipmentItem", "Maintenance").Data("maintenanceRecordData"))))
        </div>
    </text>);


            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind="html:maintenanceScheduleWithNumber"})
                .Content(@<text>
        <div>
            @if (Model.Status != EquipmentConstant.AcceptancePending && (Html.IsMaintenanceAdmin() || Html.IsMaintenanceEngineer() || Html.IsGlobalAdmin() || Html.IsEng())) {
                                @(
                    Html.Kendo().Grid<EquipmentItemMaintenanceScheduleModel>()
                    .Name("equipmentItemMaintenanceScheduleGrid")
                    .Columns(columns => {
                    columns.Template("#if(!HasExistingMaintenanceRecord){# <a class='btn btn-sm btn-primary' onclick = 'startScheduleMaintenance(#=MaintenanceBlueprintId#)'><i class='fa fa-thumbs-up pr-1'></i>Start Now </a> #} else {# <a href='/Maintenance/EditMaintenanceRecord/#=ExistingMaintenanceRecord.MaintenanceRecordId#'>#=ExistingMaintenanceRecord.Number#</a> #}#").Width(75);
                    columns.Bound(c => c.MaintenanceBlueprintName).Title("Maintenance Blueprint").Width(150);
                    columns.Bound(c => c.LastDate).Title("Last").Width(125).ClientTemplate("#=LastDateOnly#");
                    columns.Bound(c => c.RecurringDays).ClientTemplate("#=CompanyLocationId!=null? 'N/A' : RecurringDays #").Title("Recurring Days").Width(125).Visible(!GlobalSettings.IsWellsense);
                    columns.Bound(c => c.RecurringMonths).ClientTemplate("#=CompanyLocationId!=null? 'N/A' : RecurringMonths #").Title("Recurring Months").Width(125).Visible(GlobalSettings.IsWellsense);
                    columns.Bound(c => c.StartDate).ClientTemplate("#=CompanyLocationId!=null? 'N/A' : StartDateOnly #").Title("Next").Width(125);
                    columns.Bound(c => c.CompanyLocationId).ClientTemplate("#=CompanyLocationId == null? 'N/A' : CompanyLocationName #").Title("Location").Width(125);
                    if(Html.IsMaintenanceAdmin() || Html.IsMaintenanceEngineer() || Html.IsGlobalAdmin()) {
                        columns.Command(command => { 
            command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
            command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
        }).Width(125); 
        }
        })
        .ColumnMenu(c => c.Columns(true))
        .ToolBar(t => t.ClientTemplateId("equipmentItemMaintenanceScheduleGridToolbar"))
        .Editable(editable => editable.Mode(GridEditMode.PopUp).TemplateName("EquipmentItemMaintenanceSchedule").Window(w => w.Name("equipmentItemMaintenanceScheduleWindow").Title("Maintenance Schedule").Width(750).Draggable(true)))
        .Events(e => e.DataBound("updateEquipmentItemMaintenanceScheduleTotals"))
        .Sortable()
        .Groupable()
        .Scrollable(s => s.Height(200))
        .Resizable(resize => resize.Columns(true))
        .Reorderable(reorder => reorder.Columns(true))
        .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Model(model => {
        model.Id(m => m.EquipmentItemMaintenanceScheduleId);
        model.Field(m => m.RecurringMonths).DefaultValue(GlobalSettings.IsWellsense ? 1 : 0);
        model.Field(m => m.RecurringDays).DefaultValue(!GlobalSettings.IsWellsense ? 1 : 0);
        })
        .Read(read => read.Action("GetEquipmentItemMaintenanceSchedules", "Assets", new { @eId = Model.EquipmentItemId }))
        .Create(update => update.Action("UpdateEquipmentItemMaintenanceSchedule", "Assets", new { @eId = Model.EquipmentItemId }).Data("equipmentItemMaintenanceScheduleData"))
        .Update(update => update.Action("UpdateEquipmentItemMaintenanceSchedule", "Assets", new { @eId = Model.EquipmentItemId }).Data("equipmentItemMaintenanceScheduleData"))
        .Destroy(destroy => destroy.Action("DeleteEquipmentItemMaintenanceSchedule", "Assets")))
        )

        } else {

            @(Html.Kendo().Grid<EquipmentItemMaintenanceScheduleModel>()
        .Name("equipmentItemMaintenanceScheduleGrid")
        .Columns(columns => {
        columns.Bound(c => c.MaintenanceBlueprintName).Title("Maintenance Blueprint").Width(150);
        columns.Bound(c => c.LastDate).Title("Last").Format(DateConstants.DateFormat).Width(125);
        columns.Bound(c => c.RecurringDays).Title("Recurring Days").Width(125).Visible(!GlobalSettings.IsWellsense);
        columns.Bound(c => c.RecurringMonths).Title("Recurring Months").Width(125).Visible(GlobalSettings.IsWellsense);
        columns.Bound(c => c.StartDate).Title("Next").Format(DateConstants.DateFormat).Width(125);
        columns.Bound(c => c.CompanyLocationName).Title("Location").Width(125);

        })
        .ColumnMenu(c => c.Columns(true))
        .Events(e => e.DataBound("updateEquipmentItemMaintenanceScheduleTotals"))
        @*.Events(e => e.Edit("edit"))*@
        .Sortable()
        .Groupable()
        .Scrollable(s => s.Height(200))
        .Resizable(resize => resize.Columns(true))
        .Reorderable(reorder => reorder.Columns(true))
        .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Model(model => {
        model.Id(m => m.EquipmentItemMaintenanceScheduleId);
        model.Field(m => m.RecurringMonths).DefaultValue(GlobalSettings.IsWellsense ? 1 : 0);
        model.Field(m => m.RecurringDays).DefaultValue(!GlobalSettings.IsWellsense ? 1 : 0);
        })
        .Read(read => read.Action("GetEquipmentItemMaintenanceSchedules", "Assets", new { @eId = Model.EquipmentItemId }))))
        }
        </div>
    </text>);

            if (Model.Status != EquipmentConstant.AcceptancePending) {
                tabstrip.Add().Text("")
                    .HtmlAttributes(new { @data_bind="html:customStatusWithNumber"})
                    .Content(@<text>
        <div>
            @if ((Html.IsGlobalAdmin() || Html.IsAssetAdmin()) && (!Html.IsAnsaAdministrator() && !Html.IsAnsaAnalyst())) {
            @(Html.Kendo().Grid<EquipmentItemCustomStatusCodeModel>()
        .Name("customStatusGrid")
        .Columns(columns => {
        columns.Bound(c => c.CustomStatusCodeName).Title("Status");
        columns.Bound(c => c.CustomStatusCodeCountry).Title("Country");
        columns.Bound(c => c.CustomStatusCodeCode).Title("Code");
        columns.Bound(c => c.Note).Hidden(true);
        columns.Bound(c => c.ExpiryDate).Title("Expiry Date").Format(DateConstants.DateFormat);
        columns.Bound(c => c.Created).Title("Created Date").Format(DateConstants.DateFormat);
        columns.Command(command => {
        command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
        command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"});
        }).Width(200);
        })
        .ColumnMenu(c => c.Columns(true))
        .ToolBar(t => {
        t.Create().Text("Add Custom Status");
        t.Excel().Text("Export");
        }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
        .Editable(editable => editable
        .Mode(GridEditMode.PopUp)
        .TemplateName("CustomStatusWindow")
        .Window(w => w
        .Name("customStatusWindow")
        .Title("Equipment Item Custom Status")
        .Width(800)
        .Draggable(false)
        )
        )
        .Events(e => e.DataBound("updateCustomStatusTotals"))
        .Sortable()
        .Groupable()
        .Scrollable(s => s.Height(200))
        .Resizable(resize => resize.Columns(true))
        .Reorderable(reorder => reorder.Columns(true))
        .Excel(excel => excel
        .FileName(string.Format("Centerpoint_Equipment_Item_Custom_Satus_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
        .Filterable(true)
        .ProxyURL(Url.Action("Export", "Admin"))
        )
        .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Model(model => {
        model.Id(m => m.EquipmentItemCustomStatusCodeId);
        })
        .Read(read => read.Action("GetEquipmentItemCustomStatusCodes", "Assets", new { @equipmentItemId = Model.EquipmentItemId }))
        .Create(create => create.Action("UpdateEquipmentItemCustomStatusCode", "Assets", new { @eId = Model.EquipmentItemId }).Data("customStatusData"))
        .Update(update => update.Action("UpdateEquipmentItemCustomStatusCode", "Assets", new { @eId = Model.EquipmentItemId }).Data("customStatusData"))
        .Destroy(destroy => destroy.Action("DeleteEquipmentItemCustomStatusCode", "Assets"))))
        } else {
            @(Html.Kendo().Grid<EquipmentItemCustomStatusCodeModel>()
        .Name("customStatusGrid")
        .Columns(columns => {
            columns.Bound(c => c.CustomStatusCodeName).Title("Status");
            columns.Bound(c => c.CustomStatusCodeCountry).Title("Country");
            columns.Bound(c => c.CustomStatusCodeCode).Title("Code");
            columns.Bound(c => c.Note).Hidden(true);
            columns.Bound(c => c.ExpiryDate).Title("Expiry Date").Format(DateConstants.DateFormat);
            columns.Bound(c => c.Created).Title("Created Date").Format(DateConstants.DateFormat);
        })
        .ColumnMenu(c => c.Columns(true))
        .Events(e => e.DataBound("updateCustomStatusTotals"))
        .Sortable()
        .Groupable()
        .Scrollable(s => s.Height(200))
        .Resizable(resize => resize.Columns(true))
        .Reorderable(reorder => reorder.Columns(true))
        .Excel(excel => excel
        .FileName(string.Format("Centerpoint_Equipment_Item_Custom_Satus_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
        .Filterable(true)
        .ProxyURL(Url.Action("Export", "Admin"))
        )
        .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Model(model => {
        model.Id(m => m.EquipmentItemCustomStatusCodeId);
        })
        .Read(read => read.Action("GetEquipmentItemCustomStatusCodes", "Assets", new { @equipmentItemId = Model.EquipmentItemId }))))
        }
        </div>
    </text>);
            }

            if ((!Model.IsPartofBundle && Model.Status != EquipmentConstant.AcceptancePending) && (!Model.IsReserved || !Model.IsTransit)) {
                tabstrip.Add().Text("")
                    .HtmlAttributes(new { @data_bind="html:bundleItemsWithNumber"})
                    .Content(@<text>
        @if (!Model.IsPartofBundle) {
        <div>
            <div class="row">
               @* <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fa fa-tags"></i> Equipment Categories</h6>
                        </div>
                        <div class="card-body">
                            <div class="expandCollapseButtonsContainer">
                                <button class="btn btn-sm btn-primary" id="expandAll" data-bind="click:expandAll" title="Expand All">
                                    <i class="fa fa-expand m-0"></i>
                                </button>
                                <button class="btn btn-sm btn-primary" id="collapsAll" data-bind="click:collapseAll" title="Collapse All">
                                    <i class="fa fa-compress m-0"></i>
                                </button>
                            </div>
                            @(Html.Kendo().TreeView()
                                .Name("equipmentCategoryTreeView")
                                .TemplateId("equipmentCategoryTemplate")
                                .DataTextField("NewName")
                                .LoadOnDemand(false)
                                .Events(e => e.Select("equipmentCategorySelected"))
                                .DataSource(datasource => datasource
                                    .Events(e => e.RequestEnd("categoriesLoaded"))
                                    .Model(m => m.Id("EquipmentCategoryId").HasChildren("HasChildren").Children("Children"))
                                    .Read(r => r.Action("GetEquipmentCategories", "Admin"))
                                )
                            )
                        </div>
                    </div>
                </div>*@



               <div class="col-md-12">
                   @if (((Html.IsAssetAdmin() || Html.IsGlobalAdmin()) && (!Html.IsAnsaAdministrator() && !Html.IsAnsaAnalyst())) && !Model.IsTransit && !Model.ParentEquipmentItemId.HasValue)
                   {
                       <div class="row">
                           <div class="col-md-6">
                               <div class="card-header">
                                   <h6 class="mb-0"><i class="fa fa-tags"></i> Equipment Items (<span data-bind="text: totalEquipmentItems"></span>)</h6>
                               </div>
                               <div>
                                   @(Html.Kendo().Grid<EquipmentItemInfoBundleModel>()
                                       .Name("equipmentItemGrid")
                                       .Columns(columns =>
                                       {
                                           columns.Bound(c => c.EquipmentItemName).Title("Item Number").Width(130);
                                           columns.Bound(c => c.ReceivedDate).Title("Received Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                           columns.Bound(c => c.PurchasedDate).Title("Purchased Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                           columns.Bound(c => c.CurrencyName).Title("Currency").Width(100).Hidden(true);
                                           columns.Bound(c => c.Price).Format("{0:n2}").Width(100).Hidden(true);
                                           columns.Bound(c => c.DepreciatedPrice).Title("Net Book Value").Hidden(true).Format("{0:n2}").Width(100);
                                           if(!GlobalSettings.IsWellsense )
                                           {
                                                columns.Bound(c => c.PointsPerMonth).Title("Points Per Month").Hidden(true).Width(150);
                                                columns.Bound(c => c.PointsPerRun).Title("Points Per Run").Hidden(true).Width(150);
                                                columns.Bound(c => c.PointsPerMove).Title("Points Per Move").Hidden(true).Width(150);
                                                columns.Bound(c => c.Points).Title("Current Points").Width(100);
                                           }
                                           columns.Bound(c => c.DivisionName).Title("Division").Hidden(true).Width(150);
                                           columns.Bound(c => c.CurrentClientLocationName).Title("Current Location").ClientTemplate("#if(AcceptancePending){# 'N/A' #}else{# #=CurrentClientLocationName# #}#").Width(100);
                                           columns.Bound(c => c.MaintenanceScheduleDetail).Title("Maintenance Schedule Date(s)").ClientTemplate("#=MaintenanceScheduleDetail#").Width(125);
                                           columns.Bound(c => c.MRCount).Title("Active MRs").ClientTemplate("#if(MaintenanceRecordCount){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='maintenanceRecordCount(#=EquipmentItemId#)'>#=MRCount#</a>#} else {##=MRCount##}#").Width(75);
                                           columns.Bound(c => c.EquipmentInfo).Title("Info").Width(100);
                                           columns.Bound(c => c.AllStatusDescription).Title("Status").Encoded(false).Filterable(f => f.Operators(o => o.ForString(str => str.Clear().Contains("Contains").DoesNotContain("Does not contain")))).Width(150);
                                       })
                                       .ColumnMenu(c => c.Columns(true))
                                       .Events(e => e.DataBound("updateEquipmentTotals"))
                                       .ToolBar(toolbar => toolbar.ClientTemplateId("equipmentItemGridHeader"))
                                       .Filterable()
                                       .Selectable(selectable => selectable.Mode(GridSelectionMode.Multiple))
                                       .Sortable()
                                       .AutoBind(false)
                                       .Groupable()
                                       .Scrollable(s => s.Height(475))
                                       .Resizable(resize => resize.Columns(true))
                                       .Reorderable(reorder => reorder.Columns(true))
                                       .DataSource(dataSource => dataSource
                                           .Ajax()
                                           .ServerOperation(false)
                                           .Model(model => {
                                                               model.Id(m => m.EquipmentItemId);
                                           })
                                                        .Read(read => read.Action("GetEquipmentItemsNotInEquipmentItemBundleId", "Assets").Data("bundleEquipmentItemData"))
                                       )
                                       )
                               </div>
                           </div>

                           <div class="col-md-6">
                               <div class="card-header">
                                   <h6 class="mb-0"><i class="fa fa-tags"></i> Bundle Items (<span data-bind="text: totalBundledEquipmentItems"></span>)</h6>
                               </div>
                               <div>
                                   @(Html.Kendo().Grid<EquipmentItemBundleModel>()
                                       .Name("equipmentItemBundleGrid")
                                       .Columns(columns =>
                                       {
                                           columns.Bound(c => c.EquipmentItem.EquipmentItemName).Title("Item Number").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItem.EquipmentItemId#'>#=EquipmentItem.EquipmentItemName#</a>").Width(130);
                                           columns.Bound(c => c.EquipmentItem.ReceivedDate).Title("Received Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                           columns.Bound(c => c.EquipmentItem.PurchasedDate).Title("Purchased Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                           columns.Bound(c => c.EquipmentItem.CurrencyName).Title("Currency").Width(100).Hidden(true);
                                           columns.Bound(c => c.EquipmentItem.Price).Format("{0:n2}").Width(100).Hidden(true);
                                           columns.Bound(c => c.EquipmentItem.DepreciatedPrice).Title("Net Book Value").Hidden(true).Format("{0:n2}").Width(100);
                                           if(!GlobalSettings.IsWellsense )
                                           {
                                                columns.Bound(c => c.EquipmentItem.PointsPerMonth).Title("Points Per Month").Hidden(true).Width(150);
                                                columns.Bound(c => c.EquipmentItem.PointsPerRun).Title("Points Per Run").Hidden(true).Width(150);
                                                columns.Bound(c => c.EquipmentItem.PointsPerMove).Title("Points Per Move").Hidden(true).Width(150);
                                                columns.Bound(c => c.EquipmentItem.Points).Title("Current Points").Width(100);
                                           }
                                           columns.Bound(c => c.EquipmentItem.DivisionName).Title("Division").Hidden(true).Width(150);
                                           columns.Bound(c => c.EquipmentItem.CurrentClientLocationName).Title("Current Location").Width(120);
                                           columns.Bound(c => c.EquipmentItem.MaintenanceScheduleDetail).Title("Maintenance Schedule Date(s)").ClientTemplate("#=MaintenanceScheduleDetail#").Width(125);
                                           columns.Bound(c => c.MRCount).Title("Active MRs").ClientTemplate("#if(MaintenanceRecordCount){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='maintenanceRecordCount(#=EquipmentItem.EquipmentItemId#)'>#=MRCount#</a>#} else {##=MRCount##}#").Width(75);
                                           columns.Bound(c => c.EquipmentItem.EquipmentInfo).Title("Info").Width(100);
                                           columns.Bound(c => c.EquipmentItem.AllStatusDescription).Title("Status").Encoded(false).Filterable(f => f.Operators(o => o.ForString(str => str.Clear().Contains("Contains").DoesNotContain("Does not contain")))).Width(150);
                                           columns.Command(command => {
                                                                          command.Destroy().HtmlAttributes(new { @class = "bg-danger text-white grid-action-button" });
                                           }).Width(100);
                                       })
                                       .ColumnMenu(c => c.Columns(true))
                                       .Events(e => e.DataBound("updateEquipmentItemBundlesTotals"))
                                       .Filterable()
                                       .Groupable()
                                       .Editable(editable => editable.Mode(GridEditMode.InLine))
                                       .Scrollable(s => s.Height(475))
                                       .Resizable(resize => resize.Columns(true))
                                       .Reorderable(reorder => reorder.Columns(true))
                                       .ToolBar(toolbar => toolbar.ClientTemplateId("bundleItemGridHeader"))
                                       .Search(s => { s.Field(o => o.EquipmentItem.EquipmentItemName, "contains"); })
                                       .DataSource(dataSource => dataSource
                                           .Ajax()
                                           .ServerOperation(false)
                                           .Model(model => {
                                                               model.Id(m => m.EquipmentItemBundleId);
                                           })
                                           .Events(e=>e.RequestEnd("klinkor"))
                                           .Read(read => read.Action("GetBundledEquipmentItems", "Assets", new { @parentEquipmentItemId = Model.EquipmentItemId }))
                                           .Destroy(destroy => destroy.Action("DeleteBundleEquipmentItem", "Assets", new { @equipmentItemBundleId = Model.EquipmentItemId }))
                                       )
                                       )
                               </div>
                           </div>
                       </div>
                   }
                   else
                   {
                       <div class="row">
                           <div class="col-md-6">
                               <div class="card">
                                    <div class="card-header">
                                   <h6 class="mb-0"><i class="fa fa-tags"></i> Equipment Items (<span data-bind="text: totalEquipmentItems"></span>)</h6>
                               </div>
                                    <div class="card-body">
                                
                                   @(Html.Kendo().Grid<EquipmentItemInfoBundleModel>()
                                       .Name("equipmentItemGrid")
                                       .Columns(columns =>
                                       {
                                           columns.Bound(c => c.EquipmentItemName).Title("Item Number").Width(130);
                                           columns.Bound(c => c.ReceivedDate).Title("Received Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                           columns.Bound(c => c.PurchasedDate).Title("Purchased Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                           columns.Bound(c => c.CurrencyName).Title("Currency").Width(100).Hidden(true);
                                           columns.Bound(c => c.Price).Format("{0:n2}").Width(100).Hidden(true);
                                           columns.Bound(c => c.DepreciatedPrice).Title("Net Book Value").Hidden(true).Format("{0:n2}").Width(100);
                                           if(!GlobalSettings.IsWellsense)
                                           {
                                                columns.Bound(c => c.PointsPerMonth).Title("Points Per Month").Hidden(true).Width(150);
                                                columns.Bound(c => c.PointsPerRun).Title("Points Per Run").Hidden(true).Width(150);
                                                columns.Bound(c => c.PointsPerMove).Title("Points Per Move").Hidden(true).Width(150);
                                                columns.Bound(c => c.Points).Title("Current Points").Width(100);
                                           }
                                           columns.Bound(c => c.DivisionName).Title("Division").Hidden(true).Width(150);
                                           columns.Bound(c => c.CurrentClientLocationName).Title("Current Location").ClientTemplate("#if(AcceptancePending){# 'N/A' #} else{# #=CurrentClientLocationName# #}#").Width(100);
                                           columns.Bound(c => c.MaintenanceScheduleDetail).Title("Maintenance Schedule Date(s)").ClientTemplate("#=MaintenanceScheduleDetail#").Width(125);
                                           columns.Bound(c => c.MRCount).Title("Active MRs").ClientTemplate("#if(MaintenanceRecordCount){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='maintenanceRecordCount(#=EquipmentItemId#)'>#=MRCount#</a>#} else {##=MRCount##}#").Width(75);
                                           columns.Bound(c => c.EquipmentInfo).Title("Info").Width(100);
                                           columns.Bound(c => c.AllStatusDescription).Title("Status").Encoded(false).Filterable(f => f.Operators(o => o.ForString(str => str.Clear().Contains("Contains").DoesNotContain("Does not contain")))).Width(150);
                                       })
                                       .ColumnMenu(c => c.Columns(true))
                                       .Events(e => e.DataBound("updateEquipmentTotals"))
                                       .Filterable()
                                       .AutoBind(false)
                                       .Sortable()
                                       .Groupable()
                                        .ToolBar(t => t.Search())
                                        .Search(s =>
                                        {
                                            s.Field(o => o.EquipmentItemName, "contains");
                                        })
                                       .Scrollable(s => s.Height(475))
                                       .Resizable(resize => resize.Columns(true))
                                       .Reorderable(reorder => reorder.Columns(true))
                                       .DataSource(dataSource => dataSource
                                           .Ajax()
                                           .ServerOperation(false)
                                           .Model(model => {
                                                               model.Id(m => m.EquipmentItemId);
                                           })
                                            .Read(read => read.Action("GetEquipmentItemsNotInEquipmentItemBundleId", "Assets").Data("bundleEquipmentItemData"))
                                       )
                                       )
                               </div>
                               </div>
                           </div>
                           <div class="col-md-6">
                               <div class="card">
                                    <div class="card-header">
                                   <h6 class="mb-0"><i class="fa fa-tags"></i> Bundle Items (<span data-bind="text: totalBundledEquipmentItems"></span>)</h6>
                               </div>
                                    <div class="card-body">
                                <div class="k-toolbar k-grid-toolbar">
                                    <span>Parent: @Model.ParentEquipmentItem</span>
                                </div>
                                   @(Html.Kendo().Grid<EquipmentItemBundleModel>()
                                       .Name("equipmentItemBundleGrid")
                                       .Columns(columns =>
                                       {
                                           columns.Bound(c => c.EquipmentItem.EquipmentItemName).Title("Item Number").Width(130);
                                           columns.Bound(c => c.EquipmentItem.ReceivedDate).Title("Received Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                           columns.Bound(c => c.EquipmentItem.PurchasedDate).Title("Purchased Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                           columns.Bound(c => c.EquipmentItem.CurrencyName).Title("Currency").Width(100).Hidden(true);
                                           columns.Bound(c => c.EquipmentItem.Price).Format("{0:n2}").Width(100).Hidden(true);
                                           columns.Bound(c => c.EquipmentItem.DepreciatedPrice).Title("Net Book Value").Hidden(true).Format("{0:n2}").Width(100);
                                if(!GlobalSettings.IsWellsense )
                                           {
                                                columns.Bound(c => c.EquipmentItem.PointsPerMonth).Title("Points Per Month").Hidden(true).Width(150);
                                                columns.Bound(c => c.EquipmentItem.PointsPerRun).Title("Points Per Run").Hidden(true).Width(150);
                                                columns.Bound(c => c.EquipmentItem.PointsPerMove).Title("Points Per Move").Hidden(true).Width(150);
                                                columns.Bound(c => c.EquipmentItem.Points).Title("Current Points").Width(100);
                                           }
                                           columns.Bound(c => c.EquipmentItem.DivisionName).Title("Division").Hidden(true).Width(150);
                                           columns.Bound(c => c.EquipmentItem.CurrentClientLocationName).Title("Current Location").Width(120);
                                           columns.Bound(c => c.EquipmentItem.MaintenanceScheduleDetail).Title("Maintenance Schedule Date(s)").ClientTemplate("#=MaintenanceScheduleDetail#").Width(125);
                                           columns.Bound(c => c.MRCount).Title("Active MRs").ClientTemplate("#if(MaintenanceRecordCount){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='maintenanceRecordCount(#=EquipmentItem.EquipmentItemId#)'>#=MRCount#</a>#} else {##=MRCount##}#").Width(75);
                                           columns.Bound(c => c.EquipmentItem.EquipmentInfo).Title("Info").Width(100);
                                           columns.Bound(c => c.EquipmentItem.AllStatusDescription).Title("Status").Encoded(false).Filterable(f => f.Operators(o => o.ForString(str => str.Clear().Contains("Contains").DoesNotContain("Does not contain")))).Width(150);
                                       })
                                       .ColumnMenu(c => c.Columns(true))
                                       .Events(e => e.DataBound("updateEquipmentItemBundlesTotals"))
                                       .ToolBar(t => t.Search())
                                       .Search(s =>{ s.Field(o => o.EquipmentItem.EquipmentItemName, "contains");})
                                       .Filterable()
                                       .Groupable()
                                       .Scrollable(s => s.Height(475))
                                       .Resizable(resize => resize.Columns(true))
                                       .Reorderable(reorder => reorder.Columns(true))
                                       .DataSource(dataSource => dataSource
                                           .Ajax()
                                           .ServerOperation(false)
                                           .Model(model => {
                                                model.Id(m => m.EquipmentItemBundleId);
                                           })
                                           .Read(read => read.Action("GetBundledEquipmentItems", "Assets", new { @parentEquipmentItemId = Model.EquipmentItemId }))
                                       )
                                       )
                               </div>
                               </div>
                           </div>
                       </div>
                   }
               </div>
            </div>
        </div>
    }
    </text>);
           }

    tabstrip.Add().Text("")
        .HtmlAttributes(new { @data_bind="html:attachmentsWithNumber"})
        .Content(@<text>
            <div id="attachments" class="tab-pane @(ViewBag.Tab == "attachments" ? "active" : string.Empty)">
                <br />
                @if (!Html.IsAnsaAdministrator() && !Html.IsAnsaAnalyst()) {
                    <p>Click the link below to attach documents to this Equipment Item</p>
                    @(Html.Kendo().Upload()
                        .Name("equipmentItemAttachmentDocuments")
                        .Messages(m => m.Select("Attach Equipment Item Documents"))
                        .Multiple(true)
                        .Events(e => e.Success("onEquipmentItemDocumentAttached").Complete("onEquipmentItemDocumentComplete").Upload("onEquipmentItemDocumentUpload"))
                        .HtmlAttributes(new { @style = "width:300px" })
                        .Async(async => async.Save("AttachEquipmentItemDocuments", "Assets", new { @equipmentItemId = Model.EquipmentItemId }).Batch(true))
                    )
                    <br />
                }

                @(Html.Kendo().Grid<DocumentModel>()
                    .Name("equipmentItemDocumentsGrid")
                    .Columns(c => {
                        c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                        c.Bound(p => p.Created).Title("Created").ClientTemplate("#=CreatedDate#").Width(150);
                        c.Bound(p => p.UserName).Title("Created By").Width(200);
                        c.Command(command => { 
                            command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}).Visible("canEquipmentItemDocumentDelete"); 
                        }).Width(200);
                    })
                    .Events(e => e.DataBound("updateEquipmentItemDocumentGrid"))
                    .Sortable()
                    .Resizable(r => r.Columns(true))
                    .Filterable()
                    .Groupable()
                    .Editable(e => e.Mode(GridEditMode.InLine))
                    .Scrollable(s => s.Height(500))
                    .DataSource(dataSource => dataSource
                        .Ajax()
                        .ServerOperation(false)
                        .Model(model => model.Id(p => p.DocumentId))
                        .Read(read => read.Action("GetEquipmentItemDocuments", "Assets", new { @equipmentItemId = Model.EquipmentItemId }))
                        .Destroy(destroy => destroy.Action("DeleteEquipmentItemDocument", "Assets", new { @equipmentItemId = Model.EquipmentItemId }))
                        .Events( e => e.RequestEnd("onDocumenetsGridSave"))
                        )
                )
                <br />
            </div>
    </text>);

    tabstrip.Add().Text("")
        .HtmlAttributes(new { @data_bind = "html:mrAttachmentsWithNumber, visible:hasMRAttachments" })
        .Content(@<text>
            <div>
        @(Html.Kendo().Grid<MaintenanceRecordAttachmentModel>()
                .Name("maintenanceRecordAttachmentsGrid")
                .Columns(c => {
                  c.Bound(c => c.Number).Title("Maintenance Record").ClientTemplate("<a href='" + @Url.Action("EditMaintenanceRecord", "Maintenance", new { @id = "" }) + "/#=MaintenanceRecordId#'>#=MaintenanceRecordNumber#</a>");
                  c.Bound(c => c.MaintenanceRecordMaintenanceBlueprintId).Title("Maintenance Blueprint").ClientTemplate("<a href='" + @Url.Action("EditMaintenanceBlueprint", "Admin", new { @id = "" }) + "/#=MaintenanceRecordMaintenanceBlueprintId#'>#=MaintenanceRecordMaintenanceBlueprintName#</a>");
                  c.Bound(c => c.MaintenanceRecordCompletedDate).Title("Complete Date").Format(DateConstants.DateFormat);
                  c.Bound(c => c.DocumentId).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=DocumentFileName#</a>");
                })
                .Events(e => e.DataBound("updateMaintenanceRecordAttachmentsGrid"))
                .ColumnMenu(c => c.Columns(true))
                .Filterable()
                .Sortable()
                .Groupable()
                .Scrollable(s => s.Height(532))
                .Resizable(resize => resize.Columns(true))
                .Reorderable(reorder => reorder.Columns(true))
                .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Model(model => model.Id(p => p.DocumentId))
                .Read(read => read.Action("GetDocumentsByEquipmentItemId", "Maintenance").Data("equipmentData"))))
            </div>
        </text>);

    tabstrip.Add().Text("")
        .HtmlAttributes(new { @data_bind = "html:certificatesWithNumber" })
        .Content(@<text>             
            <br />
            @if (!Html.IsAnsaAdministrator() && !Html.IsAnsaAnalyst())
            {
            <p>Click the link below to attach documents to this Equipment Item</p>
            @(Html.Kendo().Upload()
                .Name("equipmentItemCertificates")
                .Messages(m => m.Select("Attach Equipment Item Certificates"))
                .Multiple(true)
                .Events(e => e.Success("onEquipmentItemCertificatesAttached").Complete("onEquipmentItemDocumentComplete").Upload("onEquipmentItemDocumentUpload"))
                .HtmlAttributes(new { @style = "width:300px" })
                .Async(async => async.Save("AttachEquipmentItemCertificates", "Assets", new { @equipmentItemId = Model.EquipmentItemId }).Batch(true))
                .Validation(validation => validation.AllowedExtensions(new string[] { ".pdf" })))
            <br />
            }

            @(Html.Kendo().Grid<EquipmentItemCertificateModel>()
                .Name("equipmentItemCertificatesGrid")
                .Columns(c => {
                    c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Assets/GetEquipmentItemCertificate?id=#=Id#'>#=FileName#</a>");
                    c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat).Width(150);
                    c.Bound(p => p.UserName).Title("Created By").Width(200);
                    c.Bound(p => p.ExpiryDate).EditorTemplateName("ExpiryDateTemplate").Title("Expiry Date").Format(DateConstants.DateFormat).Width(250);
                    c.Command(c=>{
                        c.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                        c.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"});
                    }).Width(200);
                })
                .Events(e => e.DataBound("updateEquipmentItemCertificateGrid"))
                .Resizable(r => r.Columns(true))
                .Filterable()
                .Groupable()
                .Editable(e => e.Mode(GridEditMode.InLine))
                .Scrollable(s => s.Height(500))
                .DataSource(dataSource => dataSource
                    .Ajax()
                    .ServerOperation(false)
                    .Model(model =>
                    {
                        model.Id(p => p.Id);
                        model.Field(x => x.UserName).Editable(false);
                        model.Field(x => x.Created).Editable(false);
                        model.Field(x => x.FileName).Editable(false);
                    })
                  
                    .Read(read => read.Action("GetEquipmentItemCertificates", "Assets", new { @equipmentItemId = Model.EquipmentItemId }))
                    .Update(update => update.Action("EditEquipmentItemCertificate", "Assets", new { @equipmentItemId = Model.EquipmentItemId }).Data("equipmentItemCertificatesData"))
                    .Destroy(destroy => destroy.Action("DeleteEquipmentItemCertificate", "Assets", new { @equipmentItemId = Model.EquipmentItemId }))
                    .Events( e => e.RequestEnd("onCertificatesGridSave"))
                )
            )
            <br />
        </text>);
       


    tabstrip.Add().Text("")
        .HtmlAttributes(new { @data_bind="html:equipmentItemHistoryWithNumber"})
        .Content(@<text>
            <div id="equipmentItemLog" class="tab-pane">
                <br />
                <p>The following table is a log of all changes made to this equipment item.</p>
                <hr />
                @(Html.Kendo().Grid<EquipmentItemLogModel>()
                    .Name("equipmentItemLogGrid")
                    .Columns(columns => {
                        columns.Bound(c => c.TypeDescription).Title("Type").Width(125);
                        columns.Bound(c => c.Points).Title("Points").Width(125);
                        columns.Bound(c => c.Reference).ClientTemplate("#if(ReferenceId){# <a href='" + Url.Action("EquipmentItemLogReference", "Assets") + "/#=ReferenceId#?type=#=Type#' title='View #=TypeDescription#'>#=Reference#</a> #} else if(Reference){# #=Reference# #}#").Width(125);
                        columns.Bound(c => c.Log).ClientTemplate("#=Log#");
                        columns.Bound(c => c.UserName).ClientTemplate("#=UserName ? UserName : 'System'#").Width(125);
                        columns.Bound(c => c.Date).Title("Date").Format(DateConstants.DateTimeFormat).Width(150);
                    })
                    .Events(e => e.DataBound("updateTotalLogs"))
                    .Sortable()
                    .Groupable()
                    .Reorderable(reorder => reorder.Columns(true))
                    .Resizable(resize => resize.Columns(true))
                    .Scrollable(s => s.Height(400))
                    .DataSource(dataSource => dataSource
                        .Ajax()
                        .ServerOperation(false)
                        .Read(read => read.Action("GetEquipmentItemLogsByEquipmentItemId", "Assets").Data("equipmentItemLogData"))
                    )
                )
            </div>
    </text>);
}))

@(Html.Kendo().Window().Name("acceptanceWindow")
        .Title("Project Base")
        .Content(@<text>@Html.Partial("AcceptEquipmentItem")</text>)
        .Width(800)
        .Modal(true)
        .Draggable()
        .Visible(false))

@(Html.Kendo().Window().Name("currentLocationChangeWindow")
        .Title("Current Location")
        .Content(@<text>@Html.Partial("ChangeCurrentLocation")</text>)
        .Width(650)
        .Modal(true)
        .Draggable()
        .Visible(false))

@(Html.Kendo().Window().Name("toMaintenanceRecordWindow")
        .Title("Start Maintenance Record")
        .Content(@<text>@Html.Partial("StartMaintenanceRecord")</text>)
        .Width(430)
        .Modal(true)
        .Draggable()
        .Visible(false))

@(Html.Kendo().Window()
    .Name("maintenanceRecordWindow")
    .Width(1000)
    .Height(500)
    .Title("Maintenance Record")
    .Visible(false)
    .Modal(true)
    .Events(e => e.Open("maintenanceRecordWindowOpened"))
    .Content(@<text>
        @(Html.Kendo().Grid<MaintenanceRecordModel>()
            .Name("mRGrid")
            .Columns(columns => {
                columns.Bound(c => c.Number).Title("Maintenancen Record").ClientTemplate("<a href='" + @Url.Action("EditMaintenanceRecord", "Maintenance", new { @id = "" }) + "/#=MaintenanceRecordId#'>#=Number#</a>");
                columns.Bound(c => c.MaintenanceBlueprintId).Title("Maintenancen Blueprint").ClientTemplate("<a href='" + @Url.Action("EditMaintenanceBlueprint", "Admin", new { @id = "" }) + "/#=MaintenanceBlueprintId#'>#=MaintenanceBlueprintName#</a>");
                columns.Bound(c => c.EquipmentItemName).Title("Equipment Item").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#'>#=EquipmentItemName#</a>");
                columns.Bound(c => c.PriorityDescription).Title("Priority");
                columns.Bound(c => c.UserName).Title("Created By").Hidden(true);
                columns.Bound(c => c.Created).Title("Created").Format(DateConstants.DateTimeFormat);
            columns.Bound(c => c.Modified).Title("Modified").Format(DateConstants.DateTimeFormat).Hidden(true);
                columns.Bound(c => c.StatusDescription).Title("Status").ClientTemplate("<span class='badge' style='background:#=StatusColour#;color:#=StatustextColour#'>#=StatusDescription#</span>");
            })
            .ColumnMenu(c => c.Columns(true))
            .Filterable()
            .Sortable()
            .Groupable()
            .AutoBind(false)
            .Scrollable(s => s.Height(532))
            .Resizable(resize => resize.Columns(true))
            .Reorderable(reorder => reorder.Columns(true))
            .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Model(model => {
                    model.Id(m => m.MaintenanceRecordId);
                })
                .Read(read => read.Action("GetMaintenanceRecordByEquipmentItemId", "Maintenance").Data("mRCountData"))
            )
        )
    </text>)
)

<script>

    function klinkor(e) {
        if (e.type === "destroy" && !e.response.Errors) {
            refreshEquipmentHistory();
        }
    }

    $(document).ready(function () {
        if (document.getElementById('EquipmentItemId').value) {
            var isUser = document.getElementById('ManufacturerCompanyName');
            $.ajax({
                url: `/Assets/GetEquipmentItemDropdownData`,
                data: { id: "@(Model.EquipmentItemId)"},
                dataType: "json",
                method: 'GET',
                success: (e) => {
                    e.map(d => {
                        if (d.Item) {
                            if(isUser) {
                                if(d.Name == "ManufacturerCompanyId"){ $('#ManufacturerCompanyName').data("kendoTextBox")?.value(d.Item.Text);}
                                else if(d.Name == "ManufacturerCompanyLocationId"){ $('#ManufacturerCompanyLocationName').data("kendoTextBox")?.value(d.Item.Text);}
                                else { 
                                    $(`#${d.Name} `).data("kendoDropDownList")?.dataSource.add(d.Item);
                                    $(`#${d.Name}`).data("kendoDropDownList")?.value(d.Item.Value);
                                }
                            }
                            else {  
                                $(`#${d.Name} `).data("kendoDropDownList")?.dataSource.add(d.Item);
                                $(`#${d.Name}`).data("kendoDropDownList")?.value(d.Item.Value);
                            }

                            
                        }
                    })
                },
                error: (e) => {
                    kendo.alert(e)
                }
            });
        }
    });

    const editEquipmentItemModel = {
        equipmentCategoryId: "@(Model.EquipmentCategoryId)",
        equipmentItemId: "@(Model.EquipmentItemId)",
        companyLocationId: "@(Model.CurrentCompanyLocationId)",
        equipmentItemIdNumber: @Model.EquipmentItemId,
        eId: "@(Model.EquipmentItemId.HasValue ? Model.EquipmentItemId.Value.ToString() : "")",
        maintenanceConstantClosed: "@MaintenanceConstant.Closed",
        viewBagReturnUrl: "@ViewBag.ReturnUrl",
        equipmentConstantLost: "@EquipmentConstant.Lost",
        equipmentConstantInactive: "@EquipmentConstant.Inactive",
        equipmentConstantOperational: "@EquipmentConstant.Operational",
        equipmentConstantQuarantined: "@EquipmentConstant.Quarantined",
        equipmentConstantArchived: "@EquipmentConstant.Archived",
    }

function canEquipmentItemDocumentDelete(equipmentItem) {
    return ("@Html.IsMaintenanceAdmin()" === "True" || "@Html.IsGlobalAdmin()" === "True") || data.UserEmail === '@Html.AccountEmailAddress()';
}

</script>

<script type="text/x-kendo-tmpl" id="equipmentItemGridHeader">
    <button id="addBundleBtn" class="btn btn-primary" onclick="populateItemGridClick()">
        Add to Bundle
    </button>
    <span class="k-searchbox k-input k-input-md k-rounded-md k-input-solid k-grid-search" style="float:right;width:250px">
        <span class="k-input-icon k-icon k-i-search"></span>
        <input autocomplete="off" placeholder="Search..." title="Search..." class="k-input-inner">
    </span>
</script>

<script type="text/x-kendo-tmpl" id="bundleItemGridHeader">
    <span class="k-searchbox k-input k-input-md k-rounded-md k-input-solid k-grid-search" style="float:right;width:250px">
        <span class="k-input-icon k-icon k-i-search"></span>
        <input autocomplete="off" placeholder="Search..." title="Search..." class="k-input-inner">
    </span>
</script>



<script type="text/x-kendo-tmpl" id="equipmentItemMaintenanceScheduleGridToolbar">
    <a class='btn btn-primary btn-sm k-grid-add' href='/Assets/GetEquimentItemMaintenanceSchedules/" + @Model.EquipmentItemId + "?equipmentId=" + @Model.EquipmentItemId + "&equipmentItemMaintenanceScheduleGrid-mode=insert'>
      <i class='fa fa-plus'></i>Add Maintenance Schedule
    </a>

</script>

<script id="equipmentItemNoteGridToolbar" type="text/x-kendo-tmpl">
    @if (Html.IsJuniorFieldEngineer() ||
       Html.IsFieldEngineer() ||
       Html.IsSeniorFieldEngineer() ||
       Html.IsAssetAdmin() ||
       Html.IsMaintenanceAdmin() ||
       Html.IsMaintenanceEngineer() ||
       Html.IsOperationAdmin() ||
       Html.IsGlobalAdmin() ||
       Html.IsAnsaAdministrator() ||
       Html.IsAnsaAnalyst())
    {
               <a href='/Admin/GetEquipmentItemNotes/" + @Model.EquipmentItemId + "?equipmentItemId=" + @Model.EquipmentItemId + "&equipmentItemNoteGrid-mode=insert' class='btn btn-primary btn-sm k-grid-add'><i class='fa fa-plus'></i>Add Notes</a>
    }
</script>

<script type="text/x-kendo-tmpl" id="maintenanceRecordTemplate">
    <a style="color:\\#0073d0" href="@Url.Action("EditMaintenanceRecord", "Maintenance")/#=MaintenanceRecordId#"><h3 style="color:\\#0073d0"><i class="fa fa-file-text"></i>#=Number# - (#=kendo.toString(kendo.parseDate(Created, 'yyyy-MM-dd'),'dd-MMM-yyyy HH:mm')#)</h3></a>
    <br />

</script>


<environment include="Development">
    <script src="~/js/views/assets/editAssets.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/assets/editAssets.min.js" asp-append-version="true"></script>
</environment>
