﻿@using Centerpoint.Model.ViewModels
@model NewCommentEmail
<html>
<body>
    <p style="font-family:Arial,sans-serif">Dear @Model.User,</p>
     @if (!string.IsNullOrWhiteSpace(Model.Job)) {
    <p style="font-family:Arial,sans-serif">A new comment has been added to a job "@Model.Job" in project "@Model.ProjectName"</p>
     } else {
        <p style="font-family:Arial,sans-serif">A new comment has been added to project - "@Model.ProjectName"</p>
     }
   <p style="font-family:Arial,sans-serif">Comment: @Model.Comment</p>
   <p style="font-family:Arial,sans-serif">Added By: @Model.AddedBy</p>
   <p style="font-family:Arial,sans-serif">Date: @Model.Date</p>
   
</body>
</html>