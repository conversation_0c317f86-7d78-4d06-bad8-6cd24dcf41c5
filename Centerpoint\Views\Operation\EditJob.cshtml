﻿@model JobModel

<div class="header-container-between">
    <h4>
        <i class="fa fa-building"></i>
        @(Model.JobId.HasValue ? Model.JobName : "Create New Job")
    </h4> 
</div>
<br />
   <div class="d-flex actionsContainer">
        @if (Model.JobId.HasValue && Model.ProjectStatus == Centerpoint.Common.Constants.ProjectStatusConstant.Active && !GlobalSettings.IsAisus && (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsSeniorFieldEngineer() || Html.IsJuniorFieldEngineer() || Html.IsFieldEngineer())) {
           
            <a class="btn btn-primary btn-sm" href="@Url.Action("AddRun","Operation",new { @id = Model.JobId} )"><i class="fa fa-plus"></i>Create New Run</a>
            if (!Model.HasRuns && (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsLogisticsAdmin() || Html.IsSeniorFieldEngineer() || Html.IsJuniorFieldEngineer() || Html.IsFieldEngineer())) {
                <a class="btn btn-danger btn-sm" href="#" data-bind="click:deleteJob"><i class="fa fa-ban"></i>Delete Job</a>
            }
        }
        @if (GlobalSettings.IsWellsense)
        {
            <a class="btn btn-primary btn-sm" href="@Url.Action("AddRiskIdentificationSafetyControl","Qhse",new { @id = Model.JobId} )
            "><i class="fa fa-plus"></i>Add SOC Card</a>
        }else{
            <a class="btn btn-primary btn-sm" href="@Url.Action("AddRiskIdentificationSafetyControl","Qhse",new { @id = Model.JobId} )
            "><i class="fa fa-plus"></i>Add RISC Card</a>

        }
        <a class="btn btn-success btn-sm" href="@Url.Action("EditProject","Operation",new { @id = Model.ProjectId} )"><i class="fa fa-refresh"></i>Go to Project</a>
        <a class="btn btn-info btn-sm " href="@Url.Action("Index","Operation" )"><i class="fa fa-refresh"></i>Return to Operations Dashboard</a>
    </div>
<hr />

@using (Html.BeginForm("EditJob", "Operation", FormMethod.Post, new { @id = "editJobForm" })) {
    @Html.ValidationSummary(false)
    @(Html.Kendo().TabStrip()
        .Name("editEquipmentStrips")
        .Animation(false)
        .Items( tabstrip => {
            tabstrip.Add().Text("Details")
                .HtmlAttributes(new { @data_bind="html:tabStripHeaderDetails" })
                .Content(@<text>
                    <div id="details" class="container-fluid pl-0 mt-2">
                        <div class="row">
                            @if (!Html.IsUser()) {
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Project Creation Date</label>
                                    <br />
                                    @Html.Kendo().DateTimePickerFor(m => m.ProjectProjectCreationDate).Enable(false).HtmlAttributes(new { @class = "utcTimePicker" })
                                </div>
                            </div>
                            }
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Job Creation Date</label>
                                    <br />
                                    @Html.Kendo().DateTimePickerFor(m => m.JobCreationDate).HtmlAttributes(new { @class = "utcTimePicker" })
                                </div>
                            </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        @Html.LabelFor(m => m.InvoiceNumber)
                                        <br />
                                        @(Html.TextBoxFor(m => m.InvoiceNumber, new { @class = "form-control"}))
                                    </div>
                                </div>
                        </div>
                        <hr />
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    @Html.LabelFor(m => m.CompanyFieldName)<br />
                                    @if (Model.ProjectOpportunityId.HasValue) {
                                        @(Html.Kendo().DropDownListFor(m => m.CompanyFieldId)
                                                .Filter(FilterType.Contains)
                                                .OptionLabel("Select Field")
                                                .DataTextField("CompanyFieldName")
                                                .DataValueField("CompanyFieldId")
                                                .Events(c => c.Change("oppsFieldChange"))
                                                .DataSource(source => {
                                                    source.Read(read => {
                                                        read.Action("GetFieldsByProjecOpportunityId", "Lookup").Data("filterCompanyFields");
                                                    });
                                                })
                                                .HtmlAttributes(new { @id ="oppsField" }))
                                    } else {
                                        @(Html.Kendo().DropDownListFor(m => m.CompanyFieldId)
                                        .Filter(FilterType.Contains)
                                        .OptionLabel("Select Field")
                                        .DataTextField("CompanyFieldName")
                                        .DataValueField("CompanyFieldId")
                                        .Events(c => c.Change("fieldChange"))
                                        .DataSource(source => {
                                            source.Read(read => {
                                                read.Action("GetFieldsByProjectId", "Lookup").Data("filterProjectFields");
                                            });
                                        })
                                        .HtmlAttributes(new { @id ="projectField" }))
                                    }
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    @Html.LabelFor(m => m.CompanyWellName)
                                        @(Html.Kendo().DropDownListFor(m => m.CompanyWellId)
                                        .Filter(FilterType.Contains)
                                        .OptionLabel("Select Well")
                                        .DataTextField("Name")
                                        .DataValueField("CompanyWellId")
                                        .CascadeFrom("CompanyFieldId")
                                        .Events(e => e.Change("wellChange"))
                                        .DataSource(source => {
                                            source.Read(read => {
                                                read.Action("GetWellsByCompanyFieldId", "Lookup").Data("filterCompanyWells");
                                            });
                                        })
                                        .HtmlAttributes(new { @tabindex = "5" }))
                                </div>  
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>H<sub>2</sub>S (ppm)</label>
                                    <br />
                                    @(Html.Kendo().NumericTextBoxFor(m => m.H2S)
                                    .HtmlAttributes(new { @tabindex = "3"})
                                    .Spinners(false)
                                    .Format("n0"))
                                </div>
                            </div>
                        </div>
                    
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    @Html.LabelFor(m => m.MinimumId)
                                    <br />
                                    @(Html.Kendo().NumericTextBoxFor(p => p.MinimumId)
                                    .HtmlAttributes(new { @tabindex = "4" })
                                    .Spinners(false)
                                    .Format("n3")
                                    .Decimals(3))
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    @Html.LabelFor(m => m.MaximumDeviation)
                                    <br />
                                    @(Html.Kendo().NumericTextBoxFor(p => p.MaximumDeviation)
                                    .HtmlAttributes(new { @tabindex = "5"})
                                    .Spinners(false)
                                    .Format("n0"))
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>CO<sub>2</sub> (%)</label>
                                    <br />
                                    @(Html.Kendo().NumericTextBoxFor(m => m.CO2)
                                    .HtmlAttributes(new { @tabindex = "6"})
                                    .Spinners(false)
                                    .Format("n0"))
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    @Html.LabelFor(m => m.WellType)
                                    <br />
                                    @(Html.Kendo().DropDownListFor(p => p.WellType)
                                    .DataValueField("Key")
                                    .DataTextField("Value")
                                    .Filter(FilterType.Contains)
                                    .OptionLabel("Select Well Type")
                                    .HtmlAttributes(new { @tabindex = "7" })
                                    .BindTo(Centerpoint.Common.Constants.WellTypeConstant.ValuesAndDescriptions.ToList()))
                                </div>
                            </div>
                                    @if (!GlobalSettings.IsAisus)
                                    {
                                     <div class="col-md-4">
                                         <div class="form-group">
                                             <label>Services</label>
                                             <br />
                                             @(Html.Kendo().MultiSelectFor(m => m.ObjectiveIds)
                                             .DataValueField("ObjectiveId")
                                             .DataTextField("Name")
                                             .Placeholder("Select Services")
                                             .Filter(FilterType.Contains)
                                             .HtmlAttributes(new { @tabindex = "8" })
                                             .DataSource(d => d.Read("GetObjectivesbyProjectId", "Lookup", new { @projectId = Model.ProjectId }).ServerFiltering(true)))
                                         </div>
                                     </div>
                                    }
                                   
                            <div class="col-md-4">
                                @if (!GlobalSettings.IsWellsense || GlobalSettings.IsAisus)  
                                {
                                    <div class="form-group">
                                        <label>Conveyance</label>
                                        <br />
                                        @(Html.Kendo().DropDownListFor(p => p.Conveyance)
                                        .DataValueField("Key")
                                        .DataTextField("Value")
                                        .Filter(FilterType.Contains)
                                        .OptionLabel("Select Conveyance")
                                        .HtmlAttributes(new { @tabindex = "9"})
                                        .BindTo(Centerpoint.Common.Constants.ConveyanceConstant.ValuesAndDescriptions.ToList()))
                                    </div>
                                }
                                else
                                {
                                    <div class="form-group">
                                        <label>Deployment type</label>
                                        <br />
                                        @(Html.Kendo().DropDownListFor(p => p.Conveyance)
                                        .DataValueField("Key")
                                        .DataTextField("Value")
                                        .Filter(FilterType.Contains)
                                        .OptionLabel("Select Deployment Type")
                                        .HtmlAttributes(new { @tabindex = "9"})
                                        .BindTo(Centerpoint.Common.Constants.DeploymentTypeConstant.ValuesAndDescriptions.ToList()))
                                    </div>
                                }
                                    
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 d-flex p-0">
                                <div class="col-md-8">
                                    <div class="form-group">
                                        @Html.LabelFor(m => m.MaximumPressure)
                                        <br />
                                        @(Html.Kendo().NumericTextBoxFor(p => p.MaximumPressure)
                                        .HtmlAttributes(new { @tabindex = "10"})
                                        .Spinners(false)
                                        .Format("n2"))
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>Units</label>
                                        <br />
                                        @(Html.Kendo().DropDownListFor(m => m.MaximumPressureUnits)
                                        .DataTextField("Value")
                                        .DataValueField("Key")
                                        .Filter(FilterType.Contains)
                                        .BindTo(Centerpoint.Common.Constants.MaximumPressureUnitsConstant.ValuesAndDescriptions.ToList())
                                        .HtmlAttributes(new { @tabindex = "11"}))
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 d-flex p-0">
                                <div class="col-md-8">
                                    <div class="form-group">
                                        @Html.LabelFor(m => m.MaximumTemperature)
                                        <br />
                                        @(Html.Kendo().NumericTextBoxFor(p => p.MaximumTemperature)
                                        .HtmlAttributes(new { @tabindex = "12"})
                                        .Spinners(false)
                                        .Format("n0"))
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>Degrees</label>
                                        <br />
                                        @(Html.Kendo().DropDownListFor(m => m.MaximumTemperatureDegrees)
                                        .DataTextField("Value")
                                        .DataValueField("Key")
                                        .Filter(FilterType.Contains)
                                        .BindTo(Centerpoint.Common.Constants.MaximumTemperatureDegreesConstant.ValuesAndDescriptions.ToList())
                                        .HtmlAttributes(new { @tabindex = "13"}))
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Crew</label>
                                    <br />
                                    @if (!Model.JobId.HasValue || 
                                    (Model.JobId.HasValue && (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Model.IsCrewEngineer || Html.IsSeniorUSEngineer()))) {
                                        @(Html.Kendo().MultiSelectFor(m => m.CrewsIds)
                                        .DataTextField("UserName")
                                        .DataValueField("CrewId")
                                        .Placeholder("Select Crew")
                                        .HtmlAttributes(new { @tabindex = "14" })
                                        .DataSource(source => {
                                                source.Read(read => {
                                                     read.Action("GetCrewUsersByProjectId", "Lookup", new { @projectId = Model.ProjectId });
                                                })@*.ServerFiltering(true)*@;
                                            }))
                                    }
                                    @if (Model.JobId.HasValue && (!Html.IsGlobalAdmin() && !Html.IsOperationAdmin() && !Model.IsCrewEngineer && !Html.IsSeniorUSEngineer())) {
                                        @Html.TextArea("CrewDisabled", Model.Crews, new { @class = "form-control", @readonly = "readonly" })
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Fluids</label>
                                    <br/>
                                    @(Html.Kendo().MultiSelectFor(m => m.FluidTypeIds)
                                    .Placeholder("Select Fluid(s)")
                                    .DataTextField("Name")
                                    .DataValueField("FluidTypeId")
                                    .DataSource(source => {
                                        source.Read(read => {
                                            read.Action("GetCompanyWellFluids", "Lookup");
                                        })
                                        .ServerFiltering(true);
                                    })
                                    )
                                </div>
                            </div>
                        </div>
                        @if (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsLogisticsAdmin() || Html.IsSeniorFieldEngineer() || Html.IsJuniorFieldEngineer() || Html.IsFieldEngineer() || Html.IsSeniorUSEngineer()) {
                            <button class="btn btn-sm btn-primary" onclick="validateSaveJobDetailsForm(event)">Save Job Details</button>
                        }
                        <hr />
                        @if (Model.HasRuns) {
                            <h4 class="text-primary mb-3">Runs (<span data-bind="text:totalRuns"></span>)</h4>

                            <ul id="runPanelBar" data-role="panelbar"></ul>
                        }
                    </div>



                </text>);

            if(Model.JobId.HasValue) {
            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind="html:tabStripHeaderConstructorOfCommentsWithNumber"})
                .Content(@<text>
                        @if (Model.JobId.HasValue) {
                            <div id="comments" class="tab-pane @(ViewBag.Tab=="comments" ? "active":string.Empty)">
                                @(Html.Kendo().Grid<JobCommentModel>()
                                    .Name("jobCommentGrid")
                                    .Columns(c => {
                                        c.Bound(p => p.Comment).Encoded(false).ClientTemplate("#= getHtmlNewLinesString(Comment) #");
                                        c.Bound(p => p.UserName).Width(125);
                                        c.Bound(p => p.Date).Format(DateConstants.DateTimeFormat).Width(125);
                                        c.Command(command => { 
                                            command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"}).Visible("isJobCommentGridFieldEditable");
                                            command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}).Visible("isJobCommentGridFieldEditable"); 
                                        }).Width(220);
                                    })
                                    .Editable(editable => editable.Mode(GridEditMode.PopUp).DisplayDeleteConfirmation("Are you sure you want to delete this Comment?").TemplateName("JobCommentWindow")
                                    .Window(w => w.Name("JobCommentWindow").Title("Job Comment").Width(850).Draggable(false)))
                                    .ToolBar(c => c.Create().Text("Add Comments"))
                                    .Sortable()
                                    .Filterable()
                                    .Groupable()
                                    .Events(e => e.Edit("jobCommentEdit"))
                                    .Events(e => e.DataBound("updateJobCommentGrid"))
                                    .Scrollable(s => s.Height(500))
                                    .Resizable(c => c.Columns(true))
                                    .ColumnMenu(c => c.Columns(true))
                                    .DataSource(dataSource => dataSource
                                    .Ajax()
                                    .ServerOperation(false)
                                    .Model(m => m.Id(p => p.JobCommentId))
                                    .Events(e => e.RequestEnd("onRequestEndRefreshJobCommentGrid"))
                                    .Read(read => read.Action("GetJobComments", "Operation", new { @jobId = Model.JobId }))
                                    .Create(create => create.Action("UpdateJobComment", "Operation", new { @jId = Model.JobId }).Data("jobCommentData"))
                                    .Update(update => update.Action("UpdateJobComment", "Operation", new { @jId = Model.JobId }).Data("jobCommentData"))
                                    .Destroy(destroy => destroy.Action("DeleteJobComment", "Operation"))))
                            </div>
                        }
                </text>);    
            }
        }));
    @Html.HiddenFor(m => m.JobId)
    @Html.HiddenFor(m => m.ProjectId)
    @Html.HiddenFor(m => m.ProjectCompanyId)
    @Html.HiddenFor(m => m.ProjectOperatorCompanyId)
    @Html.HiddenFor(m => m.ProjectProjectCreationDate)
    @Html.HiddenFor(m => m.Crews)
}
    <script type="text/x-kendo-tmpl" id="runTemplate">
        <div class="container-fluid">
            <div class="d-flex w-75 justify-content-between mt-4">
                <div>
                    #if(LatestRunUpdate){#
                    <h5 class="text-primary"><i class="fa fa-clock"></i> Latest Update - #=LatestRunUpdate.LogDate# (Centerpoint Time)</h5>
                    <p>#=LatestRunUpdate.Log# by <span class="text-primary">#=LatestRunUpdate.UserName#</span></p>
                    #} else {#
                    <h5 class="text-primary"><i class="fa fa-clock"></i>Latest Update</h5>
                    <p>No recent updates found.</p>
                    #}#
                    <br />
                    <h5 class="text-primary"><i class="fa fa-users"></i> Crew</h5>
                    <p>#=JobCrews ? JobCrews : 'No crew assigned'#</p>
                    <br />
                    #if(OppsName){#
                    <span><a class="text-primary" href="@Url.Action("Edit", "Sales")/#=LinkOpportunityId#?revision=#=OppsMaxRevision#"><i class="fa fa-usd"></i> #=OppsName#</a></span>
                    #}#
                </div>
                <div>
                    #if(!IsStandBy){#
                    <div class="d-flex">
                        <h5><span class="text-primary">Services</span></h5>
                        <span style="padding-left:12px; font-size:13px">#=Objectives#</span>
                    </div>
                    <div class="d-flex">
                        <h5><span class="text-primary">Top Depth</span></h5>
                        <span style="padding-left:12px; font-size:13px">#=Top# #=TopUnits == '@UnitsConstant.Feet' ? 'ft' : 'm'#</span>
                    </div>
                    <div class="d-flex">
                        <h5><span class="text-primary">Bottom Depth</span></h5>
                        <span style="padding-left:12px; font-size:13px">#=Bottom# #=TopUnits == '@UnitsConstant.Feet' ? 'ft' : 'm'#</span>
                    </div>
                    <div class="d-flex">
                        <h5><span class="text-primary">Total Log Length</span></h5>
                        <span style="padding-left:12px; font-size:13px">#=LoggedDepth# #=TopUnits == '@UnitsConstant.Feet' ? 'ft' : 'm'#</span>
                    </div>
                    #} else{#
                    <h5 class="text-primary">Standby</h5>
                    #}#
                </div>
                <div>
                    <div class="d-flex">
                        <h5> <span class="text-primary">Start Time</span></h5>
                        <span style="padding-left:12px; font-size:13px">#=RunStartOnly# </span>
                    </div>
                    <div class="d-flex">
                        <h5> <span class="text-primary">Finish Time </span></h5>
                        <span style="padding-left:12px; font-size:13px">#=RunFinishOnly#</span>
                    </div>
                    <div class="d-flex">
                        <h5> <span class="text-primary">Total Time</span></h5>
                        <span style="padding-left:12px; font-size:13px">#=TotalTime#</span>
                    </div>
                    <div class="d-flex">
                        <h5> <span class="text-primary">Lost Time</span></h5>
                        <span style="padding-left:12px; font-size:13px">#=TotalLostTime ? TotalLostTime : 'No Lost Time'#</span>
                    </div>
                </div>
            </div>
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h5 class="text-primary"> Notes</h5>
                    #if(EngineerNotes){#
                    <p>#=kendo.toString(kendo.parseDate(CreatedDate, 'yyyy-MM-dd'),'dd-MMM-yyyy HH:mm')# - <span>#=EngineerNotes#</span></p>
                    #} else{#
                    <p>No notes found.</p>
                    #}#
                </div>
                <div>
                   <a class="btn btn-sm btn-primary" href="@Url.Action("EditRun", "Operation")/#=RunId#"><i class="fa fa-folder-open"></i> Open Run</a>
                </div>
            </div>
        </div>
    </script>

    <script>
        $(document).ready(function () {
            var tabStrip = $("#editEquipmentStrips").data("kendoTabStrip");
            if ("@ViewBag.Tab" === "comments") {
                tabStrip.select(1);
            }
            else{
                tabStrip.select(0);
            }

            @if (Model.JobId.HasValue && Model.HasRuns) {
                <text>
                    getActiveRuns();
                </text>
            }
        });

        function parseDate() {
            var now = new Date();
            return new Date(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), now.getUTCHours(), now.getUTCMinutes(), now.getUTCSeconds());

        }
        
        function jobCommentData(e) {
            e.Date = e.Date.toUTCString()
        }

        function getHtmlNewLinesString(text) {
            var regexp = new RegExp('\n', 'g');
            return text ? text.replace(regexp, '<br>') : text;
        }

        function getActiveRuns() {
            $.ajax({
                url: "@Url.Action("GetRuns","Operation")",
                dataType: "json",
                data: {
                    jobId : "@Model.JobId"
                },

                success: function (result) {
                    var runPanelBar = $("#runPanelBar").data("kendoPanelBar");
                    runPanelBar.remove($("li", runPanelBar.element));

                    if (result) {
                        viewModel.set("runs", result);

                        //Todo:load detail template
                        for (var i = 0; i < result.length; i++) {
                            var runTemplate = kendo.template($("#runTemplate").html()); // load detail template
                            var runHtml = runTemplate(result[i]); // fill template with data

                            runPanelBar.append({
                                text: '<span>' + result[i].RunName + '</span> <span style=\'margin-right:10px\' class=\'pull-right\'>' + result[i].OppsName + '</span>',
                                encoded: false,
                                content:runHtml
                            });
                        }

                        runPanelBar.expand($("li:first", runPanelBar.element));
                    }
                }
            });
        }

        function filterCompanyFields() {
            return {
                companyId: "@(Model.ProjectCompanyId)",
                operatorCompanyId: "@(Model.ProjectOperatorCompanyId)"
            };
        }

        function fieldChange() {
                $("#CompanyWellId").data("kendoDropDownList").dataSource.read()
        }

        function oppsFieldChange() {
                $("#CompanyWellId").data("kendoDropDownList").dataSource.read()
         }

        function filterProjectFields() {
            return {
                projectId: "@Model.ProjectId"
            };
        }

        function filterCompanyFields() {
            return {
                projectOpportunityId: "@Model.ProjectOpportunityId"
            };
        }

        function filterCompanyWells() {
            return {
                @if (Model.ProjectOpportunityId.HasValue) {
                <text>
                companyFieldId: $("#oppsField").data("kendoDropDownList").value()
                </text>
                } else {
                <text>
                companyFieldId: $("#projectField").data("kendoDropDownList").value()
                </text>
                }
                };
        }

        function isJobCommentGridFieldEditable(data) {
           return data.UserEmail && data.UserEmail == "@Html.AccountEmailAddress()" ? true : false
        }

        function updateJobCommentGrid() {
            var jobCommentGrid = $("#jobCommentGrid").data("kendoGrid");
            var totalJobComments = jobCommentGrid.dataSource.total();
            viewModel.set("totalJobComments", totalJobComments);
        }

        function jobCommentEdit(e) {
            $(e.container).find(".k-edit-buttons").html("<a class='btn btn-primary btn-sm k-grid-update' href='#'>Update</a> " +
               "<a class='btn btn-primary btn-sm k-grid-cancel' href='#'>Cancel</a>");
            $(e.container).data('kendoWindow').bind('activate',function(e){
                $('#Comment').focus();
            })
        }

        function wellChange(e){
            var index = this.selectedIndex, dataItem;
            var well = this.dataItem(index);

            var wellMinimum = well.MinimumId;
            var wellMaximumDeviation = well.MaximumDeviation;
            var wellMaximumTemperature = well.MaximumTemperature;
            var wellMaximumPressure = well.MaximumPressure;
            var wellMaximumPressureUnits = well.MaximumPressureUnits;
            var wellMaximumTemperatureDegrees = well.MaximumTemperatureDegrees;
            var wellConveyance = well.Conveyance;
            var wellType = well.WellType;
            var wellCO2 = well.CO2;
            var wellH2S = well.H2S;
            var wellCO2 = well.CO2;
            var fluidTypes = well.FluidTypeIds;

            if(well){
                $("#MinimumId").data("kendoNumericTextBox").value(wellMinimum);
                $("#MaximumDeviation").data("kendoNumericTextBox").value(wellMaximumDeviation);
                $("#MaximumTemperature").data("kendoNumericTextBox").value(wellMaximumTemperature);
                $("#MaximumPressure").data("kendoNumericTextBox").value(wellMaximumPressure);
                $("#MaximumPressureUnits").data("kendoDropDownList").value(wellMaximumPressureUnits);
                $("#MaximumTemperatureDegrees").data("kendoDropDownList").value(wellMaximumTemperatureDegrees);
                $("#WellType").data("kendoDropDownList").value(wellType);
                $("#Conveyance").data("kendoDropDownList").value(wellConveyance);
                $("#H2S").data("kendoNumericTextBox").value(wellH2S);
                $("#CO2").data("kendoNumericTextBox").value(wellCO2);
                $("#FluidTypeIds").data("kendoMultiSelect").value(fluidTypes);
            }
        }

        function validateSaveJobDetailsForm(e){
            e.preventDefault();
            if($('#editJobForm').kendoValidator().data('kendoValidator').validate()){
                $('#editJobForm').submit();
            }
        }
        function onRequestEndRefreshJobCommentGrid(e) {
            if (e.type !== "read") {
                $("#jobCommentGrid").data("kendoGrid").dataSource.read();
            }
        }

        var viewModel = kendo.observable({
            runs: [],
            hasRuns : @(Model.HasRuns ? "true" : "false"),
            hasCrews: @(Model.HasCrews ? "true" : "false"),
            totalRuns: function () {
                var runs = this.get("runs");
                return runs.length;
            },

            totalJobComments:0,
            deleteJob:function(){
                var confirmDelete = confirm("Are you sure you wish to delete this job");

                if(confirmDelete){
                    window.location.href= "@Url.Action("DeleteJob", "Operation", new { @id = Model.JobId } )";
                }
            },
            tabStripHeaderDetails: function () {
                return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Details </span>`;
            },
            tabStripHeaderConstructorOfCommentsWithNumber: function () {
                return `<span class="k-link"><i class="fa fa-comment mr-1"></i>Comments (<span data-bind="text: totalJobComments"></span>)</span>`
            },
        });



        kendo.bind(document.body.children, viewModel);
    </script>
