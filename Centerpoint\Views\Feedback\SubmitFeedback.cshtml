﻿@{
    ViewBag.Title = "SubmitFeedback";
    Layout = "~/Views/Shared/_FeedbackLayout.cshtml";
}

@if (ViewBag.IsFeedbackLeft == true) {
    <div style="text-align:center">
        <h2>Feedback has already been left for this survey on '@ViewBag.FeedbackCreatedDate' by '@ViewBag.FeedbackCreatedBy'.</h2>
        <h4>If you have any further queries please do not hesitate to get in touch with us.</h4>
        <h4>You will be redirected to READ's website in 10 seconds</h4>
    </div>
    } else {
    <h2>Submit Feedback</h2>
    <hr />
    <div class="panel panel-default">
        <div class="panel-heading">
            <div class="panel-title">
                <h3>Performance Indicator</h3>
            </div>
        </div>
        <div class="panel-body">
            <table class="table table-striped table-bordered" id="performanceIndicator" cellspacing="0" style="word-wrap:break-word">
                <tr>
                    <td width="60%"><h4>Communication from the ANSA team  (response/support/planning)</h4></td>
                    <td width="40%">
                        @(Html.Kendo().DropDownList().Name("CommunicationFromAnsaTeamRating")
                                        .OptionLabel("Select Rating")
                                        .DataValueField("Key")
                                        .DataTextField("Value")
                                        .Filter("contains")
                                        .DataSource(d => d.Read("GetAnsaFeedbackRatings", "Lookup"))
                                        .HtmlAttributes(new { @style = "width:80%; font-size: 14px;" }))
                    </td>
                </tr>
                <tr>
                    <td width="60%"><h4>Performance of Analyst (polite/knowledgeable/helpful)</h4></td>
                    <td width="40%">
                        @(Html.Kendo().DropDownList().Name("PerformanceOfAnalystRating")
                                            .Filter("contains")
                                            .OptionLabel("Select Rating")
                                             .DataValueField("Key")
                                             .DataTextField("Value")
                                             .DataSource(d => d.Read("GetAnsaFeedbackRatings", "Lookup"))
                                            .HtmlAttributes(new { @style = "width:80%; font-size: 14px;" }))
                    </td>
                </tr>
                <tr>
                    <td width="60%"><h4>Turnaround time of the report (in relation to agreed time)</h4></td>
                    <td width="40%">
                        @(Html.Kendo().DropDownList().Name("TurnaroundTimeRating")
                                            .Filter("contains")
                                            .OptionLabel("Select Rating")
                                             .DataValueField("Key")
                                             .DataTextField("Value")
                                             .DataSource(d => d.Read("GetAnsaFeedbackRatings", "Lookup"))
                                            .HtmlAttributes(new { @style = "width:80%; font-size: 14px;" }))
                    </td>
                </tr>
                <tr>
                    <td width="60%"><h4>Quality of interpretation  (clear/concise/logical)</h4></td>
                    <td width="40%">
                        @(Html.Kendo().DropDownList().Name("QualityOfInterpretationRating")
                                            .Filter("contains")
                                            .OptionLabel("Select Rating")
                                             .DataValueField("Key")
                                             .DataTextField("Value")
                                             .DataSource(d => d.Read("GetAnsaFeedbackRatings", "Lookup"))
                                            .HtmlAttributes(new { @style = "width:80%; font-size: 14px;" }))
                    </td>
                </tr>
                <tr>
                    <td width="60%"><h4>Quality of final product and content  (format/grammar/detail)</h4></td>
                    <td width="40%">
                        @(Html.Kendo().DropDownList().Name("QualityOfFinalProductAndContentRating")
                                            .Filter("contains")
                                            .OptionLabel("Select Rating")
                                             .DataValueField("Key")
                                             .DataTextField("Value")
                                             .DataSource(d => d.Read("GetAnsaFeedbackRatings", "Lookup"))
                                            .HtmlAttributes(new { @style = "width:80%; font-size: 14px;" }))
                    </td>
                </tr>
            </table>
            <br />
            <h4>What can we do better?</h4>
            @Html.TextArea("WhatCanWeDoBetter", null, new { @class = "form-control", @style = "width:800; height:200" })
            <br />
            <h4>Which areas of the Study would you rate as particularly commendable?</h4>
            @Html.TextArea("ParticularlyCommendable", null, new { @class = "form-control", @style = "width:800; height:200" })
            <br />
            <a class="btn btn-sm btn-primary " style="margin-right: 10px; margin-top: 40px" id="submitFeedbackClick">Submit Feeback </a>
        </div>
    </div>
    }

@section Scripts{
    <script>
        @if (ViewBag.IsFeedbackLeft == true) {
       <text>
      $(document).ready(function() {
        setTimeout(function() {
            window.location.href = "http://www.readcasedhole.com/"
           }, 10000);
      });
         </text>
        }
    </script>
    <script>
    $("#submitFeedbackClick").click(function(){
        var communicationFromAnsaTeamRating = $("#CommunicationFromAnsaTeamRating").data("kendoDropDownList").value();
        var performanceOfAnalystRating = $("#PerformanceOfAnalystRating").data("kendoDropDownList").value();
        var turnaroundTimeRating = $("#TurnaroundTimeRating").data("kendoDropDownList").value();
        var qualityOfInterpretationRating = $("#QualityOfInterpretationRating").data("kendoDropDownList").value();
        var qualityOfFinalProductAndContentRating = $("#QualityOfFinalProductAndContentRating").data("kendoDropDownList").value();
        var whatCanWeDoBetter = $("#WhatCanWeDoBetter").val();
        var particularlyCommendable = $("#ParticularlyCommendable").val();
        if (communicationFromAnsaTeamRating == "" || performanceOfAnalystRating == "" || turnaroundTimeRating == "" || qualityOfInterpretationRating == "" || qualityOfFinalProductAndContentRating == "") {
            $("<div id='dialog'></div>").kendoDialog({
                closable: false,
                title: "Warning!",
                close:()=>{
                    var dialog = $("#dialog").data("kendoDialog");
                    dialog.destroy();
                },
                width: 500,
                buttonLayout: "normal",
                content: "All ratings must be completed before you can submit this feedback.",
                actions: [
                    {
                        text: "Close",
                        action: function(){
                        },
                        cssClass: 'btn-primary'
                    },
                ]
            }).data("kendoDialog").open().center()
        } else {
            $.ajax({
                type: 'POST',
                dataType: 'json',
                url: "@Url.Action("UpdateCustomerFeedback", "Feedback")",
                data: {
                    ansaProjectId: "@ViewBag.AnsaProjectId",
                    communicationFromAnsaTeamRating: communicationFromAnsaTeamRating,
                    performanceOfAnalystRating: performanceOfAnalystRating,
                    turnaroundTimeRating: turnaroundTimeRating,
                    qualityOfInterpretationRating: qualityOfInterpretationRating,
                    qualityOfFinalProductAndContentRating: qualityOfFinalProductAndContentRating,
                    whatCanWeDoBetter: whatCanWeDoBetter,
                    particularlyCommendable: particularlyCommendable
                },
                success: function () {
                    window.location.href = "/Feedback/FeedbackConfirmation";
                }
            });
        }
  })

    </script>
}

