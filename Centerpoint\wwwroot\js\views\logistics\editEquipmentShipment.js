function excelExport(e) {
    var workbook = e.workbook;
    var sheet = workbook.sheets[0];
    var customCodes = [];

    $.ajax({
        url: "/Lookup/GetEquipmentItemCustomStatusCodes",
        dataType: "json",
        success: function (result) {
            customCodes = result;

            for (var i = 1; i < sheet.rows.length; i++) {
                var customStatusCodeId = sheet.rows[i].cells[2].value;
                var customStatusCode = $.grep(customCodes, function (item) {
                    return item.EquipmentItemCustomStatusCodeId == customStatusCodeId;
                })[0];

                if (customStatusCode) {
                    sheet.rows[i].cells[2].value = customStatusCode.CustomStatusName;
                }
            }
        }
    });
}
function onEquipmentShipmentDocumentAttached() {
    var equipmentShipmentDocumentsGrid = $("#equipmentShipmentDocumentsGrid").data("kendoGrid");
    equipmentShipmentDocumentsGrid.dataSource.read();
}
function onEquipmentShipmentDocumentComplete(e) {
    $(".k-upload-files.k-reset").find("li").remove();
    $(".k-upload-files.k-reset").slideUp();
}
function onEquipmentShipmentDocumentUpload(e) {
    uploadValidation(e);

    $(".k-upload-files.k-reset").show();
}
function updateEquipmentShipmentDocumentGrid() {
    var equipmentItemDocumentsGrid = $("#equipmentShipmentDocumentsGrid").data("kendoGrid");
    var totalAttachments = equipmentItemDocumentsGrid.dataSource.total();
    viewModel.set("totalAttachments", totalAttachments);
}
function updatePendingEquipmentPackingListGrid() {
    var pendingEquipmentPackingListGrid = $("#PendingEquipmentPackingListGrid").data("kendoGrid");

    var packingListData = pendingEquipmentPackingListGrid.dataSource.data();

    $.each(packingListData, function (i, item) {
        if (item.IsSignedOff) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(1)').css("background-color", "#ff7f7f  ");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(2)').css("background-color", "#ff7f7f  ");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(3)').css("background-color", "#ff7f7f  ");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(4)').css("background-color", "#ff7f7f  ");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(5)').css("background-color", "#ff7f7f  ");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(6)').css("background-color", "#ff7f7f  ");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(7)').css("background-color", "#ff7f7f  ");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(8)').css("background-color", "#ff7f7f  ");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(1)').css("color", "#fff");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(2)').css("color", "#fff");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(3)').css("color", "#fff");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(4)').css("color", "#fff");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(5)').css("color", "#fff");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(6)').css("color", "#fff");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(7)').css("color", "#fff");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(8)').css("color", "#fff");
        }
    });
}

$(document).ready(function () {
    initialLoadSettings();
    if(editEquipmentShipmentModel.modelSentDate) {
      $("#CreatedDate").data("kendoDatePicker").max(editEquipmentShipmentModel.modelSentDate)
    }

    var PendingEquipmentPackingListGrid = $('#PendingEquipmentPackingListGrid').data("kendoGrid");
    var equipmentShipmentInvoiceGrid = $('#equipmentShipmentInvoiceGrid').data("kendoGrid");
    if(PendingEquipmentPackingListGrid){
        PendingEquipmentPackingListGrid.bind('dataBound', function (e) {
            this.element.find('.k-add').remove();
        });
    }
    if(equipmentShipmentInvoiceGrid){
        equipmentShipmentInvoiceGrid.bind('dataBound', function (e) {
            this.element.find('.k-add').remove();
        });
    }
});

function initialLoadSettings() {
    activateTabDependingOnUrlQuery();
    viewModel.set("isEditTabStripVisible", true)
}

function equipmentItemMaintenanceScheduleData() {
    return {
        eId: viewModel.get("selectedEquipmentItemId")
    }
}

function scheduleDates(equipmentItemId) {
    viewModel.set("selectedEquipmentItemId", equipmentItemId);

    $("#scheduleDatesWindow").data("kendoWindow").center().open();
}

function scheduleDatesWindowOpened() {
    var equipmentItemMaintenanceScheduleGrid = $("#equipmentItemMaintenanceScheduleGrid").data("kendoGrid");
    equipmentItemMaintenanceScheduleGrid.dataSource.read();
}

function maintenanceRecordData() {
    return {
        equipId: viewModel.get("selectedEquipmentItemId")
    }
}

function maintenanceRecordCount(equipmentItemId) {
    viewModel.set("selectedEquipmentItemId", equipmentItemId);

    $("#maintenanceRecordWindow").data("kendoWindow").center().open();
}

function maintenanceRecordWindowOpened() {
    var maintenanceRecordGrid = $("#maintenanceRecordGrid").data("kendoGrid");
    maintenanceRecordGrid.dataSource.read();
}

function updatedMaintenanceRecordGrid(e) {
    var maintenanceRecordGrid = $("#maintenanceRecordGrid").data("kendoGrid");

    var mrData = maintenanceRecordGrid.dataSource.data();

    var rows = e.sender.tbody.children();

    for (var j = 0; j < rows.length; j++) {
        var row = $(rows[j]);
        var dataItem = e.sender.dataItem(row);

        if (dataItem.get("Fail") == true) {
            row.addClass("fail");
        }
    }
}

function paperworkTypeChange(e) {
    if($('#equipmentShipmentInvoiceForm')) {
        $('#equipmentShipmentInvoiceForm').kendoValidator().data('kendoValidator').validate()
        $("#equipmentShipmentInvoiceForm").data('kendoValidator').reset()
    }
    if(this.value() == editEquipmentShipmentModel.equipmentShipmentPaperworkConstantShippingDocket || 
        this.value() == editEquipmentShipmentModel.equipmentShipmentPaperworkConstantDeliveryNote ||
        this.value() == editEquipmentShipmentModel.paperTypeMaintSchedule
      ){
        $("#valueType").parent().hide();
        $("#valueLabel").hide();
        $("#commodityCode").parent().hide();
        $("#commodityLabel").hide();
        $("#weightLabel").hide();
        $("#netWeight").parent().hide();
        $("#currency").parent().hide();
        $("#currencyLabel").hide();
        $("#currency").removeAttr("data-val-required");
        $("#valueType").removeAttr("data-val-required");
        $("#newUsed").parent().show();
        $("#newUsedLabel").show();
        $("#tempPermanent").parent().show();
        $("#tempPermanentLabel").show();
    } else{
        $("#valueType").parent().show();
        $("#valueLabel").show();
        $("#netWeight").parent().show();
        $("#weightLabel").show();
        $("#currency").parent().show();
        $("#currencyLabel").show();
        $("#commodityCode").parent().show();
        $("#commodityLabel").show();
        $("#currency").attr("data-val-required", "Currency is required");
        $("#valueType").attr("data-val-required", "Value is required");
        $("#newUsed").parent().hide();
        $("#newUsedLabel").hide();
        $("#tempPermanent").parent().hide();
        $("#tempPermanentLabel").hide();
    }
}


function onSave(e) {
    kendo.ui.progress(e.container, true);
}

function onChange(e){
    var index = this.selectedIndex, dataItem;
    var project = this.dataItem(index);
    if(project) {
        $.ajax({
                type: 'GET',
                cache: false,
                dataType: 'json',
                url: `/Operation/GetProjectCompanyInfo/${project.Value}`,
                success: function (result) {
                    fillCompanyDetails(result)
                },
            });
    }
}    
    
function fillCompanyDetails(info) {
    var toCompanyLocationId = info.CompanyLocationId;
    let toAddress;
    if (info.CompanyLocationHouseNumber != null && info.CompanyLocationStreet != null && info.CompanyLocationCity != null && info.CompanyLocationPostcode != null && info.CompanyLocationCountry != null) {
        if (info.CompanyLocationHouseNumber == null) {
            toAddress = info.CompanyLocationStreet + "\r" + info.CompanyLocationPostcode + "\r" + info.CompanyLocationCity + "\r" + info.CompanyLocationCountry + "\r" + info.CompanyLocationCountry;
        } else {
            toAddress = info.CompanyLocationHouseNumber + "\r" + info.CompanyLocationStreet + "\r" + info.CompanyLocationPostcode + "\r" + info.CompanyLocationCity + "\r" + info.CompanyLocationCountry;
        }
    } else {
        toAddress = info.CompanyLocationName;
    }
    $("#ToAddress").val(toAddress);
    viewModel.set("toCompanyId", info.CompanyId);
    viewModel.set("toCompanyLocationId", toCompanyLocationId)
    $("#ToCompanyLocationId").data("kendoDropDownList").dataSource.read().then( e => {
        $("#ToCompanyLocationId").data("kendoDropDownList").value(toCompanyLocationId)
    })
    // for Company Asset data refresh
    $("#ToCompanyFieldId").data("kendoDropDownList").dataSource.read()
}

function shipmentItemsRequestEnd(e) {
    if (!e.type || e.type == "destroy") {
        refreshEquipmentItems();
        refreshEquipmentShipmentItems();
    }
}

function equipmentItemCustomStatusCodeData() {
    var equipmentShipmentItem = viewModel.get("equipmentShipmentItem");
    if (equipmentShipmentItem) {
        return {
            equipmentItemId: equipmentShipmentItem.EquipmentItemId
        }
    }
}

function saveEquipmentShipmentItemId(e) {
   viewModel.set("equipmentShipmentItem", e.model) 
}

function onError(e, status) {
    if (e.status == "customerror") {
        alert(e.errors);

        var equipmentShipmentNonAssetItemGrid = $("#equipmentShipmentNonAssetItemGrid").data("kendoGrid");
        equipmentShipmentNonAssetItemGrid.dataSource.cancelChanges();
    }
}
function fromCompanyLocationChanged(e) {
    var index = this.selectedIndex, dataItem;
    var companyLocation = this.dataItem(index);

    if (companyLocation) {
        if(companyLocation.HouseNumber != null && companyLocation.Street != null && companyLocation.City != null && companyLocation.Postcode!= null && companyLocation.Country !=null){
            var fromAddress = companyLocation.HouseNumber + "\r" + companyLocation.Street + "\r" + companyLocation.Postcode + "\r" + companyLocation.City + "\r" + companyLocation.Country + "\r" + companyLocation.TelephoneNumber;
        } else if (companyLocation.Street != null && companyLocation.City != null && companyLocation.Postcode != null && companyLocation.Country != null) {
            var fromAddress = companyLocation.Street + "\r" + companyLocation.Postcode + "\r" + companyLocation.City + "\r" + companyLocation.Country + "\r" + companyLocation.TelephoneNumber;
        } else if (companyLocation.HouseNumber != null && companyLocation.Street != null && companyLocation.City != null && companyLocation.Country != null) {
            var fromAddress = companyLocation.HouseNumber + "\r" + companyLocation.Street + "\r" + companyLocation.City + "\r" + companyLocation.Country + "\r" + companyLocation.TelephoneNumber;
        } else if(companyLocation.Street != null && companyLocation.City != null && companyLocation.Country !=null){
            var fromAddress = companyLocation.Street  + "\r" + companyLocation.City + "\r" + companyLocation.Country + "\r" + companyLocation.TelephoneNumber;
        }
       
        $("#FromAddress").val(fromAddress);
    }
}

function toCompanyLocationChanged(e) {
    var index = this.selectedIndex, dataItem;
    var companyLocation = this.dataItem(index);
    if (companyLocation) {
        if(companyLocation.HouseNumber != null && companyLocation.Street != null && companyLocation.City != null && companyLocation.Postcode!= null && companyLocation.Country !=null){
            var toAddress = companyLocation.HouseNumber + "\r" + companyLocation.Street + "\r" + companyLocation.Postcode + "\r" + companyLocation.City + "\r" + companyLocation.Country + "\r" + companyLocation.TelephoneNumber;
        } else if (companyLocation.Street != null && companyLocation.City != null && companyLocation.Postcode != null && companyLocation.Country != null) {
            var toAddress = companyLocation.Street + "\r" + companyLocation.Postcode + "\r" + companyLocation.City + "\r" + companyLocation.Country + "\r" + companyLocation.TelephoneNumber;
        } else if (companyLocation.HouseNumber != null && companyLocation.Street != null && companyLocation.City != null && companyLocation.Country != null) {
            var toAddress = companyLocation.HouseNumber + "\r" + companyLocation.Street + "\r" + companyLocation.City + "\r" + companyLocation.Country + "\r" + companyLocation.TelephoneNumber;
        } else if(companyLocation.Street != null && companyLocation.City != null && companyLocation.Country !=null){
            var toAddress = companyLocation.Street  + "\r" + companyLocation.City + "\r" + companyLocation.Country + "\r" + companyLocation.TelephoneNumber;
        }

        $("#ToAddress").val(toAddress);
    }
}

function equipmentItemData() {
    var equipmentCategory = viewModel.get("selectedEquipmentCategory");
    var equipmentShipmentId = viewModel.get("equipmentShipmentId");
    return {
        equipmentCategoryId: equipmentCategory ? equipmentCategory.EquipmentCategoryId : "",
        equipmentShipmentId: equipmentShipmentId
    };
}

function equipmentShipmentItemData(e) {
    e.EquipmentItemPurchasedDate = toUTCString(e.EquipmentItemPurchasedDate);
    e.EquipmentItemReceivedDate = toUTCString(e.EquipmentItemReceivedDate);
    return {
        equipmentShipmentItemId: e.EquipmentShipmentItemId
    }
}

function updateEquipmentShipmentInvoiceTotals() {
    var equipmentShipmentInvoiceGrid = $("#equipmentShipmentInvoiceGrid").data("kendoGrid");
    var totalEquipmentShipmentInvoices = equipmentShipmentInvoiceGrid.dataSource.total();
    viewModel.set("totalEquipmentShipmentInvoices", totalEquipmentShipmentInvoices);

    var equipmentShipmentInvoiceGrid = $("#equipmentShipmentInvoiceGrid");
    kendo.ui.progress(equipmentShipmentInvoiceGrid, false);
}
function updateShipmentPackagesTotals() {
    var shipmentPackagesGrid = $("#equipmentShipmentPackageGrid").data("kendoGrid");
    var totalShipmentPackages = shipmentPackagesGrid.dataSource.total();
    viewModel.set("totalPackageInformation", totalShipmentPackages);

    var equipmentShipmentGrid = $("#equipmentShipmentPackageGrid");
    kendo.ui.progress(equipmentShipmentGrid, false);
}
function updateEquipmentTotals() {
    $("#resetShipmentEquipmentItemsGrid").click(function (e) {
        e.preventDefault();
        viewModel.set("selectedEquipmentItemId", "")
        resetCustomGridView('equipmentItemGrid', 'shipmentEquipmentItemGrid', 'initialShipmentEquipmentItemGrid');
        $(`#equipmentItemGrid`).data("kendoGrid").dataSource.read();
        kendo.bind($("#equipmentItemGrid"), viewModel);
    });
    if (!$.cookie('equipmentCategory')) {
        refreshEquipmentItems();
    }
    var equipmentItemGrid = $("#equipmentItemGrid").data("kendoGrid");
    var totalEquipmentItems = equipmentItemGrid.dataSource.total();
    viewModel.set("totalEquipmentItems", totalEquipmentItems);

    var equipmentData = equipmentItemGrid.dataSource.data();

    $.each(equipmentData, function (i, item) {
        if (item.MaintenanceScheduleDaysAlert) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(15)').css("color", "#E31E33 !important");
        }
        if (item.MaintenanceSchedulePointsAlert) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(16)').css("color", "#E31E33 !important");
        }
        if (item.Fail) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(19)').css("background-color", "#EA5C6B !important");
        }
    });
}

function updateEquipmentShipmentTotals() {
    $("#resetEquipmentShipmentItemGrid").click(function (e) {
        e.preventDefault();
        resetGridView('equipmentShipmentItemGrid', 'initialEquipmentShipmentItemGrid');
    });

    var equipmentShipmentItemGrid = $("#equipmentShipmentItemGrid").data("kendoGrid");
    var totalEquipmentShipmentItems = equipmentShipmentItemGrid.dataSource.total();
    viewModel.set("totalEquipmentShipmentItems", totalEquipmentShipmentItems);

    var equipmentData = equipmentShipmentItemGrid.dataSource.data();

    var hasPackingList = false;
    var equipmentItemsAtFromLocation = true;
    var dangerousGoods = false;
    var currentLocation = false;

    $.each(equipmentData, function (i, item) {
        if (item.IsExpired) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(11)').css("color", "#E31E33");
        }

        if (item.EquipmentPackingListItemId) {
            hasPackingList = true;
        }

        if(!item.EquipmentItemAtFromCompanyLocation){
            equipmentItemsAtFromLocation = false;
        }

        if(item.HasDangerousGoods){
            dangerousGoods = true;
        }

        if(item.CurrentLocation == "In Transit"){
            currentLocation = true;
        }
    });

    viewModel.set("hasPackingList", hasPackingList);
    viewModel.set("equipmentItemsAtFromLocation", equipmentItemsAtFromLocation);
    viewModel.set("currentLocation", currentLocation);
    viewModel.set("dangerousGoods", dangerousGoods);

    if (editEquipmentShipmentModel.modelEquipmentShipmentStatus === editEquipmentShipmentModel.equipmentShipmentStatusConstantReceived && $('#undoShipmentReceivedBtn')) {
        viewModel.checkCanUndoShipmentReceived();
    }
}


function updateEquipmentShipmentNonAssetItemTotals() {
    var equipmentShipmentNonAssetItemGrid = $("#equipmentShipmentNonAssetItemGrid").data("kendoGrid");
    var totalEquipmentShipmentNonAssetItems = equipmentShipmentNonAssetItemGrid.dataSource.total();
    viewModel.set("totalEquipmentShipmentNonAssetItems", totalEquipmentShipmentNonAssetItems);

}
function refreshEquipmentCategories() {
    var equipmentCategory = $("#equipmentCategoryTreeView").data("kendoTreeView");
    equipmentCategory.dataSource.read();

    var path = viewModel.get("selectedEquipmentCategoryPath");
    equipmentCategory.expandPath(path);
}

function equipmentCategorySelected(e) {
    var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
    var node = equipmentCategoryTreeView.select();
    var selectedEquipmentCategory = equipmentCategoryTreeView.dataItem(node);

    if (selectedEquipmentCategory) {
        $.removeCookie('equipmentCategory');
        $.cookie('equipmentCategory', selectedEquipmentCategory.EquipmentCategoryId, { expires: 7, path:'/' });
        viewModel.set("selectedEquipmentCategory", selectedEquipmentCategory);

        var grid = $("#equipmentItemGrid").data("kendoGrid");
        grid.dataSource.options.endless = null;
        grid._endlessPageSize = grid.dataSource.options.pageSize;
        grid.dataSource.pageSize(grid.dataSource.options.pageSize);

    }
}

function equipmentCategoryLoaded() {
    $(".k-treeview-item").click(function (e) {
        var equipmentCategoryTree = $("#equipmentCategoryTreeView").data("kendoTreeView");
        var equipmentCategorySelected = equipmentCategoryTree.select();

        if (equipmentCategorySelected && $(e.currentTarget).attr("date-uid") == $(equipmentCategorySelected.context).attr("data-uid")) {
            equipmentCategoryTree.select($());
        }
    });
}

function equipmentCategoryDropped(e) {
    var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
    var equipmentCategory = equipmentCategoryTreeView.dataItem(e.sourceNode);

    var parentEquipmentCategory = "";

    if (e.dropPosition == "over") {
        parentEquipmentCategory = equipmentCategoryTreeView.dataItem(e.destinationNode);
    } else {
        parentEquipmentCategory = equipmentCategoryTreeView.dataItem(equipmentCategoryTreeView.parent(e.destinationNode));
    }

    $.ajax({
        type: "POST",
        url: "/Admin/UpdateEquipmentCategoryParent",
        data: {
            equipmentCategoryId: equipmentCategory.EquipmentCategoryId,
            parentEquipmentCategoryId: parentEquipmentCategory ? parentEquipmentCategory.EquipmentCategoryId : ""
        },
        success: function (data) {
            viewModel.set("selectedEquipmentCategory", equipmentCategory);
            console.log(JSON.stringify(data));
        },
        dataType: "json"
    });
}

function refreshEquipmentShipmentItems() {
    var equipmentShipmentItemGrid = $("#equipmentShipmentItemGrid").data("kendoGrid");
    equipmentShipmentItemGrid.dataSource.read();
}

function refreshEquipmentShipmentNonAssetItems() {
    var equipmentShipmentNonAssetItemGrid = $("#equipmentShipmentNonAssetItemGrid").data("kendoGrid");
    equipmentShipmentNonAssetItemGrid.dataSource.read();
}


function refreshEquipmentItems() {
    var equipmentItemGrid = $("#equipmentItemGrid").data("kendoGrid");

    if(equipmentItemGrid){
        equipmentItemGrid.dataSource.read();
    }
}

function filterFromCompanyLocations() {
    return {
        fromCompanyId: $("#FromCompanyId").data("kendoDropDownList").value()
    };
}
function filterFromCompanyAssets() {
    return {
        fromCompanyId: $("#FromCompanyId").data("kendoDropDownList").value()
    };
}

function filterToCompanyLocations() {
    return {
        toCompanyId: $("#ToCompanyId").data("kendoDropDownList").value()
    };
}
function filterToCompanyAssets() {
    return {
        toCompanyId: $("#ToCompanyId").data("kendoDropDownList").value()
    };
}

function filterCompanies() {
    return {
        projectId: $("#ProjectId").data("kendoDropDownList").value()
    };
}

function addEquipmentPackingList(equipmentPackingListId) {
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: "/Logistics/AddEquipmentPackingListToEquipmentShipment",
        data: {
            equipmentShipmentId: editEquipmentShipmentModel.modelEquipmentShipmentId,
            equipmentPackingListId: equipmentPackingListId
        },
        success: function () {
            $("#equipmenPackingListWindow").data("kendoWindow").close();

            refreshEquipmentShipmentItems();
            refreshEquipmentItems();
        }
    });
}

function removeEquipmentPackingList(equipmentPackingListId) {
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: "/Logistics/RemoveEquipmentPackingListFromEquipmentShipment",
        data: {
            equipmentShipmentId: editEquipmentShipmentModel.modelEquipmentShipmentId,
            equipmentPackingListId: equipmentPackingListId
        },
        success: function () {
            $("#equipmenDeSelectPackingListWindow").data("kendoWindow").close();

            refreshEquipmentShipmentItems();
            refreshEquipmentItems();
        }
    });
}
function packingListWindowOpened() {
    var PendingEquipmentPackingListGrid = $("#PendingEquipmentPackingListGrid").data("kendoGrid");
    PendingEquipmentPackingListGrid.dataSource.read();
}

function deSelectPackingListWindowOpened() {
    var deSelectShipmentEquipmentPackingListGrid = $("#deSelectShipmentEquipmentPackingListGrid").data("kendoGrid");
    deSelectShipmentEquipmentPackingListGrid.dataSource.read();
}

function isReceivedItems(gridName) {
    var grid = $(`#${gridName}`).data("kendoGrid");
    var data = grid.dataSource.data();
    
    var initialDataLength = data.length;
    var receivedItemsLength = data.filter(function (item) {
        return item.IsReceived === true;
    }).length;

    return initialDataLength !== receivedItemsLength;
}

function loadShipmentEquipmentItemGrid() {
    var grid = $("#equipmentItemGrid").data("kendoGrid");
    if (grid) {
        var options = localStorage["shipmentEquipmentItemGrid"];
        var toolBar = $("#equipmentItemGrid .k-grid-toolbar").html();
        viewModel.set("initialShipmentEquipmentItemGrid", kendo.stringify(grid.getOptions()));
        if (options) {
            grid.setOptions(JSON.parse(options));
            $("#equipmentItemGrid .k-grid-toolbar").html(toolBar);
            $("#equipmentItemGrid .k-grid-toolbar").addClass("k-grid-top");
        }
    }
}
function loadEquipmentShipmentItemGrid() {
    var grid = $("#equipmentShipmentItemGrid").data("kendoGrid");
    if (grid) {
        var options = localStorage["equipmentShipmentItemGrid"];
        var toolBar = $("#equipmentShipmentItemGrid .k-grid-toolbar").html();
        viewModel.set("initialEquipmentShipmentItemGrid", kendo.stringify(grid.getOptions()));
        if (options) {
            grid.setOptions(JSON.parse(options));
            $("#equipmentShipmentItemGrid .k-grid-toolbar").html(toolBar);
            $("#equipmentShipmentItemGrid .k-grid-toolbar").addClass("k-grid-top");
        }
    }
}

function saveEquipmentGrid(e) {
    setTimeout(function () {
        var grid = $("#equipmentItemGrid").data("kendoGrid");
        localStorage["shipmentEquipmentItemGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}
function saveEquipmentShipmentItemGrid(e) {
    setTimeout(function () {
        var grid = $("#equipmentShipmentItemGrid").data("kendoGrid");
        localStorage["equipmentShipmentItemGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}

$(document).ready(function () {
    viewModel.set("createdDate", editEquipmentShipmentModel.modelCreatedDate);
    viewModel.set("equipmentShipmentId", editEquipmentShipmentModel.modelEquipmentShipmentId);
    viewModel.set("isProjectRelated", editEquipmentShipmentModel.modelIsProjectRelated);
    viewModel.set("isReceived", editEquipmentShipmentModel.modelEquipmentShipmentItemsHalfOrMoreReceived);
    viewModel.set("toCompanyId", editEquipmentShipmentModel.modelToCompanyId);
    viewModel.set("toCompanyLocationId", editEquipmentShipmentModel.modelToCompanyLocationId);
    viewModel.set("isProformaInvoice", editEquipmentShipmentModel.modelProforma);
    loadShipmentEquipmentItemGrid();
    loadEquipmentShipmentItemGrid();
})

var viewModel = kendo.observable({
    createdDate: "",
    equipmentShipmentId: "",
    selectedEquipmentCategory: "",
    isProjectRelated: "",
    isReceived: "",
    toCompanyId: "",
    toCompanyLocationId: "",
    equipmentShipmentItem: "",
    selectedEquipmentCategoryPath: [],
    selectedEquipmentItemId: 0,
    totalEquipmentItems: 0,
    totalEquipmentShipmentItems: 0,
    isEditTabStripVisible: false,
    isUndoShipmentReceivedVisible: false,
    totalAttachments: 0,

    totalEquipmentShipmentItemsText: function () {
        return `<span class="k-link"><i class="fa fa-tags mr-2"></i> Equipment Items (<span data-bind="text:totalEquipmentShipmentItems"></span>)</span>`;
    },
    totalEquipmentShipmentNonAssetItems: 0,
    totalEquipmentShipmentInvoices: 0,
    totalPackageInformation: 0,
    totalEquipmentShipmentInvoicesText: function () {
        return `<span class="k-link"><i class="fa fa-file-text mr-2"></i> Paperwork (<span data-bind="text:totalEquipmentShipmentInvoices"></span>)</span>`;
    },
    anyEquipmentShipmentItems: function () {
        if (this.get("totalEquipmentShipmentItems") !== 0) {
            return isReceivedItems('equipmentShipmentItemGrid');
        }
        return false;
    },
    anyEquipmentShipmentNonAssetItems: function () {
        if (this.get("totalEquipmentShipmentNonAssetItems") !== 0) {
            return isReceivedItems('equipmentShipmentNonAssetItemGrid');
        }
        return false;
    },
    totalPackageInformationText: function () {
        return `<span class="k-link"><i class="fa fa-file-text mr-2"></i> Package Information (<span data-bind="text:totalPackageInformation"></span>)</span>`;
    },
    attachmentsText: function () {
        return `<span class="k-link"><i class="fa fa-file-text mr-2"></i> Attachments (<span data-bind="text:totalAttachments"></span>)</span>`;
    },
    equipmentItemsAtFromLocation: true,
    currentLocation: false,
    dangerousGoods: false,
    logisticsDetailsText: function () {
        return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Details</span>`;
    },
    canShip: function () {
        return (this.get("totalEquipmentShipmentItems") && this.get("equipmentItemsAtFromLocation")) || this.get("totalEquipmentShipmentNonAssetItems");
    },

    isTransit: function(){
        return this.get("currentLocation");
    },
    hasPackingList: false,
    isProformaInvoice: "",

    isProformaInvoiceVisible: function () {
        var isProformaInvoice = this.get("isProformaInvoice");

        return isProformaInvoice == "True";
    },
   

    deleteShipment:function(){
        var confirmDelete = confirm("Are you sure you wish to delete this shipment");

        if (confirmDelete) {
            $.ajax({
                type: 'DELETE',
                dataType: 'json',
                traditional: true,
                url: "/Logistics/DeleteEquipmentShipment",
                data: {
                    id: editEquipmentShipmentModel.modelEquipmentShipmentId
                },
                success: function (result) {
                    window.location.href = `/Logistics`;
                }
            });
        }
    },

    showPackinglistWindow: function () {
        $("#equipmenPackingListWindow").data("kendoWindow").center().open();
    },
    showDePackinglistWindow: function () {
        $("#equipmenDeSelectPackingListWindow").data("kendoWindow").center().open();
    },
    expandAll: function () {
        var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
        equipmentCategoryTreeView.expand(".k-treeview-item");
    },
    collapseAll: function () {
        var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
        equipmentCategoryTreeView.collapse(".k-treeview-item");
    },

    shipmentClick: function () {
        var confirmation = confirm ("Are you sure you wish to mark as shipped");
        if (editEquipmentShipmentModel.isAisus) {
            //this.receiveAllItemsClick();

            this.shipmentReceivedClick();

        }
        else if (confirmation) {
            $('#markAsShippedBtn').prop('disabled', true);
            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url: `/Logistics/UpdateStatus?id=${editEquipmentShipmentModel.modelEquipmentShipmentId}&status=${editEquipmentShipmentModel.equipmentShipmentStatusConstantInTransit}`,
                data: {
                    id: editEquipmentShipmentModel.modelEquipmentShipmentId,
                },
                success: function () {
                    window.location.reload();
                },
            });
        }
    },
    backloadShipmentClick: function () {
        var confirmation = confirm("Are you sure you wish to mark as received");

        $.ajax({
            type: 'POST',
            dataType: 'json',
            url: `/Logistics/UpdateStatus?id=${editEquipmentShipmentModel.modelEquipmentShipmentId}&status=${editEquipmentShipmentModel.equipmentShipmentStatusConstantReceived}&isReceiving=`,
            data: {
                id: editEquipmentShipmentModel.modelEquipmentShipmentId,
            },
            success: function (result) {
                if (result.FlagMrCreated) {
                    $("<div id='dialog'></div>").kendoDialog({
                        closable: false,
                        title: "Items Marked as Received",
                        close: () => {
                            var dialog = $("#dialog").data("kendoDialog");
                            dialog.destroy();
                        },
                        width: 500,
                        buttonLayout: "normal",
                        content: "MR has been created",
                        actions: [
                            {
                                text: "OK",
                                action: function () {
                                    window.location.href = `/Logistics/EditEquipmentShipment?id=${editEquipmentShipmentModel.modelEquipmentShipmentId}`;
                                },
                                cssClass: 'btn-primary'
                            }]
                    }).data("kendoDialog").open().center()
                }
                window.location.reload();
            }
        });
    },

    markShipmentReceivedClick: function () {
        $('#receiveShipmentBtn').prop('disabled', true);
        window.location.href = `/Logistics/EditEquipmentShipment?id=${editEquipmentShipmentModel.modelEquipmentShipmentId}&isReceiving=true`;
    },

    createShipmentBackloadClick: function (e) {
        kendo.ui.progress($("#editTabStrip"), true);
        $("<div id='dialog'></div>").kendoDialog({
            closable: false,
            title: "Create Backload Shipment",
            close: function () {
                $("#dialog").data("kendoDialog").destroy();
            },
            width: 500,
            buttonLayout: "normal",
            content: "You tried to create a backload shipment. Are you sure this is correct?",
            actions: [
                {
                    text: "Yes", cssClass: 'btn-primary',
                    action: function () {
                        $.ajax({
                            type: 'POST',
                            dataType: 'json',
                            url: `/Logistics/CreateBackloadShipment`,
                            data: {
                                id: editEquipmentShipmentModel.modelEquipmentShipmentId,
                            },
                            success: function (result) {
                                window.location.href = `/Logistics/EditEquipmentShipment/${result.equipmentShipmentId}`;
                            },
                            error: function (e) {
                                jqXHRErrors(e);
                                kendo.ui.progress($("#editTabStrip"), false);
                            }
                        });
                    }
                },
                {
                    text: "No", cssClass: 'btn-primary',
                    action: () => kendo.ui.progress($("#editTabStrip"), false)
                },
            ]
        }).data("kendoDialog").open().center()
    },
    checkCanUndoShipmentReceived: function () {
        var equipmentData = $("#equipmentShipmentItemGrid").data("kendoGrid").dataSource.data();
        var toCompanyLocationId = editEquipmentShipmentModel.modelToCompanyLocationId;
        var canUndo = equipmentData.every(function (item) {
            return (item.IsReceived == true && item.IsReserved == false && item.IsTransit == false && item.EquipmentItemCurrentCompanyLocationId == toCompanyLocationId) || item.IsReceived == false;
        });

        viewModel.set("isUndoShipmentReceivedVisible", canUndo);
    },
    undoShipmentReceivedClick: function () {
        var confirmation = confirm("Are you sure you wish to cancel receiving of the shipment?");
        if (confirmation) {
            $('#undoShipmentReceivedBtn').prop('disabled', true);

            $.ajax({
                type: 'POST',
                dataType: 'json',
                url: `/Logistics/CheckMaintenancesCreatedByShipmentId`,
                data: { id: editEquipmentShipmentModel.modelEquipmentShipmentId },
                success: function (result) {
                    if (result) {
                        $("<div id='dialog'></div>").kendoDialog({
                            title: "MRs were created during the last receiving",
                            close: () => { $("#dialog").data("kendoDialog").destroy();  $('#undoShipmentReceivedBtn').prop('disabled', false); },
                            width: 500,
                            buttonLayout: "normal",
                            content: "There is likely to be a risk of re-creating MRs at the next receiving. <b>Are you sure you wish to continue?</b>",
                            actions: [{ text: "Yes", action: function () { $("#dialog").data("kendoDialog").destroy(); viewModel.undoShipmentReceived(); }, cssClass: 'btn-primary' },
                                { text: "No", action: function () { $("#dialog").data("kendoDialog").destroy(); $('#undoShipmentReceivedBtn').prop('disabled', false); }, cssClass: 'btn-primary' }
                            ]
                        }).data("kendoDialog").open().center()
                    }
                    else {
                        viewModel.undoShipmentReceived();
                    }
                }
            });
        }
    },
    undoShipmentReceived: function () {
        $.ajax({
            type: 'POST',
            dataType: 'json',
            url: `/Logistics/UndoShipmentReceived`,
            data: { id: editEquipmentShipmentModel.modelEquipmentShipmentId },
            success: function (result) {
                window.location.reload();
            }
        });
    },

    shipmentReceivedClick: function () {
        var isReceived = viewModel.get("isReceived");
        if (!isReceived && !editEquipmentShipmentModel.isAisus) {
            $("<div id='dialog'></div>").kendoDialog({
                closable: false,
                title: "Mark Items as Received",
                close:()=>{
                    var dialog = $("#dialog").data("kendoDialog");
                    dialog.destroy();
                },
                width: 500,
                buttonLayout: "normal",
                content: "You have tried to receive a shipment where more than 50% of the items were marked as not received. Are you sure this is correct?",
                actions: [
                    {
                        text: "Yes",
                        action: function () {
                            $('#confirmShipmentReceivedBtn').prop('disabled', true);
                            $.ajax({
                                type: 'POST',
                                dataType: 'json',
                                url:`/Logistics/UpdateStatus?id=${editEquipmentShipmentModel.modelEquipmentShipmentId}&status=${editEquipmentShipmentModel.equipmentShipmentStatusConstantReceived}&isReceiving=`,
                                data: {
                                    id: editEquipmentShipmentModel.modelEquipmentShipmentId,
                                },
                                success: function () {
                                    window.location.href = `/Logistics/EditEquipmentShipment?id=${editEquipmentShipmentModel.modelEquipmentShipmentId}`;
                                }
                            });
                        },
                        cssClass: 'btn-primary'
                    },
                    {
                        text: "No",
                        action: function(){
                        },
                        cssClass: 'btn-primary'
                    },
                ]
            }).data("kendoDialog").open().center()
        } else {
            $('#confirmShipmentReceivedBtn').prop('disabled', true);
            $.ajax({
                type: 'POST',
                dataType: 'json',
                url: `/Logistics/UpdateStatus?id=${editEquipmentShipmentModel.modelEquipmentShipmentId}&status=${editEquipmentShipmentModel.equipmentShipmentStatusConstantReceived}&isReceiving=`,
                data: {
                    id: editEquipmentShipmentModel.modelEquipmentShipmentId,
                },
                success: function (result) {
                    if (result.FlagMrCreated) {
                        $("<div id='dialog'></div>").kendoDialog({
                            closable: false,
                            title: "Items Marked as Received",
                            close: () => {
                                var dialog = $("#dialog").data("kendoDialog");
                                dialog.destroy();
                            },
                            width: 500,
                            buttonLayout: "normal",
                            content: "MR has been created",
                            actions: [
                                {
                                    text: "OK",
                                    action: function () {
                                        window.location.href = `/Logistics/EditEquipmentShipment?id=${editEquipmentShipmentModel.modelEquipmentShipmentId}`;
                                    },
                                    cssClass: 'btn-primary'
                                }]
                        }).data("kendoDialog").open().center()
                    }
                    window.location.reload();

                }
            });
        }
    },

    receiveAllItemsClick:function() {
        var confirmation = confirm ("Are you sure you wish to mark all items as received?");

        if(confirmation) {
            $.ajax({
                type: 'POST',
                dataType: 'json',
                url:`/Logistics/ReceiveAllShipmentsItems?id=${editEquipmentShipmentModel.modelEquipmentShipmentId}`,
                data: {
                    id: editEquipmentShipmentModel.modelEquipmentShipmentId,
                },
                success: function () {
                    if (!editEquipmentShipmentModel.isAisus) {
                    window.location.reload();
                    }
                },
            });
        }
    },

    receiveAllNonAssetItemsClick:function() {
        var confirmation = confirm ("Are you sure you wish to mark all non asset items as received?");

        if(confirmation) {
            $.ajax({
                type: 'POST',
                dataType: 'json',
                url:`/Logistics/ReceiveAllNonAssetShipmentsItems?id=${editEquipmentShipmentModel.modelEquipmentShipmentId}`,
                data: {
                    id: editEquipmentShipmentModel.modelEquipmentShipmentId,
                },
                success: function () {
                    window.location.reload();
                },
            });
        }
    },

});

$("#equipmentItemSearch").keyup(function () {
    var query = $("#equipmentItemSearch").val();

    var equipmentShipmentItemGrid = $("#equipmentShipmentItemGrid").data("kendoGrid");

    if (query != null && query.length > 0) {
        var filter = {
            logic: "or",
            filters: [
              { field: "EquipmentItem.CurrentClientLocationName", operator: "contains", value: query },
              { field: "EquipmentItem.Points", operator: "contains", value: query },
              { field: "EquipmentItem.EquipmentInfo", operator: "contains", value: query },
              { field: "RequestTypeDescription", operator: "contains", value: query },
              { field: "StatusDescription", operator: "contains", value: query },
            ]
        };

        equipmentShipmentItemGrid.dataSource.filter(filter);
    } else {
        equipmentShipmentItemGrid.dataSource.filter([]);
    }
});

function populateItemGridClick() {
    $('#addShipmentBtn').prop('disabled', true);
    var equipmentItemGrid = $("#equipmentItemGrid").data("kendoGrid");
    var equipmentItemIds = [];
    equipmentItemGrid.select().each(function () {
        var equipmentItem = equipmentItemGrid.dataItem($(this));

        if (equipmentItem) {
            equipmentItemIds.push(equipmentItem.EquipmentItemId);
        }
    });

    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: "/Logistics/UpdateShipmentEquipmentItems",
        data: {
            equipmentShipmentId: editEquipmentShipmentModel.modelEquipmentShipmentId,
            equipmentItemIds: equipmentItemIds
        },
        success: function () {
            $('#addShipmentBtn').prop('disabled', false);
            refreshEquipmentShipmentItems();
            refreshEquipmentItems();
        }
    });
};

function receiveEquipmentShipmentItem(equipmentShipmentItemId){

    $.ajax({
        type:"POST",
        url:"/Logistics/ReceiveEquipmentShipmentItem",
        dataType:"json",
        data:{
            equipmentShipmentItemId:equipmentShipmentItemId
        },
        success:function(){
            //refreshEquipmentShipmentItems();
            window.location.reload();
        }
    });
}

function receiveEquipmentShipmentNonAssetItem(equipmentShipmentNonAssetItemId){
    $.ajax({
        type:"POST",
        url:"/Logistics/ReceiveEquipmentShipmentNonAssetItem",
        dataType:"json",
        data:{
            equipmentShipmentNonAssetItemId:equipmentShipmentNonAssetItemId
        },
        success:function(){
            //refreshEquipmentShipmentNonAssetItems();
            window.location.reload();
        }
    });
}

function activateTabDependingOnUrlQuery () {
    if(!!window.location.search.split("isReceiving=")[1]) {
          $("#editTabStrip").data("kendoTabStrip").select("#equipmentItemsTab")
    }
}

function onCheckBoxChange(e){
    $("#projectIdContainer").kendoValidator().data("kendoValidator").validate();
}

function onEquipmentShipmentNonAssetItemGridRequestEnd(e) {
    if (e.type === "create" || e.type === "update") {
        $("#equipmentShipmentNonAssetItemGrid").data("kendoGrid").dataSource.read();
    }
}

function submitShipmentForm(e) {
    let isValid = $('#shipmentForm').kendoValidator({
        messages: {
            sendDateValidation: function (input) {
                return "Shipped Date cannot be set before the Creation date";
            },
            dateValidation: function (input) {
                return "Please enter a valid date";
            },
            customRequired: function (input) {
                return "Project is required";
            }
        },
        rules: {
            dateValidation: function (input) {
                let date = null;
                if (input.is("[name=SentDate]")) {
                    let value = input.val();
                    date = kendo.parseDate(value);
                    if (!date && value !== "") {
                        return false;
                    }
                }
                return true;
            },
            sendDateValidation: function (input) {
                if (input.is("[name=SentDate]")) {
                    let value = input.val();
                    date = kendo.parseDate(value);
                    if (date) {
                        let comparisonOfDates = compareDates($("#CreatedDate")[0].value, input.val())
                        if (comparisonOfDates === "d2IsSmaller") {
                            return false
                        };
                    }
                }
                return true;
            },
            customRequired: function (input) {
                if (input.is("[name=ProjectId]")) {
                    let value = input.val();
                    if (!value) {
                        return false
                    }
                }
                return true;
            }
        }
    }).data('kendoValidator').validate();

    if ($('#ToCompanyId').attr('disabled')) {
        $("#ToCompanyId").removeAttr("disabled");
        $("#ToCompanyLocationId").removeAttr("disabled");
    }

    e.preventDefault();
    if (isValid) {
         $('#shipmentForm').submit();
    } else {
        scrolToFirstError();
    }
}

$('#shipmentForm').submit(function (e) {
    e.preventDefault();
    var formData = $(this).serializeArray();

    $.ajax({
        type: 'POST',
        url: '/Logistics/EditEquipmentShipment',
        data: formData,
        success: function (result) {
            $("<div id='dialog'></div>").kendoDialog({
                closable: false,
                title: result.shipmentNumber,
                width: 500,
                buttonLayout: "normal",
                content: `\'${result.shipmentNumber}\' shipment details have been successfully updated`,
                actions: [
                    {
                        text: "OK",
                        action: function () {
                            window.location.href = '/Logistics/EditEquipmentShipment?id=' + result.shipmentId;
                        },
                        cssClass: 'btn-primary'
                    }]
            }).data("kendoDialog").open().center()
        },
        error: function (xhr, textStatus, errorThrown) {
            showError(xhr.responseJSON.message);
        }
    });
});
function downloadFile(fileName) {
    var element = $("#editTabStrip-3");
    kendo.ui.progress(element, true);
    setTimeout(function () { kendo.ui.progress(element, false); }, 6000);
    $("<div id='dialog'></div>").kendoDialog({
        closable: false,
        title: `Please wait...`,
        width: 500,
        buttonLayout: "normal",
        content: `Please wait... paperwork is currently being generated!`,
        actions: [{ text: "OK", cssClass: 'btn-primary' }]
    }).data("kendoDialog").open().center()
}

kendo.bind(document.body.children, viewModel);
