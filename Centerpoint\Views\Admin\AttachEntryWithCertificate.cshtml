<div class="d-flex flex-column">
     <div>
        <div class="form-group">
            <label>Certificate Category</label>
            <br />
            @(Html.Kendo().DropDownList()
                            .Name("certificateCategory")
                            .Events(e => e.Change("changeDropdownValue"))
                            .OptionLabel("Select Category")
                            .DataTextField("Name")
                            .DataValueField("CertificateCategoryId")
                            .DataSource(dataSource => dataSource.Read("GetCertificateCategories", "Lookup"))
                            .Filter("contains")
                            .HtmlAttributes(new { id="certificateCategory", @style = "width:100%", @data_value_primitive = "true" })
             )
        </div>
    </div>
    <div>
            <label>Upload Certificate</label>
            <br />
                @(Html.Kendo().Upload()
                    .Name("personnelCertificateAttachmentDocuments")
                    .Multiple(false)
                    .Enable(false)
                    .Messages(m => m.Select("Add New Entry With Certificate"))
                    .Events(e => e.Success("onPersonnelCertificateAttached").Complete("onPersonnelCertificateDocumentComplete").Upload("onPersonnelCertificateDocumentUpload"))
                    .Async(async => async.Save("AttachPersonnelCertificateDocuments", "Admin", new { @userId = Model.UserId })))
    </div>
</div>    
