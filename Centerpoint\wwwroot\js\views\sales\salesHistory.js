
        $(document).ready(function () {
            loadClosedSalesGrid();
            loadClosedLeadsGrid();
            loadClosedOpportunitesGrid();
            loadClosedActionsGrid();
            loadClosedEventsGrid();
            refreshClosedSalesGrid();
            var closedSalesGrid = $('#closedSalesGrid').data("kendoGrid");
            closedSalesGrid.bind('dataBound', function (e) {
                this.element.find('.k-i-excel').remove();
            });
            var closedLeadsGrid = $('#closedLeadsGrid').data("kendoGrid");
            closedLeadsGrid.bind('dataBound', function (e) {
                this.element.find('.k-i-excel').remove();
            });
            var closedOpportunitiesGrid = $('#closedOpportunitiesGrid').data("kendoGrid");
            closedOpportunitiesGrid.bind('dataBound', function (e) {
                this.element.find('.k-i-excel').remove();
            });
            var closedActionsGrid = $('#closedActionsGrid').data("kendoGrid");
            closedActionsGrid.bind('dataBound', function (e) {
                this.element.find('.k-i-excel').remove();
            });
            var closedEventsGrid = $('#closedEventsGrid').data("kendoGrid");
            closedEventsGrid.bind('dataBound', function (e) {
                this.element.find('.k-i-excel').remove();
            });
        });

        function showTopic() {
            var grid = $("#closedEventsGrid").data("kendoGrid");
            var model = grid.dataItem($(event.target).closest("tr"));
            var topic = model.Topic;
            swal({ html: true, title: '<i>Topic Details</i>', text: '<b> ' + topic + ' </b>' });
        }

        function showOppTopic() {
            var grid = $("#opportunityEventsGrid").data("kendoGrid");
            var model = grid.dataItem($(event.target).closest("tr"));
            var topic = model.Topic;
            swal({ html: true, title: '<i>Topic Details</i>', text: '<b> ' + topic + ' </b>' });
        }

        function closedLeadOpportunityData() {
            return {
                from: kendo.toString(viewModel.get("fromDate"), 'dd/MM/yyyy'),
                to: kendo.toString(viewModel.get("toDate"), 'dd/MM/yyyy')
            }
            getFromToSummary();
        }

        function refreshClosedSalesGrid() {
            var closedSalesGrid = $("#closedSalesGrid").data("kendoGrid");
            var closedLeadsGrid = $("#closedLeadsGrid").data("kendoGrid");
            var closedOpportunitiesGrid = $("#closedOpportunitiesGrid").data("kendoGrid");

            closedSalesGrid.dataSource.read();
            closedLeadsGrid.dataSource.read();
            closedOpportunitiesGrid.dataSource.read();

            getFromToSummary();
        }

        function getFromToSummary() {
            $.ajax({
                url: "/Sales/GetFromToSummary",
                data: {
                    from: kendo.toString(viewModel.get("fromDate"), 'dd/MM/yyyy'),
                    to: kendo.toString(viewModel.get("toDate"), 'dd/MM/yyyy')
                },
                success: function (result) {
                    if (result) {
                        viewModel.set("totalClosed", result.TotalClosed);
                        viewModel.set("totalClosedLeads", result.TotalClosedLeads);
                        viewModel.set("totalClosedOpportunities", result.TotalOpportunitiesClosed);
                        //viewModel.set("totalClosedEvents", result.TotalClosedEvents);
                        //viewModel.set("totalClosedActions", result.TotalClosedActions);
                    }
                },
                dataType: "json"
            });
        }

        // $(function () {

        //     $("#reset").click(function (e) {
        //         e.preventDefault();
        //         localStorage["closedSalesGrid"] = "";
        //         window.location.reload();
        //     });
        //     $("#resetLead").click(function (e) {
        //         e.preventDefault();
        //         localStorage["closedLeadsGrid"] = "";
        //         window.location.href = '/Sales/SalesHistory?tab=closedLeads';
        //     });
        //     $("#resetOpportunity").click(function (e) {
        //         e.preventDefault();
        //         localStorage["closedOpportunitiesGrid"] = "";
        //         window.location.href = '/Sales/SalesHistory?tab=closedOpportunities';
        //     });
        //     $("#resetAction").click(function (e) {
        //         e.preventDefault();
        //         localStorage["closedActionsGrid"] = "";
        //         window.location.href = '/Sales/SalesHistory?tab=closedActions';
        //     });
        //     $("#resetEvent").click(function (e) {
        //         e.preventDefault();
        //         localStorage["closedEventsGrid"] = "";
        //         window.location.href = '/Sales/SalesHistory?tab=closedEvents';
        //     });

        // });

        function saveClosedActionGrid(e) {
            setTimeout(function () {
                var grid = $("#closedActionsGrid").data("kendoGrid");
                localStorage["closedActionsGrid"] = kendo.stringify(grid.getOptions());
            }, 10);
        }
        function saveClosedEventGrid(e) {
            setTimeout(function () {
                var grid = $("#closedEventsGrid").data("kendoGrid");
                localStorage["closedEventsGrid"] = kendo.stringify(grid.getOptions());
            }, 10);
        }

        function saveClosedSalesGrid(e) {
            setTimeout(function () {
                var grid = $("#closedSalesGrid").data("kendoGrid");
                localStorage["closedSalesGrid"] = kendo.stringify(grid.getOptions());
            }, 10);
        }
        function saveClosedLeadsGrid(e) {
            setTimeout(function () {
                var grid = $("#closedLeadsGrid").data("kendoGrid");
                localStorage["closedLeadsGrid"] = kendo.stringify(grid.getOptions());
            }, 10);
        }
        function saveClosedOpportunityGrid(e) {
            setTimeout(function () {
                var grid = $("#closedOpportunitiesGrid").data("kendoGrid");
                localStorage["closedOpportunitiesGrid"] = kendo.stringify(grid.getOptions());
            }, 10);
        }

        function loadClosedSalesGrid() {
            var grid = $("#closedSalesGrid").data("kendoGrid");
            var toolBar = $("#closedSalesGrid .k-grid-toolbar").html();
            var options = localStorage["closedSalesGrid"];
            viewModel.set("initialClosedSalesGridOptions", kendo.stringify(grid.getOptions()));
            if (options) {
                grid.setOptions(JSON.parse(options));
                $("#closedSalesGrid .k-grid-toolbar").html(toolBar);
                $("#closedSalesGrid .k-grid-toolbar").addClass("k-grid-top");
            }
        }
        function loadClosedLeadsGrid() {
            var grid = $("#closedLeadsGrid").data("kendoGrid");
            var toolBar = $("#closedLeadsGrid .k-grid-toolbar").html();
            var options = localStorage["closedLeadsGrid"];
            viewModel.set("initialClosedLeadsGridOptions", kendo.stringify(grid.getOptions()));
            if (options) {
                grid.setOptions(JSON.parse(options));
                $("#closedLeadsGrid .k-grid-toolbar").html(toolBar);
                $("#closedLeadsGrid .k-grid-toolbar").addClass("k-grid-top");
            }
        }
        function loadClosedOpportunitesGrid() {
            var grid = $("#closedOpportunitiesGrid").data("kendoGrid");
            var toolBar = $("#closedOpportunitiesGrid .k-grid-toolbar").html();
            var options = localStorage["closedOpportunitiesGrid"];
            viewModel.set("initialClosedOpportunitiesGridOptions", kendo.stringify(grid.getOptions()));
            if (options) {
                grid.setOptions(JSON.parse(options));
                $("#closedOpportunitiesGrid .k-grid-toolbar").html(toolBar);
                $("#closedOpportunitiesGrid .k-grid-toolbar").addClass("k-grid-top");
            }
        }

        function loadClosedActionsGrid() {
            var grid = $("#closedActionsGrid").data("kendoGrid");
            var toolBar = $("#closedActionsGrid .k-grid-toolbar").html();
            var options = localStorage["closedActionsGrid"];
            viewModel.set("initialClosedActionsGridOptions", kendo.stringify(grid.getOptions()));
            if (options) {
                grid.setOptions(JSON.parse(options));
                $("#closedActionsGrid .k-grid-toolbar").html(toolBar);
                $("#closedActionsGrid .k-grid-toolbar").addClass("k-grid-top");
            }
        }
        function loadClosedEventsGrid() {
            var grid = $("#closedEventsGrid").data("kendoGrid");
            var toolBar = $("#closedEventsGrid .k-grid-toolbar").html();
            var options = localStorage["closedEventsGrid"];
            viewModel.set("initialClosedEventsGridOptions", kendo.stringify(grid.getOptions()));
            if (options) {
                grid.setOptions(JSON.parse(options));
                $("#closedEventsGrid .k-grid-toolbar").html(toolBar);
                $("#closedEventsGrid .k-grid-toolbar").addClass("k-grid-top");
            }
        }

        function updateClosedSalesGrid() {
            $("#resetClosedSalesGrid").click(function (e) {
                e.preventDefault();
                resetGridView('closedSalesGrid', 'initialClosedSalesGridOptions')
            });

            var closedSalesGrid = $("#closedSalesGrid").data("kendoGrid");
            var totalClosedSales = closedSalesGrid.dataSource.total();
            viewModel.set("totalClosedSales", totalClosedSales);
        }

        function updateClosedLeadsGrid() {
            $("#resetClosedLeadsGrid").click(function (e) {
                e.preventDefault();
                resetGridView('closedLeadsGrid', 'initialClosedLeadsGridOptions')
            });          

            var closedLeadsGrid = $("#closedLeadsGrid").data("kendoGrid");
            var totalClosedLeads = closedLeadsGrid.dataSource.total();
            viewModel.set("totalClosedLeads", totalClosedLeads);
        }

        function updateClosedOpportunityGrid() {
            $("#resetClosedOpportunitiesGrid").click(function (e) {
                e.preventDefault();
                resetGridView('closedOpportunitiesGrid', 'initialClosedOpportunitiesGridOptions')
            });

            var closedOpportunitiesGrid = $("#closedOpportunitiesGrid").data("kendoGrid");
            var totalOpportunitiesClosed = closedOpportunitiesGrid.dataSource.total();
            viewModel.set("totalOpportunitiesClosed", totalOpportunitiesClosed);
        }

        function refreshClosedGrid() {
            var closedSalesGrid = $("#closedSalesGrid").data("kendoGrid");
            closedSalesGrid.dataSource.read();
        }

        function opportunityData() {
            return {
                opportunityId: viewModel.get("opportunityId")
            }
        }

        function opportunityEventData() {
            return {
                opportunityEventId: viewModel.get("opportunityEventId")
            }
        }

        function opportunityEventCount(opportunityId) {
            viewModel.set("opportunityId", opportunityId);

            $("#opportunityEventWindow").data("kendoWindow").center().open();
        }

        function opportunityEventWindowOpened() {
            var opportunityEventsGrid = $("#opportunityEventsGrid").data("kendoGrid");
            opportunityEventsGrid.dataSource.read();
        }

        function opportunityAttachmentCount(opportunityId) {
            viewModel.set("opportunityId", opportunityId);

            $("#opportunityAttachmentWindow").data("kendoWindow").center().open();
        }

        function opportunityAttachmentWindowOpened() {
            var opportunityDocumentsGrid = $("#opportunityDocumentsGrid").data("kendoGrid");
            var opportunityActionDocumentsGrid = $("#opportunityActionDocumentsGrid").data("kendoGrid");
            var opportunityEventDocumentsGrid = $("#opportunityEventDocumentsGrid").data("kendoGrid");
            var wellDocumentsGrid = $("#wellDocumentsGrid").data("kendoGrid");

            opportunityDocumentsGrid.dataSource.read();
            opportunityActionDocumentsGrid.dataSource.read();
            opportunityEventDocumentsGrid.dataSource.read();
            wellDocumentsGrid.dataSource.read();
        }

        function opportunityActionCount(opportunityId) {
            viewModel.set("opportunityId", opportunityId);

            $("#opportunityActionWindow").data("kendoWindow").center().open();
        }

        function opportunityActionWindowOpened() {
            var opportunityActionsGrid = $("#opportunityActionsGrid").data("kendoGrid");
            opportunityActionsGrid.dataSource.read();
        }

        function updateClosedActionsGrid() {
            $("#resetClosedActionsGrid").click(function (e) {
                e.preventDefault();
                resetGridView('closedActionsGrid', 'initialClosedActionsGridOptions')
            });

            var closedActionsGrid = $("#closedActionsGrid").data("kendoGrid");
            var totalClosedActions = closedActionsGrid.dataSource.total();
            viewModel.set("totalClosedActions", totalClosedActions);
        }

        function updateClosedEventsGrid() {
            $("#resetClosedEventsGrid").click(function (e) {
                e.preventDefault();
                resetGridView('closedEventsGrid', 'initialClosedEventsGridOptions')
            });

            var closedEventsGrid = $("#closedEventsGrid").data("kendoGrid");
            var totalClosedEvents = closedEventsGrid.dataSource.total();
            viewModel.set("totalClosedEvents", totalClosedEvents);
        }
        function updateOpportunityActionsGrid() {
            var opportunityActionsGrid = $("#opportunityActionsGrid").data("kendoGrid");
            var totalOpportunityActions = opportunityActionsGrid.dataSource.total();
            viewModel.set("totalOpportunityActions", totalOpportunityActions);
        }

        function updateOpportunityEventsGrid() {
            var opportunityEventsGrid = $("#opportunityEventsGrid").data("kendoGrid");
            var totalOpportunityEvents = opportunityEventsGrid.dataSource.total();
            viewModel.set("totalOpportunityEvents", totalOpportunityEvents);
        }

        function updateFollowUpActionsGrid() {
            var followUpActionsGrid = $("#followUpActionsGrid").data("kendoGrid");
            var totalFollowUpActions = followUpActionsGrid.dataSource.total();
            viewModel.set("totalFollowUpActions", totalFollowUpActions);
        }

        function followUpActionCount(opportunityEventId) {
            viewModel.set("opportunityEventId", opportunityEventId);

            $("#followUpActionWindow").data("kendoWindow").center().open();
        }

        function followUpActionWindowOpened() {
            var followUpActionsGrid = $("#followUpActionsGrid").data("kendoGrid");
            followUpActionsGrid.dataSource.read();
        }

        function updateFollowUpActionsGrid() {
            var followUpActionsGrid = $("#followUpActionsGrid").data("kendoGrid");
            var totalFollowUpActions = followUpActionsGrid.dataSource.total();
            viewModel.set("totalFollowUpActions", totalFollowUpActions);
        }

        function updateOpportunityEventDocumentsGrid() {
            var opportunityEventDocumentsGrid = $("#opportunityEventDocumentsGrid").data("kendoGrid");
            var totalOpportunityEventDocuments = opportunityEventDocumentsGrid.dataSource.total();
            viewModel.set("totalOpportunityEventDocuments", totalOpportunityEventDocuments);
        }

        function updateOpportunityActionDocumentsGrid() {
            var opportunityActionDocumentsGrid = $("#opportunityActionDocumentsGrid").data("kendoGrid");
            var totalOpportunityActionDocuments = opportunityActionDocumentsGrid.dataSource.total();
            viewModel.set("totalOpportunityActionDocuments", totalOpportunityActionDocuments);
        }

        function updateWellDocumentsGrid() {
            var wellDocumentsGrid = $("#wellDocumentsGrid").data("kendoGrid");
            var totalOpportunityWellDocuments = wellDocumentsGrid.dataSource.total();
            viewModel.set("totalOpportunityWellDocuments", totalOpportunityWellDocuments);
        }

        function updateOpportunityDocumentsGrid() {
            var opportunityDocumentsGrid = $("#opportunityDocumentsGrid").data("kendoGrid");
            var totalOpportunityDocuments = opportunityDocumentsGrid.dataSource.total();
            viewModel.set("totalOpportunityDocuments", totalOpportunityDocuments);
        }

        function showReason() {
            var grid = $("#closedOpportunitiesGrid").data("kendoGrid");
            var model = grid.dataItem($(event.target).closest("tr"));
            var reason = model.ClosedReasonComment;
            swal({ html: true, title: '<i>Closure Reason</i>', text: '<b> ' + reason + ' </b>' });
        }

        function showClosedReason() {
            var grid = $("#closedSalesGrid").data("kendoGrid");
            var model = grid.dataItem($(event.target).closest("tr"));
            var reason = model.ClosedReasonComment;
            swal({ html: true, title: '<i>Closure Reason</i>', text: '<b> ' + reason + ' </b>' });
        }

        var viewModel = kendo.observable({
            totalFollowUpActions: 0,
            totalClosedSales: 0,
            totalClosedLeads: 0,
            totalOpportunitiesClosed: 0,
            totalClosedOpportunities: 0,
            totalOpportunityActions: 0,
            totalOpportunityEvents: 0,
            type: "",
            opportunityId: 0,
            opportunityEventId: 0,
            totalClosedActions: 0,
            totalClosedEvents: 0,
            totalOpportunityActionDocuments: 0,
            totalOpportunityEventDocuments: 0,
            totalOpportunityWellDocuments: 0,
            totalOpportunityDocuments: 0,
            fromDate: salesHistoryModel.modelFromDate,
            toDate: salesHistoryModel.modelToDate,
            totalStripText: function(){
                return `<span class="k-link"><i class="fa fa-bar-chart mr-1"></i> Total (<span data-bind="text:totalClosedSales"></span>)</span>`;
            },
            leadsStripText: function(){
                return `<span class="k-link"><i class="fa fa-lightbulb mr-1"></i> Leads (<span data-bind="text:totalClosedLeads"></span>)</span>`;
            },
            opportunitiesStripText: function(){
                return `<span class="k-link"><i class="fa fa-lightbulb mr-1"></i> Opportunities (<span data-bind="text:totalOpportunitiesClosed"></span>)</span>`;
            },
            eventsStripText: function(){
                return `<span class="k-link"><i class="fa fa-calendar mr-1"></i> Events (<span data-bind="text:totalClosedEvents"></span>)</span>`;
            },
            actionsStripText: function(){
                return `<span class="k-link"><i class="fa fa-check mr-1"></i> Actions (<span data-bind="text:totalClosedActions"></span>)</span>`;
            },

            totalClick: function () {
                this.set("type", "total");
                refreshClosedSalesGrid();
            },
            leadClick: function () {
                this.set("type", "lead");
                refreshClosedSalesGrid();
            },
            opportunityClick: function () {
                this.set("type", "opportunity");
                refreshClosedSalesGrid();
            },
        });
        kendo.bind(document.body.children, viewModel);