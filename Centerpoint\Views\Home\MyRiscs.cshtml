﻿@model UserModel
@{var cardName = GlobalSettings.IsWellsense ? "SOC" : "RISC";}
<div class="container-fluid pl-0 pr-0">
    <div class="w-100 text-right mb-3">
        @if (GlobalSettings.IsWellsense)
        {
            <a href="/Qhse/AddRiskIdentificationSafetyControl" class="btn btn-primary btn-sm">New SOC Card</a>
        }
        else
        {
            <a href="/Qhse/AddRiskIdentificationSafetyControl" class="btn btn-primary btn-sm">New RISC Card</a>
        }
    </div>
    <div class="row mb-3">
        <div class="col-md-5">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Monthly Summary</h6>
                </div>
                <div class="card-body">
                    <div class="card-list-item">
                        @(Html.Kendo().DropDownList()
                            .HtmlAttributes(new { style = "width:100px; margin-bottom:20px", data_bind = "value: summaryMonth" })
                            .Name("mySummaryDropdown")
                            .DataTextField("Text")
                            .Events(e => e.Change("summaryMonthChanged"))
                            .DataValueField("Value")
                            .Filter("contains")
                            .BindTo(DateConstants.GetAllMonths())
                        )
                    </div>
                    <div class="card-list-item">
                        <span class="card-list-item-name">Total</span>
                        <a href="#myRiscGrid" class="card-list-item-count" style="background: #000000" data-bind="click:totalClick,text:totalCreatedMonth">0</a>
                    </div>
                    <div class="card-list-item">
                        <span class="card-list-item-name">Positive</span>
                        <a  href="#myRiscGrid" class="card-list-item-count" style="background: #7CBB00" data-bind="click:positiveMonthClick,text:totalPositiveMonth">0</a>
                    </div>
                    <div class="card-list-item">
                        <span class="card-list-item-name">Corrective</span>
                        <a  href="#myRiscGrid" class="card-list-item-count" style="background: #BA141A" data-bind="click:negativeMonthClick,text:totalNegativeMonth">0</a>
                    </div>                 
                </div>
            </div>
        </div>
        <div class="col-md-7 chart">
            <div class="card">
                <div class="card-header">

                    @if (GlobalSettings.IsWellsense)
                    {
                        <h6 class="mb-0">SOC Cards per Month</h6>
                    }
                    else
                    {
                        <h6 class="mb-0">RISC Cards per Month</h6>
                    }
                    
                </div>
                <div class="card-body">
                    @(Html.Kendo().Chart<RiskIdentificationSafetyControlChartModel>()
                        .Name("riscChart")
                        .Theme("bootstrap")
                        .Series(c =>
                        {
                            c.Column(p => p.TotalYear).Name(DateTime.Now.Year.ToString());
                            c.Column(p => p.Average).Name("Average");
                            c.Column(p => p.TotalLastYear).Name(DateTime.Now.AddYears(-1).Year.ToString());
                        })
                        .ValueAxis(v => v.Numeric().Title("Count").MajorGridLines(l => l.Visible(false)).MinorGridLines(l => l.Visible(false)))
                        .Legend(l => l.Visible(true).Position(ChartLegendPosition.Bottom))
                        .CategoryAxis(c => c.Categories(p => p.Month).Title("RISC"))
                        .Tooltip(t => t.Visible(true).Template("#=category# #=series.name# - #=value#"))
                        .HtmlAttributes(new { @style = "height:290px; border:none" })
                        .DataSource(d => d.Read("GetRiscChartData", "Home"))
                    )
                </div>
            </div>
        </div>
    </div>
    <hr />
    @(Html.Kendo().Grid<RiskIdentificationSafetyControlModel>()
        .Name("myRiscGrid")
        .Columns(columns => {
            columns.Bound(c => c.RiscNumber).ClientTemplate("<a href='" + @Url.Action("EditRiskIdentificationSafetyControl", "Qhse") + "/#=RiskIdentificationSafetyControlId#'>#=RiscNumber#</a>").Width(80).Title($"{cardName} ID");
            columns.Bound(p => p.Date).Format(DateConstants.DateTimeFormat).Title("Created Date");
            columns.Bound(c => c.PositiveNegative).Title("Emphasis");
            columns.Bound(c => c.HazardObservation).Title("Type");
            columns.Bound(c => c.SafetyEnvironment).Title("Safety or Environment");
            columns.Bound(c => c.StatusDescription).Title("Status");
            columns.Bound(c => c.HseName).Title("HSE Sign-off");
            columns.Bound(p => p.HseDate).Format(DateConstants.DateTimeFormat).Title("Sign-off Date");
            columns.Bound(c => c.ItemName).Hidden(true);
        })
        .ToolBar(t => {
            t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetMyRiscGrid", @class="bg-danger text-white"});
            t.Excel().Text("Export");
        }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
        .Filterable()
        .Events(e => e.DataBound("updateRiscGrid"))
        .Events(e => e.ColumnReorder("saveRiscGrid").ColumnResize("saveRiscGrid").ColumnShow("saveRiscGrid").ColumnHide("saveRiscGrid"))
        .Excel(excel => excel
            .FileName(string.Format("Centerpoint_RISC_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Qhse")))
        .Groupable()
        .Scrollable(s => s.Height("auto"))
        .Resizable(c => c.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .Reorderable(c => c.Columns(true))
        .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Read(read => read.Action("GetMyRiskIdentificationSafetyControls", "Home").Data("riscData")))
    )
</div>




