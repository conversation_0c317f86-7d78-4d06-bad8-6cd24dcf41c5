
    $(document).ready(function () {
        
        activateTabDependingOnUrlQuery()
        refreshOpportunityForecastGrid($("#salesForecastStrips").kendoTabStrip().data("kendoTabStrip"));

        $("#btnExport").click(function () {
            var uri = $("#annualForecast").btechco_excelexport({
                containerid: "annualForecast",
                datatype: $datatype.Table,
                worksheetName: "Annual Forecast",
                returnUri: true
            });

            $(this).attr('download', 'AnnualForecast.xls')
                   .attr('href', uri);
        });

        $("#btnExportUnweighted").click(function () {
            var uri = $("#annualUnweightedForecast").btechco_excelexport({
                containerid: "annualUnweightedForecast",
                datatype: $datatype.Table,
                worksheetName: "Annual Unweighted Forecast",
                returnUri: true
            });

            $(this).attr('download', 'AnnualUnweighted.xls')
                .attr('href', uri);
        });
    });

    function refreshOpportunityForecastGrid() {
        var opportunityForecastGrid = $("#opportunityForecastGrid").data("kendoGrid");

        opportunityForecastGrid.dataSource.read();
    }

    function forecastChartData() {
        return {
            from: kendo.toString(viewModel.get("chartFromDate"), 'dd/MM/yyyy'),
            to: kendo.toString(viewModel.get("chartToDate"), 'dd/MM/yyyy')
        }
    }

    function forecastOpportunityData() {
        return {
            from: kendo.toString(viewModel.get("fromDate"), 'dd/MM/yyyy'),
            to: kendo.toString(viewModel.get("toDate"), 'dd/MM/yyyy')
        }
    }

    function yearChanged(e){
        var newYear = viewModel.get("year");
        window.location.href = "/sales/salesforecast?year=" + newYear + "&tab=" + "annual";
    }

    function yearUnweightedChanged(e) {
        var newYear = viewModel.get("yearUnweighted");
        window.location.href = "/sales/salesforecast?year=" + newYear + "&tab=" + "annualUnweighted";
    }

    function activateTabDependingOnUrlQuery () {
        let tabId = window.location.search.split("tab=")[1];
        if(tabId) {
            let tabToActivate = $(`#${tabId}`);
            if(tabToActivate) {
              $("#salesForecastStrips").data("kendoTabStrip").select(tabToActivate)
            }
        }
    }

    var viewModel = new kendo.observable({
        year: salesForecastModel.modelYear,
        yearUnweighted: salesForecastModel.modelYearUnweighted,
        fromDate: salesForecastModel.modelFromDate,
        toDate: salesForecastModel.modelToDate,
        chartFromDate: salesForecastModel.modelChartFromDate,
        chartToDate: salesForecastModel.modelChartToDate,
    });
    kendo.bind(document.body.children, viewModel);