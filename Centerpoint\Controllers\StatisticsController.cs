﻿using Centerpoint.Extensions;
using Centerpoint.Service.Interfaces;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;

namespace Centerpoint.Controllers
{
    [Authorize]
    public class StatisticsController : Controller
    {
        private readonly IStatisticsService _statisticsService;

        public StatisticsController(IStatisticsService statisticsService)
        {
            _statisticsService = statisticsService;
        }
        public async Task<ActionResult> Index()
        {
            this.SetTitle("Statistics");

            return View();
        }

        public async Task<ActionResult> GetJobChartData(int months, string showBy = "Month")
        {
            return Json(await _statisticsService.GetJobChartDataAsync(months, showBy));
        }

        public async Task<ActionResult> GetJobServiceChartData([DataSourceRequest] DataSourceRequest request, int months, string showBy = "Month")
        {
            if (months > 60)
            {
                return Json(new DataSourceResult { Errors = "Months should be less than or equal to 60." });
            }

            var result = await _statisticsService.GetJobServiceChartData(months, showBy);

            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetRunChartData(int months, string showBy = "Month")
        {
            return Json(await _statisticsService.GetRunChartData(months, showBy));
        }

        public async Task<ActionResult> GetRunServiceChartData(int months, string showBy = "Month")
        {
            return Json(await _statisticsService.GetRunServiceChartData(months, showBy));
        }

        public async Task<ActionResult> GetRiscMonthChartData(int months)
        {
            return Json(await _statisticsService.GetRiscMonthChartData(months));
        }

        public async Task<ActionResult> GetRiscYearChartData(int years)
        {
            return Json(await _statisticsService.GetRiscYearChartData(years));
        }

        public async Task<ActionResult> GetShipmentMonthChartData(int months)
        {
            return Json(await _statisticsService.GetShipmentMonthChartData(months));
        }

        public async Task<ActionResult> GetAssetValueChartData()
        {
            var result = await _statisticsService.GetAssetValueChartData();

            return Json(result);
        }

        public async Task<ActionResult> GetEquipmentShipmentItemsWithActiveMR([DataSourceRequest] DataSourceRequest request)
        {
            return Json((await _statisticsService.GetEquipmentShipmentItemsWithActiveMR()).ToDataSourceResult(request));
        }
    }
}