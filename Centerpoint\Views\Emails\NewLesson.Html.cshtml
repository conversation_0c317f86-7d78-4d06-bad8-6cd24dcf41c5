﻿@using Centerpoint.Model.ViewModels
@model NewLessonEmail
<html>
<body>
    <p style="font-family:Arial,sans-serif">Hi @Model.User,</p>
    <p style="font-family:Arial,sans-serif">A new lesson "@Model.FullLessonName" has been submitted for approval by - @Model.CreatedByUser.</p>

    @if (!string.IsNullOrWhiteSpace(Model.Description)) {
        <p style="font-family:Arial,sans-serif">Description : @Model.Description</p>
    } else {
        <p style="font-family:Arial,sans-serif">Description : N/A</p>
    }
    @if (!string.IsNullOrWhiteSpace(Model.Category)) {
        <p style="font-family:Arial,sans-serif">Category : @Model.Category</p>
    } else {
        <p style="font-family:Arial,sans-serif">Category : N/A</p>
    }
    @if(Model.EquipmentCategories.Count > 0) { 
    <p style="font-family:Arial,sans-serif">Equipment Categories : @Model.Categories</p>
    } else {
        <p style="font-family:Arial,sans-serif">Equipment Categories : N/A</p>
    }
    @if (Model.Services.Count > 0) {
    <p style="font-family:Arial,sans-serif">Related Services : @Model.ServiceList</p>
    } else {
        <p style="font-family:Arial,sans-serif">Related Services : N/A</p>
    }
    @if (Model.OnJob) { 
    <p style="font-family:Arial,sans-serif">Not Job Related? : @Model.JobRelated</p>
    }

    @if (!Model.OnJob) {
    <p style="font-family:Arial,sans-serif">Company : @Model.CompanyName</p>
    <p style="font-family:Arial,sans-serif">Job : @Model.JobName</p>
   }

    <p style="font-family:Arial,sans-serif">Please click <a href="@Model.Url">here</a> to access the lesson.</p>
    
</body>
</html>