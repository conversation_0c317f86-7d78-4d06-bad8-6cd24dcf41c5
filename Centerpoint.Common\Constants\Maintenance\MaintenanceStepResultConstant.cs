﻿namespace Centerpoint.Common.Constants
{
    public static class MaintenanceStepResultConstant
    {

        public const string Pass = "PAS";
        public const string Fail = "FAI";
        public const string OnHold = "OHL";
        public const string OnHoldRemoved = "OHR";
        public const string Repaired = "REP";
        public const string NotRepaired = "NRE";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Pass,"Pass"},
                    {Fail,"Fail"},
                    {OnHold,"OnHold"},
                    {OnHoldRemoved,"OnHold Removed"},
                    {Repaired,"Repaired"},
                    {NotRepaired,"Not Repaired"},
                };
            }
        }
    }
}
