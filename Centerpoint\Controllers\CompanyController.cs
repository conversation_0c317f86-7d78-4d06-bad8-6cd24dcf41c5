﻿using Centerpoint.Extensions;
using Centerpoint.Service.Interfaces;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Centerpoint.Controllers
{
    [Authorize]
    public class CompanyController : Controller
    {
        private readonly ICompanyService _companyService;

        public CompanyController(ICompanyService companyService)
        {
            _companyService = companyService;
        }
        public async Task<ActionResult> Companies()
        {
            this.SetTitle("Companies");
            return View();
        }

        public async Task<ActionResult> GetCompanies([DataSourceRequest] DataSourceRequest dataCompany)
        {
            return Json((await _companyService.GetCompaniesAsync()).ToDataSourceResult(dataCompany));
        }

        public async Task<ActionResult> ViewCompany(int id)
        {
            var model = await _companyService.GetCompany(id);

            this.SetTitle(string.Format("{0} {1}", model.Name, model.Description));

            ViewBag.CompanyId = id;

            return View(model);
        }

        public async Task<ActionResult> ViewCompanyWell(int id, string tab)
        {
            var model = await _companyService.ViewCompanyWell(id);
           
            this.SetTitle(string.Format("{0}", model.Name));

            ViewBag.Tab = tab;

            return View(model);
        }

        public async Task<ActionResult> GetCompanyWells([DataSourceRequest] DataSourceRequest request, int companyId)
        {
            return Json((await _companyService.GetCompanyWellsAsync(companyId)).ToDataSourceResult(request));
        }
    }
}