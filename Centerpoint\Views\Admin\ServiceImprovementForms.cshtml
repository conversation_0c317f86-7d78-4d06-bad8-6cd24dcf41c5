﻿
<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-bars"></i>
        Service Improvement Form (SIF)
    </h4>
</div>
<hr />

<div class="grid-container">
    @(Html.Kendo().TabStrip()
        .Name("adminMaintenanceBlueprintsTabStrip")
        .SelectedIndex(0)
        .Animation(false)
        .Items( tabstrip => {

            tabstrip.Add().Text("Severity")
                .HtmlAttributes(new { @data_bind="html:totalSeveritiesText"})
                .Selected(true)
                .Content(@<text>
                    @(Html.Kendo().Grid<SeverityModel>()
                        .Name("severityGrid")
                        .Columns(c => {
                            c.Bound(p => p.OrderNo).Title("Order No.");
                            c.Bound(p => p.Name);
                            c.Command(command => { 
                                command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                                command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
                            }).Width(200);
                        })
                        .Editable(editable => editable.Mode(GridEditMode.InLine))
                        .ToolBar(t => {
                                t.Create().Text("Add Severity");
                                t.Excel().Text("Export");
                            }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
                        .Sortable()
                        .Filterable()
                        .Scrollable()
                        .Resizable(c => c.Columns(true))
                        .ColumnMenu(c => c.Columns(true))
                        .Events(e => e.DataBound("updateSeverityTotal"))
                        .Excel(excel => excel
                            .FileName(string.Format("Centerpoint_QHSE_Severity_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                            .Filterable(true)
                            .ProxyURL(Url.Action("Export", "Admin"))
                        )
                        .DataSource(dataSource => dataSource
                            .Ajax()
                            .ServerOperation(false)
                            .Model(m => m.Id(p => p.SeverityId))
                            .Events(e => e.Error("onError"))
                            .Read(read => read.Action("GetSeverities", "Admin"))
                            .Create(create => create.Action("UpdateSeverity", "Admin"))
                            .Update(update => update.Action("UpdateSeverity", "Admin"))
                            .Destroy(destroy => destroy.Action("DeleteSeverity", "Admin"))
                        )
                    )
            </text>);

            tabstrip.Add().Text("Location")
                .HtmlAttributes(new { @data_bind="html:totalServiceImprovementLocationsText"})
                .Content(@<text>
                    @(Html.Kendo().Grid<ServiceImprovementLocationModel>()
                        .Name("serviceImprovementLocationGrid")
                        .Columns(c => {
                            c.Bound(p => p.Name);
                            c.Command(command => { 
                                command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                                command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
                            }).Width(200);
                        })
                        .Editable(editable => editable.Mode(GridEditMode.InLine))
                        .ToolBar(t => {
                            t.Create().Text("Add Location");
                            t.Excel().Text("Export");
                        }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
                        .Sortable()
                        .Filterable()
                        .Scrollable(s => s.Height("auto"))
                        .Resizable(c => c.Columns(true))
                        .ColumnMenu(c => c.Columns(true))
                        .Events(e => e.DataBound("updateServiceImprovementLocationTotal"))
                        .Excel(excel => excel
                            .FileName(string.Format("Centerpoint_QHSE_Location_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                            .Filterable(true)
                            .ProxyURL(Url.Action("Export", "Admin"))
                        )
                        .DataSource(dataSource => dataSource
                            .Ajax()
                            .ServerOperation(false)
                            .Model(m => m.Id(p => p.ServiceImprovementLocationId))
                            .Events(e => e.Error("onErrorLocation"))
                            .Read(read => read.Action("GetServiceImprovementLocations", "Admin"))
                            .Create(create => create.Action("UpdateServiceImprovementLocation", "Admin"))
                            .Update(update => update.Action("UpdateServiceImprovementLocation", "Admin"))
                            .Destroy(destroy => destroy.Action("DeleteServiceImprovementLocation", "Admin"))
                        )
                    )
            </text>);

            tabstrip.Add().Text("Sub-Category")
                .HtmlAttributes(new { @data_bind="html:totalSubCategoriesText"})
                .Content(@<text>
                    @(Html.Kendo().Grid<SubCategoryModel>()
                        .Name("subCategoryGrid")
                        .Columns(c => {
                            c.Bound(p => p.Name);
                            c.Command(command => { 
                                command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                                command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
                            }).Width(200);
                        })
                        .Editable(editable => editable.Mode(GridEditMode.InLine))
                        .ToolBar(t => {
                            t.Create().Text("Add Sub-Category");
                            t.Excel().Text("Export");
                        }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
                        .Sortable()
                        .Filterable()
                        .Scrollable(s => s.Height("auto"))
                        .Resizable(c => c.Columns(true))
                        .ColumnMenu(c => c.Columns(true))
                        .Events(e => e.DataBound("updateSubCategoryTotal"))
                        .Excel(excel => excel
                            .FileName(string.Format("Centerpoint_QHSE_Sub-Category_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                            .Filterable(true)
                            .ProxyURL(Url.Action("Export", "Admin"))
                        )
                        .DataSource(dataSource => dataSource
                            .Ajax()
                            .ServerOperation(false)
                            .Model(m => m.Id(p => p.SubCategoryId))
                            .Events(e => e.Error("onErrorSubCategory"))
                            .Read(read => read.Action("GetSubCategories", "Admin"))
                            .Create(create => create.Action("UpdateSubCategory", "Admin"))
                            .Update(update => update.Action("UpdateSubCategory", "Admin"))
                            .Destroy(destroy => destroy.Action("DeleteSubCategory", "Admin"))
                        )
                    )
            </text>);
        })
    )
</div>

    <script>
        $(document).ready(function () {
            var severityGrid = $('#severityGrid').data("kendoGrid");
            var serviceImprovementLocationGrid = $('#serviceImprovementLocationGrid').data("kendoGrid");
            var subCategoryGrid = $('#subCategoryGrid').data("kendoGrid");
            severityGrid.bind('dataBound', function (e) {
                this.element.find('.k-i-excel').remove();
            });
            serviceImprovementLocationGrid.bind('dataBound', function (e) {
                this.element.find('.k-i-excel').remove();
            });
            subCategoryGrid.bind('dataBound', function (e) {
                this.element.find('.k-i-excel').remove();
            });
        });

        function updateSeverityTotal() {
            var severityGrid = $("#severityGrid").data("kendoGrid");
            var totalSeverities = severityGrid.dataSource.total();
            viewModel.set("totalSeverities", totalSeverities);
        }

        function updateServiceImprovementLocationTotal() {
            var serviceImprovementLocationGrid = $("#serviceImprovementLocationGrid").data("kendoGrid");
            var totalServiceImprovementLocations = serviceImprovementLocationGrid.dataSource.total();
            viewModel.set("totalServiceImprovementLocations", totalServiceImprovementLocations);
        }

        function updateSubCategoryTotal() {
            var subCategoryGrid = $("#subCategoryGrid").data("kendoGrid");
            var totalSubCategories = subCategoryGrid.dataSource.total();
            viewModel.set("totalSubCategories", totalSubCategories);
        }

        function onError(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var severityGrid = $("#severityGrid").data("kendoGrid");
                severityGrid.dataSource.cancelChanges();
            }
        }
       
        function onErrorLocation(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var serviceImprovementLocationGrid = $("#serviceImprovementLocationGrid").data("kendoGrid");
                serviceImprovementLocationGrid.dataSource.cancelChanges();
            }
        }
        function onErrorSubCategory(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var severityGrid = $("#subCategoryGrid").data("kendoGrid");
                subCategoryGrid.dataSource.cancelChanges();
            }
        }

        var viewModel = new kendo.observable({
            totalSeverities: 0,
            totalSeveritiesText: function(){
                return `<span class="k-link"><i class="fa fa-exclamation-triangle mr-2"></i> Severity (<span data-bind="text:totalSeverities"></span>)</span>`;
            },
            totalServiceImprovementLocations: 0,
            totalServiceImprovementLocationsText: function(){
                return `<span class="k-link"><i class="fa fa-map-marker mr-2"></i> Location (<span data-bind="text:totalServiceImprovementLocations"></span>)</span>`;
            },
            totalSubCategories: 0,
            totalSubCategoriesText: function(){
                return `<span class="k-link"><i class="fa fa-level-down mr-2"></i> Sub-Category (<span data-bind="text:totalSubCategories"></span>)</span>`;
            },
        });

        kendo.bind(document.body.children, viewModel);
    </script>

