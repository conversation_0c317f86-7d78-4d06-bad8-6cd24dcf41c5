﻿namespace Centerpoint.Common.Constants
{
    public static class OpportunityClosedReasonConstant
    {

        public const string Reason1 = "RS1";
        public const string Reason2 = "RS2";
        public const string Reason3 = "RS3";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Reason1, "Reason 1"},
                    {Reason2, "Reason 2"},
                    {Reason3, "Reason 3"}
                };
            }
        }
    }
}
