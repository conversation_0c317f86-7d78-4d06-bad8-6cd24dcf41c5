﻿namespace Centerpoint.Common.Constants
{
    public static class MaintenanceResultConstant
    {

        public const string Pass = "PAS";
        public const string Fail = "FAI";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Pass,"Pass"},
                    {Fail,"Fail"},
                };
            }
        }
    }
}
