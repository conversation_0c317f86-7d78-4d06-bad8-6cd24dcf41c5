
$(document).ready(function () {
    getMonthlySummary();
    getEngineeerSummary();
    loadMaintenanceGrid();
    kendo.bind(document.body.children, viewModel);
});

function saveMaintenanceRecordGrid(e) {
    setTimeout(function(){
        var grid = $("#maintenanceRecordGrid").data("kendoGrid");
        localStorage["maintenanceRecordGrid"] = kendo.stringify(grid.getOptions());
    },10);
}

function loadMaintenanceGrid() {
    var grid = $("#maintenanceRecordGrid").data("kendoGrid");
    var toolBar = $("#maintenanceRecordGrid .k-grid-toolbar").html();
    var options = localStorage["maintenanceRecordGrid"];
    viewModel.set("initialMaintenanceRecordGridOptions", kendo.stringify(grid.getOptions()));
    if (options) {
        grid.setOptions(JSON.parse(options));
        $("#maintenanceRecordGrid .k-grid-toolbar").html(toolBar);
        $("#maintenanceRecordGrid .k-grid-toolbar").addClass("k-grid-top");
    }
}

function updatedMaintenanceRecordGrid(e) {
    $("#resetMaintenanceRecordGrid").click(function (e) {
        e.preventDefault();
        resetGridView('maintenanceRecordGrid', 'initialMaintenanceRecordGridOptions')
        viewModel.set('selectedMaintenanceRecordId', 0);
        $("#maintenanceRecordGrid").data("kendoGrid").dataSource.read();
        kendo.bind($("#maintenanceRecordGrid"), viewModel);

    });

    var maintenanceRecordGrid = $("#maintenanceRecordGrid").data("kendoGrid");
    var totalMaintenanceRecords = maintenanceRecordGrid.dataSource.total();
    viewModel.set("totalMaintenanceRecords", totalMaintenanceRecords);

    var mrData = maintenanceRecordGrid.dataSource.data();

    var rows = e.sender.tbody.children();

    for (var j = 0; j < rows.length; j++) {
        var row = $(rows[j]);
        var dataItem = e.sender.dataItem(row);

        if (dataItem.get("Fail") == true) {
            row.addClass("fail");
        }
    }
}

function refreshMaintenanceRecordGrid() {
    var maintenanceRecordGrid = $("#maintenanceRecordGrid").data("kendoGrid");
    maintenanceRecordGrid.dataSource.read();
}

function maintenanceRecordData() {
    return {
        type: viewModel.get("type"),
        month: viewModel.get("month"),
        year: viewModel.get("year"),
        engineerUserId:viewModel.get("engineerUserId"),
        companyLocationId:viewModel.get("companyLocationId")
    }
}

function monthChanged(e){
    getMonthlySummary();
    refreshMaintenanceRecordGrid();
}

function yearChanged(e){
    getMonthlySummary();
    refreshMaintenanceRecordGrid();
}

function engineerFilter(engineerUserId){
    viewModel.set("type", "engineer");
    viewModel.set("engineerUserId", engineerUserId);

    refreshMaintenanceRecordGrid();
}

function locationFilter(companyLocationId){
    viewModel.set("type", "location");
    viewModel.set("companyLocationId", companyLocationId);

    refreshMaintenanceRecordGrid();
}

function selectedMaintenanceRecord(e) {
    var selectedMaintenanceRecordRow = this.select();

    if (selectedMaintenanceRecordRow) {
        var selectedItem = this.dataItem(selectedMaintenanceRecordRow);

        if (selectedItem) {
            viewModel.set("selectedMaintenanceRecordId", selectedItem.EquipmentItemId);
        } else {
            viewModel.set("selectedMaintenanceRecordId", "");
        }
    } else {
        viewModel.set("selectedMaintenanceRecordId", "");
    }
}

function getMonthlySummary(){
    $.ajax({
        url: "/Maintenance/GetMonthlySummary",
        data: {
            month:viewModel.get("month"),
            year:viewModel.get("year")
        },
        success: function(result){
            if(result){
                var totalClosed = result.TotalClosedFailed + result.TotalClosedPassed + result.TotalClosedRepaired + result.TotalClosedNotRepaired;
                viewModel.set("totalClosed", totalClosed);
                viewModel.set("totalMrMonth", result.TotalMonthMR);
                viewModel.set("totalOutstanding", result.TotalOutstanding);
                viewModel.set("totalClosedPassed", result.TotalClosedPassed);
                viewModel.set("totalClosedFailed", result.TotalClosedFailed);
                viewModel.set("totalClosedRepaired", result.TotalClosedRepaired);
                viewModel.set("totalClosedNotRepaired", result.TotalClosedNotRepaired);
                viewModel.set("totalCarriedOver", result.TotalCarriedOver);
            }
        },
        dataType: "json"
    });
}

function getEngineeerSummary() {
    $.ajax({
        url: "/Maintenance/GetEngineeerSummary",
        data: {
        },
        success: function (result) {
            if (result) {
                viewModel.set("totalassignedByEngineer", result.TotalAssignedByEngineer);
                viewModel.set("totalinProgressByEngineer", result.TotalInProgressByEngineer);
                viewModel.set("totalonHoldByEngineer", result.TotalOnHoldByEngineer);
                viewModel.set("totalAwaitingApprovalByEngineer", result.TotalAwaitingApprovalByEngineer);
            }
        },
        dataType: "json"
    });
}

function maintenanceRecordGridUpdateData(data) {

    data.Created = toUTCString(data.Created);
    data.Modified = toUTCString(data.Modified);
    data.StartDate = toUTCString(data.StartDate);
    data.CompletedDate = toUTCString(data.CompletedDate);
}

var viewModel = kendo.observable({
    totalMaintenanceRecords: 0,
    type: "",
    engineerUserId: "",
    companyLocationId: "",
    totalMrMonth: 0,
    totalOutstanding: 0,
    totalClosed: 0,
    totalClosedPassed: 0,
    totalClosedFailed: 0,
    totalClosedRepaired: 0,
    totalClosedNotRepaired: 0,
    totalCarriedOver: 0,
    month: maintenanceModel.month,
    year: maintenanceModel.year,
    totalPendingByEngineer: 0,
    totalMRByEngineer: 0,
    totalinProgressByEngineer: 0,
    totalonHoldByEngineer: 0,
    totalAwaitingApprovalByEngineer: 0,
    totalassignedByEngineer: 0,
    selectedMaintenanceRecordId: 0,

    mrTriggerClick:function() {
        $.ajax({
            url:"/Maintenance/CheckMaintenance",
            dataType:"json",
            success:function(response){
                if(response.count){
                    var confirmation = confirm (response.count + " tool(s) require maintenance, are you sure you wish to generate maintenance records for all applicable tools?");

                    if(confirmation) {
                        window.location.href="/Maintenance/RunMaintenance";
                    }
                } else{
                    alert("There are no tools that currently require maintenance");
                }
            }
        });
    },

    totalClick: function () {
        this.set("type", "total");
        refreshMaintenanceRecordGrid();
    },
    pendingClick: function () {
        this.set("type", "pending");
        refreshMaintenanceRecordGrid();
    },
    inProgressClick: function () {
        this.set("type", "inProgress");
        refreshMaintenanceRecordGrid();
    },
    onHoldClick: function () {
        this.set("type", "onHold");
        refreshMaintenanceRecordGrid();
    },
    awaitingApprovalClick: function () {
        this.set("type", "awaitingApproval");
        refreshMaintenanceRecordGrid();
    },
    assignedClick: function () {
        this.set("type", "assigned");
        refreshMaintenanceRecordGrid();
    },
    totalOutstandingClick: function () {
        this.set("type", "outStanding");
        refreshMaintenanceRecordGrid();
    },
    totalPassedClick: function () {
        this.set("type", "passed");
        refreshMaintenanceRecordGrid();
    },
    totalFailedClick: function () {
        this.set("type", "failed");
        refreshMaintenanceRecordGrid();
    },
    totalCarriedOverClick: function () {
        this.set("type", "carriedOver");
        refreshMaintenanceRecordGrid();
    },
    totalRaisedClick: function () {
        this.set("type", "totalRaised");
        refreshMaintenanceRecordGrid();
    },
    totalClosedClick: function () {
        this.set("type", "totalClosedAll");
        refreshMaintenanceRecordGrid();
    },
    totalClosedRepairedClick: function () {
        this.set("type", "closedRepaired");
        refreshMaintenanceRecordGrid();
    },
    totalClosedNotRepairedClick: function () {
        this.set("type", "closedNotRepaired");
        refreshMaintenanceRecordGrid();
    },
    totalMRByEngineerClick: function () {
        this.set("type", "totalMRByEngineer");
        refreshMaintenanceRecordGrid();
    },
    pendingByEngineerClick: function () {
        this.set("type", "pendingByEngineer");
        refreshMaintenanceRecordGrid();
    },
    inProgressByEngineerClick: function () {
        this.set("type", "inProgressByEngineer");
        refreshMaintenanceRecordGrid();
    },
    onHoldByEngineerClick: function () {
        this.set("type", "onHoldByEngineer");
        refreshMaintenanceRecordGrid();
    },
    awaitingApprovalByEngineerClick: function () {
        this.set("type", "awaitingApprovalByEngineer");
        refreshMaintenanceRecordGrid();
    },
    assignedByEngineerClick: function () {
        this.set("type", "assignedByEngineer");
        refreshMaintenanceRecordGrid();
    },
    sifClick: function () {
        this.set("type", "sif");
        refreshMaintenanceRecordGrid();
    },
    notRunEnabledClick: function () {
        this.set("type", "noRun");
        refreshMaintenanceRecordGrid();
    },
    runEnabledClick: function () {
        this.set("type", "run");
        refreshMaintenanceRecordGrid();
    },

    quarantinedClick: function () {
        this.set("type", "quarantined");
        refreshMaintenanceRecordGrid();
    },
    editMaintenanceRecordWindow: function () {
        $("#editMaintenanceRecordWindowOpen").data("kendoWindow").center().open();
    },

});

$("#confirmDates").click(function (e) {
    var maintenanceRecordGrid = $("#maintenanceRecordGrid").data("kendoGrid");

    var selectMaintenanceRecordIds = [];

    maintenanceRecordGrid.select().each(function () {
        var selectedMaintenanceRecord = maintenanceRecordGrid.dataItem($(this));

        if (selectedMaintenanceRecord) {
            selectMaintenanceRecordIds.push(selectedMaintenanceRecord.MaintenanceRecordId);
        }
    });
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: "/Maintenance/EditMaintenanceRecordDates",
        data: {
            selectedMaintenanceRecordIds: selectMaintenanceRecordIds,
            created: $("#Created").val(),
            startDate: $("#StartDate").val(),
            completedDate: $("#CompletedDate").val(),
        },
        complete: function (e) {
            $("#editMaintenanceRecordWindowOpen").data("kendoWindow").close()
            viewModel.set('selectedMaintenanceRecordId', 0);
            maintenanceRecordGrid.dataSource.read();
        },
    });
});
kendo.bind(document.body.children, viewModel);