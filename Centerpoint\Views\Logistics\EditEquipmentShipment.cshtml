﻿@model EquipmentShipmentModel

@Html.Partial( "_GridNotification", EntityType.EquipmentShipment)

@if (Model.EquipmentShipmentStatus == EquipmentShipmentStatusConstant.Pending) {
    <div style="display:none" class="alert alert-dismissible alert-warning" data-bind="invisible:equipmentItemsAtFromLocation">
        <button type="button" class="close" data-bs-dismiss="alert">×</button>
        <strong><i class="fa fa-warning"></i> Warning!</strong> There are items on the shipment not currently situated at the shipment from location.
    </div>
    <div style="display:none" class="alert alert-dismissible alert-warning" data-bind="visible:currentLocation">
        <button type="button" class="close" data-bs-dismiss="alert">×</button>
        <strong><i class="fa fa-warning"></i> Warning!</strong> There are items on the shipment which are InTransit.
    </div> 
    <div style="display:none" class="alert alert-dismissible alert-warning" data-bind="visible:dangerousGoods">
        <button type="button" class="close" data-bs-dismiss="alert">×</button>
        <strong><i class="fa fa-warning"></i> Warning!</strong> There are items on the shipment which are Dangerous.
    </div>         
}

<div class="header-container-between">
    <h4>
        <i class="fa fa-plane"></i>
        @if (Model.EquipmentShipmentId.HasValue)
        {
            @if (Model.ShipmentNumberandStatus.Contains("Received") && GlobalSettings.IsAisus && !Model.FromProjectId.HasValue)
            {
                @(Model.ShipmentNumberandStatus.Replace("Received", "Sent"))
            }
            else
            {
                @(Model.ShipmentNumberandStatus)
            }
        }
        else{ 
            @("Create New Shipment")
        }
    </h4>
    <div class="d-flex actionsContainer">
        @if (Model.ProjectId.HasValue) {
            <a class="btn btn-info btn-sm" href="@Url.Action("EditProject", "Operation", new {@id=Model.ProjectId.Value, @tab="shipments"})">
                <i class="fa fa-refresh"></i>
                Go to Project
            </a>
        }
        @if (Model.EquipmentShipmentStatus == EquipmentShipmentStatusConstant.Pending && Model.SentDate.HasValue && (Html.IsLogisticsAdmin() || Html.IsGlobalAdmin())) {
            if (Model.FromProjectId.HasValue && GlobalSettings.IsAisus)
            {
                <a class="btn btn-success btn-sm" data-bind="click:backloadShipmentClick, visible:canShip, disabled:isTransit">
                    <i class="fa fa-thumbs-up"></i>
                    Mark as Received
                </a>
            }
            else
            {
                <button id="markAsShippedBtn" class="btn btn-success btn-sm" data-bind="click:shipmentClick, visible:canShip, disabled:isTransit">
                    <i class="fa fa-thumbs-up"></i>
                    Mark as Shipped
                </button>
            }
        }
        @if (Model.EquipmentShipmentStatus == EquipmentShipmentStatusConstant.InTransit && Html.IsShipmentReceivers() && ViewBag.IsReceiving == null)
        {
            <button id="receiveShipmentBtn" class="btn btn-success btn-sm " data-bind="click:markShipmentReceivedClick">
                <i class="fa fa-check-square"></i>
                Receive Shipment
            </button>
        }
        @if (ViewBag.IsReceiving == true && Model.EquipmentShipmentStatus != EquipmentShipmentStatusConstant.Received) {
            <button id="confirmShipmentReceivedBtn" class="btn btn-success btn-sm" data-bind="click:shipmentReceivedClick">
                <i class="fa fa-check"></i>
                Confirm Shipment Received
            </button>
        }
        @if (Model.EquipmentShipmentStatus == EquipmentShipmentStatusConstant.Received && GlobalSettings.IsWellsense && (Html.IsLogisticsAdmin() || Html.IsGlobalAdmin()) && !Model.IsProjectRelated)
        {
            <a class="btn btn-success btn-sm" data-bind="click:createShipmentBackloadClick">
                <i class="fa fa-refresh"></i>
                Create Backload Shipment
            </a>
        }
       @*  else if (Model.EquipmentShipmentStatus == EquipmentShipmentStatusConstant.Received && (Html.IsLogisticsAdmin() || Html.IsGlobalAdmin()) && !Model.IsProjectRelated)
        {
            <a class="btn btn-success btn-sm" data-bind="click:createShipmentBackloadClick">
                <i class="fa fa-refresh"></i>
                Revert Backload Shipment
            </a>
        } *@
        @if (Model.EquipmentShipmentStatus == EquipmentShipmentStatusConstant.Received && (Html.IsLogisticsAdmin() || Html.IsGlobalAdmin()))
        {
            <button id="undoShipmentReceivedBtn" class="btn btn-danger btn-sm" data-bind="click:undoShipmentReceivedClick, visible:isUndoShipmentReceivedVisible">
                <i class="fa fa-refresh"></i>
                Undo Shipment Received
            </button>
        }
        <a class="btn btn-info btn-sm" href="@Url.Action("Index","Logistics" )">
            <i class="fa fa-refresh"></i>
            Return to Logistics Dashboard
        </a>
    </div>
</div>
<hr />

@{Html.Kendo().TabStrip()
    .Name("EditTabStrip")
    .HtmlAttributes(new {@id = "editTabStrip", @data_bind="visible: isEditTabStripVisible"})
    .SelectedIndex(0)
    .Animation(animation =>
    {
        animation.Enable(false);
    })
    .Items(tabstrip =>
    {
        tabstrip.Add().Text("")
            .HtmlAttributes(new { @data_bind="html:logisticsDetailsText", @id="detailsTab"})
            .Selected(true)
            .Content(@<text>
                <partial name="Details"/>
            </text>);
        if (Model.EquipmentShipmentId.HasValue) {
            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind="html:totalEquipmentShipmentItemsText", @id="equipmentItemsTab"})
                .Content(@<text>
                <partial name="EquipmentItems"/>
                </text>);
            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind="html:totalEquipmentShipmentInvoicesText",  @id="paperworkTab"})
                .Content(@<text>
                    <partial name="Paperwork"/>
                </text>);
            tabstrip.Add().Text("")
                 .HtmlAttributes(new { @data_bind = "html:totalPackageInformationText", @id = "packageInformationTab" })
                 .Content(@<text>
                    <partial name="PackageInformation" />
                 </text>);
        }

        tabstrip.Add().Text("")
            .HtmlAttributes(new { @data_bind = "html:attachmentsText" })
            .Content(@<text>
        <div id="attachments" class="tab-pane @(ViewBag.Tab == "attachments" ? "active" : string.Empty)">
            <br />
            @if (!Html.IsAnsaAdministrator() && !Html.IsAnsaAnalyst()) {
            <p>Click the link below to attach documents to this Shipment</p>
            @(Html.Kendo().Upload()
        .Name("equipmentShipmentAttachmentDocuments")
        .Messages(m => m.Select("Attach Shipment Documents"))
        .Multiple(true)
        .Events(e => e.Success("onEquipmentShipmentDocumentAttached").Complete("onEquipmentShipmentDocumentComplete").Upload("onEquipmentShipmentDocumentUpload"))
        .HtmlAttributes(new { @style = "width:300px" })
        .Async(async => async.Save("AttachEquipmentShipmentDocuments", "Logistics", new { @equipmentShipmentId = Model.EquipmentShipmentId }).Batch(true))
            )
            <br />
        }

            @(Html.Kendo().Grid<DocumentModel>()
        .Name("equipmentShipmentDocumentsGrid")
        .Columns(c => {
        c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
        c.Bound(p => p.Created).Title("Created").ClientTemplate("#=CreatedDate#").Width(150);
        c.Bound(p => p.UserName).Title("Created By").Width(200);
        c.Command(command => {
        command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}).Visible("canEquipmentItemDocumentDelete");
        }).Width(200);
        })
        .Events(e => e.DataBound("updateEquipmentShipmentDocumentGrid"))
        .Sortable()
        .Resizable(r => r.Columns(true))
        .Filterable()
        .Groupable()
        .Editable(e => e.Mode(GridEditMode.InLine))
        .Scrollable(s => s.Height(500))
        .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Model(model => model.Id(p => p.DocumentId))
        .Read(read => read.Action("GetEquipmentShipmentDocuments", "Logistics", new { @equipmentShipmentId = Model.EquipmentShipmentId }))
        .Destroy(destroy => destroy.Action("DeleteEquipmentShipmentDocument", "Logistics", new { @equipmentShipmentId = Model.EquipmentShipmentId }))
        )
            )
            <br />
        </div>
    </text>);
    }).Render();
}
@(Html.Kendo().Window()
.Name("scheduleDatesWindow")
.Width(1000)
.Height(500)
.Title("Maintenance Schedule Dates")
.Visible(false)
.Modal(true)
.Events(e => e.Open("scheduleDatesWindowOpened"))
.Content(@<text>
    @(Html.Kendo().Grid<EquipmentItemMaintenanceScheduleModel>()
        .Name("equipmentItemMaintenanceScheduleGrid")
        .Columns(columns => {
            columns.Bound(c => c.MaintenanceBlueprintName).Title("Maintenance Blueprint").Width(150);
            columns.Bound(c => c.LastDate).Title("Last").Format(DateConstants.DateFormat).Width(125);
            columns.Bound(c => c.RecurringDays).Title("Recurring Days").Width(125).Visible(!GlobalSettings.IsWellsense);
            columns.Bound(c => c.RecurringMonths).Title("Recurring Months").Width(125).Visible(GlobalSettings.IsWellsense);
            columns.Bound(c => c.StartDate).Title("Next").ClientTemplate("#=CompanyLocationId!=null? 'N/A' : StartDateOnly #").Width(125);
        })
        .ColumnMenu(c => c.Columns(true))
        .Sortable()
        .AutoBind(false)
        .Scrollable(s => s.Height(400))
        .Resizable(resize => resize.Columns(true))
        .Reorderable(reorder => reorder.Columns(true))
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(model => {
                model.Id(m => m.EquipmentItemMaintenanceScheduleId);
            })
    .Read(read => read.Action("GetEquipmentItemMaintenanceSchedules", "Logistics").Data("equipmentItemMaintenanceScheduleData"))
        )
    ) 
    </text>)
)
    @(Html.Kendo().Window()
    .Name("maintenanceRecordWindow")
    .Width(1000)
    .Height(500)
    .Title("Maintenance Record")
    .Visible(false)
    .Modal(true)
    .Events(e => e.Open("maintenanceRecordWindowOpened"))
    .Content(@<text>
        @(Html.Kendo().Grid<MaintenanceRecordModel>()
            .Name("maintenanceRecordGrid")
            .Columns(columns => {
                columns.Bound(c => c.Number).Title("Maintenancen Record").ClientTemplate("<a href='" + @Url.Action("EditMaintenanceRecord", "Maintenance", new { @id = "" }) + "/#=MaintenanceRecordId#'>#=Number#</a>");
                columns.Bound(c => c.MaintenanceBlueprintId).Title("Maintenancen Blueprint").ClientTemplate("<a href='" + @Url.Action("EditMaintenanceBlueprint", "Admin", new { @id = "" }) + "/#=MaintenanceBlueprintId#'>#=MaintenanceBlueprintName#</a>");
                columns.Bound(c => c.EquipmentItemName).Title("Equipment Item").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#'>#=EquipmentItemName#</a>");
                columns.Bound(c => c.PriorityDescription).Title("Priority");
                columns.Bound(c => c.UserName).Title("Created By").Hidden(true);
                columns.Bound(c => c.Created).Title("Created").Format(DateConstants.DateFormat);
                columns.Bound(c => c.Modified).Title("Modified").Format(DateConstants.DateFormat).Hidden(true);
                columns.Bound(c => c.StatusDescription).Title("Status").ClientTemplate("<span class='badge' style='background:#=StatusColour#;color:#=StatustextColour#'>#=StatusDescription#</span>");
            })
            .ColumnMenu(c => c.Columns(true))
            .Events(e => e.DataBound("updatedMaintenanceRecordGrid"))
            .Filterable()
            .Sortable()
            .Groupable()
            .Scrollable(s => s.Height(532))
            .Resizable(resize => resize.Columns(true))
            .Reorderable(reorder => reorder.Columns(true))
            .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Model(model => {
                    model.Id(m => m.MaintenanceRecordId);
                })
                .Read(read => read.Action("GetMaintenanceRecordByEquipmentItemId", "Maintenance").Data("maintenanceRecordData"))
            ).AutoBind(false)
        )
    </text>)
    )
    @(Html.Kendo().Window()
        .Name("equipmenPackingListWindow")
        .Width(1000)
        .Height(500)
        .Title("Packing List ")
        .Visible(false)
        .Modal(true)
        .Events(e => e.Open("packingListWindowOpened"))
        .Content(
            Html.Kendo().Grid<EquipmentPackingListModel>()
                .Name("PendingEquipmentPackingListGrid")
                .Columns(columns => {
                    columns.Bound(c => c.Number).Title("Packing List ID");
                    columns.Bound(c => c.PackingListTitle);
                    columns.Bound(c => c.ProjectNameandObjectives).Title("Project").ClientTemplate("#if(ProjectId == null){#'N/A'#} else{# #=ProjectNameandObjectives# #}#");
                    columns.Bound(c => c.CreatedByUserName).Title("Created By").Hidden(true);
                    columns.Bound(c => c.Created).Title("Date Created").Format(DateConstants.DateFormat);
                    columns.Bound(c => c.DateRequired).Format(DateConstants.DateFormat);
                    columns.Bound(c => c.SignedOffBy).Title("Signed Off By");
                    columns.Bound(c => c.SignedOffDate).Title("Signed Off Date").Format(DateConstants.DateFormat).Hidden(true);
                    columns.Template("#if(!IsSignedOff){#<a class='btn btn-sm btn-primary' value='Select' onclick='addEquipmentPackingList(#=EquipmentPackingListId#)'>Select<a/>#}#").Width(100);
                })
                .ColumnMenu(c => c.Columns(true))
                .Sortable()
                .AutoBind(false)
                .Scrollable(s => s.Height(400))
                .Resizable(resize => resize.Columns(true))
                .Reorderable(reorder => reorder.Columns(true))
                .Events(e => e.DataBound("updatePendingEquipmentPackingListGrid"))
                    .DataSource(dataSource => dataSource
                    .Ajax()
                    .ServerOperation(false)
                    .Model(model => {
                        model.Id(m => m.EquipmentPackingListId);
                    })
                        .Read(read => read.Action("GetEquipmentPackingListsWithoutShipmentId", "Logistics"))
                    ).ToHtmlString()
            )
    )

    @(Html.Kendo().Window()
        .Name("equipmenDeSelectPackingListWindow")
        .Width(1000)
        .Height(500)
        .Title("Packing List ")
        .Visible(false)
        .Modal(true)
        .Events(e => e.Open("deSelectPackingListWindowOpened"))
        .Content(
            Html.Kendo().Grid<EquipmentPackingListModel>()
            .Name("deSelectShipmentEquipmentPackingListGrid")
            .Columns(columns => {
                columns.Bound(c => c.Number).Title("Packing List ID");
                columns.Bound(c => c.PackingListTitle);
                columns.Bound(c => c.ProjectNameandObjectives).Title("Project").ClientTemplate("#if(ProjectId == null){#'N/A'#} else{# #=ProjectNameandObjectives# #}#");
                columns.Bound(c => c.CreatedByUserName).Title("Created By").Hidden(true);
                columns.Bound(c => c.Created).Title("Date Created").Format(DateConstants.DateFormat);
                columns.Bound(c => c.DateRequired).Format(DateConstants.DateFormat);
                columns.Bound(c => c.SignedOffBy).Title("Signed Off By");
                columns.Bound(c => c.SignedOffDate).Title("Signed Off Date").Format(DateConstants.DateFormat).Hidden(true);
                columns.Template("<a class='btn btn-sm btn-primary' value='DeSelect' onclick='removeEquipmentPackingList(#=EquipmentPackingListId#)'>De-Select<a/>").Width(100);
            })
            .ColumnMenu(c => c.Columns(true))
            .Sortable()
            .AutoBind(false)
            .Scrollable(s => s.Height(400))
            .Resizable(resize => resize.Columns(true))
            .Reorderable(reorder => reorder.Columns(true))
            .Events(e => e.Edit("onError"))
            .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Model(model => {
                    model.Id(m => m.EquipmentPackingListId);
                })
                    .Read(read => read.Action("GetEquipmentPackingListsByEquimentShipment", "Logistics", new { @equipmentShipmentId = Model.EquipmentShipmentId })))
                    .ToHtmlString()
            )
    )

<script>
        const editEquipmentShipmentModel = {
            isAisus: @GlobalSettings.IsAisus.ToString().ToLower(),
            modelEquipmentShipmentStatus: "@Model.EquipmentShipmentStatus",
            equipmentShipmentStatusConstantReceived: "@EquipmentShipmentStatusConstant.Received",
            equipmentShipmentPaperworkConstantShippingDocket: "@EquipmentShipmentPaperworkConstant.ShippingDocket",
            equipmentShipmentPaperworkConstantDeliveryNote: "@EquipmentShipmentPaperworkConstant.DeliveryNote",
            paperTypeMaintSchedule:"@EquipmentShipmentPaperworkConstant.MaintenanceSchedule",
            modelEquipmentShipmentId: "@Model.EquipmentShipmentId",
            modelCreatedDate: "@(Model.CreatedDate)",
            modelIsProjectRelated: @(Model.IsProjectRelated ? "true" : "false"),
            modelEquipmentShipmentItemsHalfOrMoreReceived: @(Model.EquipmentShipmentItemsHalfOrMoreReceived ? "true" : "false"),
            modelToCompanyId: "@Model.ToCompanyId",
            modelToCompanyLocationId: "@Model.ToCompanyLocationId",
            equipmentShipmentStatusConstantInTransit: "@EquipmentShipmentStatusConstant.InTransit",
            modelProforma: "@Model.Proforma",
            modelSentDate: "@Model.SentDate",
        }


    function canEquipmentItemDocumentDelete(equipmentItem) {
        return ("@Html.IsMaintenanceAdmin()" === "True" || "@Html.IsGlobalAdmin()" === "True") || data.UserEmail === '@Html.AccountEmailAddress()';
    }
</script>

    <environment include="Development">
        <script src="~/js/views/logistics/editEquipmentShipment.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script src="~/js/views/logistics/editEquipmentShipment.min.js" asp-append-version="true"></script>
    </environment>
