﻿
<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-tint"></i>
        Fluids
        (<span data-bind="text:totalFluidTypes"></span>)
    </h4>
</div>
<hr />

<div class="grid-container">
    @(Html.Kendo().Grid<FluidTypeModel>()
        .Name("fluidTypeGrid")
        .Columns(c => {
            c.Bound(p => p.Name);
            c.Command(command => { 
                command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
            }).Width(200);                
        })
        .Editable(editable => editable.Mode(GridEditMode.InLine))
        .ToolBar(t => {
                t.Create().Text("Add Fluid Type");
                t.Excel().Text("Export");
            }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
        .Sortable()
        .Mobile(MobileMode.Auto)
        .Filterable()
        .Scrollable()
        .Resizable(c => c.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .Events(e => e.DataBound("updateFluidTypeTotal"))
        .Excel(excel => excel
            .FileName(string.Format("Centerpoint_Fluid_Types_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Admin"))
        )
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(m => m.Id(p => p.FluidTypeId))
            .Events(e => e.Error("onError"))
            .Read(read => read.Action("GetFluidTypes", "Admin"))
            .Create(create => create.Action("UpdateFluidType", "Admin"))
            .Update(update => update.Action("UpdateFluidType", "Admin"))
            .Destroy(destroy => destroy.Action("DeleteFluidType", "Admin"))
        )
    )  
</div>

    <script>

        function updateFluidTypeTotal() {
            var fluidTypeGrid = $("#fluidTypeGrid").data("kendoGrid");
            var totalFluidTypes = fluidTypeGrid.dataSource.total();
            viewModel.set("totalFluidTypes", totalFluidTypes);
        }

        function onError(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var fluidTypeGrid = $("#fluidTypeGrid").data("kendoGrid");
                fluidTypeGrid.dataSource.cancelChanges();
            }
        }

        var viewModel = new kendo.observable({
            totalFluidTypes: 0
        });

        kendo.bind(document.body.children, viewModel);
    </script>
