﻿@{
    Layout = null;
}

@model EquipmentShipmentInvoiceModel
@inject IOptions<Settings> SettingsOptions

@using Centerpoint.Common
@using Centerpoint.Model.Configuration;
@using Microsoft.Extensions.Options;
@{
    var totalNetWeightAssets = Model.EquipmentShipment.EquipmentShipmentItems != null && Model.EquipmentShipment.EquipmentShipmentItems.Any()
              ? Model.EquipmentShipment.EquipmentShipmentItems
                  .Select(item => item.EquipmentItemWeightKg)
                  .Sum()
                  .ToString("F2")
              : "0.00";
}
<!DOCTYPE html>
<html lang="en-GB">
<head>
    <base href="/" />

    <meta charset="UTF-8">
    <link rel="stylesheet" href="style/invocePDF.css" />
    <link rel="stylesheet" href="style/bootstrap.min.css" />
    <link href='http://fonts.googleapis.com/css?family=Raleway' rel='stylesheet' type='text/css'>

    <title>Document</title>
</head>
<body style="margin: 5px;">
    <div>
        <table style="width:100%">
            <tbody>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p><strong>@Model.CompanyLocationCompanyName</strong></p></td>
                </tr>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p>@Model.CompanyLocationName</p></td>
                </tr>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p> @Model.CompanyLocationHouseNumber</p></td>
                </tr>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p>@Model.CompanyLocationStreet</p></td>
                </tr>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p>@Model.CompanyLocationCity, @Model.CompanyLocationPostcode</p></td>
                </tr>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p>@Model.CompanyLocationCountry</p></td>
                </tr>
                <tr>
                    <td class="td-info td-assign"></td>
                    <td class="td-info-center td-assign"></td>
                    <td class="td-info td-assign"><p>Tel: @Model.CompanyLocationTelephone</p></td>
                </tr>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p>@(Html.Raw(!string.IsNullOrEmpty(Model.CompanyLocationComment) ? Model.CompanyLocationComment.Replace(System.Environment.NewLine, "<br />").Replace("\n", @"<br />") : string.Empty))</p></td>
                </tr>
            </tbody>
        </table>
    </div>    
    <hr />
    <div style="display: flex; justify-content: space-between;">
        <table id="info" style="width:65%">
            <tbody>
                <tr>
                    <td class="td-info title-padding"><p class="title-shipment-details">@Model.EquipmentShipment.FromTitlePDF</p></td>
                    <td class="td-info-center title-padding"><p class="title-shipment-details">@Model.EquipmentShipment.ToTitlePDF</p></td>
                </tr>
                <tr>
                    <td class="td-info"><p><strong>@Model.EquipmentShipment.FromCompanyPDF</strong></p></td>
                    <td class="td-info-center"><p><strong>@Model.EquipmentShipment.ToCompanyPDF</strong></p></td>
                </tr>
                <tr>
                    <td class="td-info"><p>@(Html.Raw(@Model.EquipmentShipment.FromCompanyLocationHousePDF.Replace(System.Environment.NewLine, "<br />").Replace("\n", @"<br />")))</p></td>
                    <td class="td-info"><p>@(Html.Raw(@Model.EquipmentShipment.ToCompanyLocationHousePDF.Replace(System.Environment.NewLine, "<br />").Replace("\n", @"<br />")))</p></td>
                </tr>
            </tbody>
        </table>

        <table id="shipment-details" style="width:30%">
            <tbody>
                <tr>
                    <td class="td-info title-padding"><p class="title-shipment-details">Shipment Details:</p></td>
                </tr>
                <tr>
                    <td class="td-info"><p><strong>@Model.PaperworkTypeDescription:</strong> @Model.EquipmentShipment.Number</p></td>
                </tr>
                <tr>
                    <td class="td-info"><p><strong>Description:</strong> @Model.EquipmentShipment.Description</p></td>
                </tr>
                <tr>
                    <td class="td-info"><p><strong>Shipment Method:</strong> @Model.EquipmentShipment.ShipmentMethodName</p></td>
                </tr>
                <tr>
                    <td class="td-info"><p><strong>Project:</strong> @Model.EquipmentShipment.ProjectName</p></td>
                </tr>
                <tr>
                    <td class="td-info"><p><strong>Date:</strong> @Model.EquipmentShipment.SentDateOnly</p></td>
                </tr>
                <tr>
                    <td class="td-info"><p><strong>Custom Status:</strong> @Model.EquipmentShipment.CustomStatus</p></td>
                </tr>
            </tbody>
        </table>
    </div>
    <hr />
   @* @{
        var totalShipmentValue = 0.0M;
    }*@
    @if (Model.EquipmentShipment.EquipmentShipmentItems != null && Model.EquipmentShipment.EquipmentShipmentItems.Any())
    {
        <div class="panel panel-default" style="border:none">
            <div class="panel-heading" style="background: #6eb6b4; color: #fff">
                <h2 class="panel-title" style="font-size:18px">Assets [@Model.EquipmentShipment.EquipmentShipmentItems.Count] </h2>
            </div>
            <br />
            <div class="panel-body" style="padding:0px;border-bottom:none">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th style="text-align:center; width:5%">Item</th>
                            <th style="text-align:center; width:5%">Quantity</th>
                            <th style="text-align:center; width:10%">Item Number</th>
                            <th style="text-align:center; width:35%">Description</th>
                            @if (GlobalSettings.IsWellsense)
                            {
                                <th style="text-align:center; width:35%">Info</th>
                                if (Model.IsNewUsed)
                                {
                                    <th style="text-align:center; width:10%">New/Used</th>
                                }
                                if (Model.IsTemproraryPermanent)
                                {
                                    <th style="text-align:center; width:10%">Temprorary/Permanent</th>
                                }
                            }
                            @if (Model.IsNetWeight)
                            {
                                <th style="text-align:center; width:10%">Net Weight (kg)</th>
                            }
                        </tr>
                    </thead>
                    <tbody>
                        @for (var i = 0; i < Model.EquipmentShipment.EquipmentShipmentItems.Count; i++)
                        {
                            <tr>
                                <td style="text-align:center; width:5%">@((i + 1).ToString())</td>
                                <td style="text-align:center; width:5%">1</td>
                                <td style="text-align:center; width:10%">@Model.EquipmentShipment.EquipmentShipmentItems[i].EquipmentItemName</td>
                                <td style="text-align:left; width:35%">@Model.EquipmentShipment.EquipmentShipmentItems[i].DescriptionName</td>
                                @if (GlobalSettings.IsWellsense)
                                {
                                    <td style="text-align:left; width:35%">@Model.EquipmentShipment.EquipmentShipmentItems[i].EquipmentItemEquipmentInfo</td>
                                    if (Model.IsNewUsed)
                                    {
                                        <td style="text-align:center; width:10%">@Model.EquipmentShipment.EquipmentShipmentItems[i].IsNewString</td>
                                    }
                                    if (Model.IsTemproraryPermanent)
                                    {
                                        <td style="text-align:center; width:10%">@Model.EquipmentShipment.EquipmentShipmentItems[i].IsPermanentString</td>
                                    }
                                }
                               
                                @if (Model.IsNetWeight)
                                {
                                    <td style="text-align:center; width:10%">@Model.EquipmentShipment.EquipmentShipmentItems[i].EquipmentItemWeightKg</td>
                                }
                            </tr>
                        }
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="4" style="text-align:right"><strong>Total Net Weight</strong></td>
                            <td style="text-align:center; width:16%">@totalNetWeightAssets</td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    }
    <br />
     @if (Model.EquipmentShipment.EquipmentShipmentNonAssetItems != null && Model.EquipmentShipment.EquipmentShipmentNonAssetItems.Any())
    {
        <div class="panel panel-default" style="border:none">
            <div class="panel-heading" style="background: #6eb6b4; color: #fff">
                <h2 class="panel-title" style="font-size:18px">Non-Assets [@Model.EquipmentShipment.EquipmentShipmentNonAssetItems.Count] </h2>
            </div>
            <br />
            <div class="panel-body" style="padding:0px;border-bottom:none">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th style="text-align:center; width:5%">Item</th>
                            <th style="text-align:center; width:5%">Quantity</th>
                            <th style="text-align:center; width:55%">Info</th>
                            @if (GlobalSettings.IsWellsense && Model.IsNonAssetNetWeight)
                            {
                                <th style="text-align:center; width:10%">Net Weight</th>
                            }
                            @if (Model.PaperworkType == EquipmentShipmentPaperworkConstant.ProformaInvoice || Model.PaperworkType == EquipmentShipmentPaperworkConstant.CustomsInvoice || Model.PaperworkType == EquipmentShipmentPaperworkConstant.CommercialInvoice)
                            {
                                <th style="text-align:center; width:10%">Unit Price</th>
                                <th style="text-align:center; width:10%">Amount</th>
                            }
                        </tr>
                    </thead>
                    <tbody>
                        @for (var i = 0; i < Model.EquipmentShipment.EquipmentShipmentNonAssetItems.Count; i++)
                        {
                            var value = 0.0M;
                            var totalValue = 0.0M;
                            <tr>
                                <td style="text-align:center ; width:5%">@((i + 1).ToString())</td>
                                <td style="text-align:center ; width:5%">@Model.EquipmentShipment.EquipmentShipmentNonAssetItems[i].Quantity</td>
                                <td style="text-align:left; width:55%">@Model.EquipmentShipment.EquipmentShipmentNonAssetItems[i].Description</td>
                                @if (GlobalSettings.IsWellsense && Model.IsNonAssetNetWeight)
                                {
                                  <td style="text-align:left; width:10%">
                                    @(Model.EquipmentShipment.EquipmentShipmentNonAssetItems[i].NetWeight.HasValue ? Model.EquipmentShipment.EquipmentShipmentNonAssetItems[i].NetWeight.Value :0)</td>
                                }

                                @if (Model.PaperworkType == EquipmentShipmentPaperworkConstant.ProformaInvoice || Model.PaperworkType == EquipmentShipmentPaperworkConstant.CustomsInvoice || Model.PaperworkType == EquipmentShipmentPaperworkConstant.CommercialInvoice)
                                {
                                    value = Math.Round(Model.EquipmentShipment.EquipmentShipmentNonAssetItems[i].UnitPricePounds / Model.CurrencyMultiple, 2);
                                    totalValue = value * (decimal)@Model.EquipmentShipment.EquipmentShipmentNonAssetItems[i].Quantity;

                                    <td style="text-align:right; width:20%">@value.ToString("n2") (@Model.CurrencyFormat)</td>
                                    <td style="text-align:right; width:10%">@totalValue.ToString("n2") (@Model.CurrencyFormat)</td>
                                }
                            </tr>
                            @*totalShipmentValue += totalValue;*@
                        }
                    </tbody>
                </table>
            </div>
        </div>
    }
    @if (Model.EquipmentShipment.EquipmentShipmentPackages != null && Model.EquipmentShipment.EquipmentShipmentPackages.Any())
    {
        <br />
        <div>
            <div class="panel-heading" style="background: #6eb6b4; color: #fff">
                <h2 class="panel-title" style="font-size:18px">Packages</h2>
            </div>
            <br />
            <div class="panel-body" style="padding:0px;border-bottom:none">
                <table class="table table-bordered packing-list-table">
                    <thead>
                        <tr>
                            <th style="text-align:center; width:17%;">Package Number / ID </th>
                            <th style="text-align:center; width:17%">Type of Package</th>
                            <th style="text-align:center; width:17%">Length (cm)</th>
                            <th style="text-align:center; width:17%">Width (cm)</th>
                            <th style="text-align:center; width:16%">Height (cm)</th>
                            <th style="text-align:center; width:16%">Weight (kg)</th>
                        </tr>
                    </thead>
                    <tbody>
                        @for (var i = 0; i < Model.EquipmentShipment.EquipmentShipmentPackages.Count; i++)
                        {
                            <tr style="height: 35px">
                                <td style="text-align:center; width:17%">@Model.EquipmentShipment.EquipmentShipmentPackages[i].PackageNumber</td>
                                <td style="text-align:center; width:17%">@Model.EquipmentShipment.EquipmentShipmentPackages[i].TypeOfPackage</td>
                                <td style="text-align:center; width:17%">@Model.EquipmentShipment.EquipmentShipmentPackages[i].Length</td>
                                <td style="text-align:center; width:17%">@Model.EquipmentShipment.EquipmentShipmentPackages[i].Width</td>
                                <td style="text-align:center; width:16%">@Model.EquipmentShipment.EquipmentShipmentPackages[i].Height</td>
                                <td style="text-align:center; width:16%">@Model.EquipmentShipment.EquipmentShipmentPackages[i].Weight</td>
                            </tr>
                        }
                    </tbody>                    
                </table>
            </div>
            <table class="table table-bordered">
                <tbody>
                    <tr>
                        <td colspan="5" style="text-align:right"><strong>Total Weight</strong></td>
                        <td style="text-align:center;width:16%">@Model.EquipmentShipment.EquipmentShipmentPackages.Select(s => s.Weight).Sum()</td>
                    </tr>
                </tbody>
            </table>
        </div>
    }
    <div style="margin-top: 30px">
        <table style="width:100%">
            <tbody>
                <tr>
                    <td style="text-align:left; width:15% "><p><strong>Delivery Method:</strong></p></td>
                    <td style="text-align:left" class="info-border"><p> @Model.EquipmentShipment.ShipmentMethodName</p></td>
                </tr>
            </tbody>
        </table>
    </div>
    <div >
        <table style="width:100%">
            <tbody>
                <tr>
                    <td style="text-align:left" class="td-sign-padding"><p><strong> Checked By: </strong></p></td>
                    <td style="text-align:left" class="td-sign-padding info-border"></td>
                    <td style="text-align:left" class="td-sign-padding"></td>
                    <td style="text-align:left" class="td-sign-padding"><p><strong> Date: </strong></p></td>
                    <td style="text-align:left" class="td-sign-padding info-border"></td>
                </tr>
                <tr>
                    <td style="text-align:left" class="td-sign-padding"><p><strong> Collected By: </strong></p></td>
                    <td style="text-align:left" class="td-sign-padding info-border"></td>
                    <td style="text-align:left" class="td-sign-padding"></td>
                    <td style="text-align:left" class="td-sign-padding"><p><strong> Date: </strong></p></td>
                    <td style="text-align:left" class="td-sign-padding info-border"></td>
                </tr>
            </tbody>
        </table>
    </div>
</body>
</html>