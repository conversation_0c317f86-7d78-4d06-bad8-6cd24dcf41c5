﻿@{
    ViewBag.Title = "Page Not Found";
}

<h1>Page not Found</h1>
<p>Sorry but the page you are looking for cannot be found. This could be due to one of the following reasons:</p>
<p> - A URL was incorrectly entered.</p>
<p> - The file no longer exists.</p>
@if (ViewBag.ReferrerUrl != null) {
<p>Go back to the <a href="@ViewBag.ReferrerUrl">previous page</a>.</p>
}

@* Error page must be longer than 512 bytes for IE and Chrome to show it. 
   So add padding in case we're short. *@
@(new string(' ', 512))