$(document).ready(function () {
    loadMaintenanceBlueprintGrid();
});

function loadMaintenanceBlueprintGrid() {
    var grid = $("#maintenanceBlueprintGrid").data("kendoGrid");
    var toolBar = $("#maintenanceBlueprintGrid .k-grid-toolbar").html();
    var options = localStorage["maintenanceBlueprintGrid"];
    viewModel.set("initialMaintenanceBlueprintGridOptions", kendo.stringify(grid.getOptions()));
    if (options) {
        grid.setOptions(JSON.parse(options));
        $("#maintenanceBlueprintGrid .k-grid-toolbar").html(toolBar);
        $("#maintenanceBlueprintGrid .k-grid-toolbar").addClass("k-grid-top");
    }
}

function saveMaintenanceBlueprintGrid(e) {
    setTimeout(function () {
        var grid = $("#maintenanceBlueprintGrid").data("kendoGrid");
        localStorage["maintenanceBlueprintGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}

function updateMaintenanceBlueprintTotal() {
    $("#resetMaintenanceBlueprintGrid").click(function (e) {
        e.preventDefault();
        resetGridView('maintenanceBlueprintGrid', 'initialMaintenanceBlueprintGridOptions')
    });

    var grid = $("#maintenanceBlueprintGrid").data("kendoGrid");
    var totalBlueprints = grid.dataSource.total();
    viewModel.set("totalBlueprints", totalBlueprints);
}

var viewModel = new kendo.observable({
    totalBlueprints: 0,
});

kendo.bind(document.body.children, viewModel);