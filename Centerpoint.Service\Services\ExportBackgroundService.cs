using Centerpoint.Common.Constants;
using Centerpoint.DataAccess.Persistence;
using Centerpoint.Service.Interfaces;
using Centerpoint.Storage.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.IO.Compression;

namespace Centerpoint.Service.Services
{

    public interface IExportBackgroundService
    {
        Task ProcessAllEquipmentExportAsync(string jobId, int userId);
        Task ProcessFilteredEquipmentExportAsync(string jobId, int userId, int[] equipmentItemIds);
        Task<ExportJobStatus> GetExportJobStatusAsync(string jobId);
    }

    public class ExportJobStatus
    {
        public string JobId { get; set; }
        public string Status { get; set; } // "Processing", "Completed", "Failed"
        public int TotalItems { get; set; }
        public int ProcessedItems { get; set; }
        public string ErrorMessage { get; set; }
        public string DownloadUrl { get; set; }
        public string BlobFileName { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public int ProgressPercentage => TotalItems > 0 ? (int)((double)ProcessedItems / TotalItems * 100) : 0;
    }

    public class ExportBackgroundService : IExportBackgroundService
    {
        private readonly IAssetService _assetService;
        private readonly IStorage _storage;
        private readonly ILogger<ExportBackgroundService> _logger;
        private readonly DataContext _context;
        private static readonly Dictionary<string, ExportJobStatus> _jobStatuses = new();

        public ExportBackgroundService(
            IAssetService assetService,
            IStorage storage,
            ILogger<ExportBackgroundService> logger,
            DataContext context)
        {
            _assetService = assetService;
            _storage = storage;
            _logger = logger;
            _context = context;
        }

        public async Task ProcessAllEquipmentExportAsync(string jobId, int userId)
        {
            var jobStatus = new ExportJobStatus
            {
                JobId = jobId,
                Status = "Processing",
                CreatedAt = DateTime.UtcNow
            };

            _jobStatuses[jobId] = jobStatus;

            try
            {
                _logger.LogInformation($"Starting export job {jobId} for user {userId}");

                // Get equipment items count first
                var equipmentItems = await _context.EquipmentItems
                    .Include(e => e.EquipmentCategory)
                    .Where(e => e.Status != EquipmentConstant.Archived)
                    .Select(e => new
                    {
                        EquipmentItemId = e.EquipmentItemId,
                        EquipmentNumber = e.EquipmentNumber,
                        EquipmentCategoryId = e.EquipmentCategoryId,
                        CategoryName = e.EquipmentCategory != null ? e.EquipmentCategory.Name : "Uncategorized"
                    })
                    .ToListAsync();

                jobStatus.TotalItems = equipmentItems.Count;
                _logger.LogInformation($"Found {equipmentItems.Count} equipment items to export");

                if (!equipmentItems.Any())
                {
                    jobStatus.Status = "Failed";
                    jobStatus.ErrorMessage = "No equipment items found to export";
                    return;
                }

                // Create temporary file for the archive
                var tempFileName = $"export_{jobId}_{DateTime.UtcNow:yyyyMMddHHmmss}.zip";
                var tempFilePath = Path.Combine(Path.GetTempPath(), tempFileName);

                using (var fileStream = new FileStream(tempFilePath, FileMode.Create))
                using (var zip = new ZipArchive(fileStream, ZipArchiveMode.Create))
                {
                    int processedCount = 0;

                    foreach (var equipmentItem in equipmentItems)
                    {
                        try
                        {
                            _logger.LogDebug($"Processing equipment item {equipmentItem.EquipmentItemId}");

                            // Get detailed data for each equipment item - use the existing archive method
                            var archiveBytes = await _assetService.GenerateEquipmentItemArchiveAsync(equipmentItem.EquipmentItemId);

                            if (archiveBytes != null && archiveBytes.Length > 0)
                            {
                                // Create folder structure: Category/Equipment Item
                                var categoryFolderName = SanitizeFileName(equipmentItem.CategoryName);
                                var equipmentFolderName = $"{categoryFolderName}/{SanitizeFileName(equipmentItem.EquipmentNumber)}";

                                // Extract the individual equipment archive into our main archive
                                using var individualArchiveStream = new MemoryStream(archiveBytes);
                                using var individualZip = new ZipArchive(individualArchiveStream, ZipArchiveMode.Read);

                                foreach (var entry in individualZip.Entries)
                                {
                                    var newEntryName = $"{equipmentFolderName}/{entry.FullName}";
                                    var newEntry = zip.CreateEntry(newEntryName, CompressionLevel.Fastest);

                                    using var originalStream = entry.Open();
                                    using var newStream = newEntry.Open();
                                    await originalStream.CopyToAsync(newStream);
                                }
                            }

                            processedCount++;
                            jobStatus.ProcessedItems = processedCount;

                            // Log progress every 50 items
                            if (processedCount % 50 == 0)
                            {
                                _logger.LogInformation($"Export job {jobId}: Processed {processedCount}/{equipmentItems.Count} items ({jobStatus.ProgressPercentage}%)");
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"Error processing equipment item {equipmentItem.EquipmentItemId} in job {jobId}");
                            // Continue with other items
                        }
                    }
                }

                // Upload the completed archive to blob storage
                var blobFileName = $"exports/all_equipment_export_{jobId}_{DateTime.UtcNow:yyyyMMddHHmmss}.zip";

                using (var fileStream = new FileStream(tempFilePath, FileMode.Open))
                {
                    var blobClient = await _storage.GetBlobClient(blobFileName);
                    await blobClient.UploadAsync(fileStream, overwrite: true);
                }

                // Clean up temp file
                File.Delete(tempFilePath);

                // Update job status
                jobStatus.Status = "Completed";
                jobStatus.CompletedAt = DateTime.UtcNow;
                jobStatus.BlobFileName = blobFileName;
                jobStatus.DownloadUrl = $"/Admin/DownloadExport?jobId={jobId}";

                _logger.LogInformation($"Export job {jobId} completed successfully. Processed {jobStatus.ProcessedItems} items.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Export job {jobId} failed");
                jobStatus.Status = "Failed";
                jobStatus.ErrorMessage = ex.Message;
            }
        }

        public async Task ProcessFilteredEquipmentExportAsync(string jobId, int userId, int[] equipmentItemIds)
        {
            var jobStatus = new ExportJobStatus
            {
                JobId = jobId,
                Status = "Processing",
                CreatedAt = DateTime.UtcNow
            };

            _jobStatuses[jobId] = jobStatus;

            try
            {
                _logger.LogInformation($"Starting filtered export job {jobId} for user {userId} with {equipmentItemIds.Length} items");

                // Get equipment items based on provided IDs
                var equipmentItems = await _context.EquipmentItems
                    .Include(e => e.EquipmentCategory)
                    .Where(e => equipmentItemIds.Contains(e.EquipmentItemId) && e.Status != EquipmentConstant.Archived)
                    .Select(e => new
                    {
                        EquipmentItemId = e.EquipmentItemId,
                        EquipmentNumber = e.EquipmentNumber,
                        EquipmentCategoryId = e.EquipmentCategoryId,
                        CategoryName = e.EquipmentCategory != null ? e.EquipmentCategory.Name : "Uncategorized"
                    })
                    .ToListAsync();

                jobStatus.TotalItems = equipmentItems.Count;
                _logger.LogInformation($"Found {equipmentItems.Count} filtered equipment items to export");

                if (!equipmentItems.Any())
                {
                    jobStatus.Status = "Failed";
                    jobStatus.ErrorMessage = "No equipment items found to export";
                    return;
                }

                // Create temporary file for the archive
                var tempFileName = $"filtered_export_{jobId}_{DateTime.UtcNow:yyyyMMddHHmmss}.zip";
                var tempFilePath = Path.Combine(Path.GetTempPath(), tempFileName);

                using (var fileStream = new FileStream(tempFilePath, FileMode.Create))
                using (var zip = new ZipArchive(fileStream, ZipArchiveMode.Create))
                {
                    int processedCount = 0;

                    foreach (var equipmentItem in equipmentItems)
                    {
                        try
                        {
                            _logger.LogDebug($"Processing equipment item {equipmentItem.EquipmentItemId}");

                            // Get detailed data for each equipment item - use the existing archive method
                            var archiveBytes = await _assetService.GenerateEquipmentItemArchiveAsync(equipmentItem.EquipmentItemId);

                            if (archiveBytes != null && archiveBytes.Length > 0)
                            {
                                // Create folder structure: Category/Equipment Item
                                var categoryFolderName = SanitizeFileName(equipmentItem.CategoryName);
                                var equipmentFolderName = $"{categoryFolderName}/{SanitizeFileName(equipmentItem.EquipmentNumber)}";

                                // Extract the individual equipment archive into our main archive
                                using var individualArchiveStream = new MemoryStream(archiveBytes);
                                using var individualZip = new ZipArchive(individualArchiveStream, ZipArchiveMode.Read);

                                foreach (var entry in individualZip.Entries)
                                {
                                    var newEntryName = $"{equipmentFolderName}/{entry.FullName}";
                                    var newEntry = zip.CreateEntry(newEntryName, CompressionLevel.Fastest);

                                    using var originalStream = entry.Open();
                                    using var newStream = newEntry.Open();
                                    await originalStream.CopyToAsync(newStream);
                                }
                            }

                            processedCount++;
                            jobStatus.ProcessedItems = processedCount;

                            // Log progress every 10 items for filtered exports (smaller batches)
                            if (processedCount % 10 == 0)
                            {
                                _logger.LogInformation($"Filtered export job {jobId}: Processed {processedCount}/{equipmentItems.Count} items ({jobStatus.ProgressPercentage}%)");
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"Error processing equipment item {equipmentItem.EquipmentItemId} in filtered job {jobId}");
                            // Continue with other items
                        }
                    }
                }

                // Upload the completed archive to blob storage
                var blobFileName = $"exports/filtered_equipment_export_{jobId}_{DateTime.UtcNow:yyyyMMddHHmmss}.zip";

                using (var fileStream = new FileStream(tempFilePath, FileMode.Open))
                {
                    var blobClient = await _storage.GetBlobClient(blobFileName);
                    await blobClient.UploadAsync(fileStream, overwrite: true);
                }

                // Clean up temp file
                File.Delete(tempFilePath);

                // Update job status
                jobStatus.Status = "Completed";
                jobStatus.CompletedAt = DateTime.UtcNow;
                jobStatus.BlobFileName = blobFileName;
                jobStatus.DownloadUrl = $"/Admin/DownloadExport?jobId={jobId}";

                _logger.LogInformation($"Filtered export job {jobId} completed successfully. Processed {jobStatus.ProcessedItems} items.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Filtered export job {jobId} failed");
                jobStatus.Status = "Failed";
                jobStatus.ErrorMessage = ex.Message;
            }
        }

        public async Task<ExportJobStatus> GetExportJobStatusAsync(string jobId)
        {
            return await Task.FromResult(_jobStatuses.TryGetValue(jobId, out var status) ? status : null);
        }

        private string SanitizeFileName(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                return "Unknown";

            var invalidChars = Path.GetInvalidFileNameChars();
            return string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
        }


    }
}
