﻿@model OpportunityModel

<div class="card">
    <div class="card-header">
        <h6 class="m-0">General (<span data-bind="text:totalOpportunityDocuments"></span>)</h6>
    </div>
    <div class="card-body">
        @if (Model.Stage != OpportunityStageConstant.Closed) {
            <p>Click the link below to attach documents</p>
            @(Html.Kendo().Upload()
                .Name("opportunityDocuments")
                .Messages(m => m.Select("Attach Documents"))
                .Multiple(true)
                .Events(e => e
                    .Success("onOpportunityDocumentAttached")
                    .Complete("onOpportunityDocumentComplete")
                    .Upload("onOpportunityDocumentUpload")
                )
                .Async(async => async.Save("AttachOpportunityDocuments", "Sales", new { @id = Model.LinkOpportunityId }).Batch(true))
            )
        }

        @(Html.Kendo().Grid<DocumentModel>()
            .Name("opportunityDocumentsGrid")
            .Columns(c => {
                c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
                c.Bound(p => p.UserName).Title("Created By");
                if(Model.Stage != OpportunityStageConstant.Closed) {
                    c.Command(command => { 
                        command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
                    }).Width(200);
                }
            })
            .Events(e => e.DataBound("updateOpportunityDocumentsGrid"))
            .Sortable()
            .ColumnMenu(c => c.Columns(true))
            .Resizable(r => r.Columns(true))
            .Filterable()
            .Groupable()
            .Editable(e => e.Mode(GridEditMode.InLine))
            .Scrollable(s => s.Height(300))
            .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Model(model => model.Id(p => p.DocumentId))
                .Read(read => read
                    .Action("GetOpportunityDocuments", "Sales", new { @opportunityId = Model.LinkOpportunityId })
                )
                .Destroy(destroy => destroy
                    .Action("DeleteOpportunityDocument", "Sales", new { @opportunityId = Model.LinkOpportunityId })
                )
            )
        )


    </div>
</div>
<div class="card mt-2" data-bind="visible:totalOpportunityEventDocuments">
    <div class="card-header">
        <h6 class="mb-0">Events (<span data-bind="text:totalOpportunityEventDocuments"></span>)</h6>
    </div>
    <div class="card-body">
        @(Html.Kendo().Grid<OpportunityEventDocumentModel>()
            .Name("opportunityEventDocumentsGrid")
            .Columns(c => {
                c.Bound(p => p.Event).Title("Event").ClientGroupHeaderTemplate("Event : #= value # (#= count#)").ClientTemplate("<a href='" + @Url.Action("EditEvent", "Sales", new { @id = "" }) + "/#=OpportunityEventId#?revision=" + ViewBag.Revision + "'>#=Event#</a>").Width(200);
                c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateFormat);
                c.Bound(p => p.Username).Title("Created By");
                if(Model.Stage != OpportunityStageConstant.Closed) {
                    c.Command(command => { 
                        command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
                    }).Width(200);  
                }
            })
            .Events(e => e.DataBound("updateOpportunityEventDocumentsGrid"))
            .Sortable()
            .Resizable(r => r.Columns(true))
            .ColumnMenu(c => c.Columns(true))
            .Editable(e => e.Mode(GridEditMode.InLine))
            .Filterable()
            .Groupable()
            .Scrollable(s => s.Height(300))
            .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Aggregates(aggregates => {
                    aggregates.Add(p => p.Event).Min().Max().Count();
                })
                .Group(group => group.Add(p => p.Event))
                .Model(model => model.Id(p => p.DocumentId))
                .Read(read => read
                    .Action("GetOpportunityEventDocumentsByOpportunityId", "Sales", new { @opportunityId = Model.LinkOpportunityId })
                )
                .Destroy(destroy => destroy
                    .Action("DeleteOpportunityEventDocumentByOpportunityId", "Sales", new { @opportunityId = Model.LinkOpportunityId })
                )
            )
        )
    </div>
</div>
<div class="card mt-2"  data-bind="visible:totalOpportunityActionDocuments">
    <div class="card-header">
        <h6 class="mb-0">Actions (<span data-bind="text:totalOpportunityActionDocuments"></span>)</h6>
    </div>
    <div class="card-body">
        @(Html.Kendo().Grid<OpportunityActionDocumentModel>()
            .Name("opportunityActionDocumentsGrid")
            .Columns(c => {
                c.Bound(p => p.Action).Title("Action").ClientGroupHeaderTemplate("Action : #= value # (#= count#)").ClientTemplate("<a href='" + @Url.Action("EditAction", "Sales", new { @id = "" }) + "/#=OpportunityActionId#?revision=" + ViewBag.Revision + "'>#=Action#</a>").Width(200);
                c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateFormat);
                c.Bound(p => p.Username).Title("Created By");
                if(Model.Stage != OpportunityStageConstant.Closed) {
                    c.Command(command => { 
                        command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
                    }).Width(200);
                }
            })
            .Events(e => e.DataBound("updateOpportunityActionDocumentsGrid"))
            .Editable(e => e.Mode(GridEditMode.InLine))
            .Sortable()
            .Resizable(r => r.Columns(true))
            .ColumnMenu(c => c.Columns(true))
            .Filterable()
            .Groupable()
            .Scrollable(s => s.Height(300))
            .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Aggregates(aggregates => {
                    aggregates.Add(p => p.Action).Min().Max().Count();
                })
                .Group(group => group.Add(p => p.Action))
                .Model(model => model.Id(p => p.DocumentId))
                .Read(read => read
                    .Action("GetOpportunityActionDocumentsByOpportunityId", "Sales", new { @opportunityId = Model.LinkOpportunityId })
                )
                .Destroy(destroy => destroy
                    .Action("DeleteOpportunityActionDocumentByOpportunityId", "Sales", new { @opportunityId = Model.LinkOpportunityId })
                )
            )
        )
    </div>
</div>
@if (Model.Type == OpportunityTypeConstant.Opportunity) {
    <div class="card mt-2"  data-bind="visible:totalOpportunityWellDocuments">
        <div class="card-header">
            <h6 class="mb-0">Well (<span data-bind="text:totalOpportunityWellDocuments"></span>)</h6>
        </div>
        <div class="card-body">
            @(Html.Kendo().Grid<CompanyWellDocumentModel>()
                .Name("wellDocumentsGrid")
                .Columns(c => {
                    c.Bound(p => p.CompanyWellName).Title("Well").ClientGroupHeaderTemplate("Well : #= value # (#= count#)").Width(200);
                    c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                    c.Bound(p => p.CompanyWellDocumentTypeDescription).Title("Type").ClientTemplate("#=CompanyWellDocumentTypeDescription ? CompanyWellDocumentTypeDescription : 'N/A'#");
                    c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateFormat);
                    c.Bound(p => p.Username).Title("Created By");
                    if(Model.Stage != OpportunityStageConstant.Closed) {
                        c.Command(command => { 
                            command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
                        }).Width(200); 
                    }
                })
                .Events(e => e.DataBound("updateWellDocumentsGrid"))
                .Sortable()
                .Resizable(r => r.Columns(true))
                .ColumnMenu(c => c.Columns(true))
                .Filterable()
                .Editable(e => e.Mode(GridEditMode.InLine))
                .Groupable()
                .Scrollable(s => s.Height(300))
                .DataSource(dataSource => dataSource
                    .Ajax()
                    .ServerOperation(false)
                    .Aggregates(aggregates => {
                        aggregates.Add(p => p.CompanyWellName).Min().Max().Count();
                    })
                    .Group(group => group.Add(p => p.CompanyWellName))
                    .Model(model => model.Id(p => p.CompanyWellDocumentId))
                    .Read(read => read.
                        Action("GetCompanyWellDocuments", "Sales").Data("companyFieldData")
                    )
                    .Destroy(destroy => destroy
                        .Action("DeleteCompanyWellDocument", "Admin")
                    )
                )
            )
        </div>
    </div>
}