﻿@model UserModel
@{
    var cardName = GlobalSettings.IsWellsense ? "SOC" : "RISC";
}
<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-home"></i>
        Home
    </h4>
</div>
<hr />
@{
    Html.Kendo().TabStrip()
    .Name("mainTabStrip")
    .SelectedIndex(0)
    .Animation(animation =>
    {
        animation.Enable(false);
    })
    .Items(tabstrip =>
        {
            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind = "html:tabStripHeaderHome" })
                .Selected(true)
                .Content(@<text>
        <partial name='MyHome' model=Model />
    </text>);

            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind = "html:tabStripHeaderMyProjects" })
                .Content(@<text>
        <partial name='MyProjects' model=Model />
    </text>);

            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind = "html:tabStripHeaderMyDetails", @id = "personnel" })
                .Content(@<text>
        @using (Html.BeginForm("EditUser", "Home", FormMethod.Post, new { @name = "home", @id = "HomeEditUserForm" })) {
        @Html.ValidationSummary(false)

        <partial name='MyDetails' model=Model />

        @Html.HiddenFor(m => m.UserId)
        @Html.HiddenFor(m => m.IsEnabled)
        @Html.HiddenFor(m => m.MobileNotification)
        @Html.HiddenFor(m => m.EmailNotification)
        @Html.HiddenFor(m => m.Roles)
    }
    </text>);

            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind = "html:totalCertificatesText" })
                .Content(@<text>
        <partial name='MyCertificates' model=Model />
    </text>);

            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind = "html:totalSifOpenText" })
                .Content(@<text>
        <partial name='MySifs' model=Model />
    </text>);

            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind = "html:totalCreatedMonthText" })
                .Content(@<text>
        <partial name='MyRiscs' model=Model />
    </text>);

            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind = "html:totalMyLessonsText" })
                .Content(@<text>
        <partial name='MyLessons' model=Model />
    </text>);
        }
    ).Render();
}

@(Html.Kendo().Window().Name("personnelCertificateAttachmentWindow")
 .Title("Email Personnel Certificate")
 .Content(@<text> <partial name="EmailPersonnelCertificate" /> </text>)
 .Width(400)
 .Modal(true)
 .Visible(false)
)

<script>
    const projectModel = {
        mobileNotification: @Model.MobileNotification.ToString().ToLower(),
        emailNotification: @Model.EmailNotification.ToString().ToLower(),
        cardName: '@cardName',
        }
</script>

<environment include="Development">
    <script src="~/js/views/home/<USER>" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/home/<USER>" asp-append-version="true"></script>
</environment>