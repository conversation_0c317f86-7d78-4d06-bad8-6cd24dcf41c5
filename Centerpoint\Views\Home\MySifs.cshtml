﻿@model UserModel

<div class="container-fluid pl-0">
<div class="row">
    <div class="col-md-5">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Summary</h6>
            </div>
            <div class="card-body">
                <div class="card-list-item">
                    <span class="card-list-item-name">Open</span>
                    <a href="#myServiceImprovementGrid" class="card-list-item-count" style="background: #000000" data-bind="click:openClick, text:totalSifOpen" title="Click here to show All SIF's Open">0</a>
                </div>
                <div class="card-list-item">
                    <span class="card-list-item-name">On Target</span>
                    <a href="#myServiceImprovementGrid" class="card-list-item-count" style="background: #999999" data-bind="click:onTargetClick, text:totalSifOnTarget" title="Click here to show Total on target">0</a>
                </div>
                <div class="card-list-item">
                    <span class="card-list-item-name">Overdue</span>
                    <a href="#myServiceImprovementGrid" class="card-list-item-count" style="background: #DB8C8C" data-bind="click:overdueClick, text:totalSifOverdue" title="Click here to show Total overdue">0</a>
                </div>
                <div class="card-list-item">
                    <span class="card-list-item-name">Closed</span>
                    <a href="#myServiceImprovementGrid" class="card-list-item-count" style="background: #DB8C8C" data-bind="click:closedClick,text:totalSifClosed" title="Click here to show Total closed">0</a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-7 chart">
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0">SIF by Category</h6>
        </div>
        <div class="card-body">
            @(Html.Kendo().Chart<ChartModel>()
                .Name("sifChart")
                .Series(c => { c.Bar(p => p.Count).ColorField("Colour"); })
                .ValueAxis(v => v.Numeric().Title("SIFs").MajorUnit(1).MajorGridLines(l => l.Visible(false)).MinorGridLines(l => l.Visible(false)))
                .Legend(l => l.Visible(false))
                .AutoBind(false)
                .CategoryAxis(c => c.Categories(p => p.Category))
                .Tooltip(t => t.Visible(true).Template("#=value#"))
                .HtmlAttributes(new { @style = "height:280px; border:none" })
               .DataSource(dataSource => dataSource.Read(read => read.Action("GetSifStatusChartData", "Home").Data("sifData"))))
        </div>
    </div>
</div>
    </div>
<hr />
     @(Html.Kendo().Grid<ServiceImprovementModel>()
    .Name("myServiceImprovementGrid")
    .Columns(columns => {
        columns.Bound(c => c.Name).Title("SIF ID").ClientTemplate("<a href='" + @Url.Action("EditServiceImprovement", "Qhse") + "/#=ServiceImprovementId#'>#=Name#</a>");
        columns.Bound(p => p.CreatedDate).Format(DateConstants.DateFormat);
        columns.Bound(c => c.CreatedByUserId).Title("Raised By").ClientTemplate("#=CreatedByUserName#").Hidden(true);
        columns.Bound(c => c.BaseCompanyLocationName).Title("Base").Hidden(true);
        columns.Bound(c => c.SifTitle).Title("Title").Hidden(true);
            columns.Bound(c => c.CorrectiveActionTargetDate).Title("Corrective Action Due Date").Format(DateConstants.DateFormat);
            columns.Bound(c => c.InvestigatorUserId).Title("Investigation Party").Format(DateConstants.DateFormat).Hidden(true);
            columns.Bound(c => c.InvestigationTargetDate).Title("Investigation Due Date").Format(DateConstants.DateFormat);
        columns.Bound(c => c.PreventiveActionUserId).Title("Action Party").ClientTemplate("#=PreventiveActionUserName == null ? '' :PreventiveActionUserName #").Hidden(true);
            columns.Bound(c => c.PreventiveActionTargetDate).Title("Preventive Action Due Date").Format(DateConstants.DateFormat);
        columns.Bound(c => c.SignOffBy).Title("Sign-off By").Hidden(true);
            columns.Bound(c => c.SignOffDate).Title("Sign-off Date").Format(DateConstants.DateTimeFormat).Hidden(true);
        columns.Bound(c => c.ServiceImprovementStatusDescription).Title("Status");
        columns.Bound(c => c.SifCategoryDescription).Title("Category");
    })
    .Scrollable(s => s.Height("auto"))
    .ToolBar(t => {
        t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetMyServiceImprovementGrid", @class="bg-danger text-white"});
        t.Excel().Text("Export");
    }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
    .Groupable()
    .ColumnMenu(c => c.Columns(true))
    .Resizable(r => r.Columns(true))
    .Reorderable(r => r.Columns(true))
    .Events(e => e.DataBound("updateServiceImprovementGrid"))
    .Events(e => e
        .ColumnReorder("saveSifGrid")
        .ColumnResize("saveSifGrid")
        .ColumnShow("saveSifGrid")
        .ColumnHide("saveSifGrid")
    )
    .Excel(excel => excel
        .FileName(string.Format("Centerpoint_Service_Improvement_Form_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
        .Filterable(true)
        .ProxyURL(Url.Action("Export", "Qhse")))
    .DataSource(dataSource => dataSource
    .Ajax()
    .ServerOperation(false)
    .Model(model => {
        model.Id(m => m.ServiceImprovementId);
    })
    .Read(read => read.Action("GetMyServiceImprovements", "Home").Data("sifData"))))
</div>

