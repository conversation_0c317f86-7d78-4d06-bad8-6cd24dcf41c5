﻿using Centerpoint.Model.ViewModels;
using Microsoft.AspNetCore.Mvc;

namespace Centerpoint.Extensions
{
    public static class ControllerExtensions
    {
        #region Set Title

        public static void SetTitle<T>(this T controller, string title = null)
           where T : Controller
        {

            controller.ViewBag.Title =
                string.IsNullOrEmpty(title) ? "Centerpoint" : string.Format("{0} | Centerpoint", title);
        }

        #endregion

        #region Set Message

        public static void SetMessage<T>(this T controller, MessageType type, string message) where T : Controller
        {

            if (!IsMessageSet(controller))
            {
                controller.TempData["MessageType"] = type;
                controller.TempData["Message"] = message;
            }
        }

        public static bool IsMessageSet<T>(this T controller) where T : Controller
        {
            return controller.TempData["Message"] != null;
        }

        #endregion 

        #region File To Byte Array


        public static byte[] FileToByteArray<T>(this T controller, IFormFile file) where T : Controller
        {
            MemoryStream stream = new MemoryStream();
            file.CopyTo(stream);
            return stream.ToArray();
        }

        public static string UploadedFileToBase64String<T>(this T controller, IFormFile file) where T : Controller
        {
            var data = controller.FileToByteArray(file);
            return string.Format("data:{0};base64,{1}", file.ContentType, Convert.ToBase64String(data));
        }

        #endregion
    }
}
