﻿namespace Centerpoint.Common.Constants
{
    public static class MaximumPressureUnitsConstant
    {

        public const string PSI = "PSI";
        public const string BAR = "bar";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {PSI,"PSI"},
                    {BAR,"bar"},
                };
            }
        }
    }
}
