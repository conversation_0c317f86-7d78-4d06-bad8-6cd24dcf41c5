﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Centerpoint.DataAccess.Migrations
{
    /// <inheritdoc />
    public partial class ShipmentAdditionalProperty : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AdditionalProperties",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Type = table.Column<int>(type: "int", nullable: false),
                    EntityId = table.Column<int>(type: "int", nullable: false),
                    PropertyName = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    PropertyValue = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AdditionalProperties", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AdditionalProperties_Type_EntityId_PropertyName",
                table: "AdditionalProperties",
                columns: new[] { "Type", "EntityId", "PropertyName" },
                unique: true,
                filter: "[PropertyName] IS NOT NULL");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AdditionalProperties");
        }
    }
}
