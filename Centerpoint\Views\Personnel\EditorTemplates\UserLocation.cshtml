﻿@model UserLocationModel
<div>
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label>Location</label>
                <br />
                @(Html.Kendo().TextBoxFor(m => m.Location)
                 .HtmlAttributes(new { @class = "form-control", @style = "width:100%", tabindex = 1 }))
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label>Action</label>
                <br />
                @(Html.Kendo().DropDownListFor(m => m.Action)
                    .Filter(FilterType.Contains)
                    .OptionLabel("Select")
                    .BindTo(new List<SelectListItem>() {
                     new SelectListItem() {
                         Text = "Arrived",
                         Value = "Arrived"
                     },
                     new SelectListItem() {
                         Text = "Departed",
                         Value = "Departed"
                     },
                      new SelectListItem() {
                         Text = "Checked In",
                         Value = "Check"
                     },
                 })
                  .HtmlAttributes(new { style = "width: 100%", tabindex = 2 }))
            </div>
        </div>
    </div>
</div>
