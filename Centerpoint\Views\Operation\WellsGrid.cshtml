
@(Html.<PERSON>().Grid<CompanyWellModel>()
    .Name("companyWellGrid")
    .Columns(c => {
        c.Bound(p=>p.Name).Title("Name").ClientTemplate("<a href='" + @Url.Action("EditCompanyWell", "Admin", new { @id = "" }) + "/#=CompanyWellId#?projectId=" + Model.ProjectId + "'>#=Name#</a>");
        c.Bound(p => p.CompanyFieldId).Title("Field").ClientTemplate("#=CompanyFieldName#");
        c.Bound(p => p.MinimumId);
        c.<PERSON>(p => p.MaximumDeviation);
        c.<PERSON>und(p => p.TotalAttachments).ClientTemplate("#if(TotalAttachments){#<a class='badge badge-primary text-white' href='\\#' onclick='wellAttachmentCount(#=CompanyWellId#)'>#=TotalAttachments#</a>#} else {##=TotalAttachments##}#"); ;
        c.<PERSON>und(p => p.MaximumPressure).Hidden(true);
        c.Bound(p => p.MaximumPressureUnits).Hidden(true);
        c.Bound(p => p.MaximumTemperature).Hidden(true);
        c.Bound(p => p.MaximumTemperatureDegrees).Hidden(true);
        c.Bound(p => p.H2S).Hidden(true);
        c.Bound(p => p.CO2).Hidden(true);
        c.Bound(p => p.FluidTypes).Hidden(true);
    })
    .Sortable()
    .Filterable()
    .Scrollable(s => s.Height(500))
    .Resizable(c => c.Columns(true))
    .ColumnMenu(c => c.Columns(true))
    .Events(e => e.DataBound("updateCompanyWellTotal"))
    .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Model(model => {
            model.Id(m => m.CompanyWellId);
        })
        .Read(read => read.Action("GetCompanyWells", "Sales").Data("companyFieldData"))
    )
)