function toggleSidebar() {
  const headerElement = document.querySelector('.body-wrapper');
  headerElement.dataset.sidebarOpen = !(headerElement.dataset.sidebarOpen === 'true');
}

function startTime() {
  const today = new Date();
  let h = today.getHours();
  let m = today.getMinutes();
  let s = today.getSeconds();
  m = checkTime(m);
  s = checkTime(s);
  document.getElementById('headerTime').innerHTML =  h + ":" + m + ":" + s;
  setTimeout(startTime, 1000);
}

function checkTime(i) {
  if (i < 10) {i = "0" + i};  // add zero in front of numbers < 10
  return i;
}

function setHeaderDate() {
  document.getElementById("headerDate").innerHTML= kendo.toString(new Date(), 'dd-MMM-yyyy');
}

function getEmployeeOnSiteCount(){
  $.ajax({
    url:"/operation/EmployeeOnSiteCount",
    success: (e)=> {
      document.getElementById("employeeOnSiteCountWrapper").innerHTML = e ?? 0
    }
  })
}

function validateForm(e){
  e.preventDefault();
  if($(e.target).parent('form').kendoValidator().data('kendoValidator').validate()){
    $(e.target).parent('form').submit();
  }
}

function categoriesLoaded() {
  var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
  var selectedEquipmentCategoryCookie = $.cookie('equipmentCategory');

   if (selectedEquipmentCategoryCookie) {
      setTimeout( () => {
        var historyDataItem = equipmentCategoryTreeView.dataSource.get(selectedEquipmentCategoryCookie);
        var historyElement = equipmentCategoryTreeView.findByUid(historyDataItem?.uid);
        if(historyElement) {
          equipmentCategoryTreeView.expandTo(equipmentCategoryTreeView.dataItem(historyElement));
          equipmentCategoryTreeView.select(historyElement);
        }   
      })   
  }
}

function resetGridView (gridName, initialGridOptions) {
  var grid = $(`#${gridName}`).data("kendoGrid");
  localStorage[`${gridName}`] = "";
  grid.dataSource.sort({});
  grid.setOptions(JSON.parse(viewModel.get(`${initialGridOptions}`)));
}
function resetCustomGridView(gridName, localStorageName, initialGridOptions) {
    var grid = $(`#${gridName}`).data("kendoGrid");
    localStorage[`${localStorageName}`] = "";
    grid.dataSource.sort({});
    grid.setOptions(JSON.parse(viewModel.get(`${initialGridOptions}`)));
}

function scrolToFirstError() {
  $('html, body').animate({
    scrollTop: $(".k-invalid").get(0).scrollIntoView({block: "center",behavior: 'smooth'})
  }, 2000);
}

function preventSubmitOnEnter() {
  $(document).on("keydown", ":input:not(textarea)", function(event) {
    if (event.key == "Enter") {
        event.preventDefault();
    }
  });
}

function correctAllTimes() {
    var dateTimePickers = document.querySelectorAll('.utcTimePicker');
    dateTimePickers.forEach(function (picker) {
        if (picker.value && $(picker).data("kendoDateTimePicker"))
            $(picker).data("kendoDateTimePicker").value(parseDateFromString(picker.value));
        else if ($(picker).text())
            $(picker).text(kendo.toString(new Date(parseDateFromString($(picker).text()).getTime()), 'dd/MM/yyyy HH:mm'));
    });
}

function parseDateFromString(dateStr) {
    const [_, day, month, year, hours = '0', minutes = '0', seconds = '0'] = dateStr.match(/^(\d{2})[-\/](\d{2})[-\/](\d{4})(?: (\d{2}):(\d{2})(?::(\d{2}))?)?$/) || [];
    var date = Date.UTC(parseInt(year, 10), parseInt(month, 10) - 1, parseInt(day, 10), parseInt(hours, 10), parseInt(minutes, 10), parseInt(seconds, 10));
    return !isNaN(date) ? new Date(date) : new Date(Date.parse(dateStr + "Z"));
}

function setCorrectDateTime(serializedArray) {
    var dateTimePickers = document.querySelectorAll('.utcTimePicker input');
    dateTimePickers.forEach(function (picker) {
        let datePicker = $(`#${picker.getAttribute('Name')}`).data('kendoDateTimePicker');

        if (picker && picker.getAttribute('Name') && datePicker && datePicker.value()) {
            serializedArray.find(i => i.name === picker.getAttribute('Name')).value = toUTCString(datePicker.value());
        }
    });
}

function toUTCString(date) {
    if (date) {
        return date.toUTCString();
    }
}

$(window).ready(function(){
  $(window).scroll(function () {
    if ($(window).scrollTop() >= 260) {
        // If page is scrolled more than 50px
        $('#return-to-top').fadeIn(200);    // Fade in the arrow
    } else {
        $('#return-to-top').fadeOut(200);   // Else fade out the arrow
    }
  });

  $('#return-to-top').click(function () {
    $("html, body").animate({ scrollTop: 0 }, 500);
    return false;
  });

  //navigation bar expand/collapse logic start
  $("header .collapse").on("shown.bs.collapse", function(e) {
    e.stopPropagation();
    const levelSelector = $(this).parents('.navigation-admin-list').length  ? '.navigation-admin-list' : 'header';
    $(`${levelSelector} .collapse:not(#${this.id})`).collapse('hide');
  });

  let activeRoute = $(`header .main-list a[href='${window.location.pathname}']`);

  if(activeRoute){
    [...activeRoute.parents('.collapse')].reverse().map(i=>$(i).collapse('show'))
    activeRoute.addClass('active');
  }

  // custom case
  if(window.location.pathname === "/Sales/SalesForecast") {
    let activeRoute = $(`header .main-list a[href='${window.location.pathname}?year=${new Date().getFullYear()}']`);

    if(activeRoute){
      [...activeRoute.parents('.collapse')].reverse().map(i=>$(i).collapse('show'))
      activeRoute.addClass('active');
    }
  }

  if(!window.location.pathname || window.location.pathname === '/'){
    $(`header .main-list a[href='/home/<USER>']`).addClass('active');
  }
  //navigation bar expand/collapse logic end

   correctAllTimes();
})
