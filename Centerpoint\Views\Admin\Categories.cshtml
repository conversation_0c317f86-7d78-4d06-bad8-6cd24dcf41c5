﻿@model CompanyModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-list-alt"></i>
        Categories
        (<span data-bind="text:totalCategories"></span>)
    </h4>
</div>
<hr />

<div class="grid-container">
    @(Html.Kendo().Grid<CategoryModel>()
        .Name("categoryGrid")
        .Columns(c => {
            c.<PERSON>und(p => p.Name);
            c.<PERSON>und(p => p.CustomerAssets).EditorTemplateName("CustomerAssets").Title("Customer Assets").ClientTemplate("#if(CustomerAssets){#Yes#}else{#No#}#");
            c.Command(c=>{
                c.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                c.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"});
            }).Width(200);
        })
        .Editable(editable => editable.Mode(GridEditMode.InLine))
        .ToolBar(t => {
            t.Create().Text("Add Category");
            t.Excel().Text("Export");
        }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
        .Sortable()
        .Filterable()
        .Scrollable()
        .Resizable(c => c.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .Events(e => e.DataBound("updateCategoryTotal"))
        .Excel(excel => excel
            .FileName(string.Format("Centerpoint_Category_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Admin"))
        )
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(m => m.Id(p => p.CategoryId))
            .Events(e => e.Error("onCategoryError"))
            .Read(read => read.Action("GetCategories", "Admin"))
            .Create(create => create.Action("UpdateCategory", "Admin"))
            .Update(update => update.Action("UpdateCategory", "Admin"))
            .Destroy(destroy => destroy.Action("DeleteCategory", "Admin"))
        )
    )
</div>


    <script>
        $(document).ready(function () {
            var categoryGrid = $('#categoryGrid').data("kendoGrid");

            categoryGrid.bind('dataBound', function (e) {
                this.element.find('.k-add').remove();
                this.element.find('.k-i-excel').remove();
            });
        });


        function updateCategoryTotal() {
            var categoryGrid = $("#categoryGrid").data("kendoGrid");
            var totalCategories = categoryGrid.dataSource.total();
            viewModel.set("totalCategories", totalCategories);
        }

        function onCategoryError(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var categoryGrid = $("#categoryGrid").data("kendoGrid");
                categoryGrid.dataSource.cancelChanges();
            }
        }
        function onError(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var companyGrid = $("#companyGrid").data("kendoGrid");
                companyGrid.dataSource.cancelChanges();
            }
        }
       
        var viewModel = new kendo.observable({            
            totalCategories: 0
        });

        kendo.bind(document.body.children, viewModel);
    </script>

