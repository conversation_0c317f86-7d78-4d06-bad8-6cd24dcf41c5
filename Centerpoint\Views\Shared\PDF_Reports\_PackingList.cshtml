﻿@using Centerpoint.Model.ViewModels
@model EquipmentPackingListPdfModel

@{
    Layout = null;
}

<!DOCTYPE html>
<html lang="en-GB">
<head>
    <base href="/"/>

    <meta charset="UTF-8">
    <link rel="stylesheet" href="style/packingListPDF.css" />
    <link rel="stylesheet" href="style/bootstrap.min.css" />
    <link href='http://fonts.googleapis.com/css?family=Raleway' rel='stylesheet' type='text/css'>

    <title>Document</title>
</head>
<body>
    <div style="display:block; height:190px"></div>
    <hr />
    <div class="panel panel-default" style="border:none; margin-bottom: 14px">
        <div class="panel-heading" style="background: #6eb6b4; color: #fff">
            <h2 class="panel-title" style="font-size:18px">Details </h2>
        </div>
        <br />
        <div class="panel-body" style="padding:0px;border-bottom:none">
            <table style="width:100%">
                <tbody>
                    <tr>
                        <td style="text-align:left; width:25%"><p>Title</p></td>
                        <td style="text-align:left; width:25%"><p>Packing List Created By</p></td>
                        <td style="text-align:left; width:25%"><p>Associated Project</p></td>
                        <td style="text-align:left; width:25%"><p>Date Required</p></td>
                    </tr>
                    <tr>
                        <td style="text-align:left; width:25%"><p>@Model.EquipmentPackingList.PackingListTitle</p></td>
                        <td style="text-align:left; width:25%"><p>@Model.EquipmentPackingList.CreatedByUserName</p></td>
                        <td style="text-align:left; width:25%"><p>@Model.EquipmentPackingList.ProjectName</p></td>
                        <td style="text-align:left; width:25%"><p>@(Model.EquipmentPackingList.DateRequired.HasValue ? Model.EquipmentPackingList.DateRequired.Value.ToLocalTime().ToString("dd/MM/yyyy") : "")</p></td>
                    </tr>
                </tbody>
            </table>
            <hr />
            <table style="width:100%">
                <tbody>
                    <tr>
                        <td style="text-align:left; width:25%"><p>Description</p></td>
                    </tr>
                    <tr>
                        <td style="text-align:left; width:25%"><p>@Model.EquipmentPackingList.Description</p></td>
                    </tr>
                </tbody>
            </table>
            <hr />
            <table style="width:100%">
                <tbody>
                    <tr>
                        <td style="text-align:left; width:50%"><p>Sign off Details</p></td>
                        <td style="text-align:left; width:50%"><p></p></td>
                    <tr />
                    <tr>
                        <td style="text-align:left; width:50%"><p>@Model.EquipmentPackingList.SignOffDetails</p></td>
                        <td style="text-align:left; width:50%"><p></p></td>
                    </tr>
                    <tr style="height:10px">
                        <td style="text-align:left; width:50%"><p></p></td>
                        <td style="text-align:left; width:50%"><p></p></td>
                    <tr />
                    <tr>
                        <td style="text-align:left; width:50%"><p>Signed Off By</p></td>
                        <td style="text-align:left; width:50%"><p>Signed Off Date</p></td>
                    <tr />
                    <tr>
                        <td style="text-align:left; width:50%"><p>@Model.EquipmentPackingList.SignedOffBy</p></td>
                        <td style="text-align:left; width:50%"><p>@(Model.EquipmentPackingList.SignedOffDate.HasValue ? Model.EquipmentPackingList.SignedOffDate.Value.ToLocalTime().ToString("dd/MM/yyyy") : "")</p></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <p style="page-break-before: always;"></p>

    <div class="panel panel-default" style="border:none">
        <div class="panel-heading" style="background: #6eb6b4; color: #fff">
            <h2 class="panel-title" style="font-size:18px">Equipment Packing List Items [@Model.EquipmentPackingListItems.Count] </h2>
        </div>
        <br />
        <div class="panel-body" style="padding:0px;border-bottom:none">
            <div>
                @if (Model.EquipmentPackingListItems != null && Model.EquipmentPackingListItems.Any())
                {
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th style="text-align:center; width:10%"><p>Item No.</p></th>
                                <th style="text-align:center; width:20%"><p>Current Location</p></th>
                                <th style="text-align:center; width:20%"><p>Maintenance Schedule Date(s)</p></th>
                                <th style="text-align:center; width:10%"><p>Active MRs</p></th>
                                <th style="text-align:center; width:10%"><p>Info</p></th>
                                <th style="text-align:center; width:10%"><p>Dangerous Goods</p></th>
                                <th style="text-align:center; width:10%"><p>Status</p></th>
                            </tr>
                        </thead>
                        <tbody>
                            @for (var i = 0; i < Model.EquipmentPackingListItems.Count; i++)
                            {
                                <tr>
                                    <td style="text-align:center; width:10%">@Model.EquipmentPackingListItems[i].EquipmentItemName</td>
                                    <td style="text-align:center; width:20%">@Model.EquipmentPackingListItems[i].CurrentClientLocationName</td>
                                    <td style="text-align:center; width:20%">@(Html.Raw(Model.EquipmentPackingListItems[i].MaintenanceScheduleDetail.Replace(System.Environment.NewLine, "<br />").Replace("\n", @"<br />")))</td>
                                    <td style="text-align:center; width:10%">@Model.EquipmentPackingListItems[i].MRCount</td>
                                    <td style="text-align:center; width:10%">@Model.EquipmentPackingListItems[i].EquipmentItem.EquipmentInfo</td>
                                    <td style="text-align:center; width:10%">@Model.EquipmentPackingListItems[i].DangerouseGoods</td>
                                    <td style="text-align:center; width:10%">@Model.EquipmentPackingListItems[i].StatusDescription</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                }
            </div>
        </div>
    </div>
</body>
</html>

