﻿<div class="mb-4">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fa fa-users mr-2"></i>Operator and Partner Contacts (<span data-bind="text: totalCompanyContacts"></span>)
        </h6>
    </div>
    <div>
    @(Html.Kendo().Grid<CompanyContactModel>()
        .Name("companyContactGrid")
        .Columns(columns => {
            columns.Bound(c => c.CompanyLocationName).Title("Location");
            columns.Bound(c => c.FirstName);
            columns.Bound(c => c.LastName);
            columns.Bound(c => c.Position);
            columns.Bound(c => c.EmailAddress).Title("Email Address").ClientTemplate("#if(EmailAddress){##=EmailAddress##} else{#N/A#}#");
            columns.Bound(c => c.Mobile).Title("Mobile").ClientTemplate("#if(Mobile){##=Mobile##} else{#N/A#}#");
            columns.Bound(c => c.WorkTelephone).Title("Work Telephone").ClientTemplate("#if(WorkTelephone){##=WorkTelephone##} else{#N/A#}#");
            columns.Bound(c => c.Comment).Hidden(true);
            if(Model.Stage != OpportunityStageConstant.Closed) {
                columns.Command(command => { 
                        command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                });   
            }
            
        })
        .ColumnMenu(c => c.Columns(true))
        .ToolBar(t => t.ClientTemplateId("companyContactGridToolbar"))
        .Editable(editable => editable
            .Mode(GridEditMode.PopUp)
            .TemplateName("CompanyContact")
            .Window(w => w.Name("companyContactWindow")
                .Title("Client Contact")
                .Width(800)
                .Draggable(false)
            )
        )
        .Events(e => e.Edit("companyContactEdit").Change("selectedCompanyContact").DataBound("updateCompanyContactTotals"))
        .ColumnMenu(c => c.Columns(true))
        .Sortable()
        .Groupable()
        .Filterable()
        .Selectable(selectable => selectable.Mode(GridSelectionMode.Single))
        .Scrollable(s => s.Height(200))
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(model => {
                model.Id(m => m.CompanyContactId);
            })
            .Events(e => e.Error(
                @<text>
                    function(e) {
                    $.proxy(onCompanyContactError, $("#companyContactGrid").data("kendoGrid"))(e);
                    }
                </text>))
            .Read(read => read.Action("GetCompanyContactsNotFromOpportunityCompanyContactId", "Sales").Data("companyData"))
            .Create(create => create.Action("UpdateCompanyContact", "Admin"))
            .Update(create => create.Action("UpdateCompanyContact", "Admin"))
        )
    )
    </div>
</div>

<div>
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fa fa-users mr-2"></i>Lead Contacts (<span data-bind="text: totalLeadContacts"></span>)
        </h6>
    </div>
    <div>
        @(Html.Kendo().Grid<OpportunityCompanyContactModel>()
            .Name("leadContactGrid")
            .Columns(columns => {
                columns.Bound(c => c.CompanyContactCompanyLocationName).Title("Location");
                columns.Bound(c => c.CompanyContactFirstName).Title("First Name");
                columns.Bound(c => c.CompanyContactLastName).Title("Last Name");
                columns.Bound(c => c.TypeDescription).Title("Role");
                columns.Bound(c => c.CompanyContactPosition).Title("Position");
                columns.Bound(c => c.CompanyContactEmailAddress).Title("Email Address").Title("Email Address").ClientTemplate("#if(CompanyContactEmailAddress){#<a href='mailto:#=CompanyContactEmailAddress#'>#=CompanyContactEmailAddress#</a>#} else{#N/A#}#");
                columns.Bound(c => c.CompanyContactMobile).Title("Mobile").ClientTemplate("#if(CompanyContactMobile){#<a href='tel:#=CompanyContactMobile#'>#=CompanyContactMobile#</a>#} else{#N/A#}#");
                columns.Bound(c => c.CompanyContactWorkTelephone).Title("Work Telephone").ClientTemplate("#if(CompanyContactWorkTelephone){#<a href='tel:#=CompanyContactWorkTelephone#'>#=CompanyContactWorkTelephone#</a>#} else{#N/A#}#");
                columns.Bound(c => c.CompanyContactComment).Hidden(true).Title("Comment");
                if(Model.Stage != OpportunityStageConstant.Closed) {
                    columns.Command(command => { 
                        command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}).Visible("canDeleteCompanyContact");
                    });
                }
            })
            .ColumnMenu(c => c.Columns(true))
            .Events(e => e.DataBound("updateLeadContactTotals"))
            .Editable(e => e.Mode(GridEditMode.InLine))
            .Sortable()
            .Groupable()
            .Filterable()
            .Scrollable(s => s.Height(200))
            .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Events(e => e.RequestEnd("leadContactsRequestEnd"))
                .Model(model => {
                    model.Id(m => m.OpportunityCompanyContactId);
                })
                .Read(read => read
                    .Action("GetLeadContacts", "Sales", new { @id = Model.LinkOpportunityId })
                )
                .Destroy(destroy => destroy.Action("DeleteLeadContacts", "Sales"))
            )
        )
    </div>
</div>

<script type="text/x-kendo-template" id="companyContactGridToolbar">
    <div class="d-flex justify-content-start w-100">
        @if (Model.Stage != OpportunityStageConstant.Closed) {
            <a class="btn btn-success d-flex align align-items-center k-grid-add mr-2" href="/Admin/GetCompanyContacts/#=data.companyId#?companyContactGrid-mode=insert">
                <i class="fa fa-plus"></i>
                Add New Contact
            </a>
            <button type="button" class="btn btn-success d-flex align align-items-center" id="companyContactAddToLeadBtn" onclick="addContactWindow()">
                <i class="fa fa-check mr-2"></i>
                    Add to Lead Contacts
            </button>
        }
    </div>
</script>
