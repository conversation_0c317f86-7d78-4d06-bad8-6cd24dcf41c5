﻿namespace Centerpoint.Common.Constants
{
    public static class MaintenanceBlueprintLogTypeConstant
    {

        public const string Step = "STP";
        public const string Update = "UPD";
        public const string Document = "DCM";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Step,"Step" },
                    {Update, "Update" },
                    {Document, "Document" }
                };
            }
        }
    }
}
