$(document).ready(function () {
    $('#table').dataTable({
        "bPaginate": false,
        "bLengthChange": false,
        "bFilter": true,
        "bInfo": false,
        "bAutoWidth": false,
        "searching": false,
    });
});

function onRequestEnd(e) {
    var grid = $("#wellDocumentsGrid").data("kendoGrid");
    var data = grid.dataSource;
    if (e.type == "destroy") {
        window.location.reload();
    }
}

function documentTypeChanged(e) {
    var documentType = $("#documentType").data("kendoDropDownList");
    var documentTypeValue = documentType.value();

    if (documentTypeValue == "DEV") {
        viewModel.set("documentType", "DEV");
        viewModel.set("canAttach", true);
    } else if (documentTypeValue == "SCH") {
        viewModel.set("documentType", "SCH");
        viewModel.set("canAttach", true);
    } else if (documentTypeValue == "TAL") {
        viewModel.set("documentType", "TAL");
        viewModel.set("canAttach", true);
    } else if (documentTypeValue == "OTH") {
        viewModel.set("documentType", "OTH");
        viewModel.set("canAttach", true);
    } else if (documentTypeValue == "") {
        viewModel.set("canAttach", false);
    }
}

function fluidData() {
    return {
        companyWellId: viewCompanyWellModel.modelCompanyWellId
    };
}

function onWellDocumentAttached() {
    var wellDocumentsGrid = $("#wellDocumentsGrid").data("kendoGrid");
    wellDocumentsGrid.dataSource.read();
}

function onWellDocumentUpload(e) {
    uploadValidation(e);

    var companyWellId = viewCompanyWellModel.modelCompanyWellId;
    var documentType = viewModel.get("documentType");
    e.data = { companyWellId: companyWellId, documentType: documentType };
    $(".k-upload-files.k-reset").show();
}

function onWellDocumentComplete(e) {
    $(".k-upload-files.k-reset").find("li").remove();
    $(".k-upload-files.k-reset").slideUp();
    window.location.href = `/Admin/EditCompanyWell?id=${viewCompanyWellModel.modelCompanyWellId}&tab=documents`;
}

function updateWellDocumentsGrid() {
    var wellDocumentsGrid = $("#wellDocumentsGrid").data("kendoGrid");
    var totalWellDocuments = wellDocumentsGrid.dataSource.total();

    viewModel.set("totalWellDocuments", totalWellDocuments);

}

var viewModel = new kendo.observable({
    totalWellDocuments: 0,
    documentType: "",
    canAttach: false,
    tabStripHeaderDetails: function () {
        return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Details </span>`;
    },
    attachmentsStripText: function(){
        return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Attachments (<span data-bind="text:totalWellDocuments"></span>)</span>`;
    },
});
kendo.bind(document.body.children, viewModel);