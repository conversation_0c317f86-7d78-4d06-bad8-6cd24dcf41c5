﻿<form id="companyWellForm">
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="companyFieldId">Field</label>
                <br />
                @(Html.Kendo().DropDownList().Name("companyFieldId")
                                            .DataTextField("Name")
                                            .DataValueField("CompanyFieldId")
                                            .Filter(FilterType.Contains)
                                            .OptionLabel("Select Fields")
                                            .HtmlAttributes(new { @data_value_primitive = "true", @required = "required" })
                                            .DataSource(dataSource => dataSource
                                                .Read(read => read.Action("GetFieldsByCompanyId", "Lookup").Data("companyData"))
                                                ).AutoBind(false)
                                            )
            </div>
            @if (GlobalSettings.IsAisus)
            {
                <div class="form-group">
                <label for="minimumId"> OD (Length)</label>
                <br />
                @(Html.Kendo().NumericTextBox().Name("minimumId")
                                            .Value(0)
                                            .Spinners(false)
                                            .Format("n3")
                                            .Decimals(3)
                                            .HtmlAttributes(new { @required = "required" }))
                </div>
            }
            else
            {
                <div class="form-group">
                <label for="minimumId">Minimum ID (Inches)</label>
                <br />
                @(Html.Kendo().NumericTextBox().Name("minimumId")
                                            .Value(0)
                                            .Spinners(false)
                                            .Format("n3")
                                            .Decimals(3)
                                            .HtmlAttributes(new { @required = "required" }))
                </div>
            }
            

            @if (!GlobalSettings.IsAisus)
            {
                <div class="d-flex">
                    <div class="form-group mr-1">
                        <label for="maximumPressure">Maximum Pressure</label>
                            <br />
                                @(Html.Kendo().NumericTextBox().Name("maximumPressure")
                                                .Spinners(false)
                                                .Format("n2")
                                                .Decimals(2)
                                                .Value(0)
                                                .HtmlAttributes(new { @style = "width:100%;", @required = "required" }))
                    </div>

                    <div class="form-group">
                        <label for="maximumPressureUnits">Units</label>
                        <br />
                        @(Html.Kendo().DropDownList().Name("maximumPressureUnits")
                                                .DataTextField("Value")
                                                .DataValueField("Key")
                                                .Filter(FilterType.Contains)
                                                .BindTo(Centerpoint.Common.Constants.MaximumPressureUnitsConstant.ValuesAndDescriptions.ToList())
                                                .Value("PSI")
                                                .HtmlAttributes(new { @style = "width:100px;", @data_value_primitive = "true" }))
                    </div>
                </div>
            }
            

            
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="name">Well</label>
                <br />
                @(Html.Kendo().TextBox().Name("name")
                                        .HtmlAttributes(new { @required = "required" }))
            </div>
            @if (!GlobalSettings.IsAisus)
            {
                <div class="form-group">
                <label for="maximumDeviation">Maximum Deviation</label>
                <br />
                @(Html.Kendo().NumericTextBox().Name("maximumDeviation")
                                            .Spinners(false)
                                            .Format("n0")
                                            .Value(0)
                                            .HtmlAttributes(new { @required = "required" }))
                </div>
            }
                
            
            <div class="d-flex w-100">
                <div class="form-group mr-1">
                    <label for="maximumTemperature">Maximum Temperature</label>
                    <br />
                    @(Html.Kendo().NumericTextBox().Name("maximumTemperature")
                                                .Spinners(false)
                                                .Format("n0")
                                                .Value(0)
                                                .HtmlAttributes(new { @style = "width:100%;" , @required = "required" }))
                </div>
                <div class="form-group">
                    <label for="maximumTemperatureDegrees">Degrees</label>
                    <br />
                    @(Html.Kendo().DropDownList().Name("maximumTemperatureDegrees")
                                            .DataTextField("Value")
                                            .DataValueField("Key")
                                            .Filter(FilterType.Contains)
                                            .BindTo(Centerpoint.Common.Constants.MaximumTemperatureDegreesConstant.ValuesAndDescriptions.ToList())
                                            .Value("C")
                                            .HtmlAttributes(new { @style = "width:100px", @data_value_primitive = "true" }))
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="wellType">Well Type</label>
                <br />
                @(Html.Kendo().DropDownList().Name("wellType")
                                            .DataValueField("Key")
                                            .DataTextField("Value")
                                            .Filter(FilterType.Contains)
                                            .OptionLabel("Select Well Type")
                                            .HtmlAttributes(new { @tabindex = "7", @required = "required" })
                                            .BindTo(Centerpoint.Common.Constants.WellTypeConstant.ValuesAndDescriptions.ToList()))
            </div>
            @if (!GlobalSettings.IsAisus)
            {
                <div class="form-group">
                    <label for="co2">CO<sub>2</sub> (%)</label>
                    <br />
                    @(
                        Html.Kendo().NumericTextBox().Name("co2")
                                                        .Spinners(false)
                                                        .Format("n0")
                                                        .Value(0)
                                                        .HtmlAttributes(new { @required = "required" })
                        )
                </div>
            }
        </div>
        <div class="col-md-6">
            @if (!GlobalSettings.IsAisus)
            {
                <div class="form-group">
                    <label for="h2s">H<sub>2</sub>S (ppm)</label>
                    <br />
                    @(Html.Kendo().NumericTextBox().Name("h2s")
                                                .Spinners(false)
                                                .Format("n0")
                                                .Value(0)
                                                .HtmlAttributes(new { @required = "required" }))
                </div>
                <div class="form-group">
                    <label for="fluidTypes">Fluids</label>
                    @(Html.Kendo().MultiSelect().Name("fluidTypes")
                                                .Placeholder("Select Fluid(s)")
                                                .DataTextField("Name")
                                                .DataValueField("FluidTypeId")
                                                .DataSource(source => {
                                                    source.Read(read => {
                                                        read.Action("GetCompanyWellFluids", "Lookup");
                                                    })
                                                    .ServerFiltering(true);
                                                }).AutoBind(false)
                                                .HtmlAttributes(new { @data_bind="value:fluidTypes" }))
                </div>
            }
            else
            {
                <div class="form-group" style="visibility:hidden">
                    <label for="h2s">H<sub>2</sub>S (ppm)</label>
                    <br />
                    @(Html.Kendo().NumericTextBox().Name("h2s")
                        .Spinners(false)
                        .Format("n0")
                        .Value(0)
                        .HtmlAttributes(new { @required = "required" }))
                </div>
                <div class="form-group" style="visibility:hidden">
                    <label for="fluidTypes">Fluids</label>
                    @(Html.Kendo().MultiSelect().Name("fluidTypes")
                        .Placeholder("Select Fluid(s)")
                        .DataTextField("Name")
                        .DataValueField("FluidTypeId")
                        .DataSource(source =>
                        {
                            source.Read(read =>
                            {
                                read.Action("GetCompanyWellFluids", "Lookup");
                            })
                            .ServerFiltering(true);
                        }).AutoBind(false)
                        .HtmlAttributes(new { @data_bind = "value:fluidTypes" }))
                </div>
            }

                
        </div>
    </div>
    <hr />
    <button type="button" onclick="addCompanyWell()" class="btn btn-sm btn-primary">save</button>
</form>
