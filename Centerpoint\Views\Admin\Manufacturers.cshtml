﻿@model ManufacturerModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-building"></i>
        Manufacturers
        (<span data-bind="text:totalManufacturers"></span>)
    </h4>
</div>
<hr />

@(Html.Kendo().Grid<ManufacturerModel>()
    .Name("manufacturerGrid")
    .Columns(c => {
        c.Bound(p => p.Name);
        c.Command(command => { 
            command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
            command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
        });
    })
    .Editable(editable => editable.Mode(GridEditMode.InLine))
    .ToolBar(t => {
        t.Create().Text("Add Manufacturer");
    })
    .Sortable()
    .Filterable()
    .Scrollable(s => s.Height(500))
    .Resizable(c => c.Columns(true))
    .ColumnMenu(c => c.Columns(true))
    .Events(e => e.DataBound("updateManufacturerTotal"))
    .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Model(m => m.Id(p => p.ManufacturerId))
        .Events(e => e.Error("onError"))
        .Read(read => read.Action("GetManufacturers", "Admin"))
        .Create(create => create.Action("UpdateManufacturer", "Admin"))
        .Update(update => update.Action("UpdateManufacturer", "Admin"))
        .Destroy(destroy => destroy.Action("DeleteManufacturer", "Admin"))))

    <script>
        function updateManufacturerTotal() {
            var manufacturerGrid = $("#manufacturerGrid").data("kendoGrid");
            var totalManufacturers = manufacturerGrid.dataSource.total();
            viewModel.set("totalManufacturers", totalManufacturers);
        }

        function onError(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var manufacturerGrid = $("#manufacturerGrid").data("kendoGrid");
                manufacturerGrid.dataSource.cancelChanges();
            }
        }

        var viewModel = new kendo.observable({
            totalManufacturers: 0
        });

        kendo.bind(document.body.children, viewModel);
    </script>
