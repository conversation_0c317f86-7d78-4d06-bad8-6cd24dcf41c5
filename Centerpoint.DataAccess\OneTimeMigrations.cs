﻿using Centerpoint.DataAccess.Persistence;
using Centerpoint.DataAccess.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Hangfire;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Centerpoint.DataAccess
{
    public static class OneTimeMigrations
    {
        public static async Task Initialize(DataContext context, IServiceProvider services)
        {
            await RunUserProjectFilterMigrations(context, services);
        }

        private static async Task RunUserProjectFilterMigrations(DataContext _context, IServiceProvider services)
        {
            var migrationName = "RecalculateItemsPoints";
            if (!_context.Database.GetAppliedMigrations().Any(m => m == migrationName))
            {
                var _service = services.GetRequiredService<IEquipmentItemRepository>();
                BackgroundJob.Schedule(() => _service.RecalculateEquipmentItemsPoints(), DateTime.Now.Date.AddHours(23));
                string query = $"INSERT dbo.__EFMigrationsHistory(MigrationId, ProductVersion) VALUES(N'{migrationName}', N'')";
                await _context.Database.ExecuteSqlRawAsync(query);
            }
        }
    }
}
