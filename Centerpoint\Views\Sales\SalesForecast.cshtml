﻿@model OpportunityForecastModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-bar-chart"></i>
        Sales Forecast
    </h4>
</div>
<hr />

    @(Html.Kendo().TabStrip()
        .Name("SalesForecastStrips")
        .HtmlAttributes(new { @id="salesForecastStrips"})
        .SelectedIndex(0)
        .Animation(false)
        .Items( tabstrip => {

        tabstrip.Add().Text("Monthly")
            .HtmlAttributes(new { @id="monthly"})
            .Selected(true)
            .Content(@<text>
                    <div class="card">
                        <div class="card-header pr-2">
                            <div class="d-flex justify-content-between p-right">
                                <div class="d-flex align-items-center">
                                    <h6 class="mb-0">Opportunities By Month</h6>
                                </div>
                                <div class="d-flex">
                                    <div class="d-flex align-items-center">
                                        <span class="mr-2">
                                            Closeout From
                                        </span>
                                        <div class="mr-3">
                                           @(Html.Kendo().DatePickerFor(m => m.FromDate).Events(e => e.Change("refreshOpportunityForecastGrid")).HtmlAttributes(new { @data_bind = "value:fromDate", @style = "font-size: 14px" }))
                                        </div>
                                    </div>
                                   <div class="d-flex align-items-center">
                                        <span class="mr-2">
                                            Closeout To
                                        </span>
                                        <div>
                                            @(Html.Kendo().DatePickerFor(m => m.ToDate).Events(e => e.Change("refreshOpportunityForecastGrid")).HtmlAttributes(new { @data_bind = "value:toDate", @style = "margin-left:5px; font-size: 14px" }))
                                        </div>
                                   </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            @(Html.Kendo().Grid<OpportunityForecastModel>()
                                        .Name("opportunityForecastGrid")
                                    .Columns(columns => {
                                        columns.Bound(c => c.Name).Title("Opportunity ID").ClientTemplate("<a href='" + @Url.Action("Edit", "Sales") + "/#=LinkOpportunityId#?revision=#=MaxRevision#'>#=Name#</a>");
                                        columns.Bound(c => c.CustomerCompanyName).Title("Customer").ClientTemplate("<a href='" + @Url.Action("EditCompany", "Admin") + "/#=CustomerCompanyId#'>#=CustomerCompanyName#</a>").ClientGroupHeaderTemplate("Customer : #= value # (#= count#)");
                                        columns.Bound(c => c.Services).Title("Service");
                                        columns.Bound(c => c.CompanyFields).Title("Fields").ClientTemplate("#=CompanyFields ? CompanyFields : 'N/A'#");
                                        columns.Bound(c => c.CompanyWells).Title("Wells").ClientTemplate("#=CompanyWells ? CompanyWells : 'N/A'#");
                                        columns.Bound(c => c.CloseoutDate).Title("Closeout").ClientTemplate("#=CloseoutDate ?  CloseoutDateOnly : 'N/A'#");
                                        columns.Bound(c => c.MobilisationDate).Title("Mobilisation").ClientTemplate("#=MobilisationDate ? MobilisationDateOnly : 'N/A'#");
                                        columns.Bound(c => c.CurrentStage).Title("Stage").ClientTemplate("<span class='badge' style='background:#=StageColour#;color:#=StagetextColour#'>#=CurrentStage#</span>");
                                        columns.Bound(c => c.PoundValue).Title("Value").Format("{0:n2}").ClientFooterTemplate("Total Value: £ #= kendo.toString(sum, 'n2')#").ClientGroupFooterTemplate("Total: £ #= kendo.toString(sum, 'n2')#").Width(200);
                                        columns.Bound(c => c.CurentProbabilityValue).Title("Probability Value").Format("{0:n2}").ClientFooterTemplate("Total: £ #= kendo.toString(sum, 'n2')#").ClientGroupFooterTemplate("Total: £ #= kendo.toString(sum, 'n2')#").Width(200);
                                    })
                                    .ToolBar(t => {
                                        t.Excel().Text("Export");
                                    }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
                                    .ColumnMenu(c => c.Columns(true))
                                    .Sortable()
                                    .Groupable()
                                    .Filterable()
                                    .Scrollable(s => s.Height("auto"))
                                    .Excel(excel => excel
                                    .FileName(string.Format("Centerpoint_Sales_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                                    .Filterable(true)
                                    .ProxyURL(Url.Action("Export", "Admin")))
                                    .Pdf(pdf => pdf
                                    .AllPages()
                                    .AvoidLinks()
                                    .PaperSize("A4")
                                    .Scale(0.8)
                                    .Margin("2cm", "1cm", "1cm", "1cm")
                                    .Landscape()
                                    .RepeatHeaders()
                                    .FileName(string.Format("Centerpoint_Sales_Export_{0}.pdf", DateTime.Now.ToString("ddMMyyHHmm")))
                                    .ProxyURL(Url.Action("Pdf", "Admin")))
                                    .DataSource(dataSource => dataSource
                                    .Ajax()
                                    .ServerOperation(false)
                                    .Aggregates(aggregates => {
                                        aggregates.Add(p => p.CustomerCompanyName).Min().Max().Count();
                                        aggregates.Add(p => p.PoundValue).Sum();
                                        aggregates.Add(p => p.CurentProbabilityValue).Sum();
                                    })
                                    .Group(group => group.Add(p => p.CustomerCompanyName))
                                    .Sort(sort => sort.Add(p => p.Name))
                                    .Model(model => {
                                        model.Id(m => m.OpportunityId);
                                    })
                                    .Read(read => read.Action("GetAllForecastOpportunities", "Sales").Data("forecastOpportunityData"))).AutoBind(false))
                        </div>
                    </div>
            </text>);

        tabstrip.Add().Text("Annual")
            .HtmlAttributes(new { @id="annual"})
            .Content(@<text>
                    <div class="card">
                        <div class="card-header pr-0">
                            <div class="d-flex justify-content-between align-items-center p-right">
                                <h6 class="mb-0">Annual Forecast</h6> 
                                <div class="d-flex w-25 justify-content-end">
                                    <div>
                                        @(Html.Kendo().DropDownList()
                                                    .Name("years")
                                                    .DataValueField("Key")
                                                    .DataTextField("Value")
                                                    .Filter(FilterType.Contains)
                                                    .DataSource(d => d.Read("GetYears", "Lookup", new { @years = 10 }))
                                                    .Events(e => e.Change("yearChanged"))
                                                    .HtmlAttributes(new { @data_bind = "value:year" }))
                                    </div>
                                        <a class="btn btn-success btn-sm ml-3" id="btnExport" download="Annual.xls">Export to xls</a>
                                    </div>
                            </div>
                        </div>
                        <div class="card-body">
                            @if (Model.Opportunities != null && Model.Opportunities.Any()) {
                                    <table class="table table-striped table-bordered" id="annualForecast" cellspacing="0" style="word-wrap:break-word">
                                    <thead>
                                        <tr class="titlerow">
                                            <th>Country</th>
                                            <th>Customer</th>
                                            <th class="text-center">Jan</th>
                                            <th class="text-center">Feb</th>
                                            <th class="text-center">Mar</th>
                                            <th class="text-center">Q1</th>
                                            <th class="text-center">Apr</th>
                                            <th class="text-center">May</th>
                                            <th class="text-center">Jun</th>
                                            <th class="text-center">Q2</th>
                                            <th class="text-center">Jul</th>
                                            <th class="text-center">Aug</th>
                                            <th class="text-center">Sep</th>
                                            <th class="text-center">Q3</th>
                                            <th class="text-center">Oct</th>
                                            <th class="text-center">Nov</th>
                                            <th class="text-center">Dec</th>
                                            <th class="text-center">Q4</th>
                                            <th class="text-center">Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @for (var i = 0; i < Model.Opportunities.Count; i++) {
                                            <tr>
                                                <td>@Model.Opportunities[i].CustomerCountry</td>
                                                <td style="width:200px">@Model.Opportunities[i].CustomerCompanyName</td>
                                                @if (@Model.Opportunities[i].January > 0) {
                                                    <td style="background-color:@Model.Opportunities[i].StageColour; color:@Model.Opportunities[i].StagetextColour;text-align:right">@Model.Opportunities[i].JanuaryValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.Opportunities[i].February > 0) {
                                                    <td style="background-color:@Model.Opportunities[i].StageColour; color:@Model.Opportunities[i].StagetextColour;text-align:right">@Model.Opportunities[i].FebruaryValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.Opportunities[i].March > 0) {
                                                    <td style="background-color:@Model.Opportunities[i].StageColour; color:@Model.Opportunities[i].StagetextColour;text-align:right">@Model.Opportunities[i].MarchValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.Opportunities[i].Q1 > 0) {
                                                    <td style="background-color:gray;color:#fff;text-align:right">@Model.Opportunities[i].Q1Value</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.Opportunities[i].April > 0) {
                                                    <td class="col8" style="background-color:@Model.Opportunities[i].StageColour; color:@Model.Opportunities[i].StagetextColour;text-align:right">@Model.Opportunities[i].AprilValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.Opportunities[i].May > 0) {
                                                    <td style="background-color:@Model.Opportunities[i].StageColour; color:@Model.Opportunities[i].StagetextColour;text-align:right">@Model.Opportunities[i].MayValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.Opportunities[i].June > 0) {
                                                    <td style="background-color:@Model.Opportunities[i].StageColour; color:@Model.Opportunities[i].StagetextColour;text-align:right">@Model.Opportunities[i].JuneValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.Opportunities[i].Q2 > 0) {
                                                    <td style="background-color:gray;color:#fff;text-align:right">@Model.Opportunities[i].Q2Value</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.Opportunities[i].July > 0) {
                                                    <td style="background-color:@Model.Opportunities[i].StageColour; color:@Model.Opportunities[i].StagetextColour;text-align:right">@Model.Opportunities[i].JulyValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.Opportunities[i].August > 0) {
                                                    <td style="background-color:@Model.Opportunities[i].StageColour; color:@Model.Opportunities[i].StagetextColour;text-align:right">@Model.Opportunities[i].AugustValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.Opportunities[i].September > 0) {
                                                    <td style="background-color:@Model.Opportunities[i].StageColour; color:@Model.Opportunities[i].StagetextColour;text-align:right">@Model.Opportunities[i].SeptemberValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.Opportunities[i].Q3 > 0) {
                                                    <td style="background-color:gray;color:#fff;text-align:right">@Model.Opportunities[i].Q3Value</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.Opportunities[i].October > 0) {
                                                    <td style="background-color:@Model.Opportunities[i].StageColour; color:@Model.Opportunities[i].StagetextColour;text-align:right">@Model.Opportunities[i].OctoberValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.Opportunities[i].November > 0) {
                                                    <td style="background-color:@Model.Opportunities[i].StageColour; color:@Model.Opportunities[i].StagetextColour;text-align:right">@Model.Opportunities[i].NovemberValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.Opportunities[i].December > 0) {
                                                    <td style="background-color:@Model.Opportunities[i].StageColour; color:@Model.Opportunities[i].StagetextColour;text-align:right">@Model.Opportunities[i].DecemberValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.Opportunities[i].Q4 > 0) {
                                                    <td style="background-color:gray;color:#fff;text-align:right">@Model.Opportunities[i].Q4Value</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.Opportunities[i].Total > 0) {
                                                    <td style="background-color:gray;color:#fff;text-align:right">@Model.Opportunities[i].TotalValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                            </tr>
                                        }
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <td></td>
                                            <td></td>
                                            <td style="text-align:right">@(Model.Opportunities.Any(o => o.January > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.January.HasValue).Sum(o => o.January.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.Opportunities.Any(o => o.February > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.February.HasValue).Sum(o => o.February.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.Opportunities.Any(o => o.March > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.March.HasValue).Sum(o => o.March.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right;background-color:gray;color:#fff">@(Model.Opportunities.Any(o => o.Q1 > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.Q1.HasValue).Sum(o => o.Q1.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.Opportunities.Any(o => o.April > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.April.HasValue).Sum(o => o.April.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.Opportunities.Any(o => o.May > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.May.HasValue).Sum(o => o.May.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.Opportunities.Any(o => o.June > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.June.HasValue).Sum(o => o.June.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right;background-color:gray;color:#fff">@(Model.Opportunities.Any(o => o.Q2 > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.Q2.HasValue).Sum(o => o.Q2.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.Opportunities.Any(o => o.July > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.July.HasValue).Sum(o => o.July.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.Opportunities.Any(o => o.August > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.August.HasValue).Sum(o => o.August.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.Opportunities.Any(o => o.September > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.September.HasValue).Sum(o => o.September.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right;background-color:gray;color:#fff">@(Model.Opportunities.Any(o => o.Q3 > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.Q3.HasValue).Sum(o => o.Q3.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.Opportunities.Any(o => o.October > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.October.HasValue).Sum(o => o.October.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.Opportunities.Any(o => o.November > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.November.HasValue).Sum(o => o.November.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.Opportunities.Any(o => o.December > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.December.HasValue).Sum(o => o.December.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right;background-color:gray;color:#fff">@(Model.Opportunities.Any(o => o.Q4 > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.Q4.HasValue).Sum(o => o.Q4.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right;background-color:gray;color:#fff">@(Model.Opportunities.Any(o => o.Total > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.Total.HasValue).Sum(o => o.Total.Value).ToString("n0")) : string.Empty)</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <br />
                                                <br />
                                            </td>
                                        </tr>
                                        @{
                                            var stages = new string[] { "Lead", OpportunityStageConstant.Enquiry, OpportunityStageConstant.Proposal, OpportunityStageConstant.Review, OpportunityStageConstant.Award, OpportunityStageConstant.Realised };
                                        }
                                        @foreach (var stage in stages) {
                                            <tr>
                                                <td></td>
                                                @if (stage == "Lead") {
                                                    <td style="background-color:#0073D0; color:#ffffff">Lead</td>
                                                } else if (stage == OpportunityStageConstant.Enquiry) {
                                                    <td style="background-color:#FF748C; color:#ffffff">Enquiry</td>
                                                } else if (stage == OpportunityStageConstant.Proposal) {
                                                    <td style="background-color:#B03060; color:#ffffff">Proposal</td>
                                                } else if (stage == OpportunityStageConstant.Review) {
                                                    <td style="background-color:#cccc00; color:#ffffff">Review</td>
                                                } else if (stage == OpportunityStageConstant.Award) {
                                                    <td style="background-color:#008000; color:#ffffff">Award</td>
                                                } else if (stage == OpportunityStageConstant.Realised) {
                                                    <td style="background-color:#008000; color:#ffffff">Realised</td>
                                                }
                                                <td style="text-align:right">@(Model.Opportunities.Any(o => o.January > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.January.HasValue && o.Stage == stage).Sum(o => o.January.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.Opportunities.Any(o => o.February > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.February.HasValue && o.Stage == stage).Sum(o => o.February.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.Opportunities.Any(o => o.March > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.March.HasValue && o.Stage == stage).Sum(o => o.March.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right;background-color:gray;color:#fff">@(Model.Opportunities.Any(o => o.Q1 > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.Q1.HasValue && o.Stage == stage).Sum(o => o.Q1.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.Opportunities.Any(o => o.April > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.April.HasValue && o.Stage == stage).Sum(o => o.April.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.Opportunities.Any(o => o.May > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.May.HasValue && o.Stage == stage).Sum(o => o.May.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.Opportunities.Any(o => o.June > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.June.HasValue && o.Stage == stage).Sum(o => o.June.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right;background-color:gray;color:#fff">@(Model.Opportunities.Any(o => o.Q2 > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.Q2.HasValue && o.Stage == stage).Sum(o => o.Q2.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.Opportunities.Any(o => o.July > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.July.HasValue && o.Stage == stage).Sum(o => o.July.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.Opportunities.Any(o => o.August > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.August.HasValue && o.Stage == stage).Sum(o => o.August.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.Opportunities.Any(o => o.September > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.September.HasValue && o.Stage == stage).Sum(o => o.September.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right;background-color:gray;color:#fff">@(Model.Opportunities.Any(o => o.Q3 > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.Q3.HasValue && o.Stage == stage).Sum(o => o.Q3.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.Opportunities.Any(o => o.October > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.October.HasValue && o.Stage == stage).Sum(o => o.October.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.Opportunities.Any(o => o.November > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.November.HasValue && o.Stage == stage).Sum(o => o.November.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.Opportunities.Any(o => o.December > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.December.HasValue && o.Stage == stage).Sum(o => o.December.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right;background-color:gray;color:#fff">@(Model.Opportunities.Any(o => o.Q4 > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.Q4.HasValue && o.Stage == stage).Sum(o => o.Q4.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right;background-color:gray;color:#fff">@(Model.Opportunities.Any(o => o.Total > 0) ? string.Format("{0}", Model.Opportunities.Where(o => o.Total.HasValue && o.Stage == stage).Sum(o => o.Total.Value).ToString("n0")) : string.Empty)</td>
                                            </tr>
                                        }
                                    </tfoot>
                                </table>
                            }
                        </div>
                    </div>
            </text>);

        tabstrip.Add().Text("Annual Unweighted")
            .HtmlAttributes(new { @id="annualUnweighted"})
            .Content(@<text>
                    <div class="card">
                        <div class="card-header pr-0">
                            <div class="d-flex justify-content-between align-items-center p-right">
                                <h6 class="mb-0">Annual Unweighted</h6> 
                                <div class="d-flex w-25 justify-content-end">
                                    <div>
                                        @(Html.Kendo().DropDownList()
                                            .Name("yearsUnweighted")
                                            .DataValueField("Key")
                                            .DataTextField("Value")
                                            .Filter(FilterType.Contains)
                                            .DataSource(d => d.Read("GetYears", "Lookup", new { @years = 10 }))
                                            .Events(e => e.Change("yearUnweightedChanged"))
                                            .HtmlAttributes(new { @data_bind = "value:yearUnweighted" }))
                                    </div>
                                    <a class="btn btn-success btn-sm ml-3" id="btnExportUnweighted">Export to xls</a>                                       
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            @if (Model.OpportunitiesUnweighted != null && Model.OpportunitiesUnweighted.Any()) {
                                    <table class="table table-striped table-bordered" id="annualUnweightedForecast" cellspacing="0" style="word-wrap:break-word">
                                    <thead>
                                        <tr class="titlerow">
                                            <th>Country</th>
                                            <th>Customer</th>
                                            <th style="text-align:center">Jan</th>
                                            <th style="text-align:center">Feb</th>
                                            <th style="text-align:center">Mar</th>
                                            <th style="text-align:center">Q1</th>
                                            <th style="text-align:center">Apr</th>
                                            <th style="text-align:center">May</th>
                                            <th style="text-align:center">Jun</th>
                                            <th style="text-align:center">Q2</th>
                                            <th style="text-align:center">Jul</th>
                                            <th style="text-align:center">Aug</th>
                                            <th style="text-align:center">Sep</th>
                                            <th style="text-align:center">Q3</th>
                                            <th style="text-align:center">Oct</th>
                                            <th style="text-align:center">Nov</th>
                                            <th style="text-align:center">Dec</th>
                                            <th style="text-align:center">Q4</th>
                                            <th style="text-align:center">Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @for (var i = 0; i < Model.OpportunitiesUnweighted.Count; i++) {
                                            <tr>
                                                <td>@Model.OpportunitiesUnweighted[i].CustomerCountry</td>
                                                <td style="width:200px">@Model.OpportunitiesUnweighted[i].CustomerCompanyName</td>
                                                @if (@Model.OpportunitiesUnweighted[i].JanuaryUnweighted > 0) {
                                                    <td style="background-color:@Model.OpportunitiesUnweighted[i].StageColour; color:@Model.OpportunitiesUnweighted[i].StagetextColour;text-align:right">@Model.OpportunitiesUnweighted[i].JanuaryUnweightedValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.OpportunitiesUnweighted[i].FebruaryUnweighted > 0) {
                                                    <td style="background-color:@Model.OpportunitiesUnweighted[i].StageColour; color:@Model.OpportunitiesUnweighted[i].StagetextColour;text-align:right">@Model.OpportunitiesUnweighted[i].FebruaryUnweightedValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.OpportunitiesUnweighted[i].MarchUnweighted > 0) {
                                                    <td style="background-color:@Model.OpportunitiesUnweighted[i].StageColour; color:@Model.OpportunitiesUnweighted[i].StagetextColour;text-align:right">@Model.OpportunitiesUnweighted[i].MarchUnweightedValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.OpportunitiesUnweighted[i].Q1Unweighted > 0) {
                                                    <td style="background-color:gray;color:#fff;text-align:right">@Model.OpportunitiesUnweighted[i].Q1UnweightedValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.OpportunitiesUnweighted[i].AprilUnweighted > 0) {
                                                    <td class="col8" style="background-color:@Model.OpportunitiesUnweighted[i].StageColour; color:@Model.OpportunitiesUnweighted[i].StagetextColour;text-align:right">@Model.OpportunitiesUnweighted[i].AprilUnweightedValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.OpportunitiesUnweighted[i].MayUnweighted > 0) {
                                                    <td style="background-color:@Model.OpportunitiesUnweighted[i].StageColour; color:@Model.OpportunitiesUnweighted[i].StagetextColour;text-align:right">@Model.OpportunitiesUnweighted[i].MayUnweightedValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.OpportunitiesUnweighted[i].JuneUnweighted > 0) {
                                                    <td style="background-color:@Model.OpportunitiesUnweighted[i].StageColour; color:@Model.OpportunitiesUnweighted[i].StagetextColour;text-align:right">@Model.OpportunitiesUnweighted[i].JuneUnweightedValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.OpportunitiesUnweighted[i].Q2Unweighted > 0) {
                                                    <td style="background-color:gray;color:#fff;text-align:right">@Model.OpportunitiesUnweighted[i].Q2UnweightedValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.OpportunitiesUnweighted[i].JulyUnweighted > 0) {
                                                    <td style="background-color:@Model.OpportunitiesUnweighted[i].StageColour; color:@Model.OpportunitiesUnweighted[i].StagetextColour;text-align:right">@Model.OpportunitiesUnweighted[i].JulyUnweightedValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.OpportunitiesUnweighted[i].AugustUnweighted > 0) {
                                                    <td style="background-color:@Model.OpportunitiesUnweighted[i].StageColour; color:@Model.OpportunitiesUnweighted[i].StagetextColour;text-align:right">@Model.OpportunitiesUnweighted[i].AugustUnweightedValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.OpportunitiesUnweighted[i].SeptemberUnweighted > 0) {
                                                    <td style="background-color:@Model.OpportunitiesUnweighted[i].StageColour; color:@Model.OpportunitiesUnweighted[i].StagetextColour;text-align:right">@Model.OpportunitiesUnweighted[i].SeptemberUnweightedValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.OpportunitiesUnweighted[i].Q3Unweighted > 0) {
                                                    <td style="background-color:gray;color:#fff;text-align:right">@Model.OpportunitiesUnweighted[i].Q3UnweightedValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.OpportunitiesUnweighted[i].OctoberUnweighted > 0) {
                                                    <td style="background-color:@Model.OpportunitiesUnweighted[i].StageColour; color:@Model.OpportunitiesUnweighted[i].StagetextColour;text-align:right">@Model.OpportunitiesUnweighted[i].OctoberUnweightedValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.OpportunitiesUnweighted[i].NovemberUnweighted > 0) {
                                                    <td style="background-color:@Model.OpportunitiesUnweighted[i].StageColour; color:@Model.OpportunitiesUnweighted[i].StagetextColour;text-align:right">@Model.OpportunitiesUnweighted[i].NovemberUnweightedValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.OpportunitiesUnweighted[i].DecemberUnweighted > 0) {
                                                    <td style="background-color:@Model.OpportunitiesUnweighted[i].StageColour; color:@Model.OpportunitiesUnweighted[i].StagetextColour;text-align:right">@Model.OpportunitiesUnweighted[i].DecemberUnweightedValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.OpportunitiesUnweighted[i].Q4Unweighted > 0) {
                                                    <td style="background-color:gray;color:#fff;text-align:right">@Model.OpportunitiesUnweighted[i].Q4UnweightedValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                                @if (@Model.OpportunitiesUnweighted[i].TotalUnweighted > 0) {
                                                    <td style="background-color:gray;color:#fff;text-align:right">@Model.OpportunitiesUnweighted[i].TotalUnweightedValue</td>
                                                } else {
                                                    <td></td>
                                                }
                                            </tr>
                                        }
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <td></td>
                                            <td></td>
                                            <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.JanuaryUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.JanuaryUnweighted.HasValue).Sum(o => o.JanuaryUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.FebruaryUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.FebruaryUnweighted.HasValue).Sum(o => o.FebruaryUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.MarchUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.MarchUnweighted.HasValue).Sum(o => o.MarchUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right;background-color:gray;color:#fff">@(Model.OpportunitiesUnweighted.Any(o => o.Q1Unweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.Q1Unweighted.HasValue).Sum(o => o.Q1Unweighted.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.AprilUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.AprilUnweighted.HasValue).Sum(o => o.AprilUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.MayUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.MayUnweighted.HasValue).Sum(o => o.MayUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.JuneUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.JuneUnweighted.HasValue).Sum(o => o.JuneUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right;background-color:gray;color:#fff">@(Model.OpportunitiesUnweighted.Any(o => o.Q2Unweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.Q2Unweighted.HasValue).Sum(o => o.Q2Unweighted.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.JulyUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.JulyUnweighted.HasValue).Sum(o => o.JulyUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.AugustUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.AugustUnweighted.HasValue).Sum(o => o.AugustUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.SeptemberUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.SeptemberUnweighted.HasValue).Sum(o => o.SeptemberUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right;background-color:gray;color:#fff">@(Model.OpportunitiesUnweighted.Any(o => o.Q3Unweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.Q3Unweighted.HasValue).Sum(o => o.Q3Unweighted.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.OctoberUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.OctoberUnweighted.HasValue).Sum(o => o.OctoberUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.NovemberUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.NovemberUnweighted.HasValue).Sum(o => o.NovemberUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.DecemberUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.DecemberUnweighted.HasValue).Sum(o => o.DecemberUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right;background-color:gray;color:#fff">@(Model.OpportunitiesUnweighted.Any(o => o.Q4Unweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.Q4Unweighted.HasValue).Sum(o => o.Q4Unweighted.Value).ToString("n0")) : string.Empty)</td>
                                            <td style="text-align:right;background-color:gray;color:#fff">@(Model.OpportunitiesUnweighted.Any(o => o.TotalUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.TotalUnweighted.HasValue).Sum(o => o.TotalUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <br />
                                                <br />
                                            </td>
                                        </tr>
                                        @{
                                            var stages = new string[] { "Lead", OpportunityStageConstant.Enquiry, OpportunityStageConstant.Proposal, OpportunityStageConstant.Review, OpportunityStageConstant.Award, OpportunityStageConstant.Realised };
                                        }
                                        @foreach (var stage in stages) {
                                            <tr>
                                                <td></td>
                                                @if (stage == "Lead") {
                                                    <td style="background-color:#0073D0; color:#ffffff">Lead</td>
                                                } else if (stage == OpportunityStageConstant.Enquiry) {
                                                    <td style="background-color:#FF748C; color:#ffffff">Enquiry</td>
                                                } else if (stage == OpportunityStageConstant.Proposal) {
                                                    <td style="background-color:#B03060; color:#ffffff">Proposal</td>
                                                } else if (stage == OpportunityStageConstant.Review) {
                                                    <td style="background-color:#cccc00; color:#ffffff">Review</td>
                                                } else if (stage == OpportunityStageConstant.Award) {
                                                    <td style="background-color:#008000; color:#ffffff">Award</td>
                                                } else if (stage == OpportunityStageConstant.Realised) {
                                                    <td style="background-color:#008000; color:#ffffff">Realised</td>
                                                }
                                                <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.JanuaryUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.JanuaryUnweighted.HasValue && o.Stage == stage).Sum(o => o.JanuaryUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.FebruaryUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.FebruaryUnweighted.HasValue && o.Stage == stage).Sum(o => o.FebruaryUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.MarchUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.MarchUnweighted.HasValue && o.Stage == stage).Sum(o => o.MarchUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right;background-color:gray;color:#fff">@(Model.OpportunitiesUnweighted.Any(o => o.Q1Unweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.Q1Unweighted.HasValue && o.Stage == stage).Sum(o => o.Q1Unweighted.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.AprilUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.AprilUnweighted.HasValue && o.Stage == stage).Sum(o => o.AprilUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.MayUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.MayUnweighted.HasValue && o.Stage == stage).Sum(o => o.MayUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.JuneUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.JuneUnweighted.HasValue && o.Stage == stage).Sum(o => o.JuneUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right;background-color:gray;color:#fff">@(Model.OpportunitiesUnweighted.Any(o => o.Q2Unweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.Q2Unweighted.HasValue && o.Stage == stage).Sum(o => o.Q2Unweighted.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.JulyUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.JulyUnweighted.HasValue && o.Stage == stage).Sum(o => o.JulyUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.AugustUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.AugustUnweighted.HasValue && o.Stage == stage).Sum(o => o.AugustUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.SeptemberUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.SeptemberUnweighted.HasValue && o.Stage == stage).Sum(o => o.SeptemberUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right;background-color:gray;color:#fff">@(Model.OpportunitiesUnweighted.Any(o => o.Q3Unweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.Q3Unweighted.HasValue && o.Stage == stage).Sum(o => o.Q3Unweighted.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.OctoberUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.OctoberUnweighted.HasValue && o.Stage == stage).Sum(o => o.OctoberUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.NovemberUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.NovemberUnweighted.HasValue && o.Stage == stage).Sum(o => o.NovemberUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right">@(Model.OpportunitiesUnweighted.Any(o => o.DecemberUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.DecemberUnweighted.HasValue && o.Stage == stage).Sum(o => o.DecemberUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right;background-color:gray;color:#fff">@(Model.OpportunitiesUnweighted.Any(o => o.Q4Unweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.Q4Unweighted.HasValue && o.Stage == stage).Sum(o => o.Q4Unweighted.Value).ToString("n0")) : string.Empty)</td>
                                                <td style="text-align:right;background-color:gray;color:#fff">@(Model.OpportunitiesUnweighted.Any(o => o.TotalUnweighted > 0) ? string.Format("{0}", Model.OpportunitiesUnweighted.Where(o => o.TotalUnweighted.HasValue && o.Stage == stage).Sum(o => o.TotalUnweighted.Value).ToString("n0")) : string.Empty)</td>
                                            </tr>
                                        }
                                    </tfoot>
                                </table>
                            }
                        </div>
                    </div>
            </text>);    

        }
        ))

<script>
    const salesForecastModel = {
        modelYear: "@Model.Year",
        modelYearUnweighted:"@Model.YearUnweighted",
        modelFromDate: "@Model.FromDate",
        modelToDate: "@Model.ToDate",
        modelChartFromDate: "@Model.ChartFromDate",
        modelChartToDate: "@Model.ChartToDate",
    }
</script>

<environment include="Development">
    <script src="~/js/views/sales/salesForecast.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/sales/salesForecast.min.js" asp-append-version="true"></script>
</environment>


<script src="~/js/helpers/jquery.btechco.excelexport.js"></script>
<script src="~/js/helpers/jquery.base64.js"></script>