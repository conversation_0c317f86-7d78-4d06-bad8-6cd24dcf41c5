﻿<div class="row">
    <div class="col-md-12">
       <h5>Please select an Equipment Category to move the Asset Item to</h5>
    </div>
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fa fa-tags"></i> Equipment Categories</h6>
            </div>
            <div class="card-body">
                <div class="expandCollapseButtonsContainer">
                    <button class="btn btn-sm btn-primary" id="expandAllItems" data-bind="click:expandAllItems" title="Expand All">
                        <i class="fa fa-expand"></i>
                    </button>
                    <button class="btn btn-sm btn-primary" id="collapsAllItems" data-bind="click:collapseAllItems" title="Collapse All">
                        <i class="fa fa-compress"></i>
                    </button>
                </div>
                @(Html.Kendo().TreeView()
                    .Name("assetItemTreeView")
                    .DataTextField("NewName")
                    .LoadOnDemand(false)
                    .Events(e => e.Change("selectedCategory"))
                    .HtmlAttributes(new { style = "height: 500px"})
                    .DataSource(datasource => datasource
                    .Model(m => m.Id("EquipmentCategoryId").HasChildren("HasChildren").Children("Children"))
                    .Read(r => r.Action("GetEquipmentCategories", "Admin"))))
            </div>
        </div>
    </div>
</div>
<button id="moveAssetItemConfirm" data-bind="enabled:selectedCategory"  class="btn btn-primary btn-sm mt-2">Confirm</button>



