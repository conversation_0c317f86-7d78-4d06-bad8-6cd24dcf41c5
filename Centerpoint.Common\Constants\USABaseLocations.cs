﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Centerpoint.Common.Constants
{
    public static class USABaseLocations
    {
        public const string Alabama = "Alabama";
        public const string Alaska = "Alaska";
        public const string Arizona = "Arizona";
        public const string Arkansas = "Arkansas";
        public const string California = "California";
        public const string Colorado = "Colorado";
        public const string Connecticut = "Connecticut";
        public const string Delaware = "Delaware";
        public const string Florida = "Florida";
        public const string Georgia = "Georgia";
        public const string Hawaii = "Hawaii";
        public const string Idaho = "Idaho";
        public const string Illinois = "Illinois";
        public const string Indiana = "Indiana";
        public const string Iowa = "Iowa";
        public const string Kansas = "Kansas";
        public const string Kentucky = "Kentucky";
        public const string Louisiana = "Louisiana";
        public const string Maine = "Maine";
        public const string Maryland = "Maryland";
        public const string Massachusetts = "Massachusetts";
        public const string Michigan = "Michigan";
        public const string Minnesota = "Minnesota";
        public const string Mississippi = "Mississippi";
        public const string Missouri = "Missouri";
        public const string Montana = "Montana";
        public const string Nebraska = "Nebraska";
        public const string Nevada = "Nevada";
        public const string NewHampshire = "New Hampshire";
        public const string NewJersey = "New Jersey";
        public const string NewMexico = "New Mexico";
        public const string NewYork = "New York";
        public const string NorthCarolina = "North Carolina";
        public const string NorthDakota = "North Dakota";
        public const string Ohio = "Ohio";
        public const string Oklahoma = "Oklahoma";
        public const string Oregon = "Oregon";
        public const string Pennsylvania = "Pennsylvania";
        public const string RhodeIsland = "Rhode Island";
        public const string SouthCarolina = "South Carolina";
        public const string SouthDakota = "South Dakota";
        public const string Tennessee = "Tennessee";
        public const string Texas = "Texas";
        public const string Utah = "Utah";
        public const string Vermont = "Vermont";
        public const string Virginia = "Virginia";
        public const string Washington = "Washington";
        public const string WestVirginia = "West Virginia";
        public const string Wisconsin = "Wisconsin";
        public const string Wyoming = "Wyoming";

        public static bool ContainsUsaState(string input)
        {
            // Convert input to lowercase for case-insensitive comparison
            string lowerInput = input.ToLower();

            // Check if input contains any U.S. state
            return typeof(USABaseLocations)
                .GetFields()
                .Select(field => field.GetValue(null)?.ToString()?.ToLower())
                .Any(state => !string.IsNullOrEmpty(state) && lowerInput.Contains(state));
        }
    }
}
