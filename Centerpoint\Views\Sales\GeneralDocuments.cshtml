﻿@model OpportunityModel
    @if (Model.Stage != OpportunityStageConstant.Closed) {
        <p>Click the link below to attach documents</p>
        @(Html.Kendo().Upload()
      .Name("opportunityDocuments")
      .Messages(m => m.Select("Attach Documents"))
      .Multiple(true)
      .Events(e => e.Success("onOpportunityDocumentAttached").Complete("onOpportunityDocumentComplete").Upload("onOpportunityDocumentUpload"))
      .HtmlAttributes(new { @style = "width:300px" })
      .Async(async => async.Save("AttachOpportunityDocuments", "Sales", new { @id = Model.ParentOpportunityId.HasValue ? Model.ParentOpportunityId : Model.OpportunityId }).Batch(true)))
    }
    <br />
    @(Html.Kendo().Grid<DocumentModel>()
  .Name("opportunityDocumentsGrid")
  .Columns(c => {
  c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
  c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
  c.Bound(p => p.UserName).Title("Created By");
  c.Command(command => { 
        command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
    });
  })
   .Events(e => e.DataBound("updateOpportunityDocumentsGrid"))
   .Sortable()
   .ColumnMenu(c => c.Columns(true))
   .Resizable(r => r.Columns(true))
   .Filterable()
   .Groupable()
   .Editable(e => e.Mode(GridEditMode.InLine))
   .Scrollable(s => s.Height(300))
   .DataSource(dataSource => dataSource
       .Ajax()
       .ServerOperation(false)
       .Model(model => model.Id(p => p.DocumentId))
       .Read(read => read.Action("GetOpportunityDocuments", "Sales", new { @opportunityId = Model.ParentOpportunityId.HasValue ? Model.ParentOpportunityId : Model.OpportunityId }))
       .Destroy(destroy => destroy.Action("DeleteOpportunityDocument", "Sales", new { @opportunityId = Model.ParentOpportunityId.HasValue ? Model.ParentOpportunityId : Model.OpportunityId }))))
