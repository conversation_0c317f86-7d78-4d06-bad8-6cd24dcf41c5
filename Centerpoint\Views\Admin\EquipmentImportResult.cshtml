﻿@model ImportResultModel
<div class="card">
    <div class="card-header">
        <h6><i class="fa fa-upload"></i>Equipment Data Import Result</h6>
    </div>
    <div class="card-body">  
        <p><strong>Assets Updated: </strong> @Model.UpdatedAssets.Count</p>
        <p><strong>Assets Not Updated: </strong> @Model.NotUpdatedAssets.Count</p>
        <hr />       
        @if (Model.NotUpdatedAssets.Any()) {
            <p>The following assets were not updated during the last import:</p>
            <br />
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Assets Not Updated</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var notUpdatedAsset in Model.NotUpdatedAssets) {
                        <tr>
                            <td><strong>@notUpdatedAsset</strong></td>
                        </tr>
                    }
                </tbody>
            </table>
        }
        <hr />
        @if (Model.UpdateErrors.Any()) {            
            <p>The following <strong>@Model.UpdateErrors.Count</strong> errors were detected during the last import:</p>
            <br />
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Row No.</th>
                        <th>Error</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var updateError in Model.UpdateErrors) {
                        <tr>
                            <td><strong>@updateError.RowNumber</strong></td>
                            <td>@Html.Raw(updateError.Error)</td>
                        </tr>
                    }
                </tbody>
            </table>
        }
    </div>
</div>