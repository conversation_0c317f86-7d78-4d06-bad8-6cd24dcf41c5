
$(document).ready(function () {
    activateTabDependingOnUrlQuery()
    loadSalesGrid();
    loadOpportunitiesGrid();
    loadActionsGrid();
    loadEventsGrid();
});

function deleteCustomer(companyId) {
    $.ajax({
        type: 'POST',
        cache: false,
        dataType: 'json',
        url: '/Sales/DeleteFocusCustomer',
        data: {
            customerId: companyId,
        },
        success: function (result) {
            window.location.reload();
        },
    });
}

$("#confirmCustomer").click(function () {
    $.ajax({
        type: 'POST',
        cache: false,
        dataType: 'json',
        url: '/Sales/AddFocusCustomer',
        data: {
            customerId: viewModel.get("customerId"),
        },
        success: function (result) {
            window.location.reload();
        },
    });
});

function onDB(e) {
    e.sender.options.series[0].labels.visible = function (point) {
        if (point.value < 0) {
            return false
        }
        else {
            return point.value
        }
    }
}

function opportunityData() {
    return {
        opportunityId: viewModel.get("opportunityId")
    }
}

function linkOpportunityData() {
    return {
        opportunityId: viewModel.get("linkOpportunityId")
    }
}

function opportunityEventData() {
    return {
        opportunityEventId: viewModel.get("opportunityEventId")
    }
}

function opportunityEventCount(opportunityId) {
    viewModel.set("opportunityId", opportunityId);

    $("#opportunityEventWindow").data("kendoWindow").center().open();
}

function opportunityEventWindowOpened() {
    var opportunityEventsGrid = $("#opportunityEventsGrid").data("kendoGrid");
    opportunityEventsGrid.dataSource.read();
}

function opportunityAttachmentCount(opportunityId, linkOpportunityId) {
    viewModel.set("linkOpportunityId", linkOpportunityId);
    viewModel.set("opportunityId", opportunityId);

    $("#opportunityAttachmentWindow").data("kendoWindow").center().open();
}

function opportunityAttachmentWindowOpened() {
    var opportunityDocumentsGrid = $("#opportunityDocumentsGrid").data("kendoGrid");
    var opportunityActionDocumentsGrid = $("#opportunityActionDocumentsGrid").data("kendoGrid");
    var opportunityEventDocumentsGrid = $("#opportunityEventDocumentsGrid").data("kendoGrid");
    var wellDocumentsGrid = $("#wellDocumentsGrid").data("kendoGrid");

    opportunityDocumentsGrid.dataSource.read();
    opportunityActionDocumentsGrid.dataSource.read();
    opportunityEventDocumentsGrid.dataSource.read();
    wellDocumentsGrid.dataSource.read();
}

function opportunityActionCount(opportunityId) {
    viewModel.set("opportunityId", opportunityId);

    $("#opportunityActionWindow").data("kendoWindow").center().open();
}

function opportunityActionWindowOpened() {
    var opportunityActionsGrid = $("#opportunityActionsGrid").data("kendoGrid");
    opportunityActionsGrid.dataSource.read();
}

function updateOpportunityActionsGrid() {
    var opportunityActionsGrid = $("#opportunityActionsGrid").data("kendoGrid");
    var totalOpportunityActions = opportunityActionsGrid.dataSource.total();
    viewModel.set("totalOpportunityActions", totalOpportunityActions);
}

function followUpActionCount(opportunityEventId) {
    viewModel.set("opportunityEventId", opportunityEventId);

    $("#followUpActionWindow").data("kendoWindow").center().open();
}

function followUpActionWindowOpened() {
    var followUpActionsGrid = $("#followUpActionsGrid").data("kendoGrid");
    followUpActionsGrid.dataSource.read();
}

function updateFollowUpActionsGrid() {
    var followUpActionsGrid = $("#followUpActionsGrid").data("kendoGrid");
    var totalFollowUpActions = followUpActionsGrid.dataSource.total();
    viewModel.set("totalFollowUpActions", totalFollowUpActions);
}

function updateOpportunityEventsGrid() {
    var opportunityEventsGrid = $("#opportunityEventsGrid").data("kendoGrid");
    var totalOpportunityEvents = opportunityEventsGrid.dataSource.total();
    viewModel.set("totalOpportunityEvents", totalOpportunityEvents);
}

function saveSalesGrid(e) {
    setTimeout(function () {
        var grid = $("#salesGrid").data("kendoGrid");
        localStorage["salesGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}

function loadSalesGrid() {
    var grid = $("#salesGrid").data("kendoGrid");
    var toolBar = $("#salesGrid .k-grid-toolbar").html();
    var options = localStorage["salesGrid"];
    viewModel.set("initialSalesGridOptions", kendo.stringify(grid.getOptions()));
    if (options) {
        grid.setOptions(JSON.parse(options));
        $("#salesGrid .k-grid-toolbar").html(toolBar);
        $("#salesGrid .k-grid-toolbar").addClass("k-grid-top");
    }
    $("#salesGrid").data("kendoGrid").dataSource.read();
}

function saveOpportunityGrid(e) {
    setTimeout(function () {
        var grid = $("#opportunitiesGrid").data("kendoGrid");
        localStorage["opportunitiesGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}

function loadOpportunitiesGrid() {
    var grid = $("#opportunitiesGrid").data("kendoGrid");
    var toolBar = $("#opportunitiesGrid .k-grid-toolbar").html();
    var options = localStorage["opportunitiesGrid"];
    viewModel.set("initialOpportunitiesGridOptions", kendo.stringify(grid.getOptions()));
    if (options) {
        grid.setOptions(JSON.parse(options));
        $("#opportunitiesGrid .k-grid-toolbar").html(toolBar);
        $("#opportunitiesGrid .k-grid-toolbar").addClass("k-grid-top");
    }
    $("#opportunitiesGrid").data("kendoGrid").dataSource.read();
}

function saveActionGrid(e) {
    setTimeout(function () {
        var grid = $("#actionsGrid").data("kendoGrid");
        localStorage["actionsGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}

function loadActionsGrid() {
    var grid = $("#actionsGrid").data("kendoGrid");
    var toolBar = $("#actionsGrid .k-grid-toolbar").html();
    var options = localStorage["actionsGrid"];
    viewModel.set("initialActionsGridOptions", kendo.stringify(grid.getOptions()));
    if (options) {
        grid.setOptions(JSON.parse(options));
        $("#actionsGrid .k-grid-toolbar").html(toolBar);
        $("#actionsGrid .k-grid-toolbar").addClass("k-grid-top");
    }
    $("#actionsGrid").data("kendoGrid").dataSource.read();
}

function saveEventGrid(e) {
    setTimeout(function () {
        var grid = $("#eventsGrid").data("kendoGrid");
        localStorage["eventsGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}

function loadEventsGrid() {
    var grid = $("#eventsGrid").data("kendoGrid");
    var toolBar = $("#eventsGrid .k-grid-toolbar").html();
    var options = localStorage["eventsGrid"];
    viewModel.set("initialEventsGridOptions", kendo.stringify(grid.getOptions()));
    if (options) {
        grid.setOptions(JSON.parse(options));
        $("#eventsGrid .k-grid-toolbar").html(toolBar);
        $("#eventsGrid .k-grid-toolbar").addClass("k-grid-top");
    }
    $("#eventsGrid").data("kendoGrid").dataSource.read();
}

function updateSalesGrid() {
    $("#resetSalesGrid").click(function (e) {
        e.preventDefault();
        resetGridView('salesGrid', 'initialSalesGridOptions')
        refreshSalesGrid()
    });

    var salesGrid = $("#salesGrid").data("kendoGrid");
    var totalSales = salesGrid.dataSource.total();
    viewModel.set("totalSales", totalSales);
}

function updateOpportunityGrid() {
    $("#resetOpportunitiesGrid").click(function (e) {
        e.preventDefault();
        resetGridView('opportunitiesGrid', 'initialOpportunitiesGridOptions')
        refreshOpportunityGrid()
    });

    var opportunitiesGrid = $("#opportunitiesGrid").data("kendoGrid");
    var totalOpportunities = opportunitiesGrid.dataSource.total();
    viewModel.set("totalOpportunities", totalOpportunities);
}

function refreshOpportunityGrid() {
    var opportunitiesGrid = $("#opportunitiesGrid").data("kendoGrid");
    opportunitiesGrid.dataSource.read();
}

function updateActionsGrid() {
    $("#resetActionsGrid").click(function (e) {
        e.preventDefault();
        resetGridView('actionsGrid', 'initialActionsGridOptions')
        $("#actionsGrid").data("kendoGrid").dataSource.read();
    });

    var actionsGrid = $("#actionsGrid").data("kendoGrid");
    var totalActions = actionsGrid.dataSource.total();
    viewModel.set("totalActions", totalActions);
}

function updateEventsGrid() {
    $("#resetEventsGrid").click(function (e) {
        e.preventDefault();
        resetGridView('eventsGrid', 'initialEventsGridOptions')
        $("#eventsGrid").data("kendoGrid").dataSource.read();
    });

    var eventsGrid = $("#eventsGrid").data("kendoGrid");
    var totalEvents = eventsGrid.dataSource.total();
    viewModel.set("totalEvents", totalEvents);
}

function refreshSalesGrid() {
    var salesGrid = $("#salesGrid").data("kendoGrid");
    salesGrid.dataSource.read();
}

function salesData() {
    return {
        type: viewModel.get("type")
    }
}

function showClosedReasonWindow(opportunityId) {
    viewModel.set("opportunityId", opportunityId);

    $("#closedReasonWindowOpen").data("kendoWindow").center().open();
}

$("#closureReasonConfirm").click(function () {
    var opportunityId = viewModel.get("opportunityId")
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: '/Sales/ClosureReason',
        data: {
            opportunityId: opportunityId,
            reasonId: $("#reason").data("kendoDropDownList").value(),
            reasonText: $("#reason").data("kendoDropDownList").text(),
            comment: $("#ClosureReason").val()
        },
        success: function () {
            refreshSalesGrid();
            refreshOpportunityGrid();
            $("#ClosureReason").val("");
            $("#reason").data("kendoDropDownList").value("");
            $("#closedReasonWindowOpen").data("kendoWindow").close();
        },
    });
});

function convertToProject(e) {
    var grid = $("#opportunitiesGrid").data("kendoGrid");
    var opportunity = grid.dataItem($(e).closest("tr"));
    if (opportunity.CompanyWells && opportunity.CompanyFields && opportunity.Value && opportunity.OpportunityObjectives) {
        var confirmConvert = confirm("Are you sure you wish to convert this opportunity to project?");
        if (confirmConvert) {
            //window.location.href = "@Url.Action("ConvertToProject", "Sales")/" + OpportunityId;
            window.location.href = "/Sales/ConvertToProject/"+ opportunity.LinkOpportunityId + "?maxRevision=" + opportunity.Revision + "&oppsId=" + opportunity.OpportunityId;
        }
    } else {
        $("<div id='dialog'></div>").kendoDialog({
            closable: false,
            title: "Warning!",
            close:()=>{
                var dialog = $("#dialog").data("kendoDialog");
                dialog.destroy();
            },
            width: 500,
            buttonLayout: "normal",
            content: "Please make sure Field, Well, Value and Objectives has value.",
            actions: [
                {
                    text: "Close",
                    action: function(){
                    },
                    cssClass: 'btn-primary'
                },
            ]
        }).data("kendoDialog").open().center()
    }
}


function deleteLead(opportunityId) {
    var confirmDelete = confirm("Are you sure you wish to delete this lead?");
    if (confirmDelete) {
        window.location.href = `/Sales/Delete/${opportunityId}`;
    }
}

function convertLead(opportunityId) {
    var confirmConvert = confirm("Are you sure you wish to convert this lead to opportunity?");
    if (confirmConvert) {
        window.location.href = `/Sales/UpdateStatus/${opportunityId}`;
    }
}

function deleteOpportunity(opportunityId) {
    var confirmDelete = confirm("Are you sure you wish to delete this opportunity?");
    if (confirmDelete) {
        window.location.href = `/Sales/Delete/${opportunityId}`;
    }
}

function updateOpportunityEventDocumentsGrid() {
    var opportunityEventDocumentsGrid = $("#opportunityEventDocumentsGrid").data("kendoGrid");
    var totalOpportunityEventDocuments = opportunityEventDocumentsGrid.dataSource.total();
    viewModel.set("totalOpportunityEventDocuments", totalOpportunityEventDocuments);
}

function updateOpportunityActionDocumentsGrid() {
    var opportunityActionDocumentsGrid = $("#opportunityActionDocumentsGrid").data("kendoGrid");
    var totalOpportunityActionDocuments = opportunityActionDocumentsGrid.dataSource.total();
    viewModel.set("totalOpportunityActionDocuments", totalOpportunityActionDocuments);
}

function updateWellDocumentsGrid() {
    var wellDocumentsGrid = $("#wellDocumentsGrid").data("kendoGrid");
    var totalOpportunityWellDocuments = wellDocumentsGrid.dataSource.total();
    viewModel.set("totalOpportunityWellDocuments", totalOpportunityWellDocuments);
}

function updateOpportunityDocumentsGrid() {
    var opportunityDocumentsGrid = $("#opportunityDocumentsGrid").data("kendoGrid");
    var totalOpportunityDocuments = opportunityDocumentsGrid.dataSource.total();
    viewModel.set("totalOpportunityDocuments", totalOpportunityDocuments);
}

function onClosedReasonWindowOpen() {
    $("#reason").data("kendoDropDownList").dataSource.read();
}

function onCustomerWindow() {
    $("#customer").data("kendoDropDownList").dataSource.read();
}

function activateTabDependingOnUrlQuery () {
    let tabId = window.location.search.split("tab=")[1];
    if(tabId) {
        let tabToActivate = $(`#${tabId}`);
        if(tabToActivate) {
          $("#matrixTabStrip").data("kendoTabStrip").select(tabToActivate)
        }
    }
}

var viewModel = kendo.observable({
    hasCustomer :false,
    totalSales: 0,
    totalOpportunities: 0,
    totalEvents: 0,
    totalActions: 0,
    type: "",
    opportunityId: 0,
    totalOpportunityActions: 0,
    totalOpportunityEvents: 0,
    totalOpportunityActionDocuments: 0,
    totalOpportunityEventDocuments: 0,
    totalOpportunityWellDocuments: 0,
    totalOpportunityDocuments: 0,
    opportunityEventId: 0,
    linkOpportunityId: 0,
    totalFollowUpActions: 0,
    customerId: "",
    leadsStripText: function(){
        return `<span class="k-link"><i class="fa fa-line-chart mr-1"></i> Leads (<span data-bind="text: totalSales"></span>)</span>`;
    },
    opportunitiesStripText: function(){
        return `<span class="k-link"><i class="fa fa-lightbulb mr-1"></i> Opportunities (<span data-bind="text:totalOpportunities"></span>)</span>`;
    },
    eventsStripText: function(){
        return `<span class="k-link"><i class="fa fa-calendar mr-1"></i> Events (<span data-bind="text:totalEvents"></span>)</span>`;
    },
    actionsStripText: function(){
        return `<span class="k-link"><i class="fa fa-check mr-1"></i> Actions (<span data-bind="text:totalActions"></span>)</span>`;
    },

    totalClick: function () {
        this.set("type", "total");
        refreshSalesGrid();
    },
    leadClick: function () {
        this.set("type", "lead");
        refreshSalesGrid();
    },
    opportunityClick: function () {
        this.set("type", "opportunity");
        refreshSalesGrid();
    },
    enquiryClick: function () {
        this.set("type", "enquiry");
        refreshSalesGrid();
    },
    proposalClick: function () {
        this.set("type", "proposal");
        refreshSalesGrid();
    },
    reviewClick: function () {
        this.set("type", "review");
        refreshSalesGrid();
    },
    awardClick: function () {
        this.set("type", "award");
        refreshSalesGrid();
    },

    showCustomerWindow: function () {
        $("#customerWindow").data("kendoWindow").center().open();
    },

});

kendo.bind(document.body.children, viewModel);