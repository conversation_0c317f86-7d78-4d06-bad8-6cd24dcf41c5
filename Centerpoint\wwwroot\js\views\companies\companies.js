$(document).ready(function () {
    var companyGrid = $('#companyGrid').data("kendoGrid");
    loadCompanyGrid();

    companyGrid.bind('dataBound', function (e) {
        this.element.find('.k-i-excel').remove();
    });

});

function loadCompanyGrid() {
    var grid = $("#companyGrid").data("kendoGrid");
    var options = localStorage["companyGrid"];
    var toolBar = $("#companyGrid .k-grid-toolbar").html();
    viewModel.set("initialCompanyGridOptions", kendo.stringify(grid.getOptions()));
    if (options) {
        grid.setOptions(JSON.parse(options));
        $("#companyGrid .k-grid-toolbar").html(toolBar);
        $("#companyGrid .k-grid-toolbar").addClass("k-grid-top");
    }
}

function saveCompanyGrid(e) {
    setTimeout(function () {
        var grid = $("#companyGrid").data("kendoGrid");
        localStorage["companyGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}
function onEdit(e) {
    //on row edit replace the Delete and Edit buttons with Update and Cancel
    $(e.container).find("td:last").html("<a href='javascript: void(0)' class='btn btn-success btn-sm' onclick='updateRow()' title='update button'><i class='fa fa-check-circle'></i>Update</a> " +
        "<a href='javascript: void(0)' class='btn btn-warning btn-sm' onclick='cancelRow()' title='cancel button'><i class='fa fa-ban'></i>Cancel</a>");
}       

function onError(e, status) {
    if (e.status == "customerror") {
        alert(e.errors);

        var companyGrid = $("#companyGrid").data("kendoGrid");
        companyGrid.dataSource.cancelChanges();
    }
}

function updateCompanyTotals() {
    $("#resetCompanyGrid").click(function (e) {
        e.preventDefault();
        resetGridView('companyGrid', 'initialCompanyGridOptions')
    });

    var companyGrid = $("#companyGrid").data("kendoGrid");
    var totalCompanies = companyGrid.dataSource.total();
    viewModel.set("totalCompanies", totalCompanies);
}

var viewModel = new kendo.observable({
    totalCompanies: 0            
});

kendo.bind(document.body.children, viewModel);