﻿@model CompanyModel

@Html.Partial( "_GridNotification", EntityType.Company)
    @if(ViewBag.Revision == null) {
        <div class="header-container-between">
            <h4>
                <i class="fa fa-building"></i>
                Entities
                (<span data-bind="text:totalCompanies"></span>)
            </h4>
            <div>
                @if (ViewBag.OpportunityId != null) {
                    <a class="btn btn-info btn-sm" href="@Url.Action("Edit","Sales",new { @id = ViewBag.OpportunityId, @revision = ViewBag.Revision} )">
                        <i class="fa fa-refresh"></i>Return to Opportunity
                    </a>
                }
            </div>
        </div>
        <hr /> 
    }
   


    
        @if (ViewBag.Revision == null) {
            <div class="mb-3">
                <div>
                    @(Html.Kendo().Grid<CompanyModel>()
                        .Name("companyGrid")
                        .Columns(columns => {
                            columns.Bound(c => c.Name).ClientTemplate("<a href='" + @Url.Action("EditCompany", "Admin", new { @id = "" }) + "/#=CompanyId#'>#=Name#</a>").Width(200); 
                            columns.Bound(c => c.Categories).Title("Category").Width(200);
                            columns.Bound(c => c.IsActive).Title("Active").ClientTemplate("#if(IsActive){#Yes#}else{#No#}#").Width(80);
                            columns.Bound(c => c.CompanyLocationCount).Title("Locations").Width(80);
                            columns.Bound(c => c.CompanyContactCount).Title("Contacts").Width(80);
                            columns.Bound(c => c.CompanyFieldsCount).Title("Fields").Width(80);
                            columns.Bound(c => c.CompanyWellsCount).Title("Wells").Width(80);
                            if (Model.CompanyId.HasValue) {
                                columns.Command(c=> c
                            .Destroy().Text("Delete").HtmlAttributes(new { @class = "bg-danger text-white grid-action-button" }).Visible("companyDeleteVisible")
                                ).Width(80);
                            }
                        })
                        .Events(e => e.DataBound("updateCompanyTotals"))
                        .Editable(e => e.Mode(GridEditMode.InLine).DisplayDeleteConfirmation("Are you sure you wish to delete this entity?"))

                        .ToolBar(t => {
                            t.Custom().ClientTemplate("<a class='btn btn-primary btn-sm d-flex align-items-center bg-primary text-white' href='/admin/AddCompany'><span class='k-icon k-i-plus k-button-icon mr-2'></span>Add New Entity</a>");
                            t.Excel().Text("Export");
                        }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
                        .ColumnMenu(c => c.Columns(true))
                        .Filterable()
                        .Sortable()
                        .Groupable()
                        .Height(440)
                        .Reorderable(c => c.Columns(true))
                        .Scrollable()
                        .Excel(excel => excel
                                .FileName(string.Format("Centerpoint_ClientCompanies_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                                .Filterable(true)
                                .ProxyURL(Url.Action("Export", "Admin"))
                                )
                        .DataSource(dataSource => dataSource
                        .Ajax()
                        .ServerOperation(false)
                        .Model(model => {
                            model.Id(m => m.CompanyId);
                        })
                        .Events(e => e.Error("onError").RequestEnd("onRequestEnd"))
                        .Read(read => read.Action("GetCompanies", "Admin"))
                        .Destroy(destroy => destroy.Action("DeleteCompany", "Admin")))
                    )
                </div>
            </div>
        }
    

    <div class="header-container-between">
        <h4>
            <i class="fa fa-building"></i>
            @(Model.CompanyId.HasValue ? Model.Name : "Add New Entity")
        </h4>
        <div>
            @if (ViewBag.Revision != null) {
               @if (ViewBag.OpportunityId != null) {
                    <a class="btn btn-info btn-sm" href="@Url.Action("Edit","Sales",new { @id = ViewBag.OpportunityId, @revision = ViewBag.Revision} )">
                        <i class="fa fa-refresh"></i>Return to Opportunity
                    </a>
                } else {
                    <a class="btn btn-info btn-sm" href="@Url.Action("Index","Sales")">
                        <i class="fa fa-refresh"></i>Return to Sales Dashboard
                    </a>
                }  
            }
        </div>
    </div>
    <hr />

    <div>
            @using (Html.BeginForm("EditCompany", "Admin")) {
                @Html.ValidationSummary(false)
                @(Html.Kendo().TabStrip()
                    .Name("editCompanyTabStrip")
                    .SelectedIndex(0)
                    .Animation(animation =>
                    {
                        animation.Enable(false);
                    })
                    .Items(tabstrip =>
                    {
                        tabstrip.Add()
                        .Text("Details")
                        .HtmlAttributes(new { @data_bind="html: detailsTabText"})
                        .Selected(true)
                        .Content(@<text>
                            <div class="container-fluid pl-0 pr-0">
                                <div class="row p-0">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            @Html.LabelFor(m => m.Name)
                                            @(Html.Kendo().TextBoxFor(m => m.Name).HtmlAttributes(new { @class = "form-control"}))
                                        </div>
                                        <div class="form-group">
                                            @Html.LabelFor(m => m.IsInternal)
                                            @(Html.Kendo().DropDownListFor(m => m.IsInternal)
                                            .Filter("contains")
                                            .DataValueField("Key")
                                            .DataTextField("Value")
                                            .BindTo(Html.BooleanListValues().ToList()))
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>Category</label>
                                            @(Html.Kendo().MultiSelectFor(m => m.CategoryIds)
                                            .DataTextField("Text")
                                            .DataValueField("Value")
                                            .Placeholder("Select Categories")
                                            .Filter("contains")                                            
                                            .DataSource(source => {
                                                source.Read(read => {
                                                     read.Action("GetCategories", "Lookup");
                                                }).ServerFiltering(true);
                                            }))
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            @Html.LabelFor(m => m.IsActive)
                                            @(Html.Kendo().DropDownListFor(m => m.IsActive)
                                            .Filter("contains")
                                            .DataValueField("Key")
                                            .DataTextField("Value")
                                            .BindTo(Html.BooleanListValues().ToList()))
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            @*<button type="submit" class="btn btn-sm btn-primary">Save Entity Details</button>*@
                        
                        </text>);

                        if (Model.CompanyId.HasValue) {
                            tabstrip.Add()
                            .Text("Locations")
                            .HtmlAttributes(new { @data_bind="html:locationsTabText"})
                            .Content(@<text>
                                @(Html.Kendo().Grid<CompanyLocationModel>()
                                    .Name("companyLocationGrid")
                                    .Columns(columns => {
                                        columns.Bound(c => c.Name);
                                        columns.Bound(c => c.Street).Title("Address Line 1");
                                        columns.Bound(c => c.HouseNumber).Title("Address Line 2").Hidden(true);
                                        columns.Bound(c => c.City);
                                        columns.Bound(c => c.Postcode).Title("Address Line 3").Hidden(true);
                                        columns.Bound(c => c.Country).Hidden(true);
                                        columns.Bound(c => c.Telephone).Hidden(true);
                                        columns.Bound(c => c.Comment).Hidden(true);
                                        columns.Bound(c => c.IsOffshore).Title("Offshore").ClientTemplate("#if(IsOffshore){#Yes#}else{#No#}#");
                                        columns.Bound(c => c.IsLocationActive).Title("Active").ClientTemplate("#if(IsLocationActive){#Yes#}else{#No#}#");
                                        columns.Command(c=>{
                                            c.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                                            c.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"});
                                        });
                                    })
                                    .ColumnMenu(c => c.Columns(true))
                                    .ToolBar(t => {
                                        t.Create().Text("Add Entity Location");
                                        t.Excel().Text("Export");
                                    }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
                                    .Editable(editable => editable
                                        .Mode(GridEditMode.PopUp)
                                        .TemplateName("CompanyLocation")
                                        .Window(w => w
                                            .Name("companyLocationWindow")
                                            .Title("Entity Location")
                                            .Width(800)
                                            .Draggable(false)
                                        )
                                    )
                                    .Events(e => e.DataBound("updateCompanyLocationTotals"))
                                    .Events(e => e.Edit("offshoreChanged"))
                                    .Sortable()
                                    .Groupable()
                                    .Filterable()
                                    .Scrollable()
                                    .Resizable(resize => resize.Columns(true))
                                    .Reorderable(reorder => reorder.Columns(true))
                                    .Excel(excel => excel
                                        .FileName(string.Format("Centerpoint_CompanyLocation_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                                        .Filterable(true)
                                        .ProxyURL(Url.Action("Export", "Admin"))
                                    )
                                    .DataSource(dataSource => dataSource
                                        .Ajax()
                                        .ServerOperation(false)
                                        .Model(model => {
                                            model.Id(m => m.CompanyLocationId);
                                            model.Field(f => f.IsLocationActive).DefaultValue(true);
                                            model.Field(f => f.IsOffshore).DefaultValue(false);
                                        })
                                        .Read(read => read.Action("GetCompanyLocations", "Admin", new { @cId = Model.CompanyId }))
                                        .Create(create => create.Action("UpdateCompanyLocation", "Admin", new { @cId = Model.CompanyId }))
                                        .Update(update => update.Action("UpdateCompanyLocation", "Admin", new { @cId = Model.CompanyId }))
                                        .Destroy(destroy => destroy.Action("DeleteCompanyLocation", "Admin"))
                                    )
                                )
                            </text>);

                            tabstrip.Add()
                            .Text("Contacts")
                            .HtmlAttributes(new { @data_bind="html:contactsTabText"})
                            .Content(@<text>
                                @(Html.Kendo().Grid<CompanyContactModel>()
                                    .Name("companyContactGrid")
                                    .Columns(columns => {
                                        columns.Bound(c => c.CompanyLocationName).Title("Location");
                                        columns.Bound(c => c.FirstName);
                                        columns.Bound(c => c.LastName);
                                        columns.Bound(c => c.Position).Hidden(true);
                                        columns.Bound(c => c.EmailAddress).Title("Email Address").ClientTemplate("<a href='mailto:#=EmailAddress#'>#=EmailAddress ? EmailAddress : '' #</a>");
                                        columns.Bound(c => c.Mobile).Hidden(true);
                                        columns.Bound(c => c.WorkTelephone).Hidden(true);
                                        columns.Bound(c => c.Comment).Hidden(true);
                                        columns.Bound(c => c.NotActive).Title("Not Active").ClientTemplate("#if(NotActive){#Yes#}else{#No#}#");
                                        columns.Command(c=>{
                                            c.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                                            c.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"});
                                        });
                                    })
                                    .ColumnMenu(c => c.Columns(true))
                                    .ToolBar(t => {
                                        t.Create().Text("Add Entity Contact");
                                        t.Excel().Text("Export");
                                    }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
                                    .Editable(editable => editable
                                        .Mode(GridEditMode.PopUp)
                                        .TemplateName("CompanyContact")
                                        .Window(w => w
                                            .Name("companyContactWindow")
                                            .Title("Entity Contact")
                                            .Width(800)
                                            .Draggable(false)
                                        )
                                    )
                                    .Events(e => e.DataBound("updateCompanyContactTotals"))
                                    .Excel(excel => excel
                                            .FileName(string.Format("Centerpoint_CompanyContact_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                                            .Filterable(true)
                                            .ProxyURL(Url.Action("Export", "Admin"))
                                        )
                                    .Sortable()
                                    .Groupable()
                                    .Filterable()
                                    .Scrollable()
                                    .DataSource(dataSource => dataSource
                                        .Ajax()
                                        .ServerOperation(false)
                                        .Model(model => {
                                            model.Id(m => m.CompanyContactId);
                                        })
                                        .Read(read => read.Action("GetCompanyContacts", "Admin", new { @companyId = Model.CompanyId }))
                                        .Create(create => create.Action("UpdateCompanyContact", "Admin"))
                                        .Update(update => update.Action("UpdateCompanyContact", "Admin"))
                                        .Destroy(destroy => destroy.Action("DeleteCompanyContact", "Admin"))
                                    )
                                )
                            </text>);

                            tabstrip.Add()
                            .Text("Assets")
                            .HtmlAttributes(new { @data_bind="html:assetsTabText"})
                            .Content(@<text>
                                <div class="row mt-3">
                                    <div class="col-md-4">
                                        <div class="text-primary mb-3">
                                           <h5>Fields (<span data-bind="text:totalCompanyFields"></span>)</h5> 
                                        </div>
                                        <div>
                                            @(Html.Kendo().Grid<CompanyFieldModel>()
                                                    .Name("companyFieldGrid")
                                                    .Columns(c => {
                                                        c.Bound(p => p.Name);
                                                        c.Command(c=>{
                                                            c.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                                                            c.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"});
                                                        }).Width(200);
                                                    })
                                                    .Editable(editable => editable.Mode(GridEditMode.InLine))
                                                    .ToolBar(t=>{
                                                        if (Model.CanAddAsset)
                                                        {
                                                            t.Create().Text("Add Field");
                                                        }
                                                    })
                                                .Sortable()
                                                .Filterable()
                                                .Scrollable()
                                                .Resizable(c => c.Columns(true))
                                                .ColumnMenu(c => c.Columns(true))
                                                .Events(e => e.DataBound("updateCompanyFieldTotal"))
                                                .DataSource(dataSource => dataSource
                                                    .Ajax()
                                                    .ServerOperation(false)
                                                    .Model(m => m.Id(p => p.CompanyFieldId))
                                                    .Read(read => read.Action("GetCompanyFields", "Admin", new { @companyId = Model.CompanyId }))
                                                    .Create(create => create.Action("UpdateCompanyField", "Admin", new { @compId = Model.CompanyId }))
                                                    .Update(update => update.Action("UpdateCompanyField", "Admin", new { @compId = Model.CompanyId }))
                                                    .Destroy(destroy => destroy.Action("DeleteCompanyField", "Admin"))
                                                )
                                            )
                                        </div>
                                    </div>
                                    @if(!GlobalSettings.IsAisus)
                                    {
                                        <div class="col-md-8">
                                            <div class="text-primary mb-3">
                                               <h5>Wells (<span data-bind="text:totalCompanyWells"></span>)</h5> 
                                            </div>
                                            <div>
                                                @(Html.Kendo().Grid<CompanyWellModel>()
                                                    .Name("companyWellGrid")
                                                    .Columns(c => {
                                                        c.Bound(p => p.Name).Title("Name").ClientTemplate("<a href='" + @Url.Action("EditCompanyWell", "Admin") + "/#=CompanyWellId#'>#=Name#</a>");
                                                        c.Bound(p => p.CompanyFieldName).Title("Field").ClientTemplate("#=CompanyFieldName#").ClientGroupHeaderTemplate("Field : #= value # (#= count#)");
                                                        c.Bound(p => p.MinimumId);
                                                        c.Bound(p => p.MaximumDeviation);
                                                        c.Bound(p => p.MaximumPressure).Hidden(true);
                                                        c.Bound(p => p.MaximumPressureUnits).Hidden(true);
                                                        c.Bound(p => p.MaximumTemperature).Hidden(true);
                                                        c.Bound(p => p.MaximumTemperatureDegrees).Hidden(true);
                                                        c.Bound(p => p.H2S).Hidden(true);
                                                        c.Bound(p => p.CO2).Hidden(true);
                                                        c.Bound(p => p.FluidTypes).Hidden(true);
                                                        c.Command(c=>{
                                                            c.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"});
                                                        }).Width(100);
                                                    })
                                                    .Editable(editable => editable.Mode(GridEditMode.InLine))
                                                    .ToolBar(t => {
                                                        if (Model.CanAddAsset) {
                                                            t.Custom().ClientTemplate("<a class='btn btn-primary btn-sm d-flex align-items-center bg-primary text-white' href='/admin/AddCompanyWell?id=" + @Model.CompanyId + "'><span class='k-icon k-i-plus k-button-icon mr-2'></span>Add New Well</a>");
                                                        }
                                                    })
                                                    .Sortable()
                                                    .Filterable()
                                                    .Groupable()
                                                    .Scrollable()
                                                    .Resizable(c => c.Columns(true))
                                                    .ColumnMenu(c => c.Columns(true))
                                                    .Events(e => e.DataBound("updateCompanyWellTotal"))
                                                    .DataSource(dataSource => dataSource
                                                        .Ajax()
                                                        .Events(e => e.Error("onCompanyWellError"))
                                                        .ServerOperation(false)
                                                        .Aggregates(aggregates => {
                                                            aggregates.Add(p => p.CompanyFieldName).Min().Max().Count();
                                                        })
                                                        .Group(group => group.Add(p => p.CompanyFieldName))
                                                        .Sort(sort => sort.Add(p => p.Name))
                                                        .Model(model => {
                                                            model.Id(m => m.CompanyWellId);
                                                        })
                                                        .Read(read => read.Action("GetCompanyWells", "Admin", new { @companyId = Model.CompanyId }))
                                                        .Destroy(destroy => destroy.Action("DeleteCompanyWell", "Admin"))
                                                    )
                                                )
                                            </div>
                                        </div>
                                    }
                                </div>
                            </text>);   
                        }
                    })
                )
        <button id="postEntityButton" type="button" class="btn btn-primary btn-sm" @*onclick="postUserDetails()"*@>Save Entity Details</button>
                @Html.HiddenFor(m => m.CompanyId)
            }
    </div>


    <script>
        function updateCompanyFieldTotal() {
            var companyFieldGrid = $("#companyFieldGrid").data("kendoGrid");
            var totalCompanyFields = companyFieldGrid.dataSource.total();
            viewModel.set("totalCompanyFields", totalCompanyFields);
        }

        function updateCompanyWellTotal() {
            var companyWellGrid = $("#companyWellGrid").data("kendoGrid");
            var totalCompanyWells = companyWellGrid.dataSource.total();
            var data = this.dataSource.view();
            for (var i = 0; i < data.length; i++) {
                var dataItem = data[i];
                var companyWellId = dataItem.CompanyWellId
                viewModel.set("companyWellId", companyWellId);
            }
            viewModel.set("totalCompanyWells", totalCompanyWells);
        }

        function onError(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var companyGrid = $("#companyGrid").data("kendoGrid");
                companyGrid.dataSource.cancelChanges();
            }

            onErrorDeleteGridHandler(e, "#companyGrid");
        }

        function onCompanyWellError(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var companyWellGrid = $("#companyWellGrid").data("kendoGrid");
                companyWellGrid.dataSource.cancelChanges();
            }
        }

        function updateCompanyLocationTotals() {
            var companyLocationGrid = $("#companyLocationGrid").data("kendoGrid");
            var totalcompanyLocations = companyLocationGrid.dataSource.total();
            viewModel.set("totalcompanyLocations", totalcompanyLocations);
        }

        function updateCompanyContactTotals() {
            var companyContactGrid = $("#companyContactGrid").data("kendoGrid");
            var totalcompanyContacts = companyContactGrid.dataSource.total();
            viewModel.set("totalcompanyContacts", totalcompanyContacts);
        }

        function updateCompanyTotals() {
            var companyGrid = $("#companyGrid").data("kendoGrid");
            var totalCompanies = companyGrid.dataSource.total();
            viewModel.set("totalCompanies", totalCompanies);
        }

        function offshoreChanged(e) {

            var isOffshore = $("#IsOffshore").data("kendoDropDownList");
            var isOffshoreValue = isOffshore.value();

            if (isOffshoreValue == "true") {
                $("#Street").removeAttr("data-val-required");
                $("#City").removeAttr("data-val-required");
            } else {
                $("#Street").attr("data-val-required", "Street is required");
                $("#City").attr("data-val-required", "City is required");
            }
        }
        function onCompanyContactError(args) {
            if (args.errors) {
                var grid = this; 
                grid.one("dataBinding", function (e) {
                    alert(args.errors);
                });
            }
            var companyContactGrid = $("#companyContactGrid").data("kendoGrid");
           companyContactGrid.dataSource.read();
        }

        function companyDeleteVisible(e){
            return e.CompanyId !== Number(document.getElementById("CompanyId").value);
        }

        var viewModel = new kendo.observable({
            totalcompanies: 0,
            detailsTabText: function(){
                return `<span class="k-link"><i class="fa fa-file-text mr-2"></i> Details</span>`;   
            },
            totalcompanyLocations: 0,
            locationsTabText: function(){
                return `<span class="k-link"><i class="fa fa-globe mr-2"></i> Locations (<span data-bind="text:totalcompanyLocations"></span>)</span>`;   
            }, 
            totalcompanyContacts: 0,
            contactsTabText: function(){
                return `<span class="k-link"><i class="fa fa-users mr-2"></i> Contacts (<span data-bind="text:totalcompanyContacts"></span>)</span>`;
            },
            assetsTabText: function(){
                return `<span class="k-link"><i class="fa fa-tags mr-2"></i> Assets </span>`;        
            },
            totalCompanyFields: 0,
            totalCompanyWells: 0,
            totalWellDocuments: 0,
            companyWellId: 0
        });
        kendo.bind(document.body.children, viewModel);
    </script>

    <script type="text/javascript" >
        $(document).ready(function(){
            submitSuccessHandler($("#Name").data("kendoTextBox").value());
            saveCompamyHandler();
        });

        function saveCompamyHandler() {
        $("#postEntityButton").click(function (event) {
                event.preventDefault();

                const items = $("#CategoryIds").data("kendoMultiSelect").value();
                let categoryListData = $("#CategoryIds").data("kendoMultiSelect").dataSource.data().filter(r => items.includes(r.CategoryId));
                const categoryList = [];

                categoryListData.forEach(i => {
                    categoryList.push({
                        Name: i.Name,
                        CategoryId: i.CategoryId,
                    })
                })

                if("@Model.CompanyId"){
                    updateCompany(items, categoryList);
                }else{
                    createCompany(items, categoryList);
                }                
            });
        }

        function createCompany(items, categoryList){
             let model = {
                        CompanyId: null,
                        IsInternal: $("#IsInternal").data("kendoDropDownList").value(),
                        CategoryIds: items,
                        CategoriesList: categoryList,
                        IsActive: $("#IsActive").data("kendoDropDownList").value(),
                        Name: $("#Name").data("kendoTextBox").value(),
                    }

                    $.ajax({
                    type: 'POST',
                    url: '/Admin/CreateCompany',
                    dataType: "json",
                    data: {
                        model: model
                    },
                    success: function (e) {
                        window.location.href = "/Admin/EditCompany/" + e.id + "?isCreated=true";
                    },
                    error: function (e) {
                    },
                })
        }

        function updateCompany(items, categoryList){
                   let model = {
                        CompanyId: Number("@Model.CompanyId"),
                        CategoriesList: categoryList,
                        IsInternal: $("#IsInternal").data("kendoDropDownList").value(),
                        CategoryIds:items,
                        IsActive: $("#IsActive").data("kendoDropDownList").value(),
                        Name: $("#Name").data("kendoTextBox").value(),
                    }

                    $.ajax({
                    type: 'POST',
                    url: '/Admin/EditCompany',
                    dataType: "json",
                    data: {
                        model: model
                    },
                    success: function (e) {
                        updateMessage($("#Name").data("kendoTextBox").value());
                        var companyGrid = $("#companyGrid").data("kendoGrid");
                        companyGrid.dataSource.read();
                    },
                    error: function (e) {
                    },
                })
        }

</script>

