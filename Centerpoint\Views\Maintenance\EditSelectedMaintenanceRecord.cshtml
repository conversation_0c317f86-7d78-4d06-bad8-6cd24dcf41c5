﻿
<div class="row">
    <div class="col-md-4">
        <div class="form-group">
            <label>Created Date</label>
            <br />
            @(Html.Ken<PERSON>().DateTimePicker()
            .Name("Created")
            .Events(x=>x.Change("createdChange"))
            .HtmlAttributes(new { @style = "width:100%" }))
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            <label>Start Date</label>
            <br />
                @(Html.Kendo().DateTimePicker()
                .Name("StartDate")
                .Events(x => x.Change("startDateChange"))
                .HtmlAttributes(new { @style = "width:100%" }))
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
                <label>Completed Date</label>
            <br />
            @(Html.Kendo().DateTimePicker().Name("CompletedDate").HtmlAttributes(new { @style = "width:100%" }))
        </div>
        
    </div>
</div>
    <span><a id="confirmDates" class="btn btn-primary btn-sm">Save</a></span>

<script>
    function createdChange(e){
        const minValue = e.sender.value();
        const datepicker = $("#StartDate").data("kendoDateTimePicker");

        datepicker.setOptions({
            min: minValue
        });
    }
    function startDateChange(e) {
        const minValue = e.sender.value();
        const datepicker = $("#CompletedDate").data("kendoDateTimePicker");

        datepicker.setOptions({
            min: minValue
        });
    }
</script>
