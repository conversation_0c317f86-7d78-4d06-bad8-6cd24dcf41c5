﻿namespace Centerpoint.Common.Constants
{
    public static class MaintenanceBlueprintStepTypeConstant
    {

        public const string Text = "TXT";
        public const string Boolean = "BOO";
        public const string Document = "DOC";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Text,"Free Text"},
                    {Boolean,"Checkbox"},
                    {Document,"Document Attachment"}
                };
            }
        }
    }
}
