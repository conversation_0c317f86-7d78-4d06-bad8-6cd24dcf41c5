﻿namespace Centerpoint.Common.Constants
{
    public static class UnitsConstant
    {

        public const string Feet = "FET";
        public const string Metres = "MET";
        public const string Inches = "INC";
        public const string Centimeters = "CEN";
        public const string Kilograms = "KGS";
        public const string Pounds = "LBS";


        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Feet,"ft"},
                    {Metres,"m"},
                    {Inches,"in"},
                    {Centimeters,"cm"},
                    {Kilograms,"kg"},
                    {Pounds,"lb"},
                };
            }
        }

        public static Dictionary<string, string> RunValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Feet,"ft"},
                    {Metres,"m"},
                };
            }
        }

        public static Dictionary<string, string> EquipmentValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Inches,"in"},
                    {Centimeters,"cm"},
                };
            }
        }

        public static Dictionary<string, string> EquipmentWeightValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                     {Kilograms,"kg"},
                     {Pounds,"lb"},
                };
            }
        }
    }
}
