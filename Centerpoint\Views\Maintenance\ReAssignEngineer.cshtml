﻿<div class="row col-md-12">
    <h5>Please select an Engineer to re-assign to this MR</h5>
</div>
<br />
<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>Assigned Engineer</label>
            <br />
            @(Html.Ken<PERSON>().DropDownList()
             .Name("enginer")
             .OptionLabel("Select Engineer")
             .DataValueField("UserId")
             .DataTextField("Name")
             .Filter("contains")
             .HtmlAttributes(new { @data_bind = "value:engineerUserId" })
             .DataSource(d => d.<PERSON>("GetEngineers", "Lookup")))
        </div>
    </div>
</div>
<span>
    <button id="reAssignEngineerConfirm" data-bind="enabled:engineerUserId" class="btn btn-primary btn-sm">Confirm</button>
</span>

