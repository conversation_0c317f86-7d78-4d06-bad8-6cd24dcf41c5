﻿@model ServiceImprovementModel

@Html.Partial("_GridNotification", EntityType.ServiceImprovement)
<div id="page" style="position: relative;">
    <div class="header-container-between">
    <h4>
        <i class="fa fa-bars"></i>
        @(Model.ServiceImprovementId.HasValue ? Model.NameTitleandStatus : "Create New Service Improvement Form")
    </h4>
    <div>
        @if (Model.ServiceImprovementId.HasValue)
        {
            <a href='@Url.Action("ServiceImprovementPdf", "Qhse")?id=@Model.ServiceImprovementId' onclick="sifPdf('@Model.NameTitleandStatus')" class="btn btn-primary btn-sm"><i class='fa fa-file-pdf'></i>Print PDF</a>
            @if (Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.Closed && (Html.IsGlobalAdmin() || Html.IsQhseAdmin() || Html.IsSifAdmin()))
            {
                <a class="btn btn-primary btn-sm" data-bind="click:reopenSifClick"><i class="bi bi-arrow-clockwise"></i>Reopen SIF</a>
            }

        }
    </div>
</div>
<hr />


@using (Html.BeginForm("EditServiceImprovement", "QHSE", FormMethod.Post, new { @id = "editServiceImprovement" })) {
    @(Html.Kendo().TabStrip()
    .Name("sifTabs")
    .SelectedIndex(Model.TabIndex)
    .Animation(false)
    .Items( tabstrip => {

    tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind = "html:tabStripHeaderDetails" })
        .Content(@<text>

            <div id="details">
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group">
                        <label>Title</label>
                        <br />
                        @(Html.Kendo().TextBoxFor(m => m.SifTitle)
                        .HtmlAttributes(new { @class = "form-control", @style = "width:100%; height :38px;", maxlength = 150}))
                    </div>
                </div>
            </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Creation Date</label>
                            <br />
                            @Html.Kendo().DatePickerFor(m => m.CreatedDate).HtmlAttributes(new { @data_bind = "value:createdDate" })
                        </div>
                        <div class="form-group">
                            <label>Category</label>
                            <br />
                            @(Html.Kendo().DropDownListFor(m => m.SifCategory)
                            .Filter(FilterType.Contains)
                            .OptionLabel("Select Category")
                            .DataTextField("Value")
                            .DataValueField("Key")
                            .BindTo(Centerpoint.Common.Constants.SifCategoryConstant.ValuesAndDescriptions.ToList())
                            )
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Raised By</label>
                            <br />
                            @(Html.Kendo().DropDownListFor(m => m.CreatedByUserId)
                            .DataTextField("Text")
                            .DataValueField("Value")
                            .Filter(FilterType.Contains)
                            .DataSource(d => d.Read("GetUsers", "Lookup"))
                            )
                        </div>

                        <div class="form-group">
                            <label>Sub Category</label>
                            <br />
                            @(Html.Kendo().MultiSelectFor(m => m.SubCategoryIds)
                                .DataTextField("Name")
                                .DataValueField("SubCategoryId")
                                .Placeholder("Select Sub Category...")
                                .Filter(FilterType.Contains)
                                .DataSource(source => {
                                                source.Read(read => {
                                                     read.Action("GetSubCategories", "Lookup");
                                                }).ServerFiltering(true);
                                            }))
                        </div>
                    </div>
                    @if (Model.ServiceImprovementId.HasValue && (Html.IsGlobalAdmin() || Html.IsSifAdmin() || Html.IsQhseAdmin()) && (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.PendingApproval && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Draft && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Rejected)) {
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>SIF Admin</label>
                                <br />
                                @(Html.Kendo().DropDownListFor(m => m.SifAdminUserId)
                            .DataTextField("Name")
                            .DataValueField("UserId")
                            .Filter(FilterType.Contains)
                            .DataSource(d => d.Read("GetSifAdmins", "Lookup"))

                            )
                            </div>
                            @if (GlobalSettings.IsRegiis) 
                            {
                                <div class="form-group">
                                    <label>Engineer</label>
                                    <br />
                                    @(Html.Kendo().TextBoxFor(m => m.Engineer)
                                    .HtmlAttributes(new { @class = "form-control"}))
                                </div>
                            }
                        </div>
                    } else {
                        @Html.HiddenFor(m => m.SifAdminUserId)
                    }
                    @if (Model.ServiceImprovementId.HasValue && !Html.IsGlobalAdmin() && !Html.IsSifAdmin() && !Html.IsQhseAdmin() && (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.PendingApproval && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Draft && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Rejected)) {
                        <label>SIF Admin</label>
                        <br />
                        @(Html.Kendo().TextBoxFor(m => m.SifAdminUserName)
                            .HtmlAttributes(new { @class = "form-control", @disabled = "disabled" }))
                    }
                </div>
                <hr />
                <h5>Event Details</h5>
                <br />
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group">
                            @if (GlobalSettings.IsRegiis){
                            <label>Client/Rig/Project</label>
                                <br />
                                @(Html.Kendo().DropDownListFor(m => m.JobId)
                                .Filter(FilterType.Contains)
                                .DataValueField("JobId")
                                .DataTextField("JobName")
                                .OptionLabel("Select Job")
                                .HtmlAttributes(new { @data_bind = "disabled:isJobRelated"})
                                .DataSource(d => d.Read("GetJobs", "Lookup")))
                            }
                            else {
                            <label>Associated Job</label>
                            @(Html.Kendo().DropDownListFor(m => m.JobId)
                            .Filter(FilterType.Contains)
                            .DataTextField("ProjectTitle")
                            .OptionLabel("Select Project")
                            .DataValueField("ProjectId")
                            .HtmlAttributes(new { @data_bind = "disabled:isJobRelated" })
                            .DataSource(d => d.Read("GetAllProjects", "Lookup")))
                            }
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                        @if (!GlobalSettings.IsAisus){
                                <label>Not Job Related</label>
                            }
                            else {
                                <label>Not Project Related</label>
                            }
                            <br />
                            <span>
                                @(Html.CheckBoxFor(m => m.IsRelated, new { @data_bind = "checked:isJobRelated" }))   
                            </span>

                        </div>
                    </div>
                </div>
               
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Region</label>
                            <br />
                            @(Html.Kendo().DropDownListFor(m => m.BaseCompanyLocationId)
                            .Filter(FilterType.Contains)
                            .OptionLabel("Select Base")
                            .DataTextField("Text")
                            .DataValueField("Value")
                            .DataSource(d => d.Read("GetBaseCompanyLocations", "Lookup"))
                            )
                        </div>
                        <div class="form-group">
                            <label>Client</label>
                            <br />
                            @(Html.Kendo().DropDownListFor(m => m.CompanyId)
                            .Filter(FilterType.Contains)
                            .OptionLabel("Select Client")
                            .DataTextField("Text")
                            .DataValueField("Value")
                            .Events(x => x.Change("companyChange"))
                            .HtmlAttributes(new {@data_cascade_to="CompanyLocationId" })
                            .DataSource(d => d.Read("GetCompanies", "Lookup"))
                            )
                        </div>
                        <div class="form-group">
                            <label>Equipment Related ?</label>
                            <br />
                            @(Html.Kendo().DropDownListFor(p => p.EquipmentRelated)
                            .DataValueField("Key")
                            .DataTextField("Value")
                            .Filter(FilterType.Contains)
                            .OptionLabel("Select Equipment Related")
                            .BindTo(Centerpoint.Common.Constants.ServiceImprovmentEquipmentConstant.ValuesAndDescriptions.ToList()))
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Severity</label>
                            <br />
                            @(Html.Kendo().DropDownListFor(m => m.SeverityId)
                            .DataValueField("SeverityId")
                            .DataTextField("Name")
                            .Filter(FilterType.Contains)
                            .OptionLabel("Select Severity")
                            .DataSource(d => d.Read("GetSeverities", "Lookup")))
                        </div>
                        <div class="form-group">
                            <label>Client Location</label>
                            <br />
                            @(Html.Kendo().DropDownListFor(m => m.CompanyLocationId)
                            .Filter(FilterType.Contains)
                            .OptionLabel("Select Client Location")
                            .DataTextField("Text")
                            .DataValueField("Value")
                            .Events(x => x.Change("cascadeDropdownFilterHelper"))
                            .HtmlAttributes(new {@data_cascade_to="CompanyContactId" })
                            .DataSource(source => {
                                source.Read(read => {
                                    read.Action("GetLocationsByCompanyId", "Lookup").Data("filterCompanyLocations");
                                });
                            })
                            )
                        </div>
                        <div class="form-group">
                        @if(GlobalSettings.IsRegiis){
                        <label>Client NCR Reference</label>
                        } else{
                             <label>Audit report number</label>
                        }
                            <br />
                            @(Html.Kendo().TextBoxFor(m => m.AuditReportNumber)
                            )
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Location</label>
                            <br />
                            @(Html.Kendo().DropDownListFor(m => m.ServiceImprovementLocationId)
                                .DataValueField("ServiceImprovementLocationId")
                                .DataTextField("Name")
                                .Filter(FilterType.Contains)
                                .OptionLabel("Select Location")
                                .DataSource(d => d.Read("GetServiceImprovementLocations", "Lookup")))
                        </div>
                        <div class="form-group">
                            <label>Client Contact</label>
                            <br />
                            @(Html.Kendo().DropDownListFor(m => m.CompanyContactId)
                                .Filter(FilterType.Contains)
                                .OptionLabel("Select Client Contact")
                                .DataTextField("Name")
                                .DataValueField("CompanyContactId")
                                .DataSource(source => {
                                    source.Read(read => {
                                        read.Action("GetContactsByCompanyLocationId", "Lookup").Data("filterCompanyContacts");
                                    });
                                })
                                )
                        </div>
                        @if(!GlobalSettings.IsRegiis){
                        <div class="form-group">
                             @Html.LabelFor(model => model.AccidentIncident)
                             @Html.TextBoxFor(model => model.AccidentIncident, new { @class = "form-control"})
                        </div>
                        }
                    </div>
                </div>
                <hr />
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            @if(GlobalSettings.IsRegiis){
                        <label> Description (Please provide a summary of the incident - what happened, relevant equipment details, SOE leading to incident) </label>
                            } else {
                        <label> Description </label>
                            }
                            <br />
                            @Html.TextAreaFor(p => p.Description, new { @class = "form-control", @style = "width:100%", @rows = "5" })
                        </div>
                        <br />
                        <div class="form-group">
                            @if(GlobalSettings.IsRegiis){
                        <label>Immediate actions/Corrections (Example: Ran backup toolstring, performed troubleshooting, client instructed to 'standdown')</label>
                            } else {
                         <label>Immediate actions/Corrections</label>
                            }
                            <br />
                            @Html.TextAreaFor(p => p.ImmediateCorrectiveAction, new { @class = "form-control", @style = "width:100%", @rows = "5", })
                        </div>
                        <br />
                        @if (Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.Rejected) {
                            <div class="form-group">
                                <label>Reject Reason</label>
                                <br />
                        @Html.TextAreaFor(p => p.RejectReason, new { @class = "form-control", @style = "width:100%", @rows = "5", @disabled = "disabled" })
                            </div>
                        }
                    </div>
                </div>
                <br />
                
                <div class="d-flex actionsContainer justify-content-end">
                    @if ((Model.ServiceImprovementId.HasValue && Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.Draft) && (Model.IsCreatedByUser || Html.IsSifAdmin() || Html.IsGlobalAdmin() || Html.IsQhseAdmin())) {
                        <button data-bind="click:deleteSif" class="btn btn-danger btn-sm">Delete</button>
                    }
                    @if (Model.ServiceImprovementId.HasValue && (Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.Draft || Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.Rejected) && Model.IsCreatedByUser) {
                        <a class="btn btn-success btn-sm" data-bind="click:submitSifClick"><i class="fa fa-thumbs-up"></i>Submit SIF</a>
                    }
                    @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Closed) {
                        <button type="button" class="btn btn-sm btn-primary" onclick="validateEditServiceImprovementForm(event)">Save</button>
                    }
                
                    @if ((Model.ServiceImprovementId.HasValue && Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.PendingApproval) && (Html.IsSifAdmin() || Html.IsGlobalAdmin() || Html.IsQhseAdmin())) {
                        <button class="btn btn-success btn-sm" data-bind="click:submitAcceptClick"><i class="fa fa-thumbs-up"></i>Accept</button>
                        <a class="btn btn-danger btn-sm" data-bind="click: showRejectWindow"><i class="fa fa-thumbs-down"></i>Reject</a>
                    }
                    @if ((Model.ServiceImprovementId.HasValue && Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.Rejected) && Model.IsCreatedByUser) {
                        <button class="btn btn-danger btn-sm" data-bind="click:submitAbandonClick"><i class="fa fa-thumbs-down"></i>Abandon SIF</button>
                    }

                    @if (Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.Closed && (Html.IsSifAdmin() || Html.IsGlobalAdmin() || Html.IsQhseAdmin())) {
                        <button type="submit" class="btn btn-sm btn-primary">Save</button>
                    }
                </div>
            </div>

        </text>);

        if (Model.ServiceImprovementId.HasValue &&
                (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.PendingApproval && 
                Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Draft && 
                Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Rejected && 
                Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Abandoned)) {
            tabstrip.Add().Text("")
            .HtmlAttributes(new { @data_bind = "html:tabStripHeaderCorrectiveAction" })
                .Content(@<text>
                @if (Model.ServiceImprovementId.HasValue && (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.PendingApproval && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Draft && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Rejected && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Abandoned)) {
                    <div id="correctiveAction">
                        <div class="row">
                            <div class="col-md-12">
                                @if (Html.IsGlobalAdmin() || Html.IsSifAdmin() || Html.IsQhseAdmin()) {
                                    <div class="form-group">
                                        <label>Requested Corrective Actions</label>
                                        <br />
                                        @Html.TextAreaFor(p => p.RequestedCorrectiveActions, new { @class = "form-control", @style = "width:100%", @rows = "5", @data_bind = "enabled:canEditCorrectiveAction", @data_value_update = "keyup" })
                                    </div>
                                } else {
                                    <label>Requested Corrective Actions</label>
                                    <br />
                                    @Html.TextAreaFor(p => p.RequestedCorrectiveActions, new { @class = "form-control", @style = "width:100%", @rows = "5", @disabled = "disabled" })
                                }
                                @if (Html.IsGlobalAdmin() || Html.IsSifAdmin() || Html.IsQhseAdmin()) {
                                    <div class="form-group">
                                        <label> Action Party</label>
                                        <br />
                                        @(Html.Kendo().DropDownListFor(m => m.CorrectiveActionUserId)
                                            .DataTextField("Text")
                                            .DataValueField("Value")
                                            .Filter(FilterType.Contains)
                                            .OptionLabel("Select User")
                                            .DataSource(d => d.Read("GetSifActionParties", "Lookup"))
                                        .HtmlAttributes(new { @data_bind = "enabled:canEditCorrectiveAction,value:correctiveActionParty", @class = "sif-action-input" }))
                                    </div>
                                } else {
                                    <br />
                                    <label> Action Party</label>
                                    <br />
                                    @Html.TextBox("ActionPartyDisabled", Model.CorrectiveActionUserName, new { @class = "form-control sif-action-input", @disabled = "disabled" })
                                }
                                @if ((Html.IsGlobalAdmin() || Html.IsSifAdmin() || Html.IsQhseAdmin()) && !Model.CorrectiveActionAcceptedDate.HasValue) {
                                    <div class="form-group">
                                        @Html.LabelFor(m => m.CorrectiveActionTargetDate)
                                        <br />
                                        @(Html.Kendo().DatePickerFor(m => m.CorrectiveActionTargetDate).Min(DateTime.Now.AddDays(1)).HtmlAttributes(new {@class = "sif-action-input", @data_bind = "value:correctiveActionTargetDate,enabled:canEditCorrectiveAction"}))
                                    </div>
                                } else if ((Html.IsGlobalAdmin() || Html.IsSifAdmin() || Html.IsQhseAdmin()) && Model.CorrectiveActionAcceptedDate.HasValue) {
                                    <br />
                                    <div class="form-group">
                                        @Html.LabelFor(m => m.CorrectiveActionTargetDate)
                                        <br />
                                        @(Html.Kendo().DatePickerFor(m => m.CorrectiveActionTargetDate).HtmlAttributes(new {@class = "sif-action-input", @disabled = "disabled" }))
                                    </div>
                                } else {
                                    <br />
                                    <div class="form-group">
                                        @Html.LabelFor(m => m.CorrectiveActionTargetDate)
                                        <br />
                                        @(Html.Kendo().DatePickerFor(m => m.CorrectiveActionTargetDate).HtmlAttributes(new {@class = "sif-action-input", @disabled = "disabled"}))
                                    </div>
                                } 

                                @if ((Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.CorrectiveActionRequested || Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.CorrectiveActionReEvaluated) && Model.IsCorrectiveActionUser) {
                                    <a id="correctiveActionDateChangeButton" href="#" data-bind="click: showCorrectiveActionRequestExtensionWindow" class="btn btn-primary btn-sm">Request Extension</a>
                                }

                                @if (!string.IsNullOrEmpty(Model.NewCorrectiveActionTargetDateComment) && Model.NewCorrectiveActionTargetDate.HasValue) {
                                    <div class="form-group">
                                        <br />
                                        <label>New Corrective Action Target Date</label>
                                        <br />
                                        @(Html.Kendo().DatePickerFor(m => m.NewCorrectiveActionTargetDate).HtmlAttributes(new {@class = "sif-action-input", @disabled = "disabled" }))
                                    </div>
                                    <div class="form-group">
                                        <label>New Corrective Action Target Date Comment</label>
                                        <br />
                                        @Html.TextAreaFor(p => p.NewCorrectiveActionTargetDateComment, new { @class = "form-control", @style = "width:100%", @rows = "5", @disabled = "disabled" })
                                    </div>

                                    if ((Html.IsSifAdmin() || Html.IsGlobalAdmin() || Html.IsQhseAdmin()) && string.IsNullOrEmpty(Model.NewCorrectiveActionTargetDateRejectComment)) {
                                        <button class="btn btn-success btn-sm " data-bind="click:acceptNewCorrectiveActionDateClick"><i class="fa fa-thumbs-up"></i>Accept</button>
                                        <a class="btn btn-danger btn-sm " data-bind="click: showRejectNewCorrectiveActionDateWindow"><i class="fa fa-thumbs-down"></i>Reject</a>
                                    }
                                }
                                @if (!string.IsNullOrEmpty(Model.NewCorrectiveActionTargetDateRejectComment)) {
                                    <div class="form-group">
                                        <br />
                                        <label>Target Date Extension Reject Reason</label>
                                        <br />
                                        @Html.TextAreaFor(p => p.NewCorrectiveActionTargetDateRejectComment, new { @class = "form-control", @style = "width:100%", @rows = "5", @disabled = "disabled", @data_bind = "enabled:canEditCorrectiveActionTargetDateRejectReason" })
                                    </div>
                                }
                            </div>
                        </div>
                        @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.PendingApproval && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Accepted) {
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <br />
                                        @if (!Model.IsCorrectiveActionUser && Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.CorrectiveActionRequested) {
                                            <label>Corrective Action Comments</label>
                                            <br />
                                            @Html.TextAreaFor(p => p.CorrectiveActionsComments, new { @class = "form-control", @style = "width:100%", @rows = "5", @disabled = "disabled" })
                                        } else {
                                            <label>Corrective Action Comments </label>
                                            <br />
                                            @Html.TextAreaFor(p => p.CorrectiveActionsComments, new { @class = "form-control", @style = "width:100%", @rows = "5", @data_bind = "enabled:canEditCorrectiveAction" })
                                        }
                                    </div>
                                </div>
                            </div>
                        }
                        @if (Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.CorrectiveActionReEvaluated) {
                            <div class="form-group">
                                <label>Corrective Action Re-Evaluated Reason</label>
                                <br />
                                @Html.TextAreaFor(p => p.CorrectiveActionRejectReason, new { @class = "form-control", @style = "width:100%", @rows = "5", @disabled = "disabled", @data_bind = "enabled:canEditCorrectiveActionReEvaluatedReason" })
                            </div>
                        }
                        <br />
                        <br />
                        <div class="d-flex actionsContainer justify-content-end">
                            @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Closed && (Html.IsSifAdmin() || Html.IsGlobalAdmin() || Html.IsQhseAdmin() || Model.IsCorrectiveActionUser)) {
                                <button type="submit" class="btn btn-sm btn-primary">Save</button>
                            }
                            @if (Model.ServiceImprovementId.HasValue && (Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.Accepted) && (Html.IsSifAdmin() || Html.IsGlobalAdmin() || Html.IsQhseAdmin())) {
                                <a class="btn btn-success btn-sm" data-bind="click:requestCorrectiveActionClick,enabled:enableCorrectionActionButton"><i class="fa fa-thumbs-up"></i>Request Corrective Action</a>
                            }
                            @if ((Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.CorrectiveActionRequested || Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.CorrectiveActionReEvaluated) && Model.IsCorrectiveActionUser) {
                                <a class="btn btn-success btn-sm" id="submitCorrectiveAction" data-bind="click:submitCorrectiveActionClick"><i class="fa fa-thumbs-up"></i>Submit Corrective Action</a>
                            }
                            @if ((Model.ServiceImprovementId.HasValue && Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.CorrectiveActionSubmitted) && (Html.IsSifAdmin() || Html.IsGlobalAdmin() || Html.IsQhseAdmin())) {
                                <a class="btn btn-info btn-sm" data-bind="click: showReEvaluateCorrectiveWindow"><i class="fa fa-thumbs-up"></i>Request Corrective Action Re-Evaluation</a>
                                <a class="btn btn-success btn-sm" data-bind="click:correctiveAcceptClick"><i class="fa fa-thumbs-up"></i>Accept Corrective Action</a>
                            }
                            @if (Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.Closed && (Html.IsSifAdmin() || Html.IsGlobalAdmin() || Html.IsQhseAdmin())) {
                                <button type="submit" class="btn btn-sm btn-primary">Save</button>
                            }
                        </div>
                        <br />
                        <hr />
                        @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.PendingApproval) {
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Corrective Action Submitted By </label>
                                        <br />
                                        <h4>@Model.CorrectiveActionRequestedBy</h4>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Corrective Action Submitted Date </label>
                                        <br />
                                        <h4>@(Model.CorrectiveActionRequestedDate.HasValue ? Model.CorrectiveActionRequestedDate.Value.ToLocalTime().ToString("dd/MM/yyyy HH:mm") : "")</h4>
                                    </div>
                                </div>
                            </div>
                        }
                        <br />
                        @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Accepted && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.CorrectiveActionRequested && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.CorrectiveActionReEvaluated && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.CorrectiveActionSubmitted) {
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Corrective Action Accepted By </label>
                                        <br />
                                        <h4>@Model.CorrectiveActionAcceptedBy</h4>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Corrective Action Accepted Date </label>
                                        <br />
                                        <h4>@(Model.CorrectiveActionAcceptedDate.HasValue ? Model.CorrectiveActionAcceptedDate.Value.ToLocalTime().ToString("dd/MM/yyyy HH:mm") : "")</h4>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }
                </text>);
        }        

        if (Model.ServiceImprovementId.HasValue &&
            (ServiceImprovementStatusConstant.IsInvestigationStatus(Model.ServiceImprovementStatus))) 
        {
            tabstrip.Add().Text("")
            .HtmlAttributes(new { @data_bind = "html:tabStripHeaderInvestigation" })
                .Content(@<text>
                @if (Model.ServiceImprovementId.HasValue && (ServiceImprovementStatusConstant.IsInvestigationStatus(Model.ServiceImprovementStatus))) {
                    <div id="investigation">
                        <br />
                        <div class="row">
                            <div class="col-md-12">
                                @if (Html.IsGlobalAdmin() || Html.IsSifAdmin() || Html.IsQhseAdmin()) {
                                    <div class="form-group">
                                        <label>Required Investigation Actions</label>
                                        <br />
                                        @Html.TextAreaFor(p => p.InvestigationActions, new { @class = "form-control", @style = "width:100%", @rows = "5", @data_bind = "enabled:canEditInvestigation", @data_value_update = "keyup" })
                                    </div>
                                } else {
                                    <label>Required Investigation Actions</label>
                                    <br />
                                    @Html.TextAreaFor(p => p.InvestigationActions, new { @class = "form-control", @style = "width:100%", @rows = "5", @disabled = "disabled" })
                                }
                                @if (Html.IsGlobalAdmin() || Html.IsSifAdmin() || Html.IsQhseAdmin()) {
                                    <div class="form-group">
                                        <label> Action Party</label>
                                        <br />
                                        @(Html.Kendo().DropDownListFor(m => m.InvestigatorUserId)
                                        .DataTextField("Text")
                                        .DataValueField("Value")
                                        .Filter(FilterType.Contains)
                                        .OptionLabel("Select User")
                                        .DataSource(d => d.Read("GetSifActionParties", "Lookup"))
                                        .HtmlAttributes(new { @data_bind = "enabled:canEditInvestigation, value:investigationActionParty", @class = "sif-action-input" }))
                                    </div>
                                } else {
                                    <br />
                                    <label> Action Party</label>
                                    <br />
                                    @Html.TextBox("ActionPartyDisabled", Model.InvestigatorUserName, new { @class = "form-control sif-action-input", @disabled = "disabled" })
                                }
                                @if ((Html.IsGlobalAdmin() || Html.IsSifAdmin() || Html.IsQhseAdmin()) && !Model.InvestigationAcceptedDate.HasValue) {
                                    <div class="form-group">
                                        @Html.LabelFor(m => m.InvestigationTargetDate)
                                        <br />
                                        @(Html.Kendo().DatePickerFor(m => m.InvestigationTargetDate).Min(Model.CorrectiveActionAcceptedDate.Value.AddDays(1)).HtmlAttributes(new { @class = "sif-action-input", @data_bind = "value:investigationTargetDate,enabled:canEditInvestigation" }))
                                    </div>
                                } else if ((Html.IsGlobalAdmin() || Html.IsSifAdmin() || Html.IsQhseAdmin()) && Model.InvestigationAcceptedDate.HasValue) {
                                    <br />
                                    <div class="form-group">
                                        @Html.LabelFor(m => m.InvestigationTargetDate)
                                        <br />
                                        @(Html.Kendo().DatePickerFor(m => m.InvestigationTargetDate).HtmlAttributes(new {@class = "sif-action-input", @disabled = "disabled"}))
                                    </div>
                                } else {
                                    <br />
                                    <div class="form-group">
                                        @Html.LabelFor(m => m.InvestigationTargetDate)
                                        <br />
                                        @(Html.Kendo().DatePickerFor(m => m.InvestigationTargetDate).HtmlAttributes(new {@class = "sif-action-input", @disabled = "disabled" }))
                                    </div>
                                } 

                                @if ((Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.InvestigationRequested || Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.InvestigationReRequested) && Model.IsInvestigatorActionUser) {
                                    <a id="dateChangeButton" href="#" data-bind="click: showRequestExtensionWindow" class="btn btn-primary btn-sm">Request Extension</a>
                                }
                                <br />
                                @if (!string.IsNullOrEmpty(Model.NewTargetDateComment) && Model.NewInvestigationTargetDate.HasValue) {
                                    <div class="form-group">
                                        <br />
                                        <label>New Investigation Target Date</label>
                                        <br />
                                        @(Html.Kendo().DatePickerFor(m => m.NewInvestigationTargetDate).HtmlAttributes(new {@class = "sif-action-input", @disabled = "disabled" }))
                                    </div>
                                    <div class="form-group">
                                        <label>New Investigation Target Date Comment</label>
                                        <br />
                                        @Html.TextAreaFor(p => p.NewTargetDateComment, new { @class = "form-control", @style = "width:100%", @rows = "5", @disabled = "disabled" })
                                    </div>
                                    <div class="d-flex actionsContainer justify-content-end">
                                        @if ((Html.IsSifAdmin() || Html.IsGlobalAdmin() || Html.IsQhseAdmin()) && string.IsNullOrEmpty(Model.NewTargetDateRejectComment)) {
                                            <button class="btn btn-success btn-sm " data-bind="click:acceptNewInvestigationDateClick"><i class="fa fa-thumbs-up"></i>Accept</button>
                                            <a class="btn btn-danger btn-sm " data-bind="click: showRejectNewDateWindow"><i class="fa fa-thumbs-down"></i>Reject</a>
                                        }
                                    </div>
                                }
                                @if (!string.IsNullOrEmpty(Model.NewTargetDateRejectComment)) {
                                    <div class="form-group">
                                        <label>Target Date Extension Reject Reason</label>
                                        <br />
                                        @Html.TextAreaFor(p => p.NewTargetDateRejectComment, new { @class = "form-control", @style = "width:100%", @rows = "5", @disabled = "disabled", @data_bind = "enabled:canEditTargetExtensionRejectReason" })
                                    </div>
                                }
                                @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.CorrectiveActionAccepted) {
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <br />
                                                @if (!Model.IsInvestigatorActionUser && Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.InvestigationRequested) {
                                                    <label>Investigation Results</label>
                                                    <br />
                                                    @Html.TextAreaFor(p => p.InvestigationResults, new { @class = "form-control", @style = "width:100%", @rows = "5", @disabled = "disabled", @data_bind = "enabled:canEditInvestigation" })
                                                } else {
                                                    <label>Investigation Results</label>
                                                    <br />
                                                    @Html.TextAreaFor(p => p.InvestigationResults, new { @class = "form-control", @style = "width:100%", @rows = "5", @data_bind = "enabled:canEditInvestigation" })
                                                }
                                            </div>
                                        </div>
                                    </div>
                                }
                                @if (Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.InvestigationReRequested) {
                                    <div class="form-group">
                                        <br>
                                        <label>Investigation Re-Requested Reason</label>
                                        <br />
                                        @Html.TextAreaFor(p => p.InvestigationRejectReason, new { @class = "form-control", @style = "width:100%", @rows = "5", @disabled = "disabled", @data_bind = "enabled:canEditReRequestedReason" })
                                    </div>
                                }
                                <br />
                                <div class="d-flex actionsContainer justify-content-end">
                                    @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Closed && (Html.IsSifAdmin() || Html.IsGlobalAdmin() || Html.IsQhseAdmin() || Model.IsInvestigatorActionUser)) {
                                        <button type="submit" class="btn btn-sm btn-primary">Save</button>
                                    }
                                    @if (Model.ServiceImprovementId.HasValue && (Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.CorrectiveActionAccepted) && (Html.IsSifAdmin() || Html.IsGlobalAdmin() || Html.IsQhseAdmin())) {
                                        <a class="btn btn-success btn-sm" id="requestInvestigation" data-bind="click:requestInvestigationClick,disabled:isInvestigationDateValid, enabled:enableInvestigationActionButton"><i class="fa fa-thumbs-up"></i>Request Investigation</a>
                                    }
                                    @if ((Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.InvestigationRequested || Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.InvestigationReRequested) && Model.IsInvestigatorActionUser) {
                                        <a class="btn btn-success btn-sm" id="submitInvestigation" data-bind="click:submitInvestigationClick"><i class="fa fa-thumbs-up"></i>Submit Investigation</a>
                                    }
                                    @if ((Model.ServiceImprovementId.HasValue && Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.InvestigationSubmitted) && (Html.IsSifAdmin() || Html.IsGlobalAdmin() || Html.IsQhseAdmin())) {
                                        <a class="btn btn-info btn-sm" data-bind="click: showReInvestigationWindow"><i class="fa fa-thumbs-up"></i>Request Re-Investigation</a>
                                        <a class="btn btn-success btn-sm" data-bind="click:acceptInvestigationClick"><i class="fa fa-thumbs-up"></i>Accept Investigation</a>
                                    }
                                    @if (Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.Closed && (Html.IsSifAdmin() || Html.IsGlobalAdmin() || Html.IsQhseAdmin())) {
                                        <button type="submit" class="btn btn-sm btn-primary">Save</button>
                                    }
                                </div>
                                <br />
                            </div>
                        </div>
                        <hr />
                        @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.PendingApproval && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Accepted) {
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Investigation Submitted By </label>
                                        <br />
                                        <h4>@Model.InvestigationRequestedBy</h4>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Investigation Submitted Date </label>
                                        <br />
                                        <h4>@(Model.InvestigationRequestedDate.HasValue ? Model.InvestigationRequestedDate.Value.ToLocalTime().ToString("dd/MM/yyyy HH:mm") : "")</h4>
                                    </div>
                                </div>
                            </div>
                        }
                        <br />
                        @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.CorrectiveActionAccepted && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.InvestigationRequested && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.InvestigationReRequested && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.InvestigationSubmitted) {
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Investigation Accepted By </label>
                                        <br />
                                        <h4>@Model.InvestigationAcceptedBy</h4>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Investigation Accepted Date </label>
                                        <br />
                                        <h4>@(Model.InvestigationAcceptedDate.HasValue ? Model.InvestigationAcceptedDate.Value.ToLocalTime().ToString("dd/MM/yyyy HH:mm") : "")</h4>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }
                </text>);      
        }        

        if (Model.ServiceImprovementId.HasValue && 
            (ServiceImprovementStatusConstant.IsPreventiveActionStatus(Model.ServiceImprovementStatus))) 
            {
            tabstrip.Add().Text("")
            .HtmlAttributes(new { @data_bind = "html:tabStripHeaderPreventiveAction" })
                .Content(@<text>
                @if (Model.ServiceImprovementId.HasValue && (ServiceImprovementStatusConstant.IsPreventiveActionStatus(Model.ServiceImprovementStatus))) {
                    <div id="preventiveAction">
                        <br />
                        <div class="row">
                            <div class="col-md-12">
                                @if (Html.IsGlobalAdmin() || Html.IsSifAdmin() || Html.IsQhseAdmin()) {
                                    <div class="form-group">
                                        <label>Requested Preventive Actions</label>
                                        <br />
                                        @Html.TextAreaFor(p => p.RequestedPreventiveActions, new { @class = "form-control", @style = "width:100%", @rows = "5", @data_bind = "enabled:canEditPreventiveAction", @data_value_update = "keyup" })
                                    </div>
                                } else {
                                    <label>Requested Preventive Actions</label>
                                    <br />
                                    @Html.TextAreaFor(p => p.RequestedPreventiveActions, new { @class = "form-control", @style = "width:100%", @rows = "5", @disabled = "disabled" })
                                }
                                @if (Html.IsGlobalAdmin() || Html.IsSifAdmin() || Html.IsQhseAdmin()) {
                                    <div class="form-group">
                                        <label> Action Party</label>
                                        <br />
                                        @(Html.Kendo().DropDownListFor(m => m.PreventiveActionUserId)
                                        .DataTextField("Text")
                                        .DataValueField("Value")
                                        .Filter(FilterType.Contains)
                                        .OptionLabel("Select User")
                                        .DataSource(d => d.Read("GetSifActionParties", "Lookup"))
                                        .HtmlAttributes(new { @data_bind = "enabled:canEditPreventiveAction,value:preventiveActionParty", @class = "sif-action-input", }))
                                    </div>
                                } else {
                                    <br />
                                    <label> Action Party</label>
                                    <br />
                                    @Html.TextBox("ActionPartyDisabled", Model.PreventiveActionUserName, new { @class = "form-control sif-action-input", @disabled = "disabled" })
                                }
                                @if ((Html.IsGlobalAdmin() || Html.IsSifAdmin() || Html.IsQhseAdmin()) && !Model.PreventiveActionAcceptedDate.HasValue) {
                                    <div class="form-group">
                                        @Html.LabelFor(m => m.PreventiveActionTargetDate)
                                        <br />
                                        @(Html.Kendo().DatePickerFor(m => m.PreventiveActionTargetDate).Min(Model.InvestigationAcceptedDate.Value.AddDays(1)).HtmlAttributes(new { @data_bind = "value:preventiveActionTargetDate,enabled:canEditPreventiveAction", @class = "sif-action-input" }))
                                    </div>
                                } else if ((Html.IsGlobalAdmin() || Html.IsSifAdmin() || Html.IsQhseAdmin()) && Model.PreventiveActionAcceptedDate.HasValue) {
                                    <br />
                                    <div class="form-group">
                                        @Html.LabelFor(m => m.PreventiveActionTargetDate)
                                        <br />
                                        @(Html.Kendo().DatePickerFor(m => m.PreventiveActionTargetDate).HtmlAttributes(new { @class = "sif-action-input", @disabled = "disabled" }))
                                    </div>
                                } else {
                                    <br />
                                    <div class="form-group">
                                        @Html.LabelFor(m => m.PreventiveActionTargetDate)
                                        <br />
                                        @(Html.Kendo().DatePickerFor(m => m.PreventiveActionTargetDate).HtmlAttributes(new {@class = "sif-action-input", @disabled = "disabled" }))
                                    </div>
                                } 
                                @if ((Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.PreventiveActionRequested || Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.PreventiveActionReEvaluated) && Model.IsPreventiveActionUser) {
                                    <a id="preventiveActionDateChangeButton" data-bind="click: showPreventiveActionRequestExtensionWindow" class="btn btn-primary btn-sm">Request Extension</a>
                                }
                                @if (!string.IsNullOrEmpty(Model.NewPreventiveActionTargetDateComment) && Model.NewPreventiveActionTargetDate.HasValue) {
                                    <div class="form-group">
                                        <br />
                                        <label>New Preventive Action Target Date</label>
                                        <br />
                                        @(Html.Kendo().DatePickerFor(m => m.NewPreventiveActionTargetDate).HtmlAttributes(new {@class = "sif-action-input", @disabled = "disabled" }))
                                    </div>
                                    <div class="form-group">
                                        <label>New Preventive Action Target Date Comment</label>
                                        <br />
                                        @Html.TextAreaFor(p => p.NewPreventiveActionTargetDateComment, new { @class = "form-control", @style = "width:100%", @rows = "5", @disabled = "disabled" })
                                    </div>

                                    if ((Html.IsSifAdmin() || Html.IsGlobalAdmin() || Html.IsQhseAdmin()) && string.IsNullOrEmpty(Model.NewPreventiveActionTargetDateRejectComment)) {
                                        <button class="btn btn-success btn-sm" data-bind="click:acceptNewPreventiveActionDateClick"><i class="fa fa-thumbs-up"></i>Accept</button>
                                        <a class="btn btn-danger btn-sm" data-bind="click: showRejectNewPreventiveActionDateWindow"><i class="fa fa-thumbs-down"></i>Reject</a>
                                    }
                                }
                                @if (!string.IsNullOrEmpty(Model.NewPreventiveActionTargetDateRejectComment)) {
                                    <div class="form-group">
                                        <br />
                                        <label>Target Date Extension Reject Reason</label>
                                        <br />
                                        @Html.TextAreaFor(p => p.NewPreventiveActionTargetDateRejectComment, new { @class = "form-control", @style = "width:100%", @rows = "5", @disabled = "disabled", @data_bind = "enabled:canEditPreventiveActionTargetDateRejectReason" })
                                    </div>
                                }
                            </div>
                        </div>
                        @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.InvestigationAccepted) {
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <br />
                                        @if (!Model.IsPreventiveActionUser && Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.PreventiveActionRequested) {
                                            <label>Preventive Action Comments</label>
                                            <br />
                                            @Html.TextAreaFor(p => p.PreventiveActionsComments, new { @class = "form-control", @style = "width:100%", @rows = "5", @disabled = "disabled" })
                                        } else {
                                            <label>Preventive Action Comments</label>
                                            <br />
                                            @Html.TextAreaFor(p => p.PreventiveActionsComments, new { @class = "form-control", @style = "width:100%", @rows = "5", @data_bind = "enabled:canEditPreventiveAction" })
                                        }
                                    </div>
                                </div>
                            </div>
                        }
                        @if (Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.PreventiveActionReEvaluated) {
                            <div class="form-group">
                                <label>Preventive Action Re-Evaluated Reason</label>
                                <br />
                                @Html.TextAreaFor(p => p.PreventiveActionRejectReason, new { @class = "form-control", @style = "width:100%", @rows = "5", @disabled = "disabled", @data_bind = "enabled:canEditPreventiveActionReEvaluatedReason" })
                            </div>
                        }
                        <br />
                        <br />
                        <div class="d-flex actionsContainer justify-content-end">
                            @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Closed && (Html.IsSifAdmin() || Html.IsGlobalAdmin() || Html.IsQhseAdmin() || Model.IsPreventiveActionUser)) {
                                <button type="submit" class="btn btn-sm btn-primary">Save</button>
                            }
                            @if (Model.ServiceImprovementId.HasValue && (Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.InvestigationAccepted) && (Html.IsSifAdmin() || Html.IsGlobalAdmin() || Html.IsQhseAdmin())) {
                                <a class="btn btn-success btn-sm" data-bind="click:requestPreventiveActionClick, disabled:isPreventiveActionDateValid,enabled:enablePreventiveActionButton"><i class="fa fa-thumbs-up"></i>Request Preventive Action</a>
                            }
                            @if ((Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.PreventiveActionRequested || Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.PreventiveActionReEvaluated) && Model.IsPreventiveActionUser) {
                                <a class="btn btn-success btn-sm" id="submitPreventiveAction" data-bind="click:submitPreventiveActionClick"><i class="fa fa-thumbs-up"></i>Submit Preventive Action</a>
                            }
                            @if ((Model.ServiceImprovementId.HasValue && Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.PreventiveActionSubmitted) && (Html.IsSifAdmin() || Html.IsGlobalAdmin() || Html.IsQhseAdmin())) {
                                <a class="btn btn-info btn-sm" data-bind="click: showReEvaluatePreventiveWindow"><i class="fa fa-thumbs-up"></i>Request Preventive Action Re-Evaluation</a>
                                <a class="btn btn-success btn-sm" data-bind="click:preventiveAcceptClick"><i class="fa fa-thumbs-up"></i>Accept Preventive Action</a>
                            }
                            @if (Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.Closed && (Html.IsSifAdmin() || Html.IsGlobalAdmin() || Html.IsQhseAdmin())) {
                                <button type="submit" class="btn btn-sm btn-primary">Save</button>
                            }
                        </div>
                        <br />
                        <hr />
                        @if (ServiceImprovementStatusConstant.IsPreventiveActionStatus(Model.ServiceImprovementStatus)) {
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Preventive Action Submitted By </label>
                                        <br />
                                        <h4>@Model.PreventiveActionRequestedBy</h4>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Preventive Action Submitted Date </label>
                                        <br />
                                        <h4>@(Model.PreventiveActionRequestedDate.HasValue ? Model.PreventiveActionRequestedDate.Value.ToLocalTime().ToString("dd/MM/yyyy HH:mm") : "")</h4>
                                    </div>
                                </div>
                            </div>
                        }
                        <br />
                        @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.PreventiveActionRequested && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.PreventiveActionReEvaluated && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.InvestigationAccepted && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.PreventiveActionSubmitted) {
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Preventive Action Accepted By </label>
                                        <br />
                                        <h4>@Model.PreventiveActionAcceptedBy</h4>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Preventive Action Accepted Date </label>
                                        <br />
                                        <h4>@(Model.PreventiveActionAcceptedDate.HasValue ? Model.PreventiveActionAcceptedDate.Value.ToLocalTime().ToString("dd/MM/yyyy HH:mm") : "")</h4>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }
                </text>);     
        }      

        if (Model.ServiceImprovementId.HasValue && (ServiceImprovementStatusConstant.IsPreventiveImplementationStatus(Model.ServiceImprovementStatus))) {
            tabstrip.Add().Text("")
            .HtmlAttributes(new { @data_bind = "html:tabStripHeaderSignOff" })
                .Content(@<text>
                @if (Model.ServiceImprovementId.HasValue && (ServiceImprovementStatusConstant.IsPreventiveImplementationStatus(Model.ServiceImprovementStatus))) {
                    <div id="signOff">
                        <div class="row">
                            <div class="col-md-12">
                                @if (Html.IsGlobalAdmin() || Html.IsSifAdmin() || Html.IsQhseAdmin()) {
                                    <div class="form-group">
                                        <label>Sign-off Comment</label>
                                        <br />
                                        @(Html.Kendo().TextAreaFor(p => p.SignOffComment)
                                                        .HtmlAttributes(new { @class = "form-control", @style = "width:100%", @rows = "5", })
                                                        .Events(e=>e.Change("onTextAreaChange")))
                                       
                                    </div>
                                } else {
                                    @Html.TextAreaFor(p => p.SignOffComment, new { @class = "form-control", @style = "width:100%", @rows = "5", @disabled = "disabled" })
                                }
                            </div>
                        </div>
                        <br />
                        <div class="d-flex actionsContainer justify-content-end">
                        @if (Model.ServiceImprovementId.HasValue && (Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.PreventiveActionAccepted) && (Html.IsSifAdmin() || Html.IsGlobalAdmin() || Html.IsQhseAdmin())) {
                        <a id ="signOffButton" class="btn btn-success btn-sm" data-bind="click:signOffClick"><i class="fa fa-thumbs-up"></i>Sign-off SIF Investigation & Actions</a>
                        }
                        </div>
                        <br />
                        <hr />
                        @if (ServiceImprovementStatusConstant.IsPreventiveImplementationStatus(Model.ServiceImprovementStatus)) {
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Sign Off By </label>
                                        <br />
                                        <h4>@Model.SignOffBy</h4>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Sign Off Date </label>
                                        <br />
                                        <h4>@(Model.SignOffDate.HasValue ? Model.SignOffDate.Value.ToLocalTime().ToString("dd/MM/yyyy HH:mm") : "")</h4>
                                    </div>
                                </div>
                            </div>
                        }

                    </div>
                }
                </text>); 
        }  

        if (Model.ServiceImprovementId.HasValue) {
            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind="html:attachmentStripText"})
                .Content(@<text>
                    <div id="attachments">
                        <p>Click the link below to attach documents to this SIF</p>
                        @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Closed) { 
                        @(Html.Kendo().Upload()
                        .Name("serviceImprovementAttachmentDocuments")
                        .Messages(m => m.Select("Attach SIF Documents"))
                        .Multiple(true)
                        .Events(e => e.Success("onServiceImprovementDocumentAttached").Complete("onServiceImprovementDocumentComplete").Upload("onServiceImprovementDocumentUpload"))
                        .Async(async => async.Save("AttachServiceImprovementDocuments", "Qhse", new { @serviceImprovementId = Model.ServiceImprovementId }).Batch(true)))
                        }
                        @if ((Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.Closed) && (Html.IsGlobalAdmin() || Html.IsQhseAdmin() || Html.IsSifAdmin())) {
                            @(Html.Kendo().Upload()
                        .Name("serviceImprovementAttachmentDocuments")
                        .Messages(m => m.Select("Attach SIF Documents"))
                        .Multiple(true)
                        .Events(e => e.Success("onServiceImprovementDocumentAttached").Complete("onServiceImprovementDocumentComplete").Upload("onServiceImprovementDocumentUpload"))
                        .Async(async => async.Save("AttachServiceImprovementDocuments", "Qhse", new { @serviceImprovementId = Model.ServiceImprovementId }).Batch(true)))
                        }
                        <br />
                        @(Html.Kendo().Grid<DocumentModel>()
                            .Name("serviceImprovementDocumentsGrid")
                            .Columns(c => {
                                c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                                c.Bound(p => p.Created).Title("Created").Width(150).Format(DateConstants.DateTimeFormat);
                                c.Bound(p => p.UserName).Title("Created By").Width(200);
                                c.Command(command => { 
                                        command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}).Visible("canDeleteServiceImprovementDocumentsGrid"); 
                                    }).Width(200);
                                })
                                .Events(e => e.DataBound("updateServiceImprovementDocumentGrid"))
                                .Sortable()
                                .Resizable(r => r.Columns(true))
                                .Filterable()
                                .Groupable()
                                .Editable(e => e.Mode(GridEditMode.InLine))
                                .Scrollable(s => s.Height(300))
                                .DataSource(dataSource => dataSource
                                .Ajax()
                                .ServerOperation(false)
                                .Model(model => model.Id(p => p.DocumentId))
                                .Read(read => read.Action("GetServiceImprovementDocuments", "Qhse", new { @sId = Model.ServiceImprovementId }))
                                .Destroy(destroy => destroy.Action("DeleteServiceImprovementDocument", "Qhse", new { @serviceImprovementId = Model.ServiceImprovementId }))))
                    </div>
                </text>);      

            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind="html:sifUpdatesStripText"})
                .Content(@<text>
                    <div id="logs">
                        <br />
                        <p>The following table is a log of all changes made to this SIF.</p>
                        <hr />
                        @(Html.Kendo().Grid<ServiceImprovementLogModel>()
                            .Name("sifLogGrid")
                            .Columns(columns => {
                                columns.Bound(c => c.Log).ClientTemplate("#=Log#");
                                columns.Bound(c => c.Details).Width(300).Encoded(false);
                                columns.Bound(c => c.UserName).Width(125);
                                columns.Bound(c => c.Date).Title("Date").Width(150).Format(DateConstants.DateTimeFormat);
                            })
                            .Events(e => e.DataBound("updateSifLogs"))
                            .Sortable()
                            .Groupable()
                            .Reorderable(reorder => reorder.Columns(true))
                            .Resizable(resize => resize.Columns(true))
                            .Scrollable(s => s.Height(400))
                            .DataSource(dataSource => dataSource
                            .Ajax()
                            .ServerOperation(false)
                            .Read(read => read.Action("GetServiceImprovementLogByServiceImprovementId", "Qhse").Data("serviceImprovementData"))))
                    </div>
                </text>);        
        }

    }
    ))

    @Html.HiddenFor(m => m.ServiceImprovementId)
    @Html.HiddenFor(m => m.ServiceImprovementStatus)
    @Html.HiddenFor(m => m.Name)
    @Html.HiddenFor(m => m.RequestedCorrectiveActions)
    @Html.HiddenFor(m => m.CorrectiveActionUserId)
    @Html.HiddenFor(m => m.CorrectiveActionTargetDate)
    @Html.HiddenFor(m => m.InvestigationActions)
    @Html.HiddenFor(m => m.InvestigationTargetDate)
    @Html.HiddenFor(m => m.InvestigatorUserId)
    @Html.HiddenFor(m => m.RequestedPreventiveActions)
    @Html.HiddenFor(m => m.PreventiveActionUserId)
    @Html.HiddenFor(m => m.PreventiveActionTargetDate)
    @Html.HiddenFor(m => m.PreventiveActionsComments)
    @Html.HiddenFor(m => m.CorrectiveActionsComments)
    @Html.HiddenFor(m => m.InvestigationResults)
    @Html.HiddenFor(m => m.InvestigationRejectReason)
    @Html.HiddenFor(m => m.NewTargetDateComment)
    @Html.HiddenFor(m => m.NewInvestigationTargetDate)
    @Html.HiddenFor(m => m.NewTargetDateRejectComment)
    @Html.HiddenFor(m => m.PreventiveActionRejectReason)
    @Html.HiddenFor(m => m.NewPreventiveActionTargetDateRejectComment)
    @Html.HiddenFor(m => m.NewPreventiveActionTargetDateComment)
    @Html.HiddenFor(m => m.NewPreventiveActionTargetDate)
    @Html.HiddenFor(m => m.CorrectiveActionRejectReason)
    @Html.HiddenFor(m => m.NewCorrectiveActionTargetDateRejectComment)
    @Html.HiddenFor(m => m.NewCorrectiveActionTargetDateComment)
    @Html.HiddenFor(m => m.NewCorrectiveActionTargetDate)
    @Html.HiddenFor(m => m.RejectReason)
    @Html.HiddenFor(m => m.TabIndex, new { @data_bind = "value:tabIndex" })
}
</div>
@(Html.Kendo().Window().Name("rejectWindow")
 .Title("Reject SIF")
 .Content(@<text>
     <partial name="RejectReason"/>
     </text>)
    .Width(600)
    .Modal(true)
    .Visible(false))

@(Html.Kendo().Window().Name("reRequestInvestigationWindow")
 .Title("Re-Requested Investigation")
 .Content(@<text>
     <partial name="ReRequestInvestigationReason"/>
     </text>)
    .Width(600)
    .Modal(true)
    .Visible(false))

@(Html.Kendo().Window().Name("reEvaluatePreventiveWindow")
 .Title("Re-Evaluated Preventive Action")
 .Content(@<text>
     <partial name="ReEvaluatePreventiveReason"/>
     </text>)
    .Width(600)
    .Modal(true)
    .Visible(false))

@(Html.Kendo().Window().Name("reEvaluateCorrectiveWindow")
 .Title("Re-Evaluated Corrective Action")
 .Content(@<text>
     <partial name="ReEvaluateCorrectiveReason"/>
     </text>)
    .Width(600)
    .Modal(true)
    .Visible(false))

@(Html.Kendo().Window().Name("requestExtensionWindow")
 .Title("Request New Investigation Target Date")
 .Content(@<text>
     <partial name="RequestExtension"/>
     </text>)
    .Width(800)
    .Modal(true)
    .Visible(false))

@(Html.Kendo().Window().Name("requestPreventiveActionExtensionWindow")
 .Title("Request New Preventive Action Target Date")
 .Content(@<text>
     <partial name="RequestPreventiveActionExtension"/>
     </text>)
    .Width(800)
    .Modal(true)
    .Visible(false))

@(Html.Kendo().Window().Name("requestCorrectiveActionExtensionWindow")
 .Title("Request New Corrective Action Target Date")
 .Content(@<text>
     <partial name="RequestCorrectiveActionExtension"/>
     </text>)
    .Width(800)
    .Modal(true)
    .Visible(false))

@(Html.Kendo().Window().Name("rejectInvestigationDateWindow")
 .Title("Reject New Investigation Date")
 .Content(@<text>
     <partial name="NewInvestigationDateRejectReason"/>
     </text>)
    .Width(600)
    .Modal(true)
    .Visible(false))

@(Html.Kendo().Window().Name("rejectPreventiveActionDateWindow")
 .Title("Reject New Preventive Action Date")
 .Content(@<text>
     <partial name="NewPreventiveActionDateRejectReason"/>
     </text>)
    .Width(600)
    .Modal(true)
    .Visible(false))

@(Html.Kendo().Window().Name("rejectCorrectiveActionDateWindow")
 .Title("Reject New Corrective Action Date")
 .Content(@<text>
     <partial name="NewCorrectiveActionDateRejectReason"/>
     </text>)
    .Width(600)
    .Modal(true)
    .Visible(false))
  

<script type="text/x-kendo-tmpl" id="sifLogDetailTemplate">
    <div style="margin:10px">
        #=Details#
    </div>
</script>

<script>
   
   

    const serviceImprovementScriptModel = {
        modelServiceImprovementId:"@Model.ServiceImprovementId",
        modelSid: "@(Model.ServiceImprovementId.HasValue ? Model.ServiceImprovementId.Value.ToString() : "")",
        isAdminModel: @(Html.IsGlobalAdmin() || Html.IsSifAdmin() || Html.IsQhseAdmin() ? "true" : "false"),
        isJobRelatedModel : @(Model.IsRelated ? "true" : "false"),
        jobId: "@(Model.JobId.HasValue ? Model.JobId.Value.ToString() : "")",
        createdDateModel :"@(Model.CreatedDate)",
        correctiveActionUserIdModel: "@(Model.CorrectiveActionUserId)",
        preventiveActionUserIdModel: "@(Model.PreventiveActionUserId)",
        investigationUserIdModel: "@(Model.InvestigatorUserId)",
        investigationTargetDateModel:"@(Model.InvestigationTargetDate.HasValue ? Model.InvestigationTargetDate.Value.ToString("dd/MM/yyyy") : string.Empty)",
        preventiveActionTargetDateModel:"@(Model.PreventiveActionTargetDate.HasValue ? Model.PreventiveActionTargetDate.Value.ToString("dd/MM/yyyy") : string.Empty)",
        correctiveActionTargetDateModel:"@(Model.CorrectiveActionTargetDate.HasValue ? Model.CorrectiveActionTargetDate.Value.ToString("dd/MM/yyyy") : string.Empty)",
        newInvestigationTargetDateModel: "@(Model.NewInvestigationTargetDate.HasValue ? Model.NewInvestigationTargetDate.Value.ToString("dd/MM/yyyy") : string.Empty)",
        newPreventiveActionTargetDateModel: "@(Model.NewPreventiveActionTargetDate.HasValue ? Model.NewPreventiveActionTargetDate.Value.ToString("dd/MM/yyyy") : string.Empty)",
        newCorrectiveActionTargetDateModel: "@(Model.NewCorrectiveActionTargetDate.HasValue ? Model.NewCorrectiveActionTargetDate.Value.ToString("dd/MM/yyyy") : string.Empty)",
        correctiveActionAcceptedDateModel: "@(Model.CorrectiveActionAcceptedDate.HasValue ? Model.CorrectiveActionAcceptedDate.Value.ToString("dd/MM/yyyy") : string.Empty)",
        preventiveActionAcceptedDateModel: "@(Model.PreventiveActionAcceptedDate.HasValue ? Model.PreventiveActionAcceptedDate.Value.ToString("dd/MM/yyyy") : string.Empty)",
        investigationAcceptedDateModel: "@(Model.InvestigationAcceptedDate.HasValue ? Model.InvestigationAcceptedDate.Value.ToString("dd/MM/yyyy") : string.Empty)",
        isInvestigationCompleteModel: @(Model.IsInvestigationComplete ? "true" : "false"),
        isPreventiveActionCompleteModel: @(Model.IsPreventiveActionComplete ? "true" : "false"),
        isCorrectivelActionCompleteModel: @(Model.IsCorrectiveActionComplete ? "true" : "false"),
        newInvestigationActionCommentModel:"@(Html.Raw(!string.IsNullOrWhiteSpace(Model.NewTargetDateComment) ? Model.NewTargetDateComment.Replace(System.Environment.NewLine, "").Replace("\"", "") : string.Empty))",
        newCorrectiveActionCommentModel:"@(Html.Raw(!string.IsNullOrWhiteSpace(Model.NewCorrectiveActionTargetDateComment) ? Model.NewCorrectiveActionTargetDateComment.Replace(System.Environment.NewLine, "").Replace("\"", "") : string.Empty))",
        newPreventiveActionCommentModel: "@(Html.Raw(!string.IsNullOrWhiteSpace(Model.NewPreventiveActionTargetDateComment) ? Model.NewPreventiveActionTargetDateComment.Replace(System.Environment.NewLine, "").Replace("\"", "") : string.Empty))",
        requestCorrectiveActionModel: "@(Html.Raw(Html.Encode(!string.IsNullOrWhiteSpace(Model.RequestedCorrectiveActions) ? Model.RequestedCorrectiveActions.Replace(System.Environment.NewLine, @"\r\n").Replace("\"", "\\\"").Replace("\"", "") : string.Empty)))",
        correctiveActionPartyModel: "@(Model.CorrectiveActionUserId)",
        requestPreventiveActionModel: "@(Html.Raw(Html.Encode(!string.IsNullOrWhiteSpace(Model.RequestedPreventiveActions) ? Model.RequestedPreventiveActions.Replace(System.Environment.NewLine, @"\r\n").Replace("\"", "") : string.Empty)))",
        preventiveActionPartyModel: "@(Model.PreventiveActionUserId)",
        requestInvestigationActionModel: "@(Html.Raw(Html.Encode(!string.IsNullOrWhiteSpace(Model.InvestigationActions) ? Model.InvestigationActions.Replace(System.Environment.NewLine, @"\r\n").Replace("\"", "") : string.Empty)))",
        investigationActionPartyModel: "@(Model.InvestigatorUserId)",
        tabIndexModel: "@Model.TabIndex",
        sifAdminUserIdModel: "@(Model.SifAdminUserId)",
        modelNewInvestigationTargetDate: "@Model.NewInvestigationTargetDate",
        modelNewPreventiveActionTargetDate: "@Model.NewPreventiveActionTargetDate",
        modelNewCorrectiveActionTargetDate: "@Model.NewCorrectiveActionTargetDate",
        serviceImprovementStatusConstantInvestigationRequested: "@ServiceImprovementStatusConstant.InvestigationRequested",
        modelInvestigationTargetDate: "@Model.InvestigationTargetDate",
        serviceImprovementStatusConstantPendingApproval: "@ServiceImprovementStatusConstant.PendingApproval",
        serviceImprovementStatusConstantAccepted: "@ServiceImprovementStatusConstant.Accepted",
        serviceImprovementStatusConstantAbandoned: "@ServiceImprovementStatusConstant.Abandoned",
        serviceImprovementStatusConstantInvestigationSubmitted: "@ServiceImprovementStatusConstant.InvestigationSubmitted",
        serviceImprovementStatusConstantInvestigationAccepted: "@ServiceImprovementStatusConstant.InvestigationAccepted",
        serviceImprovementStatusConstantPreventiveActionRequested: "@ServiceImprovementStatusConstant.PreventiveActionRequested",
        modelPreventiveActionTargetDate: "@Model.PreventiveActionTargetDate",
        serviceImprovementStatusConstantPreventiveActionSubmitted: "@ServiceImprovementStatusConstant.PreventiveActionSubmitted",
        serviceImprovementStatusConstantPreventiveActionAccepted: "@ServiceImprovementStatusConstant.PreventiveActionAccepted",
        serviceImprovementStatusConstantCorrectiveActionRequested: "@ServiceImprovementStatusConstant.CorrectiveActionRequested",
        serviceImprovementStatusConstantCorrectiveActionSubmitted: "@ServiceImprovementStatusConstant.CorrectiveActionSubmitted",
        serviceImprovementStatusConstantCorrectiveActionAccepted: "@ServiceImprovementStatusConstant.CorrectiveActionAccepted",
        serviceImprovementStatusConstantClosed: "@ServiceImprovementStatusConstant.Closed"
    }

    function canDeleteServiceImprovementDocumentsGrid(data) {
        if(
            "@Html.IsGlobalAdmin()" === "True" ||
            "@Html.IsQhseAdmin()" === "True" ||
            "@Html.IsSifAdmin()" === "True" ||
            "@Model.ServiceImprovementStatus" != "@ServiceImprovementStatusConstant.Closed" &&
            data.UserEmail == "@Html.AccountEmailAddress()"
          ){
          return true
        }
        return false
    }
</script>
<style>
    .sif-action-input {
        width: 250px;
        font-size: 14px;
    }
</style>

<environment include="Development">
    <script src="~/js/views/qhse/serviceImprovementScripts.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/qhse/serviceImprovementScripts.min.js" asp-append-version="true"></script>
</environment>