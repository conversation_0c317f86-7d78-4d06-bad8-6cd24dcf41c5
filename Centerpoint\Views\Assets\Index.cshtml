﻿<div class="d-flex justify-content-between align-items-center mb-3">
    <div class="header-container-single-item-with-hr flex-grow-1">
        <h4>
            <i class="fa fa-tags"></i>
            Assets
        </h4>
    </div>
    <div>
        @if(Html.IsGlobalAdmin() || Html.IsAssetAdmin()){
        <a id="exportAllAssetsBtn" class="btn btn-success btn-sm" href="@Url.Action("ExportAllAssets", "Assets")" onclick="return showExportLoading(this);">
            <i class="fas fa-download"></i> <span id="exportBtnText">Export All Assets</span>
        </a>
        }
    </div>
</div>
<hr />

<div>
<div class="row">
    <div class="col-md-3">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fa fa-tags"></i> Equipment Categories</h6>
            </div>
            <div class="card-body">
                <div class="expandCollapseButtonsContainer">
                    <button class="btn btn-sm btn-primary" id="expandAll" data-bind="click:expandAll" title="Expand All">
                        <i class="fa fa-expand m-0"></i>
                    </button>
                    <button class="btn btn-sm btn-primary" id="collapsAll" data-bind="click:collapseAll" title="Collapse All">
                        <i class="fa fa-compress m-0"></i>
                    </button>
                </div>
                    <input id="filterText" class="k-input k-textbox k-input-solid k-input-md k-rounded-md" type="text" placeholder="Search categories" />
                    <br />
                    <br />
                @(Html.Kendo().TreeView()
                    .Name("equipmentCategoryTreeView")
                    .DataTextField("NewName")
                    .LoadOnDemand(false)
                    .Animation(false)
                    .Events(e => e
                        .Change("equipmentCategorySelected")
                        .Drop("equipmentCategoryDropped")
                        .DataBound("equipmentCategoryLoaded")
                        .Select("userEquipmentCategorySelected")
                    )
                    .DataSource(datasource => datasource
                        .Events(e => e.RequestEnd("categoriesLoaded"))
                        .Model(m => m.Id("EquipmentCategoryId").HasChildren("HasChildren").Children("Children"))
                        .Read(r => r.Action("GetEquipmentCategories", "Admin"))
                        .ServerFiltering(false)
                    )
                )
            </div>
        </div>
    </div>
    <div class="col-md-9" data-bind="visible:selectedEquipmentCategory">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0" data-bind="text:selectedAssetItemNameWithCount"></h6>
            </div>
            <div class="card-body">
                @(Html.Kendo().TabStrip()
                    .Name("assetsStrips")
                    .SelectedIndex(0)
                    .Animation(false)
                    .Items( tabstrip => {

                    tabstrip.Add().Text("")
                        .HtmlAttributes(new { @data_bind="html:tabStripHeaderConstructorOfAssetWithNumber"})
                        .Selected(true)
                        .Content(@<text>
                            <div>
                                <div class="d-flex">
                                    <div class="form-group w-50 mr-5">
                                        <label>Filter by Equipment No/Category</label><br />
                                        @(Html.Kendo().TextBox().Name("equipmentQuery"))
                                    </div>
                                    <div class="form-group w-50">
                                            <label>Show by Status</label>
                                            @(Html.Kendo().MultiSelect()
                                            .Filter(FilterType.Contains)
                                            .Name("equipmentStatusFilter")
                                            .DataTextField("Value")
                                            .DataValueField("Key")
                                            .Events(e => e.Close("closeEquipmentGridFilter").Deselect("deselectEquipmentGridFilter"))
                                            .AutoClose(false)
                                            .HtmlAttributes(new { @data_bind = "value:equipmentFilterOptions" })
                                            .BindTo(Html.EquipmentFilterOptions()))
                                    </div>
                                </div>
                                <div class="alert alert-info" data-bind="invisible:selectedEquipmentItemId" role="alert">
                                    <h6 class="mb-0">Please select an Asset Item below to move </h6>
                                </div>
                                <div class="alert alert-info" data-bind="visible:selectedEquipmentItemId" role="alert">
                                    <h6 class="mb-0">To select multiple Asset Items or to unselect an Asset Item, please press Ctrl + click.</h6>
                                </div>
                                                @(
                            Html.Kendo().Grid<EquipmentItemModel>()
                                .Name("assetEquipmentItemGrid")
                                .Columns(columns => {
                                    columns.Bound(c => c.EquipmentItemName).Title("Equipment Number").ClientTemplate("<a href='/Assets/EditEquipmentItem/#=EquipmentItemId#?returnUrl=#=window.location.href#'>#=EquipmentItemName#</a>").Width(150);
                                    columns.Bound(c => c.EquipmentPackingListProjectName).Title("Selected To").ClientTemplate("#if(EquipmentPackingListProjectName){#<a href='" + @Url.Action("EditProject", "Operation") + "/#=EquipmentPackingListProjectId#'>#=NewProjectName#</a>#}else{#N/A#}#").Width(150);
                                    columns.Bound(c => c.ProjectName).Title("Assigned To").ClientTemplate("#=ProjectName ? EquipmentProjectName : ''#").Width(150);
                                    columns.Bound(c => c.CountryOfOrigin).Title("Country Of Origin").Width(100).Hidden(true);
                                    if(GlobalSettings.IsWellsense ){
                                        columns.Bound(c => c.USHTSCommodityCode).Title("USHTS Commodity Code").ClientTemplate("#=USHTSCommodityCode ? USHTSCommodityCode : ''#").Width(100);
                                        columns.Bound(c => c.ImportCommodityCode).Title("Import Commodity Code").ClientTemplate("#=ImportCommodityCode ? ImportCommodityCode : ''#").Width(100);
                                        columns.Bound(c => c.ExportCommodityCode).Title("Export Commodity Code").ClientTemplate("#=ExportCommodityCode ? ExportCommodityCode : ''#").Width(100);
                                        columns.Bound(c => c.WaiverCode).Title("Waiver Code").ClientTemplate("#=WaiverCode ? WaiverCode : ''#").Width(100);
                                        columns.Bound(c => c.WaiverRequirement).Title("Waiver Requirement").ClientTemplate("#=WaiverRequirement ? WaiverRequirement : ''#").Width(100);
                                    }
                                    columns.Bound(c => c.ReceivedDate).Title("Received Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                    columns.Bound(c => c.PurchasedDate).Title("Purchased Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                    columns.Bound(c => c.CommodityCode).Title("Commodity Code").Width(100).Hidden(true);
                                    columns.Bound(c => c.CurrencyName).Title("Currency").Width(100).Hidden(true);
                                    columns.Bound(c => c.Price).Format("{0:n2}").Width(100).Hidden(true);
                                    columns.Bound(c => c.DepreciatedPrice).Title("Net Book Value").Hidden(true).Format("{0:n2}").Width(100);
                                    columns.Bound(c => c.CommentsandUser).Title("Acceptance Note").Hidden(true);
                                    if (!GlobalSettings.IsWellsense )
                                    {
                                        columns.Bound(c => c.PointsPerMonth).Title("Points Per Month").Hidden(true).Width(150);
                                        columns.Bound(c => c.PointsPerRun).Title("Points Per Run").Hidden(true).Width(150);
                                        columns.Bound(c => c.PointsPerMove).Title("Points Per Move").Hidden(true).Width(150);
                                        columns.Bound(c => c.Points).Title("Current Points").ClientTemplate("#if(MaintenanceSchedulePointsAlert){#<span style='color:\\#E31E33'href='\\#'>#=Points#</span>#}else{#<span href='\\#'>#=Points#</span>#}#").Width(85);
                                    }
                                    columns.Bound(c => c.DivisionName).Title("Division").Hidden(true).Width(150);
                                    columns.Bound(c => c.InternalInvoiceNumber).Title("Internal Inv.Nbr").Hidden(true).Width(150);
                                    columns.Bound(c => c.ExternalInvoiceNumber).Title("External Inv.Nbr").Hidden(true).Width(150);
                                    columns.Bound(c => c.TrackedNonAssetItem).Title("Tracked Non-Asset Item").ClientTemplate("#if(TrackedNonAssetItem){#Yes#}else{#No#}#").Hidden(true).Width(100);

                                    columns.Bound(c => c.CurrentClientLocationName).Title("Current Location").ClientTemplate("#=CurrentClientLocationName ? CurrentClientLocationName : 'Not Yet Accepted'#").Width(125);
                                    columns.Bound(c => c.ManufacturerCompanyName).Title("Manufacturer").ClientTemplate("#=ManufacturerCompanyName ? ManufacturerCompanyName : 'N/A'#").Width(125);
                                    columns.Bound(c => c.MaintenanceScheduleDetail).Title("Maintenance Schedule Date(s)").ClientTemplate("#if(MaintenanceScheduleDaysAlert){#<a style='color:\\#E31E33'href='\\#' onclick='scheduleDates(#=EquipmentItemId#)'>#=MaintenanceScheduleDetail#</a>#}else if(MaintenanceScheduleDetail != ''){#<a href='\\#' onclick='scheduleDates(#=EquipmentItemId#)'>#=MaintenanceScheduleDetail#</a>#}else{##=MaintenanceScheduleDetail##}#").Width(125);
                                    columns.Bound(c => c.MRCount).Title("Active MRs").ClientTemplate("#if(MaintenanceRecordCount){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='maintenanceRecordCount(#=EquipmentItemId#)'>#=MRCount#</a>#} else {##=MRCount##}#").Width(75);
                                    columns.Bound(c => c.EquipmentInfo).Title("Info").Width(100);
                                    columns.Bound(c => c.AllStatusDescription).Title("Status").Encoded(false).Filterable(f => f.Operators(o => o.ForString(str => str.Clear().Contains("Contains").DoesNotContain("Does not contain")))).Width(150).Exportable(false);
                                    columns.Bound(c => c.StatusNames).IncludeInMenu(false).Title("Status").Hidden(true).Exportable(true);
                                    columns.Bound(c => c.IsPartofBundle).Visible(false);

                                    columns.Bound(c => c.Height).Title("Length").Hidden(true).Width(100);
                                    columns.Bound(c => c.HeightUnitDescription).Title("Lenght Unit").Hidden(true).Width(100);
                                    columns.Bound(c => c.Width).Title("Width").Hidden(true).Width(100);
                                    columns.Bound(c => c.WidthUnitDescription).Title("Width Unit").Hidden(true).Width(100);
                                    columns.Bound(c => c.Depth).Title("Depth").Hidden(true).Width(100);
                                    columns.Bound(c => c.DepthUnitDescription).Title("Depth Unit").Hidden(true).Width(100);
                                    columns.Bound(c => c.OuterDiameter).Title("OD").Hidden(true).Width(100);
                                    columns.Bound(c => c.OuterDiameterUnitDescription).Title("OD Unit").Hidden(true).Width(100);
                                    columns.Bound(c => c.Weight).Title("Weight").Hidden(true).Width(100);
                                    columns.Bound(c => c.WeightUnitDescription).Title("Weight Unit").Hidden(true).Width(100);
                                })
                                .AutoBind(false)
                                .ColumnMenu(c => c.Columns(true))
                                .Events(e => e
                                    .DataBound("updateEquipmentTotals")
                                    .ColumnReorder("saveEquipmentGrid")
                                    .ColumnResize("saveEquipmentGrid")
                                    .ColumnShow("saveEquipmentGrid")
                                    .ColumnHide("saveEquipmentGrid")
                                )
                                .Sortable()
                                .Scrollable(scrollable => scrollable.Endless(true).Height("auto"))
                                .Selectable(selectable => selectable.Mode(GridSelectionMode.Multiple))
                                .Events(e => e.Change("selectedEquipmentItem"))
                                .Filterable()
                                .Groupable()
                                .Excel(excel => excel
                                    .FileName(string.Format("Centerpoint_Assets_Equipment_Items_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                                    .Filterable(true)
                                    .AllPages(true)
                                    .ProxyURL(Url.Action("Export", "Asset"))
                                )
                                .ToolBar(t => {
                                    if (Html.IsAssetAdmin() || Html.IsGlobalAdmin()) {
                                        t.Custom().HtmlAttributes(new{@class="text-white bg-primary", @data_bind="visible: selectedEquipmentItemId, click: moveAssetItemWindow" }).Text("Move Asset Item");
                                        t.Custom().HtmlAttributes(new{@class="text-white bg-primary", @data_bind="visible: selectedEquipmentItemId, click: editItemWindow"}).Text("Edit Info");
                                    }
                                    t.Custom().Text("Reset Grid View").HtmlAttributes(new{ @id="resetAssetEquipmentItemGrid", @class="bg-danger text-white float-right"});
                                    t.Excel().Text("Export");
                                })
                                .HtmlAttributes( new { @style="height:80vh" , @class="justify-toolbar-content-to-end"})
                                .Resizable(resize => resize.Columns(true))
                                .Reorderable(reorder => reorder.Columns(true))
                                .DataSource(dataSource => dataSource
                                .Ajax()
                                .Model(model => {
                                    model.Id(m => m.EquipmentItemId);
                                })
                                .PageSize(100)
                                .Read(read => read.Action("GetEquipmentItems", "Assets").Data("equipmentItemData")))
                            )

                            </div>
                    </text>);



                    tabstrip.Add().Text("Category")
                        .Content(@<text>
                            <div class="tab-pane" id="category">
                                <div class="form-group">
                                    <label>Name</label>
                                    <input type="text" data-bind="value:selectedEquipmentCategory.Name" data-value-update="keyup" class="form-control"/>
                                </div>
                                <div class="form-group">
                                    <label>Details</label>
                                    <textarea data-bind="value:selectedEquipmentCategory.Details" data-value-update="keyup" class="form-control" rows="2"></textarea>
                                </div>
                                <div class="row">
                                @if (!GlobalSettings.IsAisus)
                                   {
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="d-inline-block mr-2">Division</label>
                                                <div class="d-inline-block">
                                                    @(
                                                     Html.Kendo().DropDownList()
                                                    .Name("Division")
                                                    .DataTextField("Text")
                                                    .DataValueField("Value")
                                                    .Filter("contains")
                                                    .OptionLabel("Select Division")
                                                    .AutoWidth(true)
                                                    .HtmlAttributes(new { @data_bind = "value:selectedEquipmentCategory.DivisionId", @style = "width:100px" })
                                                    .DataSource(d => d.Read("GetDivisions", "Lookup"))
                                                    )
                                                </div>
                                            </div>
                                        </div>
                                   }

                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <input class="form-check-input" type="checkbox" data-bind="checked:selectedEquipmentCategory.IsTopLevelOnly" />
                                            <label class="form-check-label">Top Level Only</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group" data-bind="invisible:selectedEquipmentCategory.IsTopLevelOnly">
                                            <input  class="form-check-input" type="checkbox" data-bind="checked:selectedEquipmentCategory.IsDangerous" />
                                            <label class="form-check-label">Dangerous</label>
                                        </div>
                                    </div>
                                </div>

                                <div data-bind="invisible:selectedEquipmentCategory.IsTopLevelOnly">
                                    <div class="card mb-2">
                                        <div class="card-header">
                                            <h6 class="mb-0">Dimensions</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="d-flex">
                                                <div class="d-flex w-50">
                                                    <div class="form-group">
                                                        <label class="editEquipmentLabelSmall">Length</label>
                                                        <input data-role="numerictextbox" data-spinners="false" data-min="0" data-format="n3" data-decimals="3" data-bind="value:selectedEquipmentCategory.Height" style="width:90px" />
                                                    </div>
                                                    <div class="form-group ml-2">
                                                        @(Html.Kendo().DropDownList()
                                                        .Name("HeightUnit")
                                                        .ValuePrimitive(true)
                                                        .Filter("contains")
                                                        .OptionLabel("Select")
                                                        .DataValueField("Key")
                                                        .DataTextField("Value")
                                                        .HtmlAttributes(new { @data_bind = "value:selectedEquipmentCategory.HeightUnit", @style="width:100px" })
                                                        .BindTo(Centerpoint.Common.Constants.UnitsConstant.EquipmentValuesAndDescriptions.ToList()))
                                                    </div>
                                                </div>
                                                <div class="d-flex w-50">
                                                    <div class="form-group">
                                                        <label class="editEquipmentLabelSmall">OD</label>
                                                        <input data-role="numerictextbox" style="width:90px" data-spinners="false" data-min="0" data-format="n3" data-decimals="3" data-bind="value:selectedEquipmentCategory.OuterDiameter" />
                                                    </div>
                                                    <div class="form-group ml-2">
                                                        @(Html.Kendo().DropDownList()
                                                        .Name("OuterDiameterUnit")
                                                        .ValuePrimitive(true)
                                                        .Filter("contains")
                                                        .OptionLabel("Select")
                                                        .DataValueField("Key")
                                                        .DataTextField("Value")
                                                        .HtmlAttributes(new { @data_bind = "value:selectedEquipmentCategory.OuterDiameterUnit", @style="width:100px" })
                                                        .BindTo(Centerpoint.Common.Constants.UnitsConstant.EquipmentValuesAndDescriptions.ToList()))
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="d-flex">
                                                <div class="d-flex w-50">
                                                    <div class="form-group">
                                                        <label class="editEquipmentLabelSmall">Width</label>
                                                        <input data-role="numerictextbox" data-spinners="false" data-min="0" data-format="n3" data-decimals="3" data-bind="value:selectedEquipmentCategory.Width" style="width:90px" />
                                                    </div>
                                                    <div class="form-group ml-2">
                                                        @(Html.Kendo().DropDownList()
                                                        .Name("WidthUnit")
                                                        .ValuePrimitive(true)
                                                        .Filter("contains")
                                                        .OptionLabel("Select")
                                                        .DataValueField("Key")
                                                        .DataTextField("Value")
                                                        .HtmlAttributes(new { @data_bind = "value:selectedEquipmentCategory.WidthUnit", @style="width:100px" })
                                                        .BindTo(Centerpoint.Common.Constants.UnitsConstant.EquipmentValuesAndDescriptions.ToList()))
                                                    </div>
                                                </div>
                                                <div class="d-flex w-50">
                                                    <div class="form-group">
                                                        <label class="editEquipmentLabelSmall">Weight</label>
                                                        <input data-role="numerictextbox" style="width:90px" data-spinners="false" data-min="0" data-format="n3" data-decimals="3" data-bind="value:selectedEquipmentCategory.Weight" />
                                                    </div>
                                                    <div class="form-group ml-2">
                                                        @(Html.Kendo().DropDownList()
                                                        .Name("WeightUnit")
                                                        .ValuePrimitive(true)
                                                        .Filter("contains")
                                                        .OptionLabel("Select")
                                                        .DataValueField("Key")
                                                        .DataTextField("Value")
                                                        .HtmlAttributes(new { @data_bind = "value:selectedEquipmentCategory.WeightUnit", @style="width:100px" })
                                                        .BindTo(Centerpoint.Common.Constants.UnitsConstant.EquipmentWeightValuesAndDescriptions.ToList()))
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="d-flex">
                                                <div class="d-flex w-50">
                                                    <div class="form-group">
                                                        <label class="editEquipmentLabelSmall">Depth</label>
                                                        <input data-role="numerictextbox" data-spinners="false" data-min="0" data-format="n3" data-decimals="3" data-bind="value:selectedEquipmentCategory.Depth" style="width:90px" />
                                                    </div>
                                                    <div class="form-group ml-2">
                                                        @(Html.Kendo().DropDownList()
                                                        .Name("DepthUnit")
                                                        .ValuePrimitive(true)
                                                        .Filter("contains")
                                                        .OptionLabel("Select")
                                                        .DataValueField("Key")
                                                        .DataTextField("Value")
                                                        .HtmlAttributes(new { @data_bind = "value:selectedEquipmentCategory.DepthUnit", @style="width:100px" })
                                                        .BindTo(Centerpoint.Common.Constants.UnitsConstant.EquipmentValuesAndDescriptions.ToList()))
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">Maintenance</h6>
                                        </div>
                                        <div class="card-body">
                                                    @* to do *@
                                                <div class="row mt-2" data-bind="visible:canEditEquipmentItems">
                                                    <div class="col-md-12">
                                                       @(Html.Kendo().Grid<EquipmentCategoryMaintenanceStepModel>()
                                                       .Name("equipmentCategoryMaintenanceStepGrid")
                                                       .Columns(c =>
                                                       {
                                                         c.Bound(p => p.MaintenanceBlueprintId).EditorTemplateName("MaintenanceBlueprintName").ClientTemplate("#=MaintenanceBlueprintName ? MaintenanceBlueprintName : ''#").Title("Maintenance Blueprint");
                                                         c.Bound(p => p.RunEnabled).Title("Run Enabled").ClientTemplate("#if(RunEnabled){#Yes#}else{#No#}#");
                                                       })
                                                       .Sortable()
                                                       .Selectable()
                                                       .Filterable()
                                                       .Groupable()
                                                       .Scrollable(s => s.Height(200))
                                                       .Resizable(c => c.Columns(true))
                                                       .ColumnMenu(c => c.Columns(true))
                                                       .DataSource(dataSource => dataSource
                                                       .Ajax()
                                                       .Model(m =>
                                                       {
                                                        m.Id(p => p.EquipmentCategoryMaintenanceStepId);
                                                       })
                                                       .Read(read => read.Action("GetEquipmentCategoryMaintenanceSteps", "Assets").Data("categoryData"))))
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                    </text>);
                    }
                   ))
            </div>
        </div>
    </div>
</div>

</div>

@(Html.Kendo().Window()
.Name("scheduleDatesWindow")
.Width(1000)
.Height(500)
.Title("Maintenance Schedule Dates")
.Visible(false)
.Modal(true)
.Events(e => e.Open("scheduleDatesWindowOpened"))
.Content(@<text>
        @(Html.Kendo().Grid<EquipmentItemMaintenanceScheduleModel>()
           .Name("equipmentItemMaintenanceScheduleGrid")
           .Columns(columns => {
               columns.Bound(c => c.MaintenanceBlueprintName).Title("Maintenance Blueprint").Width(150);
               columns.Bound(c => c.LastDate).Title("Last").Format(DateConstants.DateFormat).Width(125);
               columns.Bound(c => c.RecurringDays).Title("Recurring Days").Width(125).Visible(!GlobalSettings.IsWellsense);
               columns.Bound(c => c.RecurringMonths).Title("Recurring Months").Width(125).Visible(GlobalSettings.IsWellsense);
               columns.Bound(c => c.StartDate).Title("Next").ClientTemplate("#=CompanyLocationId!=null? 'N/A' : StartDateOnly#").Width(125);
        })
           .ColumnMenu(c => c.Columns(true))
           .Sortable()
           .AutoBind(false)
           .Scrollable(s => s.Height(400))
           .Resizable(resize => resize.Columns(true))
           .Reorderable(reorder => reorder.Columns(true))
           .DataSource(dataSource => dataSource
           .Ajax()
           .ServerOperation(false)
           .Model(model => {
               model.Id(m => m.EquipmentItemMaintenanceScheduleId);
           })
           .Read(read => read.Action("GetEquipmentItemMaintenanceSchedules", "Assets").Data("equipmentItemMaintenanceScheduleData")))) </text>
         ))
@(Html.Kendo().Window()
.Name("maintenanceRecordWindow")
.Width(1000)
.Height(500)
.Title("Maintenance Record")
.Visible(false)
.Modal(true)
.Events(e => e.Open("maintenanceRecordWindowOpened"))
.Content(@<text>
        @(Html.Kendo().Grid<MaintenanceRecordModel>()
    .Name("maintenanceRecordGrid")
    .Columns(columns => {
        columns.Bound(c => c.Number).Title("Maintenancen Record").ClientTemplate("<a href='" + @Url.Action("EditMaintenanceRecord", "Maintenance", new { @id = "" }) + "/#=MaintenanceRecordId#'>#=Number#</a>");
        columns.Bound(c => c.MaintenanceBlueprintId).Title("Maintenancen Blueprint").ClientTemplate("<a href='" + @Url.Action("EditMaintenanceBlueprint", "Admin", new { @id = "" }) + "/#=MaintenanceBlueprintId#'>#=MaintenanceBlueprintName#</a>");
        columns.Bound(c => c.EquipmentItemName).Title("Equipment Item").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#'>#=EquipmentItemName#</a>");
        columns.Bound(c => c.PriorityDescription).Title("Priority");
        columns.Bound(c => c.UserName).Title("Created By").Hidden(true);
            columns.Bound(c => c.Created).Title("Created").Format(DateConstants.DateTimeFormat);
            columns.Bound(c => c.Modified).Title("Modified").Format(DateConstants.DateTimeFormat).Hidden(true);
        columns.Bound(c => c.StatusDescription).Title("Status").ClientTemplate("<span class='badge' style='background:#=StatusColour#;color:#=StatustextColour#'>#=StatusDescription#</span>");
    })
       .ColumnMenu(c => c.Columns(true))
       .Events(e => e.DataBound("updatedMaintenanceRecordGrid"))
       .Filterable()
       .Sortable()
       .Groupable()
       .Scrollable(s => s.Height(532))
       .Resizable(resize => resize.Columns(true))
       .Reorderable(reorder => reorder.Columns(true))
    .DataSource(dataSource => dataSource
    .Ajax()
    .ServerOperation(false)
    .Model(model => {
        model.Id(m => m.MaintenanceRecordId);
    })
    .Read(read => read.Action("GetMaintenanceRecordByEquipmentItemId", "Maintenance").Data("maintenanceRecordData"))))</text>
         ))
@(Html.Kendo().Window()
 .Name("moveAssetItemWindowOpen")
 .Title("Move Asset Item")
 .Content(@<text>
               <partial name="MoveAssetItem"/>
     </text>)
 .Width(650)
 .Modal(true)
 .Visible(false))
@(Html.Kendo().Window()
 .Name("editItemWindowOpen")
 .Title("Edit Item")
 .Content(@<text>
               <partial name="EditSelectedItem"/>
     </text>)
 .Width(250)
 .Modal(true)
 .Visible(false))


<script>
    const assetsModel = {
        statuses: [],
        equipmentFilterOptions: @Html.Raw(Json.Serialize(Html.DefaultEquipmentFilterOptions()))
    }

    @foreach (var status in EquipmentConstant.ValuesAndDescriptions.ToList()){
        @:assetsModel.statuses.push("@status.Key");
    }

</script>

<environment include="Development">
    <script src="~/js/views/assets/assets.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/assets/assets.min.js" asp-append-version="true"></script>
</environment>
