var viewModel = new kendo.observable({
    isMobileNotification: projectModel.mobileNotification,
    isEmailNotification: projectModel.emailNotification,
    myQueries: 0,
    myQueriesText: function () {
        return `<span class="k-link"><i class="fa fa-bars mr-1"></i> My Queries (<span data-bind="text:myQueries">${this.myQueries}</span>)</span>`;
    },
    riscCount: 0,
    emailAddress: "",
    validateEmail: function () {
        return String(this.get("emailAddress")).toLowerCase()
        .match(/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/);
    },
    documentIds: [],
    summaryMonth: new Date().getMonth() + 1,
    daysOnSiteMonth: new Date().getMonth() + 1,
    totalExpiringThisMonth: 0,
    totalExpiringOneMonth: 0,
    totalExpiringTwoMonth: 0,
    totalExpiringThreeMonth: 0,
    totalExpiringThisYear: 0,
    totalUserNotes: 0,
    totalSifOpen: 0,
    tabStripHeaderHome: function () {
        return `<span class="k-link"><i class="fa fa-user mr-1"></i> Home </span>`;
    },
    tabStripHeaderMyProjects: function () {
        return `<span class="k-link"><i class="fa fa-globe mr-1"></i> My Projects </span>`;
    },
    tabStripHeaderMyDetails: function () {
        return `<span class="k-link"><i class="fa fa-user mr-1"></i> My Details </span>`;
    },
    totalSifOpenText: function () {
        return `<span class="k-link"><i class="fa fa-bars mr-1"></i> My SIFs (<span data-bind="text:totalSifOpen">${this.totalSifOpen}</span>)</span>`;
    },
    totalSifOnTarget: 0,
    totalSifOverdue: 0,
    totalSifClosed: 0,
    totalMyLessons: 0,
    totalMyLessonsText: function () {
        return `<span class="k-link"><i class="fa fa-bars mr-1"></i> My Lessons (<span data-bind="text:totalMyLessons">${this.totalMyLessons}</span>)</span>`;
    },
    totalPendingApproval: 0,
    totalCreatedMonth: 0,
    totalCreatedMonthText: function () {
        return `<span class="k-link"><i class="fa fa-bars mr-1"></i> My ${projectModel.cardName} Cards (<span data-bind="text:totalCreatedMonth">${this.totalCreatedMonth}</span>)</span>`;
    },
    totalPositiveMonth: 0,
    totalNegativeMonth: 0,
    totalInLast30Days: 0,
    month: "",
    year: "",
    totalServiceImprovements: 0,
    totalCertificates: 0,
    totalCertificatesText: function () {
        return `<span class="k-link"><i class="fa fa-bars mr-1"></i> My Certificates (<span data-bind="text:totalCertificates">${this.totalCertificates}</span>)</span>`;
    },
    totalMyProjects: 0,
    totalProjectLessons: 0,
    totalProjectLessonsText: function () {
        return `<span class="k-link"><i class='fa fa-bars mr-2'></i>Related Lessons (<span data-bind='text:totalProjectLessons'></span>)</span>`;
    },
    totalLessons: 0,
    totalComments: 0,
    totalCommentsText: function () {
        return `<span class="k-link"> <i class='fa fa-comments mr-2'></i>Chat (<span data-bind='text:totalComments'></span>)</span>`;
    },
    type: "",
    projectId: 0,
    showAll: true,
    emailWindow: function () {
        $("#personnelCertificateAttachmentWindow").data("kendoWindow").center().open();
    },

    showAllProject: function () {
        this.set("showAll", true);
        refreshAllComment();
        // showAllProjectComments();
    },

    showMyProject: function () {
        this.set("showAll", false);
        refreshMyComment();
        // showMyProjectComments();
    },

    totalLessonClick: function () {
        this.set("type", "totalLesson");
        refreshMyLessonsGrid();
    },

    pendingApprovalClick: function () {
        this.set("type", "pendingApproval");
        refreshMyLessonsGrid();
    },

    thisMonthClick: function () {
        this.set("month", 0);
        this.set("year", "");
        refreshCertificatesGrid();
    },

    oneMonthClick: function () {
        this.set("month", 1);
        this.set("year", "");
        refreshCertificatesGrid();
    },

    twoMonthClick: function () {
        this.set("month", 2);
        this.set("year", "");
        refreshCertificatesGrid();
    },

    threeMonthClick: function () {
        this.set("month", 3);
        this.set("year", "");
        refreshCertificatesGrid();
    },

    thisYearClick: function () {
        this.set("year", 0);
        this.set("month", "");
        refreshCertificatesGrid();
    },

    openClick: function () {
        this.set("type", "open");
        refreshServiceImprovementGrid();
        refreshSifChart();
    },
    onTargetClick: function () {
        this.set("type", "onTarget");
        refreshServiceImprovementGrid();
        refreshSifChart();
    },
    overdueClick: function () {
        this.set("type", "overdue");
        refreshServiceImprovementGrid();
        refreshSifChart();
    },
    closedClick: function () {
        this.set("type", "closed");
        refreshServiceImprovementGrid();
        refreshSifChart();
    },

    totalClick: function () {
        viewModel.set("type", "total")
        refreshRiscGrid();
        refreshRiscChart();
    },
    positiveMonthClick: function () {
        viewModel.set("type", "positive")
        refreshRiscGrid();
        refreshRiscChart();
    },
    negativeMonthClick: function () {
        viewModel.set("type", "corrective")
        refreshRiscGrid();
        refreshRiscChart();
    },
});

$(document).ready(function () {
    activateTabDependingOnUrlQuery();
    getMyCertificates();
    getMySifs();
    getMyLessonsLearnedSummary();
    getRiscMonthlySummary();
    getDaysOnSiteMonthlySummary();
    loadCertificateGrid();
    loadSifGrid();
    loadRiscGrid();
    loadLessonGrid();
    refreshSifChart();
    refreshRiscChart();
    kendo.bind(document.body.children, viewModel);
});


function loadLessonGrid() {
    var grid = $("#myLessonGrid").data("kendoGrid");
    var toolBar = $("#myLessonGrid .k-grid-toolbar").html();
    var options = localStorage["myLessonGrid"];
    viewModel.set("initialMyLessonGridOptions", kendo.stringify(grid.getOptions()));
    if (options) {
        grid.setOptions(JSON.parse(options));
        $("#myLessonGrid .k-grid-toolbar").html(toolBar);
        $("#myLessonGrid .k-grid-toolbar").addClass("k-grid-top");
    }
}

function saveLessonGrid(e) {
    setTimeout(function () {
        var grid = $("#myLessonGrid").data("kendoGrid");
        localStorage["myLessonGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}


function updateMyLessonGrid() {
    $("#resetMyLessonGrid").click(function (e) {
        e.preventDefault();
        resetGridView('myLessonGrid', 'initialMyLessonGridOptions')
    });
    
    var myLessonGrid = $("#myLessonGrid").data("kendoGrid");
    var totalMyLessons = myLessonGrid.dataSource.total();
    viewModel.set("totalMyLessons", totalMyLessons);
}

function refreshMyLessonsGrid(){
    var myLessonGrid = $("#myLessonGrid").data("kendoGrid");
    myLessonGrid.dataSource.read();
}

function loadCertificateGrid() {
    var grid = $("#myPersonnelCertificateDocumentGrid").data("kendoGrid");
    var toolBar = $("#myPersonnelCertificateDocumentGrid .k-grid-toolbar").html();
    var options = localStorage["myPersonnelCertificateDocumentGrid"];
    viewModel.set("initialMyPersonnelCertificateDocumentGridOptions", kendo.stringify(grid.getOptions()));
    if (options) {
        grid.setOptions(JSON.parse(options));
        $("#myPersonnelCertificateDocumentGrid .k-grid-toolbar").html(toolBar);
        $("#myPersonnelCertificateDocumentGrid .k-grid-toolbar").addClass("k-grid-top");
    }
}

function validateHomeEditUserForm(e) {
    const items = $("#RolesList").data("kendoMultiSelect").value();
    let rolesListData = $("#RolesList").data("kendoMultiSelect").dataSource.data().filter(r => items.includes(r.Value));
    const rolesList = [];
    rolesListData.forEach(i => {
        rolesList.push({
            Text: i.Text,
            Value: i.Value,
            Selected: i.Selected
        })
    })
    $("#Roles").val(JSON.stringify(rolesList));

    let isValid = $('#HomeEditUserForm').kendoValidator().data('kendoValidator').validate();
    if(!isValid){
        e.preventDefault();
        scrolToFirstError();
    } else {
        $('#HomeEditUserForm').submit();
    }
}

function saveCertificateGrid(e) {
    setTimeout(function () {
        var grid = $("#myPersonnelCertificateDocumentGrid").data("kendoGrid");
        localStorage["myPersonnelCertificateDocumentGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}

function certificateData() {
    return {
        month: viewModel.get("month"),
        year: viewModel.get("year")
    }
}

function updateMyPersonnelCertificateDocumentGrid(e) {
    $("#resetMyPersonnelCertificateDocumentGrid").click(function (e) {
        e.preventDefault();
        resetGridView('myPersonnelCertificateDocumentGrid', 'initialMyPersonnelCertificateDocumentGridOptions')
    });

    var myPersonnelCertificateDocumentGrid = $("#myPersonnelCertificateDocumentGrid").data("kendoGrid");
    var totalCertificates = myPersonnelCertificateDocumentGrid.dataSource.total();
    viewModel.set("totalCertificates", totalCertificates);

}

function refreshCertificatesGrid() {
    var myPersonnelCertificateDocumentGrid = $("#myPersonnelCertificateDocumentGrid").data("kendoGrid");
    myPersonnelCertificateDocumentGrid.dataSource.read();
}

function loadSifGrid(e) {
    var grid = $("#myServiceImprovementGrid").data("kendoGrid");
    var options = localStorage["myServiceImprovementGrid"];
    var toolBar = $("#myServiceImprovementGrid .k-grid-toolbar").html();
    viewModel.set("initialMyServiceImprovementGridOptions", kendo.stringify(grid.getOptions()));
    if (options) {
        grid.setOptions(JSON.parse(options));
        $("#myServiceImprovementGrid .k-grid-toolbar").html(toolBar);
        $("#myServiceImprovementGrid .k-grid-toolbar").addClass("k-grid-top");
    }
}

function saveSifGrid(e) {
    setTimeout(function () {
        var grid = $("#myServiceImprovementGrid").data("kendoGrid");
        localStorage["myServiceImprovementGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}

function updateServiceImprovementGrid() {
    $("#resetMyServiceImprovementGrid").click(function (e) {
        e.preventDefault();
        resetGridView('myServiceImprovementGrid', 'initialMyServiceImprovementGridOptions')
    });

    var myServiceImprovementGrid = $("#myServiceImprovementGrid").data("kendoGrid");
    var totalServiceImprovements = myServiceImprovementGrid.dataSource.total();
    viewModel.set("totalServiceImprovements", totalServiceImprovements);
}

$(window).resize(function(){
    $("#sifChart").data("kendoChart").redraw();
});

function refreshSifChart(){
    var sifChart = $("#sifChart").data("kendoChart");
    sifChart.dataSource.read();
}

function sifData(){
    return{
        type:viewModel.get("type")
    }
}

function refreshServiceImprovementGrid(){
    var myServiceImprovementGrid = $("#myServiceImprovementGrid").data("kendoGrid");
    myServiceImprovementGrid.dataSource.read();
}

function userNoteEdit(e) {
    $(e.container).find(".k-edit-buttons").html("<a class='btn btn-success btn-sm k-grid-update' href='#'><i class='fa fa-check-circle'></i>Update</a> " +
       "<a class='btn btn-warning btn-sm k-grid-cancel' href='#'><i class='fa fa-ban'></i>Cancel</a>");
}

function updateUserNoteTotal() {
    var userNoteGrid = $("#userNoteGrid").data("kendoGrid");
    var totalUserNotes = userNoteGrid.dataSource.total();
    viewModel.set("totalUserNotes", totalUserNotes);
}

function updateTotalMyProjects() {
    var myProjects = $("#myProjects").data("kendoListView");
    var totalMyProjects = myProjects.dataSource.total();
    viewModel.set("totalMyProjects", totalMyProjects);
}

function updateProjectLessonTotals() {
    var projectLessonGrid = $("#projectLessonGrid").data("kendoGrid");
    var totalProjectLessons = projectLessonGrid.dataSource.total();
    viewModel.set("totalProjectLessons", totalProjectLessons);
}

function updateMyCommentTotals() {
    var myCommentGrid = $("#myCommentGrid").data("kendoGrid");
    var totalComments = myCommentGrid.dataSource.total();
    viewModel.set("totalComments", totalComments);
}

function refreshMyComment(){
    var myCommentGrid = $("#myCommentGrid").data("kendoGrid");
    myCommentGrid.dataSource.read();
}

function updateAllCommentTotals() {
    var allCommentGrid = $("#allCommentGrid").data("kendoGrid");
    var totalAllComments = allCommentGrid.dataSource.total();
    viewModel.set("totalComments", totalAllComments);
}

function refreshAllComment(){
    var allCommentGrid = $("#allCommentGrid").data("kendoGrid");
    allCommentGrid.dataSource.read();
}

function getMyCertificates() {
    $.ajax({
        url: "/Home/GetMyCertificates",
        data: {
        },
        success: function (result) {
            if (result) {
                viewModel.set("totalExpiringThisMonth", result.TotalExpiringThisMonth);
                viewModel.set("totalExpiringOneMonth", result.TotalExpiringOneMonth);
                viewModel.set("totalExpiringTwoMonth", result.TotalExpiringTwoMonth);
                viewModel.set("totalExpiringThreeMonth", result.TotalExpiringThreeMonth);
                viewModel.set("totalExpiringThisYear", result.TotalExpiringThisYear);
            }
        },
        dataType: "json"
    });
}

function getMySifs() {
    $.ajax({
        url: "/Home/GetMySifs",
        data: {
        },
        success: function (result) {
            if (result) {
                viewModel.set("totalSifOpen", result.TotalSifOpen ? result.TotalSifOpen :  0);
                viewModel.set("totalSifOnTarget", result.TotalSifOnTarget ? result.TotalSifOnTarget : 0);
                viewModel.set("totalSifOverdue", result.TotalSifOverdue ? result.TotalSifOverdue : 0);
                viewModel.set("totalSifClosed", result.TotalSifClosed ? result.TotalSifClosed : 0);
            }
        },
        dataType: "json"
    });
}

function getMyLessonsLearnedSummary() {
    $.ajax({
        url: "/Home/GetMyLessonsLearnedSummary",
        data: {
        },
        success: function (result) {
            if (result) {
                viewModel.set("totalLessons", result.TotalLessons);
                viewModel.set("totalPendingApproval", result.TotalPendingApproval);
            }
        },
        dataType: "json"
    });
}

function lessonData() {
    return {
        type: viewModel.get("type"),
    };
}

function loadRiscGrid() {
    var grid = $("#myRiscGrid").data("kendoGrid");
    var toolBar = $("#myRiscGrid .k-grid-toolbar").html();
    var options = localStorage["myRiscGrid"];
    viewModel.set("initialMyRiscGridOptions", kendo.stringify(grid.getOptions()));
    if (options) {
        grid.setOptions(JSON.parse(options));
        $("#myRiscGrid .k-grid-toolbar").html(toolBar);
        $("#myRiscGrid .k-grid-toolbar").addClass("k-grid-top");
    }
}

function saveRiscGrid(e) {
    setTimeout(function () {
        var grid = $("#myRiscGrid").data("kendoGrid");
        localStorage["myRiscGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}

function riscData() {
    return {
        type: viewModel.get("type"),
    };
}

function updateRiscGrid() {
    $("#resetMyRiscGrid").click(function (e) {
        e.preventDefault();
        resetGridView('myRiscGrid', 'initialMyRiscGridOptions')
    });
    $("#resetMyPersonnelCertificateDocumentGrid").click(function (e) {
        e.preventDefault();
        resetGridView('lessonGrid', 'initialLessonGridOptions')
    });

    var myRiscGrid = $("#myRiscGrid").data("kendoGrid");
    var riscCount = myRiscGrid.dataSource.total();
    viewModel.set("riscCount", riscCount);

    refreshRiscChart();
}

function summaryMonthChanged(e){
    getRiscMonthlySummary();
}

function getRiscMonthlySummary() {
    $.ajax({
        url: "/Home/GetMyRiscMonthlySummary",
        data: {
            month:viewModel.get("summaryMonth")
        },
        success: function(result){
            if(result){
                viewModel.set("totalCreatedMonth", result.TotalRiscCreated ? result.TotalRiscCreated : 0);
                viewModel.set("totalPositiveMonth", result.TotalRiscPositive ? result.TotalRiscPositive : 0);
                viewModel.set("totalNegativeMonth", result.TotalRiscNegative ? result.TotalRiscNegative : 0);
            }
        },
        dataType: "json"
    });
}

function daysOnsiteMonthChanged(e){
    getDaysOnSiteMonthlySummary();
}

function getDaysOnSiteMonthlySummary() {
    $.ajax({
        url: "/Home/GetMyCrewSummary",
        data: {
            month:viewModel.get("daysOnSiteMonth")
        },
        success: function(result){
            if(result){
                viewModel.set("totalInLast30Days",result.TotalInLast30Days);
            }
        },
        dataType: "json"
    });
}

$(window).resize(function(){
    $("#riscChart").data("kendoChart").redraw();
});

function refreshRiscChart(){
    var riscChart = $("#riscChart").data("kendoChart");
    riscChart.dataSource.read();
}

function refreshRiscGrid() {
    var myRiscGrid = $("#myRiscGrid").data("kendoGrid");
    myRiscGrid.dataSource.read();
}

function refreshProjectLessonGrid(){
    var projectLessonGrid = $("#projectLessonGrid").data("kendoGrid");
    projectLessonGrid.dataSource.read();
}

function projectChange(e){
    var project = $("#myProjectsLessons").data("kendoDropDownList");
    var index = project.selectedIndex;
    var myProject = project.dataItem(index);

    viewModel.set("projectId", myProject.ProjectId);

    refreshProjectLessonGrid();
}

function projectData(){
    return{
        projectId : viewModel.get("projectId")
    }
}


$(function () {
    var myPersonnelCertificateDocumentGrid = $("#myPersonnelCertificateDocumentGrid").data("kendoGrid");

    myPersonnelCertificateDocumentGrid.table.on("click", ".checkbox" , selectRow);
});

function selectRow() {
    var checked = this.checked,
    row = $(this).closest("tr"),
    grid = $("#myPersonnelCertificateDocumentGrid").data("kendoGrid"),
    dataItem = grid.dataItem(row);
    var documentIds = viewModel.get("documentIds");

    if(checked){
        documentIds.push(dataItem.id);
        viewModel.set("documentIds", documentIds);
    } else{
        var index = documentIds.indexOf(dataItem.id);

        if(index>-1){
            documentIds.splice(index, 1);
        }
    }
}

function sendEmailPersonneCertificate() {
    let isValid = $('#certificateEmailAddressForm').kendoValidator({ 
        messages: { 
            required: function(input) {
                return getPersonneCertificateCustomMessage(input);
            },
            emailValidation: function(input) {
                return "Please provide a valid email address";
            },
        },
        rules: {
            emailValidation: function(input) {
                if (input.is("[name=CertificateEmailAddress]")) {
                    return String(input.val()).toLowerCase()
                    .match(/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/);
                }
                return true;
            }
        }
    }).data('kendoValidator').validate();
    if (isValid) {
        $("#myPersonnelCertificateDocumentGrid").data("kendoGrid").dataSource.read();
        $("#personnelCertificateAttachmentWindow").data("kendoWindow").close();
        
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: "/Home/EmailPersonnelCertificate",
            data: {
                emailAddress: viewModel.get("emailAddress"),
                documentIds: viewModel.get("documentIds").toJSON()
            },
            success: function () {
                viewModel.set("emailAddress", "");
                viewModel.set("documentIds", []);
                $("#myPersonnelCertificateDocumentGrid").data("kendoGrid").dataSource.read();

                $("<div id='dialog'></div>").kendoDialog({
                    closable: false,
                    title: "Personnel Certificates",
                    width: 500,
                    buttonLayout: "normal",
                    content: "The Email was sent",
                    actions: [
                        {
                            text: "OK",
                            cssClass: 'btn-primary'
                        }]
                }).data("kendoDialog").open().center()
            }
        });
    } 
}

function getPersonneCertificateCustomMessage(input) {
    return  input[0] && input[0].labels[0] && input[0].labels[0].textContent ? input[0].labels[0].textContent + " is required" : "This field is required"
}
function checkMobileNotification() {
    var showAll = viewModel.get("showAll");
    var value = this.value();
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: "/Account/UpdateMobileNotification",
        data: {
            mobileNotification: value,
            showAll: showAll
        },
        success: function () {
            $('home').submit();
        }
    });
}

function checkEmailNotification() {
    var showAll = viewModel.get("showAll");
    var value = this.value();
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: "/Account/UpdateEmailNotification",
        data: {
            emailNotification: value,
            showAll: showAll
        },
        success: function () {
            $('home').submit();
        }
    });
}

function showMyProjectComments() {
    var showAll = viewModel.get("showAll");
     $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: "/Account/ShowMyProjectComments",
        data: {
            showAll: showAll,
        },
        success: function () {
            $('home').submit();
        }
    });
}

function showAllProjectComments() {
    var showAll = viewModel.get("showAll");
     $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: "/Account/ShowAllProjectComments",
        data: {
            showAll: showAll,
        },
        success: function () {
            $('home').submit();
        }
    }); 
}

function showMyAmericaProjectComments() {
    var showAll = viewModel.get("showAllAmerica");
     $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: "/Account/ShowMyProjectComments",
        data: {
            showAll: showAll,
        },
        success: function () {
            $('home').submit();
        }
    });
}

function showAllAmericaProjectComments() {
    var showAll = viewModel.get("showAllAmerica");
     $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: "/Account/ShowAllProjectComments/",
        data: {
            showAll: showAll,
        },
        success: function () {
            $('home').submit();
        }
    }); 
}

function activateTabDependingOnUrlQuery () {
    let tabId = window.location.search.split("tab=")[1];
    if(tabId) {
        let tabToActivate = $(`#${tabId}`);
        if(tabToActivate) {
          $("#mainTabStrip").data("kendoTabStrip").select(tabToActivate)
        }
    }
}