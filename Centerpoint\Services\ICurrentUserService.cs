﻿using Centerpoint.Model.Configuration;
using Microsoft.Extensions.Options;
using System.Security.Claims;

namespace Centerpoint.Services
{
    public interface ICurrentUserService
    {
        public int UserId { get; }
    }

    public class CurrentUserService : ICurrentUserService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly AzureAdSettings _azureAdSettings;

        public CurrentUserService(IHttpContextAccessor httpContextAccessor, IOptions<AzureAdSettings> azureAdSettings)
        {
            _httpContextAccessor = httpContextAccessor;
            _azureAdSettings = azureAdSettings.Value;
        }

        public int UserId { 
            get 
            {
                return Convert.ToInt32(_httpContextAccessor.HttpContext.User.FindFirstValue(_azureAdSettings.Enabled ? ClaimTypes.UserData : ClaimTypes.NameIdentifier));
            }
        }
    }
}
