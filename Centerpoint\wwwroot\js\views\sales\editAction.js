
function opportunityChange(e) {
    var opportunityId = this.value();

    if (opportunityId) {
        viewModel.set("opportunityId", opportunityId);
        var dropdownlist = $("#CompanyId").data("kendoDropDownList");
        dropdownlist.enable(false);
    } else {
        var dropdownlist = $("#CompanyId").data("kendoDropDownList");
        dropdownlist.enable(true);
    }

    viewModel.set("companyId", "");
}

function opportunityDatabound(e) {
    var opportunityId = this.value();

    if (opportunityId) {
        viewModel.set("opportunityId", opportunityId);

        var dropdownlist = $("#CompanyId").data("kendoDropDownList");
        dropdownlist.enable(false);

        viewModel.set("companyId", "");
    }
}

function companyChange(e) {
    var companyId = this.value();

    if (companyId) {
        viewModel.set("companyId", companyId);
        var dropdownlist = $("#OpportunityId").data("kendoDropDownList");
        dropdownlist.enable(false);
    } else {
        var dropdownlist = $("#OpportunityId").data("kendoDropDownList");
        dropdownlist.enable(true);
    }

    viewModel.set("opportunityId", "");
}

function companyDatabound(e) {
    var companyId = this.value();

    if (companyId) {
        viewModel.set("companyId", companyId);

        var dropdownlist = $("#OpportunityId").data("kendoDropDownList");
        dropdownlist.enable(false);

        viewModel.set("opportunityId", "");
    }
}

function opportunityData() {
    return {
        opportunityId: editActionModel.modelOpportunityId
    }
}

function onOpportunityActionDocumentAttached() {
    var opportunityActionDocumentsGrid = $("#opportunityActionDocumentsGrid").data("kendoGrid");
    opportunityActionDocumentsGrid.dataSource.read();
}

function onOpportunityActionDocumentUpload(e) {
    uploadValidation(e);
    $(".k-upload-files.k-reset").show();
}

function onOpportunityActionDocumentComplete(e) {
    $(".k-upload-files.k-reset").find("li").remove();
    $(".k-upload-files.k-reset").slideUp();
}

function updateOpportunityActionDocumentsGrid() {
    var opportunityActionDocumentsGrid = $("#opportunityActionDocumentsGrid").data("kendoGrid");
    var totalOpportunityActionDocuments = opportunityActionDocumentsGrid.dataSource.total();
    viewModel.set("totalOpportunityActionDocuments", totalOpportunityActionDocuments);
}

var viewModel = new kendo.observable({
    totalOpportunityActionDocuments: 0,
    totalCompletedEvents: 0,
    opportunityId: editActionModel.modelOpportunityId,
    companyId: editActionModel.modelCompanyId,
    tabStripHeaderDetails: function () {
        return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Details </span>`;
    },
    attachmentsStripText: function(){
        return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Attachments (<span data-bind="text:totalOpportunityActionDocuments"></span>)</span>`;
    },
});
kendo.bind(document.body.children, viewModel);