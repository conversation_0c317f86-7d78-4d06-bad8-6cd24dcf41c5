﻿@using Centerpoint.Common.Enums
@using Centerpoint.Extensions
@using Centerpoint.Model.ViewModels
@using Kendo.Mvc.UI

@model CompanyModel

@Html.Partial( "_GridNotification", EntityType.Company)

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-building"></i>
        Entities 
        (<span data-bind="text:totalCompanies"></span>)
    </h4>
</div>
<hr />

<div class="grid-container-big">
    @(Html.Kendo().Grid<CompanyModel>()
        .Name("companyGrid")
        .Columns(columns => {
            columns.Bound(c => c.Name).ClientTemplate("<a href='/Admin/EditCompany/#=CompanyId#'>#=Name#</a>").Width(200);
            columns.Bound(c => c.Categories).Title("Categories").ClientTemplateId("categoriesTemplate").Width(260);
            columns.Bound(c => c.IsActive).Title("Active").ClientTemplate("#if(IsActive){#Yes#}else{#No#}#").Width(100);
            columns.Bound(c => c.CompanyLocationCount).Title("Locations").Width(155);
            columns.Bound(c => c.CompanyContactCount).Title("Contacts").Width(155);
            columns.Bound(c => c.CompanyFieldsCount).Title("Fields").Width(155);
            columns.Bound(c => c.CompanyWellsCount).Title("Wells").Width(155);
            if (Html.IsGlobalAdmin()) {
                columns.Command(command =>{
                    command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"});
                }).Width(200);
            }
        })
        .Events(e => e.DataBound("updateCompanyTotals").ColumnReorder("saveCompanyGrid").ColumnResize("saveCompanyGrid").ColumnShow("saveCompanyGrid").ColumnHide("saveCompanyGrid"))
        .ToolBar(t => {
            t.Custom().HtmlAttributes(new{@class="text-white bg-primary ml-2", @onclick="window.location.href='/Admin/AddCompany'"}).Text(" Add New Entity");
            t.Excel().Text("Export").HtmlAttributes(new{@class="float-right ml-1"});
            t.Custom().Text("Reset Grid View").HtmlAttributes(new{ @id="resetCompanyGrid", @class="bg-danger text-white float-right"});
        })
        .ColumnMenu(c => c.Columns(true))
        .Resizable(c => c.Columns(true))
        .Filterable()
        .HtmlAttributes(new { @class="gridWithThreeToolbarButtons" })
        .Sortable()
        .Groupable()
        .Reorderable(c => c.Columns(true))
        .Scrollable()
        .Excel(excel => excel
            .FileName(string.Format("Centerpoint_ClientCompanies_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Admin"))
            )
        .DataSource(dataSource => dataSource
                .Ajax()
                .Model(model => {
                    model.Id(m => m.CompanyId);
                    model.Field(e => e.CategoriesList).DefaultValue(new List<CategoryModel>());
                    model.Field(f => f.CompanyLocationCount).Editable(false);
                    model.Field(f => f.CompanyContactCount).Editable(false);
                    model.Field(f => f.CompanyFieldsCount).Editable(false);
                    model.Field(f => f.CompanyWellsCount).Editable(false);

                })
                .Events(e => e.Error("onError").RequestEnd("onRequestEnd"))
                .Read(read => read.Action("GetCompanies", "Admin"))
                .Create(create=>create.Action("AddCompany","Admin"))
                .Update(update=>update.Action("EditCompany","Admin"))
                .Destroy(destroy => destroy.Action("DeleteCompany", "Admin"))
        )
    )
</div>

    <script type="text/kendo" id="categoriesTemplate">
        <ul class="companyCategoriesList">#if(data.CategoriesList){#
                #for(var i = 0; i< data.CategoriesList.length; i++){#
                    <li>#:data.CategoriesList[i].Name#</li>
                #}#
            #}#
        </ul>
    </script>

    

    <script type="text/kendo" id="companyNameTemplate">
        <a href='@Url.Action("EditCompany", "Admin")/#=CompanyId#'>#=Name#</a>
    </script>

    <script type="text/javascript">
        var categoriesTemplate = kendo.template($("#categoriesTemplate").html(), { useWithBlock: false });
    </script>


    <script>
        $(document).ready(function () {
            var companyGrid = $('#companyGrid').data("kendoGrid");
            loadCompanyGrid();
            companyGrid.bind('dataBound', function (e) {
                this.element.find('.k-i-excel').remove();
            });
        });

        function loadCompanyGrid() {
            var grid = $("#companyGrid").data("kendoGrid");
            var options = localStorage["companyGrid"];
            var toolBar = $("#companyGrid .k-grid-toolbar").html();
            viewModel.set("initialCompanyGridOptions", kendo.stringify(grid.getOptions()));
            if (options) {
                grid.setOptions(JSON.parse(options));
                $("#companyGrid .k-grid-toolbar").html(toolBar);
                $("#companyGrid .k-grid-toolbar").addClass("k-grid-top");
            }
        }

        function saveCompanyGrid(e) {
            setTimeout(function () {
                var grid = $("#companyGrid").data("kendoGrid");
                localStorage["companyGrid"] = kendo.stringify(grid.getOptions());
            }, 10);
        }    

        function onError(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var companyGrid = $("#companyGrid").data("kendoGrid");
                companyGrid.dataSource.cancelChanges();
            }

            onErrorDeleteGridHandler(e, "#companyGrid");
        }

        function updateCompanyTotals() {
            $("#resetCompanyGrid").click(function (e) {
                e.preventDefault();
                resetGridView('companyGrid', 'initialCompanyGridOptions')
            });

            var companyGrid = $("#companyGrid").data("kendoGrid");
            var totalCompanies = companyGrid.dataSource.total();
            viewModel.set("totalCompanies", totalCompanies);
        }

        var viewModel = new kendo.observable({
            totalCompanies: 0            
        });

        kendo.bind(document.body.children, viewModel);
    </script>

<style>
    .companyCategoriesList {
        padding: 0;
        margin: 0;
    }
</style>