﻿
<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-cogs"></i>
        Event Types
        (<span data-bind="text:totalEventTypes"></span>)
    </h4>
</div>
<hr />

<div class="grid-container">
    @(Html.Kendo().Grid<EventTypeModel>()
        .Name("eventTypeGrid")
        .Columns(c => {
            c.Bound(p => p.Name);
            c.Command(command => { 
                command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
            }).Width(200);
        })
        .Editable(editable => editable.Mode(GridEditMode.InLine))
        .ToolBar(t => {
            t.Create().Text("Add Event Type");
            t.Excel().Text("Export");
        }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
        .Sortable()
        .Mobile(MobileMode.Auto)
        .Filterable()
        .Scrollable()
        .Resizable(c => c.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .Events(e => e.DataBound("updateEventTypeTotal"))
        .Excel(excel => excel
            .FileName(string.Format("Centerpoint_Event_Types_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Admin"))
        )
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(m => m.Id(p => p.EventTypeId))
            .Events(e => e.Error("onError"))
            .Read(read => read.Action("GetEventTypes", "Admin"))
            .Create(create => create.Action("UpdateEventType", "Admin"))
            .Update(update => update.Action("UpdateEventType", "Admin"))
            .Destroy(destroy => destroy.Action("DeleteEventType", "Admin"))
        )
    )
</div>



    <script>
        $(document).ready(function () {
            var eventTypeGrid = $('#eventTypeGrid').data("kendoGrid");
            eventTypeGrid.bind('dataBound', function (e) {
                this.element.find('.k-add').remove();
                this.element.find('.k-i-excel').remove();
            });
        });

        function updateEventTypeTotal() {
            var eventTypeGrid = $("#eventTypeGrid").data("kendoGrid");
            var totalEventTypes = eventTypeGrid.dataSource.total();
            viewModel.set("totalEventTypes", totalEventTypes);
        }

        function onError(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var eventTypeGrid = $("#eventTypeGrid").data("kendoGrid");
                eventTypeGrid.dataSource.cancelChanges();
            }
        }

        var viewModel = new kendo.observable({
            totalEventTypes: 0
        });

        kendo.bind(document.body.children, viewModel);
    </script>
