﻿<p>Please fill in a reason for closure</p>
<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>Reason</label>
            @(Html.Kendo().DropDownList()
                .Name("projectReason")
                .OptionLabel("Select Role")
                .DataValueField("OpportunityClosedReasonId")
                .DataTextField("Name")
                .Filter(FilterType.Contains)
                .HtmlAttributes(new { @style = "font-size: 14px;" })
                .DataSource(d => d.<PERSON>("GetClosureReasons", "Lookup")))
        </div>
        <div class="form-group">
            <label>Comment</label>
           @Html.TextArea("ClosureReason",null, new { @class = "form-control", @style = "width:90%", @rows = "5", })
        </div>
    </div>
</div>
<button id="projectClosureReasonConfirm" class="btn btn-primary btn-sm">Confirm</button>
