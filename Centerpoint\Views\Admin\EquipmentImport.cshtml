﻿<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-upload"></i>
        Equipment Data Import
    </h4>
</div>
<hr />
              
@(Html.Kendo().Upload()
    .Name("formFile")
    .Messages(m => m.Select("Attach Import File (*.xlsx)"))
    .Async(a => a.Save("ImportEquipmentItems", "Admin").AutoUpload(true))
    .Multiple(false))


<button onclick="window.location.href='/Admin/GetEquipmentItemImportTemplate';" title="Equipment Item Import Template" class="btn btn-primary" style="margin-top:20px;">
    <h5>
        <i class="fas fa-download"></i>
        Download Equipment Item Import Template
    </h5>
</button>
