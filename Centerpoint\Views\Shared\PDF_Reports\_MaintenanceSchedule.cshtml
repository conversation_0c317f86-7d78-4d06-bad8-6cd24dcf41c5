﻿@{
    Layout = null;
}
@using Centerpoint.Common
@model EquipmentShipmentInvoiceModel

@using Centerpoint.Model.Configuration;
@using Microsoft.Extensions.Options;
<!DOCTYPE html>
<html lang="en-GB">
<head>
    <base href="/" />

    <meta charset="UTF-8">
    <link rel="stylesheet" href="style/invocePDF.css" />
    <link rel="stylesheet" href="style/bootstrap.min.css" />
    <link href='http://fonts.googleapis.com/css?family=Raleway' rel='stylesheet' type='text/css'>

    <title>Document</title>
</head>
<body style="margin: 5px;">
    <div>
        <table style="width:100%">
            <tbody>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p><strong>@Model.CompanyLocationCompanyName</strong></p></td>
                </tr>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p>@Model.CompanyLocationName</p></td>
                </tr>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p> @Model.CompanyLocationHouseNumber</p></td>
                </tr>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p>@Model.CompanyLocationStreet</p></td>
                </tr>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p>@Model.CompanyLocationCity, @Model.CompanyLocationPostcode</p></td>
                </tr>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p>@Model.CompanyLocationCountry</p></td>
                </tr>
                <tr>
                    <td class="td-info td-assign"></td>
                    <td class="td-info-center td-assign"></td>
                    <td class="td-info td-assign"><p>Tel: @Model.CompanyLocationTelephone</p></td>
                </tr>
                <tr>
                    <td class="td-info"></td>
                    <td class="td-info-center"></td>
                    <td class="td-info"><p>@(Html.Raw(!string.IsNullOrEmpty(Model.CompanyLocationComment) ? Model.CompanyLocationComment.Replace(System.Environment.NewLine, "<br />").Replace("\n", @"<br />") : string.Empty))</p></td>
                </tr>
            </tbody>
        </table>
    </div>
    <hr />
    <div>
            <div style="display: flex; justify-content: space-between;">
                <table id="info" style="width:65%">
                    <tbody>
                        <tr>
                            <td class="td-info title-padding"><p class="title-shipment-details">@Model.EquipmentShipment.FromTitlePDF</p></td>
                            <td class="td-info-center title-padding"><p class="title-shipment-details">@Model.EquipmentShipment.ToTitlePDF</p></td>
                        </tr>
                        <tr>
                            <td class="td-info"><p><strong>@Model.EquipmentShipment.FromCompanyPDF</strong></p></td>
                            <td class="td-info-center"><p><strong>@Model.EquipmentShipment.ToCompanyPDF</strong></p></td>
                        </tr>
                        <tr>
                            <td class="td-info"><p>@(Html.Raw(@Model.EquipmentShipment.FromCompanyLocationHousePDF.Replace(System.Environment.NewLine, "<br />").Replace("\n", @"<br />")))</p></td>
                            <td class="td-info"><p>@(Html.Raw(@Model.EquipmentShipment.ToCompanyLocationHousePDF.Replace(System.Environment.NewLine, "<br />").Replace("\n", @"<br />")))</p></td>
                        </tr>
                    </tbody>
                </table>

                <table id="shipment-details" style="width:30%">
                    <tbody>
                        <tr>
                            <td class="td-info title-padding"><p class="title-shipment-details">Shipment Details:</p></td>
                        </tr>
                        <tr>
                            <td class="td-info"><p><strong>@Model.PaperworkTypeDescription:</strong> @Model.EquipmentShipment.Number</p></td>
                        </tr>
                        <tr>
                            <td class="td-info"><p><strong>Description:</strong> @Model.EquipmentShipment.Description</p></td>
                        </tr>
                        <tr>
                            <td class="td-info"><p><strong>Shipment Method:</strong> @Model.EquipmentShipment.ShipmentMethodName</p></td>
                        </tr>
                        <tr>
                            <td class="td-info"><p><strong>Project:</strong> @Model.EquipmentShipment.ProjectName</p></td>
                        </tr>
                        <tr>
                            <td class="td-info"><p><strong>Date:</strong> @Model.EquipmentShipment.SentDateOnly</p></td>
                        </tr>
                        <tr>
                            @if (GlobalSettings.IsWellsense)
                            {
                                <td class="td-info"><p><strong>Incoterm:</strong> @Model.EquipmentShipment.CustomStatus</p></td>
                            }
                            else
                            {
                                <td class="td-info"><p><strong>Custom Status:</strong> @Model.EquipmentShipment.CustomStatus</p></td>
                            }
                        </tr>
                    </tbody>
                </table>
            </div>
    </div>
    <hr />
    @{
        var totalShipmentValue = 0.0M;
    }
    @if (Model.EquipmentShipment.EquipmentShipmentItems != null && Model.EquipmentShipment.EquipmentShipmentItems.Any())
    {
        <div class="panel panel-default" style="border:none">
            <div class="panel-heading" style="background: #6eb6b4; color: #fff">
                <h2 class="panel-title" style="font-size:20px">Assets [@Model.EquipmentShipment.EquipmentShipmentItems.Count] </h2>
            </div>
            <br />
            <div class="panel-body" style="padding:0px;border-bottom:none">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th style="text-align:center; width:5%;font-size:20px">Item</th>
                            <th style="text-align:center; width:10%;font-size:20px">Item Number</th>
                            <th style="text-align:center; width:45%;font-size:20px">Current Location</th>
                            @if (GlobalSettings.IsRegiis)
                            {
                                <th style="text-align:center; width:5%;font-size:20px">Current Points</th>
                            }
                            <th style="text-align:center; width:5%;font-size:20px">Active MR's</th>
                            <th style="text-align:center; width:30%;font-size:20px">Maintenance Schedule</th>
                        </tr>
                    </thead>
                    <tbody>
                        @for (var i = 0; i < Model.EquipmentShipment.EquipmentShipmentItems.Count; i++)
                        {
                            var value = 0.0M;
                            <tr>
                                <td style="text-align:center; width:5%;font-size:20px">@((i + 1).ToString())</td>
                                <td style="text-align:center; width:10%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentItems[i].EquipmentItemName</td>
                                <td style="text-align:center; width:45%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentItems[i].CurrentLocation</td>
                                @if (GlobalSettings.IsRegiis)
                                {
                                    <td style="text-align:left; width:5%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentItems[i].EquipmentItemPoints</td>
                                }
                                <td style="text-align:center; width:5%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentItems[i].MRCount</td>
                                <td style="text-align:center; width:30%;font-size:20px">@Model.EquipmentShipment.EquipmentShipmentItems[i].MaintenanceScheduleDetail</td>
                            </tr>
                            totalShipmentValue += value;
                        }
                    </tbody>
                </table>
            </div>
        </div>
    }
    <br />
   
    @if (!string.IsNullOrEmpty(Model.EquipmentShipment.ShipmentNotes))
    {
        <div style="font-size:20px !important">
            <p class="title-shipment-details">Shipment Notes:</p><p> @(Html.Raw(@Model.EquipmentShipment.ShipmentNotes.Replace(System.Environment.NewLine, "<br />").Replace("\n", @"<br />")))</p>
        </div>
    }
    @if (!string.IsNullOrEmpty(Model.EquipmentShipment.PackingNotes))
    {
        <div style="font-size:20px !important">
            <p class="title-shipment-details">Packing Notes:</p><p> @(Html.Raw(Model.EquipmentShipment.PackingNotes.Replace(System.Environment.NewLine, "<br />").Replace("\n", @"<br />")))</p>
        </div>
    }
    <hr />
    <div>
        <table style="width:100%">
            <tbody>
                <tr>
                    <td class="td-info-label td-assign"><p><label><strong> Sent By: </strong></label></p></td>
                    <td class="td-info-sign td-assign"><p>______________________________________________</p></td>
                    <td class="td-info-label td-assign"><p></p></td>
                    <td class="td-info-label td-assign"><p><label><strong> Received By: </strong></label></p></td>
                    <td class="td-info-sign td-assign">______________________________________________<p></p></td>

                </tr>
                <tr>
                    <td class="td-info-label td-assign"><p><label><strong>Print:</strong></label></p></td>
                    <td class="td-info-sign td-assign"><p>______________________________________________</p></td>
                    <td class="td-info-label td-assign"><p></p></td>
                    <td class="td-info-label td-assign"><p><label><strong>Print:</strong></label></p></td>
                    <td class="td-info-sign td-assign"><p>______________________________________________</p></td>

                </tr>
                <tr class="table-assign">
                    <td class="td-info-label td-assign"><p><label><strong>Sign:</strong></label> </p></td>
                    <td class="td-info-sing td-assign"><p>______________________________________________</p></td>
                    <td class="td-info-label td-assign"><p></p></td>
                    <td class="td-info-label td-assign"><p><label><strong>Sign:</strong></label> </p></td>
                    <td class="td-info-sing td-assign"><p>______________________________________________</p></td>
                </tr>
                <tr>
                    <td class="td-info-label td-assign"><p><label><strong>Date:</strong></label> </p></td>
                    <td class="td-info-sing td-assign"><p>______________________________________________</p></td>
                    <td class="td-info-label td-assign"><p></p></td>
                    <td class="td-info-label td-assign"><p><label><strong>Date:</strong></label> </p></td>
                    <td class="td-info-sing td-assign"><p>______________________________________________</p></td>
                </tr>
            </tbody>
        </table>
    </div>
</body>
</html>