.k-treeview {
    height: calc(100vh - 330px);
}

/* actionsContainer */
.actionsContainer a:not(:first-child),
.actionsContainer > button:not(:first-child)
{
    margin-left: 10px;
}
.margin-l {
    margin-left: 10px;
}
.margin-r {
    margin-right: 10px;
}
/* actionsContainer */

.editEquipmentLabelSmall{
    width:60px;
}
.editEquipmentLabelMedium{
    width:130px;
}

nav .nav-item .collapse.show{
    padding: 0;
}

nav .nav-item .collapsing{
    -webkit-transition: none;
    transition: none;
    display: none;
}

nav .nav-item .collapse.show ul{
    padding: 8px 0 0 28px;
}

nav .nav-item .collapse.show ul li{
    margin-bottom: 10px;
}

nav .nav-item .collapse.show ul li a i, nav .nav-item .collapse.show ul li a img{
    height: 1rem;
    width: 0.9rem;
    margin-right: 0.7rem;
    transition: 0.3s;
}

nav .nav-item .collapse.show ul li a{
    color: #ffffff;
}

nav .nav-item .collapse-action{
    display: flex;
    align-items: center;
    align-items: center;
    padding-right: 15px;
}

nav .nav-item .collapse-action span ~ i{
    margin-left: auto;
}

nav .nav-item .collapse-action[aria-expanded="true"] .fa-caret-left{
    display: none;
}

nav .nav-item .collapse-action[aria-expanded="false"] .fa-caret-down{
    display: none;
}

.navigation-admin-list li a img, .navigation-admin-list li a i {
    margin-right: 10px;
}

.navigation-admin-list li a i {
    margin-right: 10px;
}

.second-level-collapse{
    width: 100%;
}

#customerListView .k-listview-content {
    display: flex;
    flex-wrap: wrap;
}

.k-listview-content h3 {
    color: #6EB6B4!important;
}

.without-border {
    border: none;
}

.card-list-item-count-exacrt-width{
   width: 150px;
}

.p-right {
    margin-right: 1.25rem;
}

.gridWithThreeToolbarButtons > .k-toolbar{
    display: block;
}


/* header */

.header-container-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-container-single-item {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 2rem;
}

.header-container-single-item-with-hr {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.header-container-between h4,
.header-container-single-item h4,
.header-container-single-item-with-hr h4 
{
    margin-bottom: 0;
    color: #6eb6b4!important;
}
/* header */

.grid-container {
   height: calc(100vh - 250px);
}

.grid-container-big {
    height: calc(100vh - 200px);
 }

.grid-container-standard {
    height: calc(100vh - 320px);
}
.table-bordered {
    border: none;
}

.expandCollapseButtonsContainer {
    margin-bottom: 15px;
}
.expandCollapseButtonsContainer button {
    margin-right: 5px;
}

.rolesTemplateUl {
    margin-left: -40px;
}

/* Grid resize (width) fix of kendo */
#equipmentPackingListItemGrid table,
#equipmentPackingListItemGridInDetails table
{
    min-width:100%;
}

.preview-container {
    width: 130px;
}

.logo-img {
    max-width: 100%;
    height: auto;
}

.logo-img-container {
    width: 120px;
    align-content: center;
    float: left;
    margin: 5px;
    padding: 5px;
    background: #dedede;
}
#logo{
    height: 100%;
    width: 100%;
    object-fit: contain;
}