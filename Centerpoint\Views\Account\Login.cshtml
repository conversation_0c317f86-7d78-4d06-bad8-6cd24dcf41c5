﻿@using Centerpoint.Common;
@using Microsoft.AspNetCore.Mvc.ModelBinding

@{
    Layout = "LoginLayout";
    string errors = string.Join(" ", ViewData.ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage));
}

<div class="login-wrapper">
    <div class="container-fluid h-100 w-100">
        <div class="row h-100 ">
            <div class="col-xl-8 d-none d-lg-none d-xl-block p-0">
                <img src="/img/login-bg.png" alt="centerpoint login image" class="img-cover">
            </div>
            <div class="col-xl-4 bg-white">
                <div class="login-form-wrapper">
                    <img src="/img/logo.svg" alt="centerpoint logo">
                    <form novalidate id="loginForm" method="post" action="/account/login" onsubmit="handleSubmit(event)">
                        <h3 class="mb-4">Welcome to centerpoint</h3> 
                        <div class="mb-2 error-login-message">
                            <p><span class="text-danger">@errors</span></p>
                        </div>
                        <div class="form-group mb-3">
                            <label for="email" class="input-label ms-2 mb-2">Login</label>
                            <input 
                                id="email" 
                                type="email" 
                                name="email" 
                                placeholder="Enter email" 
                                class="form-control" 
                                required="required"
                            />

                        </div>
                        <div class="form-group mb-3">
                            <label for="password" class="input-label ms-2 mb-2">Password</label>
                            <div class="form-group position-relative">
                                <input 
                                    id="password" 
                                    type="password" 
                                    required="required" 
                                    name="password" 
                                    placeholder="Enter password" 
                                    class="form-control"
                                />
                                <button class="btn show-password-btn" type="button" id="showPassword" data-target="password" data-password-visible='false' onclick="togglePasswordVisibility(this)">
                                    <i class="bi bi-eye-fill show-password"></i>
                                    <i class="bi bi-eye-slash-fill hide-password"></i>
                                </button>
                            </div>
                        </div>
                        <div class="form-group d-flex justify-content-between mb-5">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="flexSwitchCheckChecked">
                                <label class="custom-control-label" for="flexSwitchCheckChecked">Remember me</label>
                            </div>
                            <a href="/Account/ForgotPassword">Forgot password?</a>                            
                        </div>                        
                        <button class="btn btn-primary w-100" type="submit">Sign in</button>
                        <hr class="mt-5 mb-5">
                    </form>
                    <span class="copy-r">© Centrepoint @DateTime.Now.Year</span>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(document).ready(function() {
        sentEmailHandler();
        $("#email").kendoTextBox();
        $("#password").kendoTextBox();
    })

    function sentEmailHandler() {
        let params = new URLSearchParams(window.location.search);
        let isSentEmail = params.get('sentEmail');
        if (isSentEmail != undefined || isSentEmail != null) {
            if (isSentEmail.toLowerCase() == 'true') {
                handleSuccessMessages('@ResourceMessages.ForgotPasswordSentEmail');
            }
            window.history.replaceState({}, '', window.location.pathname);
        }
    }

    function handleSubmit (event) {
        
        var validator = $("#loginForm").kendoValidator({
            messages:{
                    email: "Please enter a valid email address"
            }
        }).data("kendoValidator");
        if (!(validator.validate())) {
           event.preventDefault();  
        }
    }

</script>