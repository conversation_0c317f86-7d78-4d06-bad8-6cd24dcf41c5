﻿@model LogisticsDashboardModel

<div class="header-container-between">
    <h4>
        <i class="fa fa-plane"></i>
        Shipments
        (<span data-bind="text: totalEquipmentShipments">0</span>)
    </h4>
    <div>
        @if (Html.IsLogisticsAdmin() || Html.IsGlobalAdmin()) {
            <a class="btn btn-primary btn-sm" href="@Url.Action("AddEquipmentShipment", "Logistics")">
                <i class="fa fa-plus"></i> 
                Create New Shipment
            </a>
        }
    </div>
</div>
<hr />


<div>
    <div class="row mb-3">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Monthly Shipments</h6>
                </div>
                <div class="card-body">
                    <div class="card-list-item">
                        @(Html.Kendo().DropDownListFor(m => m.Month)
                        .DataValueField("Key")
                        .DataTextField("Value")
                        .Filter("contains")
                        .DataSource(d => d.Read("GetMonths", "Lookup"))
                        .Events(e => e.Change("monthChanged"))
                        .HtmlAttributes(new { @data_bind = "value:month" })
                        )
                    </div>
                    <div class="card-list-item">
                            <span class="card-list-item-name">Total</span>
                            <span class="card-list-item-count bg-dark" data-bind="click:totalMonthClick,text:totalMonth"></span>
                    </div>
                    <div class="card-list-item">
                            <span class="card-list-item-name">Sent</span>
                            <span class="card-list-item-count bg-dark" data-bind="click:totalSentMonthClick,text:totalSentMonth"></span>
                    </div>
                    <div class="card-list-item">
                            <span class="card-list-item-name">Received</span>
                            <span class="card-list-item-count" style="background-color: #999;" data-bind="click:totalReceivedMonthClick,text:totalReceivedMonth"></span>
                    </div>
                    @if (!GlobalSettings.IsAisus)
                    {
                        <div class="card-list-item">
                            <span class="card-list-item-name">In Transit</span>
                            <span class="card-list-item-count" style="background-color: #BA141A;" data-bind="click:totalInTransitMonthClick,text:totalInTransitMonth"></span>
                        </div>
                    }
                    <div class="card-list-item">
                            <span class="card-list-item-name">Pending</span>
                            <span class="card-list-item-count" style="background-color: #BA141A;" data-bind="click:totalPendingMonthClick,text:totalPendingMonth"></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Total Shipments</h6>
                </div>
                <div class="card-body">
                    <div class="card-list-item">
                        @(Html.Kendo().DropDownList()
                        .Name("year")
                        .DataValueField("Key")
                        .DataTextField("Value")
                        .ValuePrimitive(true)
                        .Filter("contains")
                        .DataSource(d => d.Read("GetYears", "Lookup", new { @years = 10 }))
                        .Events(e => e.Change("yearChanged"))
                        .HtmlAttributes(new { @data_bind = "value:year"})
                        )
                    </div>
                    <div class="card-list-item">
                        <span class="card-list-item-name">Total</span>
                        <span class="card-list-item-count bg-dark" data-bind="click:totalYearClick,text:totalYear"></span>
                    </div>

                    <div class="card-list-item">
                        <span class="card-list-item-name">Sent</span>
                        <span class="card-list-item-count bg-dark" data-bind="click:totalSentYearClick,text:totalSentYear"></span>
                    </div>

                    <div class="card-list-item">
                        <span class="card-list-item-name">Received</span>
                        <span class="card-list-item-count" style="background: #999" data-bind="click:totalReceivedYearClick,text:totalReceivedYear"></span>
                    </div>
                    @if (!GlobalSettings.IsAisus)
                    {
                        <div class="card-list-item">
                            <span class="card-list-item-name">In Transit</span>
                            <span class="card-list-item-count" style="background: #BA141A" data-bind="click:totalInTransitYearClick,text:totalInTransitYear"></span>
                        </div>
                    }

                    <div class="card-list-item">
                        <span class="card-list-item-name">Pending</span>
                        <span class="card-list-item-count" style="background: #BA141A" data-bind="click:totalPendingYearClick,text:totalPendingYear"></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Shipment Methods</h6>
                </div>
                <div class="card-body">
                    @foreach (var shipmentMethod in Model.TotalInShipmentMethods) {
                        <div class="card-list-item">
                            <span class="card-list-item-name">@shipmentMethod.Key</span>
                            <span class="card-list-item-count" style="background-color: #7CBB00" onclick="shipmentMethodFilter('@shipmentMethod.Key')">@shipmentMethod.Value</span>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    @(Html.Kendo().TabStrip()
        .Name("mainTabStrip")
        .SelectedIndex(0)
        .Animation(animation =>
        {
            animation.Enable(false);
        })
        .Items(tabstrip =>
        {
            tabstrip.Add().Text("Current Shipments")
                .HtmlAttributes(new { @data_bind="html:totalCurrentEquipmentShipmentsText"})
                .Selected(true)
                .Content(@<text>
                    @(Html.Kendo().Grid<EquipmentShipmentModel>()
                        .Name("currentEquipmentShipmentGrid")
                        .Columns(columns => {
                            columns.Bound(c => c.Number).Title("Shipment ID").ClientTemplate("<a href='" + @Url.Action("EditEquipmentShipment", "Logistics") + "/#=EquipmentShipmentId#'>#=Number#</a>");
                            columns.Bound(c => c.EquipmentShipmentStatusDescription).ClientTemplate("#=IsSent ? 'Sent' : EquipmentShipmentStatusDescription#").Title("Status");
                            columns.Bound(c => c.NewProjectFrom).Title("Project From");
                            columns.Bound(c => c.NewProjectName).Title("Project To");
                            columns.Bound(c => c.FromCompanyName).Title("From Client").ClientTemplate("#=FromCompanyName ? FromCompanyName : 'N/A'#");
                            columns.Bound(c => c.FromCompanyLocationName).Title(" From Location").ClientTemplate("#=FromCompanyLocationName ? FromCompanyLocationName : 'N/A'#");
                            columns.Bound(c => c.ToCompanyName).Title("To Client").ClientTemplate("#=ToCompanyName ? ToCompanyName : 'N/A'#");
                            columns.Bound(c => c.ToCompanyLocationName).Title(" To Location").ClientTemplate("#=ToCompanyLocationName ? ToCompanyLocationName : 'N/A'#");
                            columns.Bound(c => c.ToCompanyLocationCountry).Title(" To Country").ClientTemplate("#=ToCompanyLocationCountry ? ToCompanyLocationCountry : 'N/A'#");
                            columns.Bound(c => c.ShipmentMethodName).Title("Shipment Method").Hidden(true);
                            columns.Bound(c => c.CustomStatus).Title(!GlobalSettings.IsWellsense? "Custom Status" : "Incoterm");
                            columns.Bound(c => c.CreatedDate).Title("Created").Hidden(true).Format(DateConstants.DateFormat);
                            columns.Bound(c => c.SentDate).Title("Shipped").Format(DateConstants.DateFormat);
                            columns.Bound(c => c.EquipmentItemsCount).Title("Items Sent");
                            columns.Bound(c => c.EquipmentItemsReceivedCount).Title("Items Received");
                            columns.Bound(c => c.EquipmentNonAssetItemsCount).Title("Non-Asset Sent");
                            columns.Bound(c => c.EquipmentNonAssetItemsReceivedCount).Title("Non-Asset Received");
                        })
                        .Events(e => e
                            .DataBound("updateCurrentEquipmentShipmentGrid")
                            .ColumnReorder("saveCurrentEquipmentShipmentGrid")
                            .ColumnResize("saveCurrentEquipmentShipmentGrid")
                            .ColumnShow("saveCurrentEquipmentShipmentGrid")
                            .ColumnHide("saveCurrentEquipmentShipmentGrid")
                        )
                        .ToolBar(t => {
                            t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetCurrentEquipmentShipmentGrid", @class="bg-danger text-white"});
                            t.Excel().Text("Export");
                        }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
                        .Groupable()
                        .Filterable()
                        .Sortable()
                        .Scrollable(scrollable => scrollable.Endless(true).Height(535))
                        .ColumnMenu(c => c.Columns(true))
                        .Resizable(r => r.Columns(true))
                        .Reorderable(r => r.Columns(true))
                        .Excel(excel => excel
                            .FileName(string.Format("Centerpoint_Shipments_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                            .Filterable(true)
                            .ProxyURL(Url.Action("Export", "Logistics")))
                        .DataSource(dataSource => dataSource
                            .Ajax()
                            .PageSize(300)
                            .ServerOperation(false)
                            .Model(model => {
                                model.Id(m => m.EquipmentShipmentId);
                            })
                            .Events(e => e.Error("onCurrentError"))
                            .Read(read => read.Action("GetCurrentEquipmentShipments", "Logistics").Data("logisticsData"))
                        )
                    )
                </text>);

            tabstrip.Add().Text("Received Shipments")
                .HtmlAttributes(new { @data_bind="html:totalReceivedEquipmentShipmentsText"})
                .Content(@<text>
                    @(Html.Kendo().Grid<EquipmentShipmentModel>()
                        .Name("receivedEquipmentShipmentGrid")
                        .Columns(columns => {
                            columns.Bound(c => c.Number).Title("Shipment ID").ClientTemplate("<a href='" + @Url.Action("EditEquipmentShipment", "Logistics") + "/#=EquipmentShipmentId#'>#=Number#</a>");
                            columns.Bound(c => c.EquipmentShipmentStatusDescription).Title("Status");
                            columns.Bound(c => c.NewProjectFrom).Title("Project From");
                            columns.Bound(c => c.NewProjectName).Title("Project To");
                            columns.Bound(c => c.FromCompanyName).Title("From Client").ClientTemplate("#=FromCompanyName ? FromCompanyName : 'N/A'#");
                            columns.Bound(c => c.FromCompanyLocationName).Title(" From Location").ClientTemplate("#=FromCompanyLocationName ? FromCompanyLocationName : 'N/A'#");
                            columns.Bound(c => c.ToCompanyName).Title("To Client").ClientTemplate("#=ToCompanyName ? ToCompanyName : 'N/A'#");
                            columns.Bound(c => c.ToCompanyLocationName).Title(" To Location").ClientTemplate("#=ToCompanyLocationName ? ToCompanyLocationName : 'N/A'#");
                            columns.Bound(c => c.ToCompanyLocationCountry).Title(" To Country").ClientTemplate("#=ToCompanyLocationCountry ? ToCompanyLocationCountry : 'N/A'#");
                            columns.Bound(c => c.ShipmentMethodName).Title("Shipment Method").Hidden(true);
                            columns.Bound(c => c.CustomStatus).Title(!GlobalSettings.IsWellsense? "Custom Status" : "Incoterm");
                            columns.Bound(c => c.CreatedDate).Title("Created").Hidden(true).Format(DateConstants.DateFormat);
                            columns.Bound(c => c.SentDate).Title("Shipped").Format(DateConstants.DateFormat);
                            columns.Bound(c => c.ReceivedDate).Title("Received").Format(DateConstants.DateFormat);
                            columns.Bound(c => c.EquipmentItemsCount).Title("Items Sent");
                            columns.Bound(c => c.EquipmentItemsReceivedCount).Title("Items Received");
                            columns.Bound(c => c.EquipmentNonAssetItemsCount).Title("Non-Asset Sent");
                            columns.Bound(c => c.EquipmentNonAssetItemsReceivedCount).Title("Non-Asset Received");
                        })
                        .Events(e => e
                            .DataBound("updateReceivedEquipmentShipmentGrid")
                            .ColumnReorder("saveReceivedEquipmentShipmentGrid")
                            .ColumnResize("saveReceivedEquipmentShipmentGrid")
                            .ColumnShow("saveReceivedEquipmentShipmentGrid")
                            .ColumnHide("saveReceivedEquipmentShipmentGrid")
                        )
                        .ToolBar(t => {
                            t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetReceivedEquipmentShipmentGrid", @class="bg-danger text-white"});
                            t.Excel().Text("Export");
                        }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
                        .Groupable()
                        .Filterable()
                        .Sortable()
                        .Scrollable(scrollable => scrollable.Endless(true).Height(535))
                        .ColumnMenu(c => c.Columns(true))
                        .Resizable(r => r.Columns(true))
                        .Reorderable(r => r.Columns(true))
                        .Excel(excel => excel
                            .FileName(string.Format("Centerpoint_Shipments_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                            .Filterable(true)
                            .ProxyURL(Url.Action("Export", "Logistics")))
                        .DataSource(dataSource => dataSource
                            .Ajax()
                            .PageSize(300)
                            .ServerOperation(false)
                            .Model(model => {
                                model.Id(m => m.EquipmentShipmentId);
                            })
                            .Events(e => e.Error("onReceivedError"))
                            .Read(read => read.Action("GetReceivedEquipmentShipments", "Logistics").Data("logisticsData"))
                        )
                    )
                </text>);
        })
    )
</div>
<script>
    const logisticsModel= {
        month: @DateTime.Now.Month,
        year: @DateTime.Now.Year,
        totalSentMonth: @Model.TotalSentThisMonth,
        totalReceivedMonth: @Model.TotalReceivedThisMonth,
        totalInTransitMonth:@Model.TotalInTransitThisMonth,
        totalPendingMonth: @Model.TotalPendingThisMonth,
        totalSentYear: @Model.TotalSentThisYear,
        totalReceivedYear: @Model.TotalReceivedThisYear,
        totalInTransitYear: @Model.TotalInTransitThisYear,
        totalPendingYear: @Model.TotalPendingThisYear,
        totalMonth: @Model.TotalMonth,
        totalYear: @Model.TotalYear
    }
</script>

<environment include="Development">
    <script src="~/js/views/logistics/logistics.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/logistics/logistics.min.js" asp-append-version="true"></script>
</environment>
