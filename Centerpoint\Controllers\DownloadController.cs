﻿using Centerpoint.Extensions;
using Centerpoint.Model.Configuration;
using Centerpoint.Model.ViewModels;
using Centerpoint.Service.Interfaces;
using Centerpoint.Services;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Net.Mime;

namespace Centerpoint.Controllers
{
    [Authorize]
    public class DownloadController : Controller
    {
        private readonly IDownloadService _downloadService;
        private readonly Settings _settings;
        private readonly ICurrentUserService _currentUser;

        public DownloadController(IDownloadService downloadService,
                                  IOptions<Settings> settings,
                                  ICurrentUserService currentUser)
        {
            _downloadService = downloadService;
            _settings = settings.Value;
            _currentUser = currentUser;
        }

        public ActionResult Index()
        {
            this.SetTitle("Downloads");
            return View();
        }

        #region Download Folders
        public async Task<ActionResult> GetDownloadFolders([DataSourceRequest] DataSourceRequest request, int? id)
        {
            var result = new TreeDataSourceResult();

            var downloadFolders = await _downloadService.GetAllDownloadFolders();
            result = downloadFolders.ToTreeDataSourceResult(request,
                e => e.DownloadFolderId,
                e => e.ParentDownloadFolderId,
                e => id.HasValue ? e.ParentDownloadFolderId == id : e.ParentDownloadFolderId == null,
                e => e);

            return Json(result);

        }

        public async Task<ActionResult> GetDownloadFolderFiles([DataSourceRequest] DataSourceRequest request, int downloadFolderId)
        {
            var downloadFolder = await _downloadService.GetDownloadFolderById(downloadFolderId);
            var files = _downloadService.GetDownloadFiles(downloadFolderId, downloadFolder.Path);

            return Json(files.ToDataSourceResult(request));
        }

        public async Task<ActionResult> UploadDownloadFiles(IEnumerable<IFormFile> downloadFiles, int downloadFolderId)
        {
            if (downloadFiles != null)
            {
                var downloadFolder = await _downloadService.GetDownloadFolderById(downloadFolderId);

                await _downloadService.UploadDownloadFiles(downloadFiles, downloadFolder.Path);
            }

            return Content("");
        }

        public async Task<ActionResult> DownloadFile(int id, string name)
        {
            var mimeType = FileExtensions.GetMimeTypeForFileExtension(name);
            var downloadFolder = await _downloadService.GetDownloadFolderById(id);

            var contentDisposition = new ContentDisposition
            {
                FileName = name,
                Inline = true
            };

            Response.Headers.Append("Content-Disposition", contentDisposition.ToString());

            var filePath = Path.Combine(downloadFolder.Path, name);

            return File(filePath, mimeType);
        }

        [HttpPost]
        public async Task<ActionResult> DeleteDownloadFolderFile([DataSourceRequest] DataSourceRequest request, DownloadFileModel model)
        {
            var downloadFolder = await _downloadService.GetDownloadFolderById(model.DownloadFolderId);
            _downloadService.DeleteDownloadFolderFile(downloadFolder.Path, model.Name);

            return Json(new[] { model }.ToTreeDataSourceResult(request, ModelState));
        }

        [HttpPost]
        public async Task<ActionResult> DeleteDownloadFolder([DataSourceRequest] DataSourceRequest request, DownloadFolderModel model)
        {
            await _downloadService.DeleteDownloadFolder(model.DownloadFolderId);
            return Json(new[] { model }.ToTreeDataSourceResult(request, ModelState));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateDownloadFolderParent(int downloadFolderId, int? parentDownloadFolderId)
        {
            await _downloadService.UpdateDownloadFolderParent(downloadFolderId, parentDownloadFolderId);

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<ActionResult> UpdateDownloadFolder([DataSourceRequest] DataSourceRequest request, DownloadFolderModel downloadFolderModel)
        {
            var model = await _downloadService.UpdateDownloadFolder(downloadFolderModel, _currentUser.UserId);

            return Json(new[] { model }.ToTreeDataSourceResult(request, ModelState));
        }

        public async Task<ActionResult> UpdateDownloadFolders(bool ftp = false)
        {
            await _downloadService.UpdateDownloadFolders(_currentUser.UserId, ftp);
            this.SetMessage(MessageType.Success, "Download folder structure updated");

            return RedirectToAction("Index");
        }
        #endregion        
    }
}
