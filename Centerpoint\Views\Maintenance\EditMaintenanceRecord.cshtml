﻿@model MaintenanceRecordModel
@if (Model.IsUnderMaintenance && Model.Status == MaintenanceConstant.Assigned) {
    <div class="alert alert-dismissible alert-warning">
        <button type="button" class="close" data-bs-dismiss="alert">×</button>
        <strong>Warning!</strong> This MR cannot be started as this asset item already has an on-going maintenance record.
    </div>
}

<div class="header-container-single-item-with-hr">
        @if (Model.IsClosed) {
            if (Model.IsRepair) {
                if (Model.IsRepairFailed) {
                    <h4 class="text-primary"><i class="fa fa-file-image"></i> @(Model.MaintenanceRecordId.HasValue ? Model.NotRepairedName : "Create MR")</h4>
                } else if (Model.Pass) {
                    <h4 class="text-primary"><i class="fa fa-file-image"></i> @(Model.MaintenanceRecordId.HasValue ? Model.RepairedName : "Create MR")</h4>
                } else if (Model.Fail) {
                    <h4 class="text-primary"><i class="fa fa-file-image"></i> @(Model.MaintenanceRecordId.HasValue ? Model.FailedName : "Create MR")</h4>
                }
            } else {
                if (Model.Pass) {
                    <h4 class="text-primary"><i class="fa fa-file-image"></i> @(Model.MaintenanceRecordId.HasValue ? Model.PassedName : "Create MR")</h4>
                } else if (Model.Fail) {
                    <h4 class="text-primary"><i class="fa fa-file-image"></i> @(Model.MaintenanceRecordId.HasValue ? Model.FailedName : "Create MR")</h4>
                }
            }
        } else {
            if (Model.MaintenanceRecordOnHold) {
                <h4 class="text-primary"><i class="fa fa-file-image"></i> @(Model.MaintenanceRecordId.HasValue ? Model.OnHoldName : "Create MR")</h4>
            } else if (Model.IsRepair) {
                <h4 class="text-primary"><i class="fa fa-file-image"></i> @(Model.MaintenanceRecordId.HasValue ? Model.InProgressRepair : "Create MR")</h4>
            } else if (Model.Pass) {
                <h4 class="text-primary"><i class="fa fa-file-image"></i> @(Model.MaintenanceRecordId.HasValue ? Model.PassedName : "Create MR")</h4>
            } else if (Model.Fail) {
                <h4 class="text-primary"><i class="fa fa-file-image"></i> @(Model.MaintenanceRecordId.HasValue ? Model.FailedName : "Create MR")</h4>
            } else {
                <h4 class="text-primary"><i class="fa fa-file-image"></i> @(Model.MaintenanceRecordId.HasValue ? Model.NewName : "Create MR")</h4>
            }
        }
</div>
<hr />

<div class="mt-5">
            @{Html.Kendo().TabStrip()
            .Name("EditMaintenanceStrips")
            .SelectedIndex(0)
            .HtmlAttributes(new { @data_bind="visible: isEditMaintenanceStripsVisible" })
            .Animation(false)
            .Items( tabstrip => {

            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind="html:tabStripHeaderDetails", @id="details"})
                .Selected(true)
                .Content(@<text>
                        <div class="">
                            @using (Html.BeginForm("EditMaintenanceRecord", "Maintenance", FormMethod.Post, new { @id = "editMaintenanceRecord" })) {
                                @Html.ValidationSummary(false)
                            if (!Model.MaintenanceRecordId.HasValue) {
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>Equipment Item <span class="text-danger">*</span></label>
                                                <br />
                                                @(Html.Kendo().DropDownListFor(m => m.EquipmentItemId)
                                                .Filter("contains")
                                                .OptionLabel("Select Equipment Item")
                                                .Filter("contains")
                                                .DataTextField("EquipmentItemName")
                                                .DataValueField("EquipmentItemId")
                                                .Events(x=>x.Change("cascadeDropdownFilterHelper"))
                                                .DataSource(d => d.Read("GetAllOpertionalEquipmentItems", "Lookup"))
                                                .HtmlAttributes(new { @style = "width:100%", @data_bind = "value:equipmentItemId", required = "required",@data_cascade_to="MaintenanceBlueprintId"})
                                                )
                                            </div>
                                        </div>
                                    </div>
                            }
                            if (Model.MaintenanceRecordId.HasValue) {
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>Equipment Item</label>
                                                <br />
                                                <a href="@Url.Action("EditEquipmentItem", "Assets", new { @id = Model.EquipmentItemId, @maintenanceRecordId = Model.MaintenanceRecordId })">@Model.EquipmentItemName</a>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label> Maintenance Blueprint </label>
                                                <br />
                                                <a href="@Url.Action("EditMaintenanceBlueprint", "Admin", new { @id = Model.MaintenanceBlueprintId, @maintenanceRecordId = Model.MaintenanceRecordId })"> @Model.MaintenanceBlueprintName </a>
                                            </div>
                                        </div>
                                    </div>
                            }
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            @if ((Html.IsGlobalAdmin() || Html.IsMaintenanceAdmin() || Html.IsMaintenanceEngineer())) {
                                                <label>Priority</label>
                                                <br />
                                                @(Html.Kendo()
                                                .DropDownListFor(c => c.Priority)
                                                .DataValueField("Key")
                                                .DataTextField("Value")
                                                .Filter("contains")
                                                .HtmlAttributes(new { @style = "width:100%" })
                                                .BindTo(Centerpoint.Common.Constants.MaintenancePriorityConstant.ValuesAndDescriptions.ToList()))
                                        } else {
                                                <label>Priority</label>
                                                <br />
                                                @(Html.Kendo()
                                                .DropDownListFor(c => c.Priority)
                                                .DataValueField("Key")
                                                .DataTextField("Value")
                                                .Filter("contains")
                                                .HtmlAttributes(new { @style = "width:100%" })
                                                .BindTo(Centerpoint.Common.Constants.MaintenancePriorityConstant.ValuesAndDescriptions.ToList()))
                                        }

                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            @if (Model.Status != MaintenanceConstant.Closed) {
                                                <label>Associated SIF</label>
                                                <br />
                                                @(Html.Kendo().DropDownListFor(m => m.ServiceImprovementId)
                                                .Filter("contains")
                                                .OptionLabel("Select SIF")
                                                .DataTextField("Text")
                                                .DataValueField("Value")
                                                .DataSource(d => d.Read("GetAllServiceImprovementsByStatus", "Lookup"))
                                                .HtmlAttributes(new { @style = "width:100%"}))
                                        } else if (Model.Status == MaintenanceConstant.Closed && (Html.IsGlobalAdmin() || Html.IsMaintenanceAdmin() || Html.IsMaintenanceEngineer())) {
                                                <label>Associated SIF</label>
                                                <br />
                                                @(Html.Kendo().DropDownListFor(m => m.ServiceImprovementId)
                                                .Filter("contains")
                                                .OptionLabel("Select SIF")
                                                .DataTextField("Text")
                                                .DataValueField("Value")
                                                .DataSource(d => d.Read("GetAllServiceImprovementsByStatus", "Lookup"))
                                                .HtmlAttributes(new { @style = "width:100%"}))
                                        } else {
                                                <label>Associated SIF</label>
                                                <br />
                                                @(Html.Kendo().DropDownListFor(m => m.ServiceImprovementId)
                                                .Filter("contains")
                                                    .OptionLabel("Select SIF")
                                                    .DataTextField("Text")
                                                    .DataValueField("Value")
                                                    .DataSource(d => d.Read("GetAllServiceImprovementsByStatus", "Lookup"))
                                                    .HtmlAttributes(new { @style = "width:100%", @readonly = "readonly" }))
                                        }
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            @if (Model.RunId.HasValue) {
                                                <label>Raised By</label>
                                                <br />
                                                @(Html.Kendo().TextBoxFor(m => m.RaisedByUser)
                                                .HtmlAttributes(new { @style = "width:100%", @readonly = "readonly" }))
                                        } else if (!Model.RunId.HasValue && !Model.UserId.HasValue) {
                                                <label> Raised By </label>
                                                <br />
                                                @(Html.Kendo().TextBoxFor(m => m.RaisedByPoints)
                                                .HtmlAttributes(new { @style = "width:100%", @readonly = "readonly" }))
                                        } else if (Html.IsGlobalAdmin() || Html.IsMaintenanceAdmin()) {
                                                <label> Raised By </label>
                                                <br />
                                                @(Html.Kendo().DropDownListFor(m => m.UserId)
                                            .DataTextField("Text")
                                            .DataValueField("Value")
                                            .Filter("contains")
                                            .DataSource(d => d.Read("GetUsers", "Lookup"))
                                            .HtmlAttributes(new { @style = "width:100%" }))
                                        } else {
                                                <label> Raised By </label>
                                                <br />
                                                @(Html.Kendo().DropDownListFor(m => m.UserId)
                                            .DataTextField("Text")
                                            .DataValueField("Value")
                                            .Filter("contains")
                                            .DataSource(d => d.Read("GetUsers", "Lookup"))
                                            .HtmlAttributes(new { @style = "width:100%", @readonly = "readonly" }))
                                        }
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    @if (!Model.MaintenanceRecordId.HasValue) {
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>Maintenance Blueprint <span class="text-danger">*</span></label>
                                                <br />
                                                @(Html.Kendo().DropDownListFor(m => m.MaintenanceBlueprintId)
                                            .OptionLabel("Select Maintenance Blueprint")
                                            .DataTextField("Name")
                                            .DataValueField("MaintenanceBlueprintId")
                                            .Filter("contains")
                                            .DataSource(source => {
                                                source.Read(read => {
                                                    read.Action("GetAllMaintenanceBlueprintsByCategoryId", "Lookup").Data("filterMaintenanceBlueprints");
                                                });
                                            })
                                            .HtmlAttributes(new { @style = "width:100%", @required = "required" }))
                                            </div>
                                        </div>
                                }
                                    @if (Model.MaintenanceRecordId.HasValue) {
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>Created Date</label>
                                                <br />
                                                @(Html.Kendo().DateTimePickerFor(m => m.Created)
                                                    .Enable(Model.Status != MaintenanceConstant.Closed && (Html.IsGlobalAdmin() || Html.IsMaintenanceAdmin() || Html.IsMaintenanceEngineer()))
                                                    .HtmlAttributes(new { @style = "width:100%", @class = "utcTimePicker" }))
                                            </div>
                                        </div>
                                    }
                                    <div class="col-md-3">
                                    @if (Model.MaintenanceRecordId.HasValue && (Html.IsGlobalAdmin() || Html.IsMaintenanceAdmin() || Html.IsMaintenanceEngineer()) && (Model.Status != MaintenanceConstant.UnAssigned && Model.Status != MaintenanceConstant.Assigned && Model.Status != MaintenanceConstant.Closed)) {
                                        <div class="form-group">
                                            <label>Start Date</label>
                                            <br />
                                            @(Html.Kendo().DateTimePickerFor(m => m.StartDate).Min(Model.Created).HtmlAttributes(new { @style = "width:100%", @class = "utcTimePicker" }))
                                        </div>
                                    } else if (Model.MaintenanceRecordId.HasValue && (Model.Status == MaintenanceConstant.InProgress || Model.Status == MaintenanceConstant.Closed || Model.Status == MaintenanceConstant.AwaitingApproval || Model.Status == MaintenanceConstant.OnHold) || Model.Status == MaintenanceConstant.OnGoing) {
                                        <div class="form-group">
                                            <label>Start Date</label>
                                            <br />
                                            @(Html.Kendo().DateTimePickerFor(m => m.StartDate).Enable(false).HtmlAttributes(new { @style = "width:100%", @class = "utcTimePicker" }))
                                        </div>
                                    }
                                    </div>
                                    @if (Model.MaintenanceRecordId.HasValue && Model.StartDate.HasValue && Model.Status != MaintenanceConstant.Closed && (Html.IsGlobalAdmin() || Html.IsMaintenanceAdmin() || Html.IsMaintenanceEngineer())) {
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>Completed Date</label>
                                                <br />
                                                @(Html.Kendo().DateTimePickerFor(m => m.CompletedDate).HtmlAttributes(new { @style = "width:100%; font-size: 14px;", @class = "utcTimePicker" }))
                                            </div>
                                        </div>
                                } else if (Model.MaintenanceRecordId.HasValue && Model.StartDate.HasValue) {
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>Completed Date</label>
                                                <br />
                                                @(Html.Kendo().DateTimePickerFor(m => m.CompletedDate).Enable(false).HtmlAttributes(new { @style = "width:100%; font-size: 14px;", @class = "utcTimePicker" }))
                                            </div>
                                        </div>
                                }
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        @if (Model.EngineerUserId.HasValue && Model.MaintenanceRecordId.HasValue) {
                                            <div class="form-group">
                                                <label>Assigned Engineer</label>
                                                <br />
                                                @(Html.Kendo().TextBoxFor(m => m.EngineerUserName).HtmlAttributes(new { @style = "width:100%; font-size: 14px;", @readonly = "readonly" }))
                                            </div>
                                    } else {
                                            @Html.HiddenFor(m => m.EngineerUserId)
                                            @Html.HiddenFor(m => m.EngineerUserName)
                                    }
                                    </div>
                                    @if (Model.MaintenanceRecordId.HasValue && Model.TotalMaintenanceTime.HasValue && Model.TotalMaintenanceTime.Value.TotalMinutes >= 1) {
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>Time On-Going</label>
                                                <br />
                                                @(Html.Kendo().TextBoxFor(m => m.MaintenanceTimeString).HtmlAttributes(new { @style = "width:100%; font-size: 14px;", @readonly = "readonly" }))
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>Time On Hold</label>
                                                <br />
                                                @(Html.Kendo().TextBoxFor(m => m.OnHoldHoursString).HtmlAttributes(new { @style = "width:100%; font-size: 14px;", @readonly = "readonly" }))
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>Total Time</label>
                                                <br />
                                                @(Html.Kendo().TextBoxFor(m => m.TotalMaintenanceTimeString).HtmlAttributes(new { @style = "width:100%; font-size: 14px;", @readonly = "readonly" }))
                                            </div>
                                        </div>
                                }
                                </div>
                            if ((Html.IsGlobalAdmin() || Html.IsMaintenanceAdmin() || Html.IsMaintenanceEngineer()) && Model.Status != MaintenanceConstant.Closed) {
                                    <div class="form-group">
                                        <label>Comments</label>
                                        <br />
                                        @Html.TextAreaFor(p => p.Comment, new { @class = "form-control", @rows = "5"})
                                    </div>
                            } else {
                                    <div class="form-group">
                                        <label>Comments</label>
                                        <br />
                                        @Html.TextAreaFor(p => p.Comment, new { @class = "form-control", @rows = "5", @readonly = "readonly"})
                                    </div>
                            }
                            if (!string.IsNullOrEmpty(Model.RejectReason)) {
                                    <hr />
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label>Reject Reason</label>
                                                @Html.TextAreaFor(m => m.RejectReason, new { @rows = 5, @style = "width:100%", @class = "form-control", @disabled = "disabled" })
                                            </div>
                                        </div>
                                    </div>
                            }
                                <br />
                                <br />
                                <div class="d-flex actionsContainer">
                                    @if ((!Model.MaintenanceRecordId.HasValue || Html.IsGlobalAdmin() || Html.IsMaintenanceAdmin() || Html.IsMaintenanceEngineer())) {
                                        <button type="button" class="btn btn-sm btn-primary" onclick="validateMaintenanceRecordForm(event)">Save MR Details</button>
                                }
                                    @if (Model.MaintenanceRecordId.HasValue && Model.IsEngineerUser && !Html.IsMaintenanceAdmin() && !Html.IsMaintenanceEngineer() & !Html.IsGlobalAdmin() && Model.Status != MaintenanceConstant.Closed) {
                                        <button type="button" class="btn btn-sm btn-primary" onclick="validateMaintenanceRecordForm(event)">Save MR Details</button>
                                }
                                    @if (Model.Status == MaintenanceConstant.UnAssigned && (Html.IsGlobalAdmin() || Html.IsMaintenanceAdmin())) {
                                        <button type="button" class="btn btn-primary btn-sm " data-bind="click:showAssignMaintenancRecordWindow"><i class="fa fa-check"></i>Assign Engineer</button>
                                }
                                    @if ((Model.Status == MaintenanceConstant.UnAssigned 
                                    || Model.Status == MaintenanceConstant.Assigned 
                                    || Model.MaintenanceRecordOnHold) 
                                    && !Model.IsEngineerUser 
                                    && (Html.IsAbleToSelfAssign())) {
                                        <button type="button" class="btn btn-primary btn-sm " data-bind="click:selfAssignMRClick"><i class="fa fa-check"></i>Self-Assign MR</button>
                                }
                                    @if (Model.MaintenanceRecordId.HasValue && Model.EngineerUserId.HasValue && Model.Status != MaintenanceConstant.UnAssigned && Model.Status != MaintenanceConstant.Closed && (Model.IsEngineerUser || Html.IsMaintenanceAdmin() || Html.IsGlobalAdmin()))
                                {
                                        <button type="button" class="btn btn-primary btn-sm " data-bind="click:showReAssignMaintenancRecordWindow"><i class="fa fa-check"></i>Re-Assign Engineer</button>
                                }
                                    @if ((Html.IsGlobalAdmin() || Html.IsMaintenanceAdmin() || Html.IsMaintenanceEngineer()) && Model.MaintenanceRecordId.HasValue && (Model.Status == MaintenanceConstant.UnAssigned || Model.Status == MaintenanceConstant.Assigned || Model.Status == MaintenanceConstant.Closed)) {
                                        <button type="button" class="btn btn-danger btn-sm " data-bind="click:deleteMaintenanceRecord"><i class="fa fa-ban"></i>Delete MR</button>
                                }
                                    @if (Model.MaintenanceRecordId.HasValue && !Model.Approved && Model.MaintenanceRecordPassAndFail && Model.Status != MaintenanceConstant.Closed && Model.Status != MaintenanceConstant.UnAssigned && Model.StartDate.HasValue && (Html.IsGlobalAdmin() || Html.IsMaintenanceEngineer() || Html.IsMaintenanceAdmin() || Model.IsEngineerUser || Model.IsRaisedByUser)) {
                                        <button type="button" class="btn btn-info btn-sm " data-bind="click:completeMaintenancRecordClick"><i class="fa fa-close"></i>Close MR</button>
                                }
                                    @if (Model.Approved && Model.MaintenanceRecordPassAndFail && Model.Status != MaintenanceConstant.AwaitingApproval && Model.Status != MaintenanceConstant.Closed && Model.Status != MaintenanceConstant.UnAssigned && Model.Status != MaintenanceConstant.OnGoing && Model.Status != MaintenanceConstant.Assigned && (Html.IsGlobalAdmin() || Html.IsMaintenanceEngineer() || Html.IsMaintenanceAdmin() || Model.IsEngineerUser || Model.IsRaisedByUser)) {
                                        <button type="button" class="btn btn-info btn-sm " data-bind="click:closeMaintenancRecordClick"><i class="fa fa-close"></i>Close MR</button>
                                }
                                    @if (!Model.MaintenanceRecordOnHold && Model.Approved && Model.Status == MaintenanceConstant.AwaitingApproval && (Html.IsMaintenanceAdmin() || Html.IsGlobalAdmin())) {
                                        <button type="button" class="btn btn-success btn-sm " data-bind="click:acceptMaintenancRecordClick"><i class="fa fa-thumbs-up"></i>Accept MR</button>
                                }
                                    @if (!Model.MaintenanceRecordOnHold && Model.Approved && Model.Status == MaintenanceConstant.AwaitingApproval && (Html.IsMaintenanceAdmin() || Html.IsGlobalAdmin())) {
                                        <button type="button" class="btn btn-warning btn-sm " data-bind="click:showRejectWindow"><i class="fa fa-thumbs-down"></i>Reject & Restart MR</button>
                                }
                                    @if (Model.Status == MaintenanceConstant.Assigned && (Html.IsGlobalAdmin() || Html.IsMaintenanceEngineer() || Html.IsMaintenanceAdmin() || Model.IsEngineerUser)) {
                                        <button type="button" class="btn btn-primary btn-sm " data-bind="click:startMaintenancRecordClick"><i class="fa fa-thumbs-up"></i>Start MR</button>
                                }
                                    @if (Model.Status == MaintenanceConstant.InProgress && Html.IsGlobalAdmin() && Model.StartDate.HasValue) {
                                        <button type="button" class="btn btn-warning btn-sm " data-bind="click:revertStartMaintenancRecordClick"><i class="fa fa-recycle"></i>Undo Start</button>
                                }
                                    @if ((Model.Status == MaintenanceConstant.Closed) && (Model.IsEngineerUser || Html.IsGlobalAdmin() || Html.IsMaintenanceAdmin() || Model.IsRaisedByUser || Html.IsMaintenanceEngineer())) {
                                        <button type="button" class="btn btn-primary btn-sm " data-bind="click:reOpenMaintenancRecordClick"><i class="fa fa-thumbs-up"></i>Re-Open MR</button>
                                }
                                </div>
                                @Html.HiddenFor(m => m.Complexity)
                                @Html.HiddenFor(m => m.PassedName)
                                @Html.HiddenFor(m => m.FailedName)
                                @Html.HiddenFor(m => m.Status)
                                @Html.HiddenFor(m => m.MaintenanceRecordId)
                                @Html.HiddenFor(m => m.EquipmentItemId)
                                @Html.HiddenFor(m => m.MaintenanceBlueprintId)
                                @Html.HiddenFor(m => m.Created)
                                @Html.HiddenFor(m => m.Modified)
                                @Html.HiddenFor(m => m.StartDate)
                                @Html.HiddenFor(m => m.CompletedDate)
                                @Html.HiddenFor(m => m.RejectReason)
                                @Html.HiddenFor(m => m.NewName)
                                @Html.HiddenFor(m => m.NewPassedName)
                                @Html.HiddenFor(m => m.NewFailedName)
                                @Html.HiddenFor(m => m.NewRepairedName)
                                @Html.HiddenFor(m => m.NewRepairFailedName)
                                @Html.HiddenFor(m => m.PassedName)
                                @Html.HiddenFor(m => m.FailedName)
                                @Html.HiddenFor(m => m.OnHoldName)
                                @Html.HiddenFor(m => m.MaintenanceTime)
                                @Html.HiddenFor(m => m.Priority)
                                @Html.HiddenFor(m => m.UserId)
                                @Html.HiddenFor(m => m.EngineerUserId)
                                @Html.HiddenFor(m => m.EngineerUserName)
                                @Html.HiddenFor(m => m.ServiceImprovementId)
                                @Html.HiddenFor(m => m.RunId)
                                @Html.HiddenFor(m => m.Number)
                                @Html.HiddenFor(m => m.MaintenanceBlueprintName)
                                @Html.HiddenFor(m => m.EquipmentItemName)
                                @Html.HiddenFor(m => m.OnHold)
                                @Html.HiddenFor(m => m.OnHoldHours)
                                @Html.HiddenFor(m => m.OnHoldHoursString)
                                @Html.HiddenFor(m => m.OnHoldTime)
                                @Html.HiddenFor(m => m.ClosedDate)
                                @Html.HiddenFor(m => m.ClosedBy)
                        }
                        </div>
                    </text>);


    if (Model.MaintenanceRecordId.HasValue && Model.Status != MaintenanceConstant.UnAssigned && Model.HasMaintenanceRecordSteps && Model.StartDate.HasValue) {
        tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind="html:stepsTabStripTitle", @id="steps"})
                .Content(@<text>
                        <div id="steps">
                            <div data-template="maintenanceRecordStepTemplate" data-bind="source:maintenanceSteps"></div>
                        </div>
                    </text>);
    }

    if (Model.MaintenanceRecordId.HasValue && Model.Status != MaintenanceConstant.UnAssigned) {
               tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind="html:mrAttachmentsTabStripTitle", @id="attachments"})
                .Content(@<text>

                        <div>
                            <br />
                            @if (Model.Status != MaintenanceConstant.Closed) {
                                <p>Click the link below to attach documents to this record</p>
                                @(Html.Kendo().Upload()
                                .Name("maintenanceRecordAttachmentDocuments")
                                .Messages(m => m.Select("Attach MR Documents"))
                                .Multiple(true)
                                .Events(e => e.Success("onMaintenanceRecordDocumentAttached").Complete("onMaintenanceRecordDocumentComplete").Upload("onMaintenanceRecordDocumentUpload"))
                                .HtmlAttributes(new { @style = "width:300px" })
                                .Async(async => async.Save("AttachMaintenanceRecordDocuments", "Maintenance", new { @mId = Model.MaintenanceRecordId }).Batch(true)))
                        }
                            <br />
                            @(Html.Kendo().Grid<DocumentModel>()
                            .Name("maintenanceRecordDocumentsGrid")
                            .Columns(c => {
                                c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                                c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
                                c.Bound(p => p.UserName).Title("Created By");
                                c.Command(command => { 
                                    command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
                                }).Width(150);
                            })
                            .Events(e => e.DataBound("updateMaintenanceRecordDocumentGrid"))
                            .Sortable()
                            .Resizable(r => r.Columns(true))
                            .Filterable()
                            .Groupable()
                            .Editable(e => e.Mode(GridEditMode.InLine))
                            .Scrollable(s => s.Height(300))
                            .DataSource(dataSource => dataSource
                                .Ajax()
                                .ServerOperation(false)
                                .Model(model => model.Id(p => p.DocumentId))
                                .Read(read => read.Action("GetMaintenanceRecordDocuments", "Maintenance", new { @maintenanceRecordId = Model.MaintenanceRecordId }))
                                .Destroy(destroy => destroy.Action("DeleteMaintenanceRecordDocument", "Maintenance", new { @mId = Model.MaintenanceRecordId }))))
                        </div>

                    </text>);
    }


    if (Model.MaintenanceRecordId.HasValue && Model.Status != MaintenanceConstant.UnAssigned && Model.HasMaintenanceBlueprintAttachments) {
        tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind="html:blueprintAttachmentsTabStripTitle"})
                .Content(@<text>                    
                            <div>
                                <br />
                                @(Html.Kendo().Grid<DocumentModel>()
                            .Name("maintenanceBlueprintDocumentsGrid")
                            .Columns(c => {
                                c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document/Index/#=DocumentId#'>#=FileName#</a>");
                                c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
                                c.Bound(p => p.UserName).Title("Created By");
                            })
                            .Events(e => e.DataBound("updateMaintenanceBlueprintDocumentGrid"))
                            .Sortable()
                            .Resizable(r => r.Columns(true))
                            .Filterable()
                            .Groupable()
                            .Editable(e => e.Mode(GridEditMode.InLine))
                            .Scrollable(s => s.Height(300))
                            .DataSource(dataSource => dataSource
                                .Ajax()
                                .ServerOperation(false)
                                .Model(model => model.Id(p => p.DocumentId))
                                .Read(read => read.Action("GetMaintenanceBlueprintDocuments", "Admin", new { @maintenanceBlueprintId = Model.MaintenanceBlueprintId }))))
                            </div>
                    </text>);
    }


    if (Model.MaintenanceRecordId.HasValue) {
               tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind="html:mrLogTabStripTitle"})
                .Content(@<text>
                        <div>
                            <br />
                            <p>The following table is a log of all changes made to this MR.</p>
                            <hr />
                            @(Html.Kendo().Grid<MaintenanceRecordLogModel>()
                            .Name("maintenanceRecordLogGrid")
                            .Columns(columns => {
                                columns.Bound(c => c.Log).ClientTemplate("#=getHtmlNewLinesString(Log)#");
                                columns.Bound(c => c.Details).Width(250).Encoded(false);
                                columns.Bound(c => c.UserName).Width(125);
                                columns.Bound(c => c.Date).Format(DateConstants.DateTimeFormat).Title("Date").Width(150);
                            })
                                .Events(e => e.DataBound("updateMaintenanceRecordLogs"))
                                .Sortable()
                                .Groupable()
                                .Reorderable(reorder => reorder.Columns(true))
                                .Resizable(resize => resize.Columns(true))
                                .Scrollable(s => s.Height(400))
                                .DataSource(dataSource => dataSource
                                .Ajax()
                                .ServerOperation(false)
                                .Read(read => read.Action("GetMaintenanceRecordLogByMaintenanceRecordId", "Maintenance").Data("maintenanceRecordData"))))
                        </div>
                    </text>);
        }
    }).Render();
    }
</div>





@(Html.Kendo().Window().Name("rejectWindow")
    .Title("Reason for Rejection")
    .Content(@<text><partial name="RejectMaintenanceRecordReason"/></text>)
    .Width(600)
    .Modal(true)
    .Visible(false))
@(Html.Kendo().Window().Name("assignMaintenancRecordWindow")
    .Title("Assign Engineer")
    .Content(@<text><partial name="AssignEngineer"/></text>)
    .Width(400)
    .Modal(true)
    .Visible(false))

@(Html.Kendo().Window().Name("reAssignMaintenancRecordWindow")
    .Title("Re-Assign Engineer")
    .Content(@<text><partial name="ReAssignEngineer"/></text>)
    .Width(400)
    .Modal(true)
    .Visible(false))

@(Html.Kendo().Window().Name("repairWindow")
    .Title("Step Failed")
    .Content(@<text><partial name="MaintenanceRecordStepRepairWindow"/></text>)
    .Width(400)
    .Modal(true)
    .Visible(false))


    <script type="text/x-kendo-tmpl" id="mrLogDetailTemplate">
        <div style="margin:10px">
            #= kendo.toString(replaceString(Details))#
        </div>
    </script>
    <script type="text/javascript">
        function replaceString(value) {
            return value.replace(/(\n)+/g, '<br />');
        }
    </script>
    <script type="text/x-kendo-tmpl" id="maintenanceRecordStepTemplate">
        <div class="card mb-2" id="step#=MaintenanceRecordStepId#">
            #if(IsPass){#
            <div class="card-header" style="background-color:\\#43ac6a !important; color:\\#FFFFFF !important; border-color:\\#43ac6a !important; box-shadow: inset 0 0 10px 3px \\#43ac6a !important">
                <div class="panel-title">
                    <span>#=NewName#</span>
                </div>
            </div>
            #} else if(IsFail){#
            <div class="card-header" style="background-color:\\#FF0000 !important; color:\\#FFFFFF !important; border-color:\\#FF0000 !important; box-shadow: inset 0 0 10px 3px \\#FF0000 !important">
                <div class="panel-title">
                    <span>#=NewName#</span>
                </div>
            </div>
            #} else if(IsOnHold){#
            <div class="card-header" style="background-color:\\#F7A54A !important; color:\\#FFFFFF !important; border-color:\\#F7A54A !important; box-shadow: inset 0 0 10px 3px \\#F7A54A !important">
                <div class="panel-title">
                    <span>#=NewName#</span>
                </div>
            </div>
            #} else if(IsRepaired){#
            <div class="card-header" style="background-color:\\#43ac6a !important; color:\\#FFFFFF !important; border-color:\\#43ac6a !important; box-shadow: inset 0 0 10px 3px \\#43ac6a !important">
                <div class="panel-title">
                    <span>#=NewName#</span>
                </div>
            </div>
            #} else if(IsNotRepaired){#
            <div class="card-header" style="background-color:\\#FF0000 !important; color:\\#FFFFFF !important; border-color:\\#FF0000 !important; box-shadow: inset 0 0 10px 3px \\#FF0000 !important">
                <div class="panel-title">
                    <span>#=NewName#</span>
                </div>
            </div>
            #}else if(IsRepair){#
            <div class="card-header">
                <div class="panel-title">
                    <span title="Repair Step">#=StepName# <i class="fa fa-warning"></i></span>
                </div>
            </div>
            #}else {#
            <div class="card-header">
                <div class="panel-title">
                    <span>#=StepName#</span>
                </div>
            </div>
            #}#
            <div class="card-body">
                #if(IsTextBox){#
                <div class="row">
                    <div class="col-md-11">
                        <p>#=Description#</p>
                    </div>
                    <div class="col-md-12">
                        <label style="color:\\#6eb6b4">Result</label>
                        @(Html.TextArea("Result", new { @class = "form-control", @style = "width:100%", @rows = "3", @data_bind = "value:Result" }))
                        <br />
                    </div>
                    <div class="col-md-12">
                        <label style="color:\\#6eb6b4">Note</label>
                        @(Html.TextArea("Note", new { @class = "form-control", @style = "width:100%", @rows = "3", @data_bind = "value:Note" }))
                        <br />
                    </div>
                </div>
                #}
                else if(IsCheckbox){#
                <div class="row">
                    <div class="col-md-11">
                        <p>#=Description#</p>
                    </div>
                    <div class="col-md-12">
                        #if(TaskList){#
                        <br />
                        #var tasksSplit = Tasks.split('|~');#
                        <label style="color:\\#6eb6b4">Tasks (#=tasksSplit.length#)</label>
                        #for(var i = 0;i< tasksSplit.length;i++){#
                        <div class="checkbox">
                            <label>
                                <input name="TasksCompleted" type="checkbox" data-bind="checked:TasksCompleted" value="#=tasksSplit[i]#"> #=tasksSplit[i]#
                            </label>
                        </div>
                        #}#
                        <br />
                        #}#
                    </div>
                    <div class="col-md-12">
                        <label style="color:\\#6eb6b4">Note</label>
                        @(Html.TextArea("Note", new { @class = "form-control", @style = "width:100%", @rows = "3", @data_bind = "value:Note" }))
                        <br />
                    </div>
                </div>
                #} else if(IsAttachment){#
                <div class="row">
                    <div class="col-md-11">
                        <p>#=Description#</p>
                    </div>
                    <div class="col-md-12">
                        <label style="color:\\#6eb6b4">Note</label>
                        @(Html.TextArea("Note", new { @class = "form-control", @style = "width:100%", @rows = "3", @data_bind = "value:Note" }))
                        <br />
                    </div>
                    @if (Model.IsEngineerUser || Html.IsGlobalAdmin() || Html.IsMaintenanceAdmin()) {
                        <text>
                            <div class="col-md-6">
                            <label style="color:\\#6eb6b4">Upload files</label>
                                <input name="files"
                                       type="file"
                                       data-role="upload"
                                       data-multiple="true"
                                       data-bind="events:{success:onAttachmentSuccess}"
                                       onchange="validateFileName(this)"
                                       data-async="{ saveUrl: '/Maintenance/AttachMaintenanceRecordStepDocument/#=MaintenanceRecordStepId#', autoUpload: true }" />
                                <br />
                                # if (!HasDocument) { #
                                    <h4 style="color:\\#F8AC59"><strong>Warning!</strong> This step requires a minimum of one file to be attached before proceeding.</h4>
                                # }
                                else if (HasDocument) { #
                                <label class="mb-2 d-block" style="color:\\#6eb6b4">Attachments</label>
                                    # for (var i = 0; i != Documents.length; i++) { #
                                        <div style="margin-bottom:10px">
                                            <a href="/Document/Index?id=#=Documents[i].Key#" class="btn btn-primary btn-sm" style="margin-right:10px" target="_blank">
                                                <i class='fa fa-file-text'></i>#=Documents[i].Value ? Documents[i].Value : ''#
                                            </a>
                                            #if(!IsMaintenanceRecordCompleted) {#
                                                <a href="\\#" class="btn btn-danger btn-sm" onclick="deleteAttachment(#=MaintenanceRecordStepId#, #=Documents[i].Key#)"><i class="fa fa-ban"></i>Delete Attachment</a>
                                            #}#
                                        </div>
                                    # } #
                                # } #
                                <br />
                            </div>
                        </text>
                        } 
                        else
                        {
                        <div class="col-md-6">
                            #if(HasDocument){#
                                # for (var i = 0; i != Documents.length; i++) { #
                                    <div style="margin-bottom:10px">
                                        <a href="/Document/Index?id=#=Documents[i].Key#" class="btn btn-primary btn-sm" style="margin-right:10px" target="_blank">
                                            <i class='fa fa-file-text'></i>#=Documents[i].Value ? Documents[i].Value : ''#
                                        </a>
                                    </div>
                                # } #
                            #}#
                            <br />
                        </div>
                    }
                </div>
                #}#
                <br />
                <br />
                @if (Model.IsEngineerUser || Html.IsGlobalAdmin() || Html.IsMaintenanceAdmin() || Html.IsMaintenanceEngineer()) {
                    <text>
                        <div class="d-flex actionsContainer justify-content-end">
                            #if(!IsMaintenanceRecordCompleted){#
                            <a class="btn btn-sm btn-primary" onclick="saveMaintenanceRecordClick(#=MaintenanceRecordStepId#)">Save</a>
                            #}#
                            #if(!IsOnHold && !IsMaintenanceRecordCompleted && !IsAttachment){#
                            <a class="btn btn-sm btn-warning" onclick="holdMaintenanceRecordClick(#=MaintenanceRecordStepId#)">On Hold</a>
                            #}#
                            #if(IsOnHold){#
                            <a class="btn btn-sm btn-warning" onclick="removeHoldMaintenanceRecordClick(#=MaintenanceRecordStepId#)">Remove OnHold</a>
                            #}#
                            #if(!IsFail && !IsMaintenanceRecordCompleted && !IsAttachment && !IsRepair){#
                            <a class="btn btn-sm btn-danger" onclick="showRepairWindow(#=MaintenanceRecordStepId#)">Fail</a>
                            #}#
                            #if(IsRepair && !IsFail && !IsMaintenanceRecordCompleted){#
                            <a class="btn btn-sm btn-danger" onclick="failMaintenanceRecordClick(#=MaintenanceRecordStepId#)">Fail</a>
                            #}#
                            #if(!IsPass && !IsMaintenanceRecordCompleted && !IsAttachment){#
                            <a class="btn btn-sm btn-success" onclick="passMaintenanceRecordClick(#=MaintenanceRecordStepId#)">Pass</a>
                            #}#
                            #if(!IsOnHold && !IsMaintenanceRecordCompleted && IsAttachment){#
                            <a class="btn btn-sm btn-warning" onclick="holdMaintenanceRecordClick(#=MaintenanceRecordStepId#)">On Hold</a>
                            #}#
                            #if(!IsFail && !IsMaintenanceRecordCompleted && IsAttachment && HasDocument && !IsRepair){#
                            <a class="btn btn-sm btn-danger" onclick="showRepairWindow(#=MaintenanceRecordStepId#)">Fail</a>
                            #}#
                            #if(!IsPass && !IsMaintenanceRecordCompleted && IsAttachment && HasDocument){#
                            <a class="btn btn-sm btn-success" onclick="passMaintenanceRecordClick(#=MaintenanceRecordStepId#)">Pass</a>
                            #}#
                        </div>
                    </text>
                }

                <br />
            </div>
        </div>
    </script>

    <script>
        function validateMaintenanceRecordForm(e){
            e.preventDefault();
            if($('#editMaintenanceRecord').kendoValidator().data('kendoValidator').validate()){
                $('#editMaintenanceRecord').submit();
            } else {
                scrolToFirstError();  
            }
        }
        function validateFileName(input) {
            var fileName = input.value.split('\\').pop();
            var pattern = new RegExp(/[~`!#$%\^&*+=\[\]\\';,/{}|\\":<>\?]/);
            if (pattern.test(fileName)) {
                alert('Your filename contains illegal characters.');
                input.value = '';
            }
        }

        const editMaintenanceRecordModel = {
            maintenanceRecordId: "@Model.MaintenanceRecordId",
            maintenanceRecordIdHasValue: "@Model.MaintenanceRecordId.HasValue", 
            MaintenanceConstantOnGoing:  "@MaintenanceConstant.OnGoing",
            mRId: "@(Model.MaintenanceRecordId.HasValue ? Model.MaintenanceRecordId.Value.ToString() : "")",
            maintenanceStepResultConstantOnHold: "@MaintenanceStepResultConstant.OnHold",
            maintenanceStepResultConstantOnHoldRemoved: "@MaintenanceStepResultConstant.OnHoldRemoved",
            maintenanceStepResultConstantFail: "@MaintenanceStepResultConstant.Fail",
            maintenanceStepResultConstantPass: "@MaintenanceStepResultConstant.Pass",
            engineerUserId: "@Model.EngineerUserId",
            equipmentItemId: "@Model.EquipmentItemId",
            maintenanceConstantAwaitingApproval: "@MaintenanceConstant.AwaitingApproval",
            maintenanceConstantClosed: "@MaintenanceConstant.Closed",
            maintenanceConstantInProgress: "@MaintenanceConstant.InProgress",
        }

    $('#editMaintenanceRecord').submit(function (e) {
            e.preventDefault();
            var formData = $(this).serializeArray();
            setCorrectDateTime(formData);

            $.ajax({
                type: 'POST',
                url: '/Maintenance/EditMaintenanceRecord',
                data: formData,
                success: function (result) {
                    $("<div id='dialog'></div>").kendoDialog({
                        closable: false,
                        title: result.mrNumber,
                        width: 500,
                        buttonLayout: "normal",
                        content: `\'${result.mrNumber}\' MR has been successfully updated`,
                        actions: [
                            {
                                text: "OK",
                                action: function () {
                                window.location.href = '/Maintenance/EditMaintenanceRecord?id=' + result.mrId;
                                },
                                cssClass: 'btn-primary'
                            }]
                    }).data("kendoDialog").open().center()
                },
                error: function (xhr, textStatus, errorThrown) {
                    showError(xhr.responseJSON.message);
            }
            });
        });
    </script>

    <environment include="Development">
        <script src="~/js/views/maintenance/editMaintenanceRecord.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script src="~/js/views/maintenance/editMaintenanceRecord.min.js" asp-append-version="true"></script>
    </environment>