    function onEdit(e) {
        //on row edit replace the Delete and Edit buttons with Update and Cancel
        $(e.container).find("td:last").html("<a href='javascript: void(0)' class='btn btn-success btn-sm' onclick='updateRow()' title='update button'><i class='fa fa-check-circle'></i>Update</a> " +
            "<a href='javascript: void(0)' class='btn btn-warning btn-sm' onclick='cancelRow()' title='cancel button'><i class='fa fa-ban'></i>Cancel</a>");

        viewModel.set("maintenanceRecord", e.model);
    }

    function mrData(data) {
        var maintenanceRecord = viewModel.get("maintenanceRecord");

        data.StartDate = toUTCString(data.StartDate);
        data.CompletedDate = toUTCString(data.CompletedDate);
        data.Modified = toUTCString(data.Modified);


        if (maintenanceRecord) {
            return {
                mrId: maintenanceRecord.MaintenanceRecordId
            }
        }
    }

    function cancelRow() {
        grid = $("#runTriggeredMRGrid").data("kendoGrid");
        grid.cancelRow();
    }

    function editRow(element) {
        grid = $("#runTriggeredMRGrid").data("kendoGrid");
        grid.editRow($(element).closest("tr"));
    }

    function updateRow(e) {
        grid = $("#runTriggeredMRGrid").data("kendoGrid");

        var maintenanceRecord = viewModel.get("maintenanceRecord");

        if (maintenanceRecord) {
            if (maintenanceRecord.StartDate < maintenanceRecord.Created) {
                alert("Start Date cannot be set before Created Date");
                grid.cancelRow();
            } else if (maintenanceRecord.CompletedDate < maintenanceRecord.StartDate) {
                alert("CompletedDate cannot be set before Start Date");
                grid.cancelRow();
            } else {
                grid.saveRow();
            }
        }
    }

    function startMR(maintenanceRecordId) {
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: '/Maintenance/StartRunTriggeredMR',
            data: {
                maintenanceRecordId: maintenanceRecordId,
            },
            success: function () {
                $("#runTriggeredMRGrid").data("kendoGrid").dataSource.read();
            }
        });
    }

    function passMR(maintenanceRecordId) {
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: '/Maintenance/PassRunTriggeredMR',
            data: {
                maintenanceRecordId: maintenanceRecordId,
            },
            success: function () {
                $("#runTriggeredMRGrid").data("kendoGrid").dataSource.read();
            }
        });
    }

    function failMR(maintenanceRecordId) {
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: '/Maintenance/FailRunTriggeredMR',
            data: {
                maintenanceRecordId: maintenanceRecordId,
            },
            success: function () {
                $("#runTriggeredMRGrid").data("kendoGrid").dataSource.read();
            }
        });
    }

    function closeMR(maintenanceRecordId) {
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: '/Maintenance/CloseRunTriggeredMR',
            data: {
                maintenanceRecordId: maintenanceRecordId,
            },
            success: function () {
                $("#runTriggeredMRGrid").data("kendoGrid").dataSource.read();
            }
        });
    }

    function runEquipmentItemsRequestEnd(e) {
        if (!e.type || e.type == "destroy") {
            refreshRunEquipmentItems();
            refreshEquipmentItems();
        }
    }
    $(function () {
        var maintenanceRecordGrid = $("#maintenanceRecordGrid").data("kendoGrid");

        $("#resetMaintenanceRecordGrid").click(function (e) {
            e.preventDefault();
            localStorage["maintenanceRecordGrid"] = "";
            window.location.href = `/Operation/EditRun?tab=${runTriggeredMRs}`;
        });
    });

    function saveMaintenanceRecordGrid(e) {
        setTimeout(function(){
            var maintenanceRecordGrid = $("#maintenanceRecordGrid").data("kendoGrid");
            localStorage["maintenanceRecordGrid"] = kendo.stringify(maintenanceRecordGrid.getOptions());
        },10);
    }

    function loadMaintenanceGrid() {
        var maintenanceRecordGrid = $("#maintenanceRecordGrid").data("kendoGrid");
        var options = localStorage["maintenanceRecordGrid"];
        if (options) {
            maintenanceRecordGrid.setOptions(JSON.parse(options));
        }
    }

    function updatedMaintenanceRecordGrid() {
        var runTriggeredMRGrid = $("#runTriggeredMRGrid").data("kendoGrid");
        var totalRunTriggeredMRs = runTriggeredMRGrid.dataSource.total();
        viewModel.set("totalRunTriggeredMRs", totalRunTriggeredMRs);
    }

    function refreshMaintenanceRecordGrid() {
        var maintenanceRecordGrid = $("#maintenanceRecordGrid").data("kendoGrid");
        maintenanceRecordGrid.dataSource.read();
    }

    function refreshEquipmentItems() {
        var equipmentItemGrid = $("#equipmentItemGrid").data("kendoGrid");
        equipmentItemGrid.dataSource.read();
    }

    function refreshRunEquipmentItems() {
        var runEquipmentItemGrid = $("#runEquipmentItemGrid").data("kendoGrid");
        runEquipmentItemGrid.dataSource.read();
    }
    function onTopUnitsChange(e) {
        var bottomUnits = $("#BottomUnits").data("kendoDropDownList");
        bottomUnits.select(this.select());
    }

    function onBottomUnitsChange(e) {
        var topUnits = $("#TopUnits").data("kendoDropDownList");
        topUnits.select(this.select());
    }

    function updateEquipmentTotals() {
        var equipmentItemGrid = $("#equipmentItemGrid").data("kendoGrid");
        var totalEquipmentItems = equipmentItemGrid.dataSource.total();
        viewModel.set("totalEquipmentItems", totalEquipmentItems);

        var equipmentData = equipmentItemGrid.dataSource.data();

        $.each(equipmentData, function (i, item) {
            if (item.MaintenanceScheduleDaysAlert) {
                $('tr[data-uid="' + item.uid + '"] td:nth-child(15)').css("color", "#ff7f7f   !important");
            }
            if (item.MaintenanceSchedulePointsAlert) {
                $('tr[data-uid="' + item.uid + '"] td:nth-child(14)').css("color", "#ff7f7f   !important");
            }
        });
    }

    function updateRunEquipmentItemsTotals() {
        var runEquipmentItemGrid = $("#runEquipmentItemGrid").data("kendoGrid");
        var totalRunEquipmentItems = runEquipmentItemGrid.dataSource.total();
        viewModel.set("totalRunEquipmentItems", totalRunEquipmentItems);

        var runEquipmentData = runEquipmentItemGrid.dataSource.data();

        $.each(runEquipmentData, function (i, item) {
            if (item.MaintenanceScheduleDaysAlert) {
                $('tr[data-uid="' + item.uid + '"] td:nth-child(14)').css("color", "#ff7f7f   !important");
            }
            if (item.MaintenanceSchedulePointsAlert) {
                $('tr[data-uid="' + item.uid + '"] td:nth-child(13)').css("color", "#ff7f7f   !important");
            }
        });
        runEquipmentItemGrid.bind('dataBound', function (e) {
            this.element.find('.k-delete').remove();
        });
    }

    function equipmentItemData() {
        return {
            projectId: editRunModel.projectId,
            runId: editRunModel.runId
        };
    }

    function runData() {
        return {
            runId: editRunModel.runId
        };
    }

    function standByChanged(e) {
        var isStandBy = $("#IsStandBy").data("kendoDropDownList");
        var isStandByValue = isStandBy.value();

        if (isStandByValue == editRunModel.booleanFalseString) {
            $("#Objectives").removeAttr("data-val-required");

        } else {
            $("#Objectives").attr("data-val-required", "Services is required");

        }
    }

    function populateRunItemGridClick () {
        var equipmentItemGrid = $("#equipmentItemGrid").data("kendoGrid");

        var equipmentItemIds = [];

        equipmentItemGrid.select().each(function () {
            var equipmentItem = equipmentItemGrid.dataItem($(this));

            if (equipmentItem) {
                equipmentItemIds.push(equipmentItem.EquipmentItemId);
            }
        });

        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: "/Operation/UpdateRunEquipmentItems",
            data: {
                runId: editRunModel.runId,
                equipmentItemIds: equipmentItemIds
            },
            success: function () {
                refreshRunEquipmentItems();
                refreshEquipmentItems();
            }
        });
}

$("#runSaveDetails").click(function () {
    this.disabled = true;
    var validator = $('#editRunForm').kendoValidator().data('kendoValidator');
    let data = $("#editRunForm").serializeArray();
    setCorrectDateTime(data);

    if (validator) {
        if (validator.validate()) {
            $.ajax({
                type: "POST",
                url: '/Operation/EditRun',
                dataType: "json",
                data: data,
                success: function (result) {
                    window.location.href = `/Operation/EditRun/${result.runId}`;
                    updateMessage(editRunModel.name);
                    $("#runSaveDetails").removeAttr("disabled");
                },
                error: function (e) {
                    jqXHRErrors(e);
                    $("#runSaveDetails").removeAttr("disabled");
                }
            });
        } else {
            scrolToFirstError();
            this.disabled = false;
        }
    } else {
        this.disabled = false;
    }
});


    var viewModel = kendo.observable({
        totalRunEquipmentItems: 0,
        totalEquipmentItems: 0,
        totalRunTriggeredMRs: 0,
        isStandBy: editRunModel.modelIsStandBy,
        maintenanceRecord: "",
        tabStripHeaderDetails: function () {
            return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Details </span>`;
        },
        tabStripHeaderRuns: function () {
            return `<span class="k-link"><i class="fa fa-bar-chart mr-1"></i> Runs </span>`;
        },
        tabStripHeaderRunsByService: function () {
            return `<span class="k-link"><i class="fa fa-bar-chart mr-1"></i> Runs by Service </span>`;
        },
        runEquipmentItemsStripText: function(){
            return `<span class="k-link"><i class="fa fa-tags mr-1"></i> Run Equipment Items (<span data-bind="text:totalRunEquipmentItems"></span>)</span>`;
        },

        runTriggeredMRsStripText: function(){
            return `<span class="k-link" data-bind="visible:totalRunTriggeredMRs"><i class="fa fa-wrench mr-1"></i> Run Triggered MRs (<span data-bind="text:totalRunTriggeredMRs"></span>)</span>`;
        },

        otherRunFieldsVisible: function () {
            var isStandBy = this.get("isStandBy");

            return isStandBy == "True";
        },

        deleteRun: function () {
            var confirmDelete = confirm("Are you sure you wish to delete this run");

            if (confirmDelete) {
                window.location.href = `/Operation/DeleteRun?id=${editRunModel.runId}`;
            }
        }

    });

    kendo.bind(document.body.children, viewModel);