﻿
<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-file-text"></i>
        Closure Reasons
        (<span data-bind="text: totalOpportunityClosedReasons"></span>)
    </h4>
</div>
<hr />

<div class="grid-container">
  @(Html.Kendo().Grid<OpportunityClosedReasonModel>()
            .Name("opportunityClosedReasonGrid")
            .Columns(c => {
                c.Bound(p => p.Name);
                c.Command(command => { 
                    command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                    command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
                }).Width(200);
            })
            .Editable(editable => editable.Mode(GridEditMode.InLine))
            .ToolBar(t => {
                t.Create().Text("Add Reason");
                t.Excel().Text("Export");
            }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
            .Sortable()
            .Mobile(MobileMode.Auto)
            .Filterable()
            .Scrollable()
            .Resizable(c => c.Columns(true))
            .ColumnMenu(c => c.Columns(true))
            .Events(e => e.DataBound("updateOpportunityClosedReasonTotal"))
            .Excel(excel => excel
                .FileName(string.Format("Centerpoint_Action_Types_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                .Filterable(true)
                .ProxyURL(Url.Action("Export", "Admin"))
            )
            .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Model(m => m.Id(p => p.OpportunityClosedReasonId))
                .Events(e => e.Error("onError"))
                .Read(read => read.Action("GetOpportunityClosedReasons", "Admin"))
                .Create(create => create.Action("UpdateOpportunityClosedReason", "Admin"))
                .Update(update => update.Action("UpdateOpportunityClosedReason", "Admin"))
                .Destroy(destroy => destroy.Action("DeleteOpportunityClosedReason", "Admin"))
            )
        )
</div>

    <script>
        $(document).ready(function () {
            var opportunityClosedReasonGrid = $('#opportunityClosedReasonGrid').data("kendoGrid");
            opportunityClosedReasonGrid.bind('dataBound', function (e) {
                this.element.find('.k-add').remove();
                this.element.find('.k-i-excel').remove();
            });
        });

        function updateOpportunityClosedReasonTotal() {
            var opportunityClosedReasonGrid = $("#opportunityClosedReasonGrid").data("kendoGrid");
            var totalOpportunityClosedReasons = opportunityClosedReasonGrid.dataSource.total();
            viewModel.set("totalOpportunityClosedReasons", totalOpportunityClosedReasons);
        }


        function onError(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var opportunityClosedReasonGrid = $("#opportunityClosedReasonGrid").data("kendoGrid");
                opportunityClosedReasonGrid.dataSource.cancelChanges();
            }
        }

        var viewModel = new kendo.observable({
            totalOpportunityClosedReasons: 0
        });

        kendo.bind(document.body.children, viewModel);
    </script>
