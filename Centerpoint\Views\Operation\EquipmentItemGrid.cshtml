@if (ViewBag.IsReceiving == null && !GlobalSettings.IsWellsense && Html.IsUsaUser())
{
        <div class="row mb-2">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fa fa-tags"></i> Equipment Categories</h6>
                    </div>
                    <div class="card-body">
                        <div class="expandCollapseButtonsContainer">
                            <button type="button" class="btn btn-sm btn-primary" id="expandAll" onclick="expandAllView()" title="Expand All">
                                <i class="fa fa-expand m-0"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="collapsAll" onclick="collapseAllView()" title="Collapse All">
                                <i class="fa fa-compress m-0"></i>
                            </button>
                        </div>
                        @(Html.Kendo().TreeView()
                        .Name("equipmentCategoryTreeView")
                        .TemplateId("equipmentCategoryTemplate")
                        .DataTextField("NewName")
                        .LoadOnDemand(false)
                        .Events(e => e
                            .Change("equipmentCategorySelected")
                            .Drop("equipmentCategoryDropped")
                            .DataBound("equipmentCategoryLoaded")
                        )
                        .DataSource(datasource => datasource
                            .Events(e => e.RequestEnd("categoriesLoaded"))
                            .Model(m => m.Id("EquipmentCategoryId").HasChildren("HasChildren").Children("Children"))
                            .Read(r => r.Action("GetEquipmentCategories", "Admin"))
                            ).AutoBind(false)
                        )
                    </div>
                </div>
            </div>
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fa fa-tags"></i> Equipment Items (<span data-bind="text: totalEquipmentItems"></span>)
                        </h6>
                    </div>
                    <div class="card-body">
                        @(Html.Kendo().Grid<EquipmentItemModel>()
                            .Name("availableEquipmentItemsGrid")
                            .Columns(columns => {
                                columns.Bound(c => c.EquipmentPackingListProjectName).Title("Assigned To").ClientTemplate("#=EquipmentPackingListProjectName ? NewProjectName : 'N/A'#").Width(150);
                                columns.Bound(c => c.EquipmentItemName).Title("Item Number").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#?projectId=" + Model.ProjectId + "'>#=EquipmentItemName#</a>").Width(150);
                                columns.Bound(c => c.ReceivedDate).Title("Manufacture Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                columns.Bound(c => c.PurchasedDate).Title("Purchased Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                columns.Bound(c => c.CurrencyName).Title("Currency").Width(100).Hidden(true);
                                columns.Bound(c => c.Price).Format("{0:n2}").Width(100).Hidden(true);
                                columns.Bound(c => c.DepreciatedPrice).Title("Net Book Value").Hidden(true).Format("{0:n2}").Width(100);
                                columns.Bound(c => c.PointsPerMonth).Title("Points Per Month").Hidden(true).Width(150);
                                columns.Bound(c => c.PointsPerRun).Title("Points Per Run").Hidden(true).Width(150);
                                columns.Bound(c => c.PointsPerMove).Title("Points Per Move").Hidden(true).Width(150);
                                columns.Bound(c => c.DivisionName).Title("Division").Hidden(true).Width(150);
                                columns.Bound(c => c.TrackedNonAssetItem).Title("Tracked Non-Asset Item").ClientTemplate("#if(TrackedNonAssetItem){#Yes#}else{#No#}#").Hidden(true).Width(100);
                                columns.Bound(c => c.CurrentClientLocationName).Title("Current Location").ClientTemplate("#=CurrentClientLocationName ? CurrentClientLocationName : 'Not Yet Accepted'#").Width(100);
                                columns.Bound(c => c.CustomStatusCode).Title("Custom Status").Width(100);
                                columns.Bound(c => c.Points).Title("Current Points").Width(100);
                                columns.Bound(c => c.MaintenanceScheduleDetail).Title("Maintenance Schedule Date(s)").ClientTemplate("#if(MaintenanceScheduleDaysAlert){#<a style='color:\\#E31E33'href='\\#' onclick='scheduleDates(#=EquipmentItemId#)'>#=MaintenanceScheduleDetail#</a>#}else if(MaintenanceScheduleDetail != 'N/A'){#<a href='\\#' onclick='scheduleDates(#=EquipmentItemId#)'>#=MaintenanceScheduleDetail#</a>#}else{##=MaintenanceScheduleDetail##}#").Width(125);
                                columns.Bound(c => c.MRCount).Title("Active MRs").ClientTemplate("#if(MaintenanceRecordCount){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='maintenanceRecordCount(#=EquipmentItemId#)'>#=MRCount#</a>#} else {##=MRCount##}#").Width(75);
                                columns.Bound(c => c.EquipmentInfo).Title("Info").Width(100);
                                columns.Bound(c => c.AllStatusDescription).Title("Status").Encoded(false).Filterable(f => f.Operators(o => o.ForString(str => str.Clear().Contains("Contains").DoesNotContain("Does not contain")))).Width(150);
                            })
                            .ColumnMenu(c => c.Columns(true))
                            .Events(e => e.DataBound("updateEquipmentTotals"))
                            .ToolBar(toolbar => { toolbar.ClientTemplateId((Html.IsLogisticsAdmin() || Html.IsGlobalAdmin()) ? "equipmentItemGridHeader" : "");})
                            .Filterable()
                            .Selectable(selectable => selectable.Mode(GridSelectionMode.Multiple))
                            .Sortable()
                            .Groupable()
                            .Scrollable(scrollable => scrollable.Endless(true).Height(535))
                            .Resizable(resize => resize.Columns(true))
                            .Reorderable(reorder => reorder.Columns(true))
                            .DataSource(dataSource => dataSource
                                .Ajax()
                                .Model(model => {
                                    model.Id(m => m.EquipmentItemId);
                                })
                                .ServerOperation(true)
                                .PageSize(100)
                                .Read(read => read.Action("GetOperationalEquipmentItemsNotLinkedToProjects", "Operation").Data("equipmentItemData"))
                            ).AutoBind(false)
                        )
                    </div>
                </div>
            </div>
        </div>
    }

<div class="card">
   <div class="card-header">
        <h6 class="mb-0">
            <i class="fa fa-tags"></i> Projects Equipment Items (<span data-bind="text: totalProjectEquipmentItems"></span>)
        </h6>
   </div>
   <div class="card-body">
        @(
            Html.Kendo().Grid<EquipmentItemModel>()
            .Name("selectedEquipmentItemsGrid")
            .Columns(columns => {
            if (!GlobalSettings.IsWellsense && Html.IsUsaUser())
            {
                columns.Template("#if(IsReceived){# <span class='btn btn-success btn-sm' style='cursor: auto;'><i class='fa fa-check'></i><span class='ml-1'>Received</span></span> #} else{# <span class='btn btn-danger btn-sm' style='cursor: auto;'> <span class='ml-1'>Not Received</span></span> #}#").Title("Item");
            }
                columns.Bound(c => c.EquipmentPackingListProjectName).Title("Assigned To").ClientTemplate("#=EquipmentPackingListProjectName ? NewProjectName : 'N/A'#").Width(150);
                columns.Bound(c => c.EquipmentItemName).Title("Item Number").ClientTemplate(@"<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#?projectId=" + Model.ProjectId + "'>#=EquipmentItemName#</a>").Width(250);
                columns.Bound(c => c.ReceivedDate).Title("Manufacture Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                columns.Bound(c => c.PurchasedDate).Title("Purchased Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                columns.Bound(c => c.CurrencyName).Title("Currency").Width(100).Hidden(true);
                columns.Bound(c => c.Price).Format("{0:n2}").Width(100).Hidden(true);
                columns.Bound(c => c.DepreciatedPrice).Title("Net Book Value").Hidden(true).Format("{0:n2}").Width(100);
                columns.Bound(c => c.PointsPerMonth).Title("Points Per Month").Hidden(true).Width(150);
                columns.Bound(c => c.PointsPerRun).Title("Points Per Run").Hidden(true).Width(150);
                columns.Bound(c => c.PointsPerMove).Title("Points Per Move").Hidden(true).Width(150);
                columns.Bound(c => c.DivisionName).Title("Division").Hidden(true).Width(150);
                columns.Bound(c => c.TrackedNonAssetItem).Title("Tracked Non-Asset Item").ClientTemplate("#if(TrackedNonAssetItem){#Yes#}else{#No#}#").Hidden(true).Width(100);
                columns.Bound(c => c.CurrentClientLocationName).Title("Current Location").Width(100);
                columns.Bound(c => c.CustomStatusCode).Title("Custom Status").Width(100).Hidden(true);
                columns.Bound(c => c.Points).Title("Current Points").Width(100);
                columns.Bound(c => c.MaintenanceScheduleDetail).Title("Maintenance Schedule Date(s)").ClientTemplate("#=MaintenanceScheduleDetail#").Width(125);
                columns.Bound(c => c.MRCount).Title("Active MRs").ClientTemplate("#if(MaintenanceRecordCount){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='maintenanceRecordCount(#=EquipmentItemId#)'>#=MRCount#</a>#} else {##=MRCount##}#").Width(75);
                columns.Bound(c => c.EquipmentInfo).Title("Info").Width(100);
                columns.Bound(c => c.AllStatusDescription).Title("Status").Encoded(false).Filterable(f => f.Operators(o => o.ForString(str => str.Clear().Contains("Contains").DoesNotContain("Does not contain")))).Width(150);
                if (ViewBag.IsReceiving == null && !GlobalSettings.IsWellsense && Html.IsUsaUser())
                {
                    columns.Command(c => c.Destroy().HtmlAttributes(new{ @class="bg-danger text-white grid-action-button"} )).Width(80);
                }
            })
            .ToolBar(t => {
                          t.Search();
                          t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetSelectedEquipmentItemsGrid", @class="bg-danger text-white"});
                          if (!GlobalSettings.IsWellsense && Html.IsUsaUser())
                          {
                              t.Custom().Text("Receive Items").HtmlAttributes(new { @id = "receiveItemsButton", @class = "bg-primary text-white" });
                          }
                t.Excel().Text("Export");
            }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
            .ColumnMenu(c => c.Columns(true))
            .Events(e => e
                .DataBound("updateSelectedEquipmentItemsGridTotals")
            )
            .Search(s => { s.Field(o => o.EquipmentItemName, "contains"); })
            .Filterable()
            .Sortable()
            .Groupable()
            .Excel(excel => excel
                .FileName(string.Format("Centerpoint_EquipmentItems_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                .Filterable(true)
                .ProxyURL(Url.Action("Export", "Logistics")))
            .Scrollable(s => s.Height(532))
            .Resizable(resize => resize.Columns(true))
            .Reorderable(reorder => reorder.Columns(true))
            .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Model(model => {
                    model.Id(m => m.EquipmentItemId);
                })
            .Events(ev => ev.RequestEnd("selectedEquipmentItemsGridRequestEnd"))
                .Read(read => read.Action("GetAllEquipmentItemsByProjectId", "Operation").Data("logisticsData"))
                .Destroy(destroy => destroy.Action("UnlinkEquipmentItemFromProject", "Operation"))
            ).AutoBind(true)
        )
   </div>
</div>


@(Html.Kendo().Window()
.Name("scheduleDatesWindow")
.Width(1000)
.Height(500)
.Title("Maintenance Schedule Dates")
.Visible(false)
.Modal(true)
.Events(e => e.Open("scheduleDatesWindowOpened"))
.Content(@<text>
    @(Html.Kendo().Grid<EquipmentItemMaintenanceScheduleModel>()
    .Name("equipmentItemMaintenanceScheduleGrid")
    .Columns(columns => {
        columns.Bound(c => c.MaintenanceBlueprintName).Title("Maintenance Blueprint").Width(150);
        columns.Bound(c => c.LastDate).Title("Last").Format(DateConstants.DateFormat).Width(125);
        columns.Bound(c => c.RecurringDays).Title("Recurring Days").Width(125).Visible(!GlobalSettings.IsWellsense);
        columns.Bound(c => c.RecurringMonths).Title("Recurring Months").Width(125).Visible(GlobalSettings.IsWellsense);
        columns.Bound(c => c.StartDate).Title("Next").ClientTemplate("#=CompanyLocationId!=null? 'N/A' : StartDateOnly #").Width(125);
    })
    .ColumnMenu(c => c.Columns(true))
    .Sortable()
    .AutoBind(false)
    .Scrollable(s => s.Height(400))
    .Resizable(resize => resize.Columns(true))
    .Reorderable(reorder => reorder.Columns(true))
    .DataSource(dataSource => dataSource
    .Ajax()
    .ServerOperation(false)
    .Model(model => {
    model.Id(m => m.EquipmentItemMaintenanceScheduleId);
    })
    .Read(read => read.Action("GetEquipmentItemMaintenanceSchedules", "Assets").Data("equipmentItemMaintenanceScheduleData"))))
</text> ))

@(Html.Kendo().Window()
.Name("maintenanceRecordWindow")
.Width(1000)
.Height(500)
.Title("Maintenance Record")
.Visible(false)
.Modal(true)
.Events(e => e.Open("maintenanceRecordWindowOpened"))
.Content(@<text>
    @(Html.Kendo().Grid<MaintenanceRecordModel>()
        .Name("maintenanceRecordGrid")
        .Columns(columns => {
            columns.Bound(c => c.Number).Title("Maintenancen Record").ClientTemplate("<a href='" + @Url.Action("EditMaintenanceRecord", "Maintenance", new { @id = "" }) + "/#=MaintenanceRecordId#'>#=Number#</a>");
            columns.Bound(c => c.MaintenanceBlueprintId).Title("Maintenancen Blueprint").ClientTemplate("<a href='" + @Url.Action("EditMaintenanceBlueprint", "Admin", new { @id = "" }) + "/#=MaintenanceBlueprintId#'>#=MaintenanceBlueprintName#</a>");
            columns.Bound(c => c.EquipmentItemName).Title("Equipment Item").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#'>#=EquipmentItemName#</a>");
            columns.Bound(c => c.PriorityDescription).Title("Priority");
            columns.Bound(c => c.UserName).Title("Created By").Hidden(true);
            columns.Bound(c => c.Created).Title("Created").Format(DateConstants.DateTimeFormat);
            columns.Bound(c => c.Modified).Title("Modified").Format(DateConstants.DateTimeFormat).Hidden(true);
            columns.Bound(c => c.StatusDescription).Title("Status").ClientTemplate("<span class='badge' style='background:#=StatusColour#;color:#=StatustextColour#'>#=StatusDescription#</span>");
        })
        .ColumnMenu(c => c.Columns(true))
        .Filterable()
        .Sortable()
        .Groupable()
        .Scrollable(s => s.Height(532))
        .Resizable(resize => resize.Columns(true))
        .Reorderable(reorder => reorder.Columns(true))
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(model => {
                model.Id(m => m.MaintenanceRecordId);
            })
            .Read(read => read.Action("GetMaintenanceRecordByEquipmentItemId", "Maintenance").Data("maintenanceRecordData"))
            ).AutoBind(false)
        )
</text>
))

<script type="text/x-kendo-tmpl" id="equipmentItemGridHeader">
    <button class="btn btn-primary" onclick="handleItemGridClick(event)">
        Add to Project
    </button>
</script>
