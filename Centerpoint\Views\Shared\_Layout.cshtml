﻿@using Centerpoint.Common

<!DOCTYPE html>
<html lang="en">
<link rel="icon" type="image/x-icon" href="/img/logo-fav.png">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Centerpoint</title>
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:400,600,300" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="~/style/bootstrap.min.css"/>
    <link rel="stylesheet" href="~/style/default-main.css"/>
    <link rel="stylesheet" href="~/style/globalRules.css" asp-append-version="true"/>
    <link rel="stylesheet" href="~/style/layout.css" asp-append-version="true"/>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
</head>

<body>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-cookie/1.4.1/jquery.cookie.min.js" integrity="sha512-3j3VU6WC5rPQB4Ld1jnLV7Kd5xr+cq9avvhwqzbH/taCRNURoeEpoPBK9pDyeukwSxwRPJ8fDgvYXd6SkaZ2TA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.dirtyforms/1.2.3/jquery.dirtyforms.min.js" integrity="sha512-w00k6T+kr7YmkYGUBvDIlMO1oKmE7/fTcCmAbFx9prJZ5Za6CVJXM02g6UWeBUR7phxH2DH3IrWJIrcE2vF3nA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/jszip/dist/jszip.min.js"></script>
    <script src="~/lib/kendo/kendo.all.min.js"></script>
    <script src="~/lib/kendo/kendo.aspnetmvc.min.js"></script>
    <script src="~/lib/kendo/kendo.culture.en-GB.min.js"></script>
    <script>
    $(window).ready(() => {
        kendo.culture("en-GB")
    })
    </script>
    
    <div class="body-wrapper" data-sidebar-open='true'>
        <partial name="NavigationBar" />
            <main role="main">
                <div class="row" style="height: 50px;margin: 0;">
                    <div class="col-4">
                        <button type="button" class="btn btn-primary sidebar-control-btn" id='sideBarToggler'>
                            <svg width="18" height="13" viewBox="0 0 18 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M1 1H17" stroke="white" stroke-width="2" stroke-linecap="round" />
                                <path d="M1 7H17" stroke="white" stroke-width="2" stroke-linecap="round" />
                                <path d="M1 13H17" stroke="white" stroke-width="2" stroke-linecap="round" />
                            </svg>
                        </button>
                    </div>
                    <div class="col-4" style="align-self: center;">
                        @if (Html.IsTestInstance())
                        {
                            <div id="banner">
                                <div style="margin-top: -2px;">
                                    <i style="padding-right: 3px;" class="text-secondary bi bi-exclamation-triangle-fill"></i>
                                </div>
                                <span class="text-primary">You are currently using the TEST System</span>
                            </div>
                        }
                    </div>
                    <div class="col-4">
                        <div class="main-content-time-zone" id='dataAndTime'>
                            <div class="header-date mr-2">
                                <i class="fa fa-calendar text-secondary"></i>
                                <span id="headerDate" class="text-primary"></span>
                            </div>
                            <div class='header-time'>
                                <i class="fa fa-clock text-secondary"></i>
                                <span id="headerTime" class="text-primary"></span>
                            </div>
                        </div>
                    </div>
                </div>
                  <div class="main-content-body">
                      @RenderBody()
                  </div>
            </main>
            <a id="return-to-top"><i class="fa fa-chevron-up"></i></a>
            <div id="notificationWrapper" class="notificationWrapper"></div>
    </div>
    @{
        var cardName = GlobalSettings.IsWellsense ? "SOC" : "RISC"; 
    }          
    <script >
        const settings = {
            safetyCardName: "@cardName",
        }
    </script>

    <script src="~/js/helpers/domMutator.js" asp-append-version="true"></script>
    <script src="~/js/views/layout/layout.js" asp-append-version="true"></script>
    <script src="~/js/views/layout/messageInterceptor.js" asp-append-version="true"></script>
    <script src="~/js/helpers/filterHelper.js" asp-append-version="true"></script>
    <script src="~/js/helpers/validations.js" asp-append-version="true"></script>
</body>

</html>
