$(document).ready(function () {
    loadMaintenanceRecordHistoryGrid();
    kendo.bind(document.body.children, viewModel);
});


function selectedMaintenanceHistoryRecord(e) {
    var selectedMaintenanceRecordRow = this.select();

    if (selectedMaintenanceRecordRow) {
        var selectedItem = this.dataItem(selectedMaintenanceRecordRow);

        if (selectedItem) {
            viewModel.set("selectedMaintenanceRecordId", selectedItem.EquipmentItemId);
        } else {
            viewModel.set("selectedMaintenanceRecordId", "");
        }
    } else {
        viewModel.set("selectedMaintenanceRecordId", "");
    }
}
function loadMaintenanceRecordHistoryGrid() {
    var grid = $("#maintenanceRecordHistoryGrid").data("kendoGrid");
    var toolBar = $("#maintenanceRecordHistoryGrid .k-grid-toolbar").html();
    var options = localStorage["maintenanceRecordHistoryGrid"];
    viewModel.set("initialMaintenanceRecordHistoryGridOptions", kendo.stringify(grid.getOptions()));
    if (options) {
        grid.setOptions(JSON.parse(options));
        $("#maintenanceRecordHistoryGrid .k-grid-toolbar").html(toolBar);
        $("#maintenanceRecordHistoryGrid .k-grid-toolbar").addClass("k-grid-top");
    }

    getFromToSummary();
}

function saveMaintenanceRecordHistoryGrid(e) {
    setTimeout(function () {
        var grid = $("#maintenanceRecordHistoryGrid").data("kendoGrid");
        localStorage["maintenanceRecordHistoryGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}

$(window).on("resize", function () {
    kendo.resize($(".chart-wrapper"));
});

function updatedMaintenanceRecordGrid() {
    $("#resetMaintenanceRecordHistoryGrid").click(function (e) {
        e.preventDefault();
        resetGridView('maintenanceRecordHistoryGrid', 'initialMaintenanceRecordHistoryGridOptions')
        viewModel.set('selectedMaintenanceRecordId', 0);
        $("#maintenanceRecordHistoryGrid").data("kendoGrid").dataSource.read();
        kendo.bind($("#maintenanceRecordHistoryGrid"), viewModel);
    });

    var maintenanceRecordHistoryGrid = $("#maintenanceRecordHistoryGrid").data("kendoGrid");
    var totalMaintenanceRecords = maintenanceRecordHistoryGrid.dataSource.total();
    viewModel.set("totalMaintenanceRecords", totalMaintenanceRecords);

    
    saveMaintenanceRecordHistoryGrid();
}

function refreshMaintenanceRecordHistoryGrid() {
    var maintenanceRecordHistoryGrid = $("#maintenanceRecordHistoryGrid").data("kendoGrid");
    maintenanceRecordHistoryGrid.dataSource.read();

    getFromToSummary();
}

function maintenanceRecordData() {
    return {
        type: viewModel.get("type"),
        from: kendo.toString(viewModel.get("fromDate"), 'dd/MM/yyyy'),
        to: kendo.toString(viewModel.get("toDate"), 'dd/MM/yyyy')
    }
    getFromToSummary();
}

function getFromToSummary() {
    $.ajax({
        url: "/Maintenance/GetFromToSummary",
        data: {
            from: kendo.toString(viewModel.get("fromDate"), 'dd/MM/yyyy'),
            to: kendo.toString(viewModel.get("toDate"), 'dd/MM/yyyy')
        },
        success: function (result) {
            if (result) {
                viewModel.set("totalClosedPassed", result.TotalClosedPassed);
                viewModel.set("totalClosedFailed", result.TotalClosedFailed);
                viewModel.set("totalClosedFailedActive", result.TotalClosedFailedActive);
                viewModel.set("totalClosedRepaired", result.TotalClosedRepaired);
                viewModel.set("totalClosedNotRepaired", result.TotalClosedNotRepaired);
            }
        },
        dataType: "json"

    });
}

$("#confirmDates").click(function (e) {
    var maintenanceRecordGrid = $("#maintenanceRecordHistoryGrid").data("kendoGrid");

    var selectMaintenanceRecordIds = [];

    maintenanceRecordGrid.select().each(function () {
        var selectedMaintenanceRecord = maintenanceRecordGrid.dataItem($(this));

        if (selectedMaintenanceRecord) {
            selectMaintenanceRecordIds.push(selectedMaintenanceRecord.MaintenanceRecordId);
        }
    });
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: "/Maintenance/EditMaintenanceRecordDates",
        data: {
            selectedMaintenanceRecordIds: selectMaintenanceRecordIds,
            created: $("#Created").val(),
            startDate: $("#StartDate").val(),
            completedDate: $("#CompletedDate").val(),
        },
        complete: function (e) {
            $("#editMaintenanceRecordWindowOpen").data("kendoWindow").close()
            viewModel.set('selectedMaintenanceRecordId', 0);
            maintenanceRecordGrid.dataSource.read();
        },
    });
});

var viewModel = kendo.observable({
    maintenanceChartData: function () {
        var passed = viewModel.get("totalClosedPassed");
        var failed = viewModel.get("totalClosedFailed");
        var repaired = viewModel.get("totalClosedRepaired");
        var notRepaired = viewModel.get("totalClosedNotRepaired");

        var data = [];

        if (passed) {
            data.push({ Name: "Passed", Count: passed, Color: '#7CBB00' });
        }

        if (failed) {
            data.push({ Name: "Failed - Inactive", Count: failed, Color: '#FF0000' });
        }

        if (repaired) {
            data.push({ Name: "Repaired", Count: repaired, Color: '#7bcce4' });
        }

        if (notRepaired) {
            data.push({ Name: "Not Repaired", Count: notRepaired, Color: '#f2b661' });
        }

        return data;
    },
    totalMaintenanceRecords: 0,
    type: "",
    totalMrMonth: 0,
    totalClosedPassed: 0,
    totalClosedFailed: 0,
    totalClosedRepaired: 0,
    totalClosedNotRepaired: 0,
    totalClosedFailedActive: 0,
    fromDate: maintenanceHistoryModel.fromDate,
    toDate: maintenanceHistoryModel.toDate,
    selectedMaintenanceRecordId: 0,

    hasMaintenance: function () {
        var closedPassed = this.get("totalClosedPassed");
        var closedFailed = this.get("totalClosedFailed");
        var closedFailedActive = this.get("totalClosedFailedActive");
        var closedRepaired = this.get("totalClosedRepaired");
        var closedNotRepaired = this.get("totalClosedNotRepaired");

        return closedPassed || closedFailed || closedRepaired || closedNotRepaired || closedFailedActive;
    },
    totalClosedPassedClick: function () {
        this.set("type", "passed");
        refreshMaintenanceRecordHistoryGrid();
    },
    totalClosedFailedClick: function () {
        this.set("type", "failed");
        refreshMaintenanceRecordHistoryGrid();
    },
    totalClosedFailedActiveClick: function () {
        this.set("type", "failedActive");
        refreshMaintenanceRecordHistoryGrid();
    },
    totalClosedRepairedClick: function () {
        this.set("type", "repaired");
        refreshMaintenanceRecordHistoryGrid();
    },
    totalClosedNotRepairedClick: function () {
        this.set("type", "notRepaired");
        refreshMaintenanceRecordHistoryGrid();
    },
    editMaintenanceRecordWindow: function () {
        $("#editMaintenanceRecordWindowOpen").data("kendoWindow").center().open();
    },

});



kendo.bind(document.body.children, viewModel);