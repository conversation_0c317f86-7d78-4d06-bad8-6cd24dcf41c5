﻿@model EquipmentItemModel
@using Centerpoint.Common
@using Microsoft.Extensions.Options;
@using Centerpoint.Extensions;
@Html.Partial("_GridNotification", EntityType.EquipmentItem)

<div id="createAssetsDetails">
<div class="container-fluid" style="height: 80vh; overflow:auto">
    <div class="row">
        <div class="col-md-4">
            <div class="form-group">
                <label>Equipment Number</label>
                @(Html.Kendo().TextBoxFor(m => m.EquipmentNumber))
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label>Info</label>
                @(Html.Kendo().TextBoxFor(m => m.EquipmentInfo))
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label>Division</label>
                @(Html.Kendo().DropDownListFor(m=>m.DivisionId)
                    .DataTextField("Text")
                    .DataValueField("Value")
                    .Filter("contains")
                    .OptionLabel("Select Division")
                    .DataSource(d => d.Read("GetDivisions", "Lookup"))
                    .HtmlAttributes(new {@data_value_primitive = "true"})
                )
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-4">
            <div class="form-group">
                <label>Country of Origin</label>
                @(Html.Kendo().TextBoxFor(m => m.CountryOfOrigin))
            </div>
        </div>
        @if (GlobalSettings.IsWellsense)
        {
            <div class="col-md-4">
                <div class="form-group">
                    <label>Import Commodity Code</label>
                    @(Html.Kendo().TextBoxFor(m => m.ImportCommodityCode))
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label>Export Commodity Code</label>
                    @(Html.Kendo().TextBoxFor(m => m.ExportCommodityCode))
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label>US HTS Commodity Code</label>
                    @(Html.Kendo().TextBoxFor(m => m.USHTSCommodityCode))
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label>Waiver Code</label>
                    @(Html.Kendo().TextBoxFor(m => m.WaiverCode))
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label>Waiver Requirement</label>
                    @(Html.Kendo().TextBoxFor(m => m.WaiverRequirement))
                </div>
            </div>
        }
        else
        {
            <div class="col-md-4">
                <div class="form-group">
                    <label>Commodity Code</label>
                    @(Html.Kendo().TextBoxFor(m => m.CommodityCode))
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label>Serial Number</label>
                    @(Html.Kendo().TextBoxFor(m => m.SerialNumber))
                </div>
            </div>
        }
        
    </div>
  @*   <div class="row">
        <div class="col-md-4">
            <div class="form-group">
                <label>Country of Origin</label>
                @(Html.Kendo().TextBoxFor(m => m.CountryOfOrigin))
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label>Commodity Code</label>
                @(Html.Kendo().TextBoxFor(m => m.CommodityCode))
            </div>
        </div>
        @if (ClientConstants.IsAisus(SettingsOptions.Value.Client))
        {
            <div class="col-md-4">
                <div class="form-group">
                    <label>Serial Number</label>
                    @(Html.Kendo().TextBoxFor(m => m.SerialNumber))
                </div>
            </div>
        }

    </div> *@
    <hr />
    <h4 class="text-primary">Details</h4>
    <div class="row">
        <div class="col-md-4">
            <div class="form-group">
                <label>Manufacturer</label>
                @(Html.Kendo().DropDownListFor(m => m.ManufacturerCompanyId)
                    .Filter("contains")
                    .OptionLabel("Select Company")
                    .DataTextField("Text")
                    .DataValueField("Value")
                    .Events(x=>x.Change("cascadeDropdownFilterHelper"))
                    .DataSource(d => d.Read("GetCompanies", "Lookup"))
                    .HtmlAttributes(new {@data_value_primitive = "true", @data_cascade_to="ManufacturerCompanyLocationId"})
                )
            </div>
            <div class="form-group">
                <label>Purchase Price</label>
                @(Html.Kendo().NumericTextBoxFor<decimal>(m => m.Price).Spinners(false).Value(0))
            </div>
            <div class="form-group">
                <label>Tracked Non-Asset Item</label>
                @(Html.Kendo().DropDownListFor(m => m.TrackedNonAssetItem)
                .Filter("contains")
                .DataValueField("Key")
                .DataTextField("Value")
                .OptionLabel("Select")
                .BindTo(Html.BooleanListValues().ToList())
                .HtmlAttributes(new {@data_value_primitive = "true" }))
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label>Manufacturer Location</label>
                @(Html.Kendo().DropDownListFor(m => m.ManufacturerCompanyLocationId)
                    .Filter("contains")
                    .OptionLabel("Select Location")
                    .DataTextField("Text")
                    .DataValueField("Value")
                    .DataSource(source => {
                        source.Read(read => {
                        read.Action("GetLocationsByCompanyId", "Lookup").Data("filterCompanyLocations");
                    });
                })
                .HtmlAttributes(new { @data_value_primitive = "true" }))
            </div>
            <div class="form-group">
                <label>Purchased Date</label>
                @(Html.Kendo().DatePickerFor(m => m.PurchasedDate).Max(DateTime.Now))
            </div>
            <div class="form-group">
                <label>Internal Invoice Number</label>
                @(Html.Kendo().TextBoxFor(m => m.InternalInvoiceNumber))
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label>Currency</label>
                @(Html.Kendo().DropDownListFor(m => m.CurrencyId)
                    .DataTextField("Text")
                    .DataValueField("Value")
                    .Filter("contains")
                    .OptionLabel("Select Currency")
                    .HtmlAttributes(new { @data_value_primitive = "true" })
                    .DataSource(dataSource => dataSource.Read("GetCurrencies", "Lookup"))
                )
            </div>
            <div class="form-group">
                <label>Received Date</label>
                @(Html.Kendo().DatePickerFor(m => m.ReceivedDate).Max(DateTime.Now))
            </div>
            <div class="form-group">
                <label>External Invoice Number</label>
                @(Html.Kendo().TextBoxFor(m => m.ExternalInvoiceNumber))
            </div>
        </div>
    </div>
    <hr />
    <h4 class="text-primary">Dimensions</h4>
    <div class="row">
        <div class="col-md-3">
            <div class="form-group">
                <label>Length</label>
                @(Html.Kendo().NumericTextBoxFor(m => m.Height)
                    .Value(0)
                    .Spinners(false)
                    .Min(0)
                    .Format("n3")
                    .Decimals(3)
                    .Events(e => e.Change("changeLength"))
                )
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label> Length Unit</label>
                @(Html.Kendo().DropDownListFor(m => m.HeightUnit)
                    .ValuePrimitive(true)
                    .Filter("contains")
                    .OptionLabel("Select")
                    .DataValueField("Key")
                    .DataTextField("Value")
                    .HtmlAttributes(new { @id = "itemHeightUnit"})
                    .BindTo(Centerpoint.Common.Constants.UnitsConstant.EquipmentValuesAndDescriptions.ToList())
                )
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label style="padding-right:20px">OD</label>
                @(Html.Kendo().NumericTextBoxFor(m => m.OuterDiameter)
                                .Value(0)
                    .Spinners(false)
                    .Min(0)
                    .Format("n3")
                    .Decimals(3)
                    .Events(e => e.Change("changeOD"))
                )
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label> OD Unit</label>
                @(Html.Kendo().DropDownListFor(m => m.OuterDiameterUnit)
                    .ValuePrimitive(true)
                    .Filter("contains")
                    .OptionLabel("Select")
                    .DataValueField("Key")
                    .DataTextField("Value")
                    .HtmlAttributes(new { @id = "itemOdUnit", })
                    .BindTo(Centerpoint.Common.Constants.UnitsConstant.EquipmentValuesAndDescriptions.ToList())
                )
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-3">
            <div class="form-group">
                <label>Width</label>
                @(Html.Kendo().NumericTextBoxFor(m => m.Width)
                    .Value(0)
                    .Spinners(false)
                    .Min(0)
                    .Format("n3")
                    .Decimals(3)
                    .Events(e => e.Change("changeWidth"))
                )
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label> Width Unit</label>
                @(Html.Kendo().DropDownListFor(m => m.WidthUnit)
                    .ValuePrimitive(true)
                    .Filter("contains")
                    .OptionLabel("Select")
                    .DataValueField("Key")
                    .DataTextField("Value")
                    .HtmlAttributes(new { @id = "itemWidthUnit"})
                    .BindTo(Centerpoint.Common.Constants.UnitsConstant.EquipmentValuesAndDescriptions.ToList())
                )
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label>Weight</label>
                @(Html.Kendo().NumericTextBoxFor(m => m.Weight)
                .Value(0)
                    .Spinners(false)
                    .Min(0)
                    .Format("n3")
                    .Decimals(3)
                    .Events(e => e.Change("changeWeight"))
                )
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label> Weight Unit</label>
                @(Html.Kendo().DropDownListFor(m => m.WeightUnit)
                    .ValuePrimitive(true)
                    .Filter("contains")
                    .OptionLabel("Select")
                    .DataValueField("Key")
                    .DataTextField("Value")
                    .HtmlAttributes(new { @id = "itemWeightUnit", })
                    .BindTo(Centerpoint.Common.Constants.UnitsConstant.EquipmentWeightValuesAndDescriptions.ToList())
                )
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-3">
            <div class="form-group">
                <label>Depth</label>
                @(Html.Kendo().NumericTextBoxFor(m => m.Depth)
                .Value(0)
                    .Spinners(false)
                    .Min(0)
                    .Format("n3")
                    .Decimals(3)
                    .Events(e => e.Change("changeDepth"))
                )
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label> Depth Unit</label>
                @(Html.Kendo().DropDownListFor(m => m.DepthUnit)
                    .ValuePrimitive(true)
                    .Filter("contains")
                    .OptionLabel("Select")
                    .DataValueField("Key")
                    .DataTextField("Value")
                    .HtmlAttributes(new { @id="itemDepthUnit", })
                    .BindTo(Centerpoint.Common.Constants.UnitsConstant.EquipmentValuesAndDescriptions.ToList())
                )
            </div>
        </div>
    </div>
    <hr />
   
</div>
    <input id="createEquipmentItem" type="submit" class="btn btn-sm btn-primary" value="Save Details" />

@Html.HiddenFor(m => m.Status)
</div>


<script type="text/javascript">
    var equipmentConstants = @Html.Raw(Json.Serialize(Centerpoint.Common.Constants.EquipmentConstants.ValuesAndDescriptions.ToList()));
</script>
