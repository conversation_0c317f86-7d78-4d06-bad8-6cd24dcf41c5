﻿@(Html.<PERSON>().TabStrip()
    .Name("jobsStrips")
    .SelectedIndex(0)
    .Animation(false)
    .Items( tabstrip => {

     tabstrip.Add().Text("")
         .HtmlAttributes(new { @data_chart_selector = "jobChart", @data_bind = "html:tabStripHeaderJobs, click:reDraw<PERSON>hart" })
         .Selected(true)
         .Content(@<text>
                       <partial name="StatisticsJobChart"/>
           </text>
         );

         tabstrip.Add().Text("")
         .HtmlAttributes(new { @data_bind = "click:refreshJobServiceChart, html:tabStripHeaderJobsByService" })
         .Content(@<text>
                       <partial name="StatisticsJobChartsByService"/>
           </text>
         );
    }
      )
    )
