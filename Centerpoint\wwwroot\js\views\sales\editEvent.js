
        $("#addContact").click(function () {
            function getValidationCustomMessage(input) {
                const isRequired = "is required";
                switch (input[0].name) {
                    case 'companyLocationId':
                      return `Location ${isRequired}`
                      break;
                    case 'firstName':
                      return `First Name ${isRequired}`
                      break;
                    case 'lastName':
                        return `Last Name ${isRequired}`
                        break;
                    default:
                      return `${input[0].name} ${isRequired}`
                  }
              }

            if($('#contactWindowForm').kendoValidator({ 
                messages: { required: function(input) {
                        return getValidationCustomMessage(input);
                    }
                }
            }).data('kendoValidator').validate()) {
                let locationId = viewModel.get("locationId")
                let data = new FormData($("#contactWindowForm")[0])
                const finalData = Object.fromEntries(data.entries())
                finalData.NotActive = $("#NotActive").data("kendoCheckBox").value();
                finalData.companyLocationId = locationId;
                console.log(finalData);
                $.ajax({
                    type: 'POST',
                    dataType: 'json',
                    traditional: true,
                    url: '/Admin/UpdateCompanyContact',
                    data: finalData,
                    success: function (result) {
                        $("#contactWindow").data("kendoWindow").close();
                        $("#contactWindowForm")[0].reset();
                        refreshContacts();
                    },
                })
            }
        });

        function onWindowClose(e) {
            let senderElement = e.sender.element[0];
            if(senderElement) {
               let form = senderElement.getElementsByTagName("form")[0];
               form ? form.reset() : ''
               if($(`#${form.id}`).data('kendoValidator')) {
                    $(`#${form.id}`).data('kendoValidator').reset();
               }
            }
        }

        function refreshContacts() {
            var opportunityId = viewModel.get("opportunityId");
            var companyId = viewModel.get("companyId");
            if (opportunityId) {
                var companyContactIds = $("#opportunityCompanyContacts").data("kendoMultiSelect");
                let leadCompanyContacts = $("#leadCompanyContacts").data("kendoMultiSelect");
                if(companyContactIds) {
                    companyContactIds.dataSource.read();
                }
                if(leadCompanyContacts) {
                    leadCompanyContacts.dataSource.read();
                }
             } else if (companyId) {
                 var operatorCompanyContactsIds = $("#operatorCompanyContacts").data("kendoMultiSelect")
                 if(operatorCompanyContactsIds) {
                    operatorCompanyContactsIds.dataSource.read();
                 }
             }
        }
        function opportunityChange(e) {
            var index = this.selectedIndex,
                dataItem;
            dataItem = this.dataItem(index);
            var oppsCompanyId = dataItem.CompanyId;
            var oppsPartnerCompanyId = dataItem.PartnerCompanyId;
            var opportunityId = dataItem.OpportunityId;
            var opportunityCustomerCompanyId = dataItem.CustomerCompanyId;
            viewModel.set("opportunityCustomerCompanyId", opportunityCustomerCompanyId);
            viewModel.set("opportunityCustomerCountry", dataItem.CustomerCountry);
            viewModel.set("opportunityCustomerCompany", dataItem.CustomerCompany);
            viewModel.set("opportunityId", opportunityId);
            if (oppsCompanyId) {
                viewModel.set("eventCompanyId", oppsCompanyId);
            } else {
                viewModel.set("eventCompanyId", oppsPartnerCompanyId);
            }

            if (opportunityId) {
                viewModel.set("opportunityId", dataItem.OpportunityId);
                var dropdownlist = $("#CompanyId").data("kendoDropDownList");
                dropdownlist.value(oppsCompanyId);
                dropdownlist.enable(false);
                viewModel.set("companyId", "");
            } else {
                var dropdownlist = $("#CompanyId").data("kendoDropDownList");
                dropdownlist.enable(true);
            }
           
            var companyContactIds = $("#leadCompanyContacts").data("kendoMultiSelect");

            if (companyContactIds) {
                companyContactIds.dataSource.read();
            }

            var companyLocation = $("#companyLocationId").data("kendoDropDownList");

            companyLocation.dataSource.read();
        }

        function opportunityDatabound(e) {
            var index = this.selectedIndex,
                dataItem;
            dataItem = this.dataItem(index);

            viewModel.set("opportunityId", dataItem.OpportunityId);
            viewModel.set("opportunityCustomerCompanyId", dataItem.CustomerCompanyId);
            viewModel.set("opportunityCustomerCountry", dataItem.CustomerCountry);
            if (dataItem.CompanyId) {
                viewModel.set("eventCompanyId", dataItem.CompanyId);
            } else {
                viewModel.set("eventCompanyId", dataItem.PartnerCompanyId);
            }

            if (dataItem.OpportunityId) {
                var dropdownlist = $("#CompanyId").data("kendoDropDownList");
                dropdownlist.value(dataItem.CompanyId);
                dropdownlist.enable(false);

                viewModel.set("companyId", "");               
            }

            var opportunitycompanyContactIds = $("#opportunityCompanyContacts").data("kendoMultiSelect");

            if (opportunitycompanyContactIds) {
                opportunitycompanyContactIds.dataSource.read();
            }

            var companyLocation = $("#companyLocationId").data("kendoDropDownList");

            companyLocation.dataSource.read();
        }

        function companyChange(e) {
            var companyId = this.value();
            viewModel.set("companyId", companyId);
            viewModel.set("eventCompanyId", companyId);

            if(companyId){
            var dropdownlist = $("#OpportunityId").data("kendoDropDownList");
            dropdownlist.enable(false);
            } else {
                var dropdownlist = $("#OpportunityId").data("kendoDropDownList");
                dropdownlist.enable(true);
            }

            viewModel.set("opportunityId", "");

            var operatorCompanyContactsIds = $("#operatorCompanyContacts").data("kendoMultiSelect");

            if (operatorCompanyContactsIds) {
                operatorCompanyContactsIds.dataSource.read();
            }

            var companyLocation = $("#companyLocationId").data("kendoDropDownList");

            companyLocation.dataSource.read();
        }

        function companyDatabound(e) {
            var companyId = this.value();
            var oppId = viewModel.get("opportunityId");
            if (!oppId) {
                viewModel.set("companyId", companyId);
            if (companyId) {
                var dropdownlist = $("#OpportunityId").data("kendoDropDownList");
                dropdownlist.enable(false);
                viewModel.set("eventCompanyId", companyId);
            }

     
                var companyContactIds = $("#operatorCompanyContacts").data("kendoMultiSelect");
                var leadCompanyContactIds = $("#leadCompanyContacts").data("kendoMultiSelect");

                if (companyContactIds) {
                    companyContactIds.dataSource.read();
                }
                if (leadCompanyContactIds) {
                    leadCompanyContactIds.dataSource.read();
                }
                refreshContacts();
            }

            var companyLocation = $("#companyLocationId").data("kendoDropDownList");

            companyLocation.dataSource.read();
        }

        function locationChange(e) {
            var locationId = this.value();
            viewModel.set("locationId", locationId);
        }

        function opportunityContactData() {
            var opportunityId = viewModel.get("opportunityId");

            if(opportunityId){
            return {
                opportunityId: opportunityId
            };
            } else {
                return {
                    opportunityId: editEventModel.modelOpportunityId
                }
            }
        }

        function leadContactData(e) {
            var opportunityCustomerCountry = viewModel.get("opportunityCustomerCountry");
            var opportunityCustomerCompanyId = viewModel.get("opportunityCustomerCompanyId");
            var text = getTextValue(e);

            if (opportunityCustomerCountry) {
                return {
                    opportunityCustomerCountry: opportunityCustomerCountry,
                    opportunityCustomerCompanyId: opportunityCustomerCompanyId,
                    text: text
                };
            } else {
                return {
                    opportunityCustomerCompanyId: editEventModel.modelOpportunityCustomerCompanyId,
                    opportunityCustomerCountry: editEventModel.modelOpportunityCustomerCountry,
                    text: text
                }
            }
        }

        function companyContactData(e) {
            var companyId = viewModel.get("companyId");

            if (companyId) {
                return {
                    companyId: companyId,
                    text: getTextValue(e)
                };
            }
        }

        function companyData() {
            var eventCompanyId = viewModel.get("eventCompanyId")
            if (eventCompanyId) {
                return {
                    eventCompanyId: eventCompanyId
                };
            }
        }

        function opportunityData() {
            return {
                opportunityId : editEventModel.modelOpportunityId
            }
        }

        function onOpportunityEventDocumentAttached() {
            var opportunityEventDocumentsGrid = $("#opportunityEventDocumentsGrid").data("kendoGrid");
            opportunityEventDocumentsGrid.dataSource.read();
        }

        function onOpportunityEventDocumentUpload(e) {
            uploadValidation(e);
            $(".k-upload-files.k-reset").show();
        }

        function onOpportunityEventDocumentComplete(e) {
            $(".k-upload-files.k-reset").find("li").remove();
            $(".k-upload-files.k-reset").slideUp();
        }

        function updateOpportunityEventDocumentsGrid() {
            var opportunityEventDocumentsGrid = $("#opportunityEventDocumentsGrid").data("kendoGrid");
            var totalOpportunityEventDocuments = opportunityEventDocumentsGrid.dataSource.total();
            viewModel.set("totalOpportunityEventDocuments", totalOpportunityEventDocuments);
        }

        function updateOpportunityEventActionsGrid() {
            var opportunityEventActionsGrid = $("#opportunityEventActionsGrid").data("kendoGrid");
            var totalOpportunityEventActions = opportunityEventActionsGrid.dataSource.total();
            viewModel.set("totalOpportunityEventActions", totalOpportunityEventActions);
        }

        var viewModel = new kendo.observable({
            totalOpportunityEventDocuments: 0,
            totalOpportunityEventActions: 0,
            opportunityId: 0,
            opportunityCustomerCompanyId: 0,
            companyId: editEventModel.modelCompanyId,
            eventCompanyId: 0,
            locationId: 0,
            opportunityCustomerCountry: "",
            opportunityCustomerCompany: "",
            tabStripHeaderDetails: function () {
                return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Details </span>`;
            },
            attachmentsStripText: function(){
                return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Attachments (<span data-bind="text:totalOpportunityEventDocuments"></span>)</span>`;
            },
            followUpActionsStripText: function(){
                return `<span class="k-link"><i class="fa fa-check mr-1"></i> Follow-up Actions (<span data-bind="text:totalOpportunityEventActions"></span>)</span>`;
            },
            showContactWindow: function () {
                $("#contactWindow").data("kendoWindow").center().open();
            },
        });
        kendo.bind(document.body.children, viewModel);

