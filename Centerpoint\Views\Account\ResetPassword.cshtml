﻿@model ResetPasswordRequest
@{
    Layout = "LoginLayout";
}
<div class="login-wrapper">
    <div class="container-fluid h-100 w-100">
        <div class="row h-100">
            <div class="col-xl-8 d-none d-lg-none d-xl-block p-0">
                <img src="/img/login-bg.png" alt="centerpoint login image" class="img-cover">
            </div>
            <div class="col-xl-4 bg-white">
                <div class="login-form-wrapper">
                    <img src="/img/logo.svg" alt="centerpoint logo">
                    <form novalidate id="resetPasswordValidator" method="post" action="/account/ResetPassword" onsubmit="handleSubmit(event)">
                        <div class="w-100">

                            <div class="form-group">
                                <label hidden class="form-label">UserId</label>
                                <input 
                                   hidden 
                                   class="form-control" 
                                   type="text" 
                                   name="UserId" 
                                   value="@Model.UserId"
                                />
                            </div>
                            <div class="form-group">
                                <label hidden class="form-label">Token</label>
                                <input 
                                   hidden 
                                   class="form-control" 
                                   type="text" 
                                   name="Token" 
                                   value="@Model.Token" 
                                />
                            </div>
                            <div class="form-group">
                                <label class="form-label">New password <span class="text-danger">*</span></label>
                                <input 
                                   class="form-control" 
                                   id="NewPassword" 
                                   type="password" 
                                   name="NewPassword"
                                   pattern="^(?=.*[A-Za-z])(?=.*\d)(?=.*[@@$!%*#?&])[A-Za-z\d@@$!%*#?&]{8,}$" 
                                   required="required" 
                                />
                            </div>
                            <div class="form-group">
                                <label class="form-label">Confirm new password <span class="text-danger">*</span></label>
                                <input 
                                   class="form-control"
                                   id="ConfirmNewPassword"
                                   type="password" 
                                   name="ConfirmNewPassword" 
                                   required="required"
                                />
                            </div>

                            <div>
                                <button class="btn btn-danger">Change Password</button>
                            </div>

                        </div>
                    </form>
                    <span class="copy-r">© Centrepoint @DateTime.Now.Year</span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        $("#NewPassword").kendoTextBox();

        $("#ConfirmNewPassword").kendoTextBox();
    })

    function handleSubmit (event) {
        var validator = $("#resetPasswordValidator").kendoValidator({
            rules: {
                    minLength: function(input){
                     if(input.is("[name=NewPassword]") || input.is("[name=ConfirmNewPassword]")){
                            return input.val().length >= 8
                        }
                        return true
                    },
                    passwordsDontMatch: function(input) {
                         if(input.is("[name=ConfirmNewPassword]")) {
                            return input.val() === $("#NewPassword").val()
                         }
                         return true
                    }
            },
            messages:{
                    minLength: "Password must be 8 or more characters",
                    passwordsDontMatch: "Passwords do not match",
                    pattern: "Password must contain at least one letter, one number and one special character",
                    required: "Please fill the field"
            }
        }).data("kendoValidator");
        if (!(validator.validate())) {
            event.preventDefault();
        }
    }

</script>