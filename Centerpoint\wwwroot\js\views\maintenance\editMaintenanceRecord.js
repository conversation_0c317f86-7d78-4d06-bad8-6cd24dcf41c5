
$(document).ready(function () {
    if (editMaintenanceRecordModel.maintenanceRecordIdHasValue) {
        getMaintenanceSteps();    
    }
    initialLoadSettings();

    // it was like this
    // @if (editMaintenanceRecordModel.maintenanceRecordIdHasValue) {
    //     <text>
    // getMaintenanceSteps();
    // </text>
    // }
});

function initialLoadSettings() {
    activateTabDependingOnUrlQuery("EditMaintenanceStrips");
    viewModel.set("isEditMaintenanceStripsVisible", true)
}

function activateTabDependingOnUrlQuery (tabStripId) {
    let tabId = window.location.search.split("tab=")[1];
    if(tabId) {
        let tabToActivate = $(`#${tabId}`);
        if(tabToActivate) {
          $(`#${tabStripId}`).data("kendoTabStrip").select(tabToActivate)
        }
    }
}

function getHtmlNewLinesString(text) {
    var regexp = new RegExp('\n', 'g');
    return text ? text.replace(regexp, '<br>') : text;
}

$("#assignEngineerConfirm").click(function () {
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url:'/Maintenance/AssignEngineer',
        data: {
            id: editMaintenanceRecordModel.maintenanceRecordId,
            engineerId : viewModel.get("engineerUserId"),
        },
        success: function(){
            window.location.href = `/Maintenance/EditMaintenanceRecord?id=${editMaintenanceRecordModel.maintenanceRecordId}&tab=`
        },
    });
});

$("#reAssignEngineerConfirm").click(function () {
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url:'/Maintenance/ReAssignEngineer',
        data: {
            id: editMaintenanceRecordModel.maintenanceRecordId,
            engineerId : viewModel.get("engineerUserId"),
        },
        success: function(){
            window.location.href = `/Maintenance/EditMaintenanceRecord?id=${editMaintenanceRecordModel.maintenanceRecordId}&tab=`
        },
    });
});

$("#rejectMaintenancRecordClick").click(function () {
    var confirmReject = confirm("Are you sure you wish to reject this maintenance record");

    if (confirmReject){
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url:`/Maintenance/RejectMaintenanceRecordReason?id=${editMaintenanceRecordModel.maintenanceRecordId}&status=${editMaintenanceRecordModel.MaintenanceConstantOnGoing}`,
            data: {
                id: editMaintenanceRecordModel.maintenanceRecordId,
                comment: $("#RejectMaintenanceRecordReason").val()
            },
            success: function(){
                window.location.href = `/Maintenance/EditMaintenanceRecord?id=${editMaintenanceRecordModel.maintenanceRecordId}&tab=`
            },
        });
    }
});

function filterMaintenanceBlueprints() {
    var equipmentItemList = $("#EquipmentItemId").data("kendoDropDownList");
    var index = equipmentItemList.selectedIndex;
    var equipmentItem = equipmentItemList.dataItem(index);

    return {
        equipmentCategoryId: equipmentItem.EquipmentCategoryId
    };
}

function getMaintenanceSteps() {
    $.ajax({
        url: "/Maintenance/GetMaintenanceRecordSteps",
        dataType: "json",
        data: {
            maintenanceRecordId: editMaintenanceRecordModel.maintenanceRecordId,
        },
        success: function (result) {
            if (result) {
                viewModel.set("maintenanceSteps", result);
            }
        }
    });
}

function maintenanceRecordData() {
    return {
        mRId: editMaintenanceRecordModel.mRId,
    };
}

function onMaintenanceRecordDocumentAttached() {
    var maintenanceRecordDocumentsGrid = $("#maintenanceRecordDocumentsGrid").data("kendoGrid");
    maintenanceRecordDocumentsGrid.dataSource.read();
}

function onMaintenanceRecordDocumentUpload(e) {
    uploadValidation(e);
    $(".k-upload-files.k-reset").show();
}

function onMaintenanceRecordDocumentComplete(e) {
    $(".k-upload-files.k-reset").find("li").remove();
    $(".k-upload-files.k-reset").slideUp();
}

function updateMaintenanceRecordDocumentGrid() {
    var maintenanceRecordDocumentsGrid = $("#maintenanceRecordDocumentsGrid").data("kendoGrid");
    var totalMaintenanceRecordDocuments = maintenanceRecordDocumentsGrid.dataSource.total();
    viewModel.set("totalMaintenanceRecordDocuments", totalMaintenanceRecordDocuments);

    refreshMaintenanceRecordGrid();
}

function updateMaintenanceBlueprintDocumentGrid() {
    var maintenanceBlueprintDocumentsGrid = $("#maintenanceBlueprintDocumentsGrid").data("kendoGrid");
    var totalMaintenanceBlueprintDocuments = maintenanceBlueprintDocumentsGrid.dataSource.total();
    viewModel.set("totalMaintenanceBlueprintDocuments", totalMaintenanceBlueprintDocuments);
}

function updateMaintenanceRecordLogs() {
    var maintenanceRecordLogGrid = $("#maintenanceRecordLogGrid").data("kendoGrid");
    var totalMaintenanceRecordLogs = maintenanceRecordLogGrid.dataSource.total();
    viewModel.set("totalMaintenanceRecordLogs", totalMaintenanceRecordLogs);

    var grid = this;

    grid.table.find("tr[role='row']").each(function () {
        var model = grid.dataItem(this);

        if (!model.Details) {
            $(this).find(".k-hierarchy-cell .k-icon").hide();
        }
    });
}

function refreshMaintenanceRecordGrid() {
    var maintenanceRecordLogGrid = $("#maintenanceRecordLogGrid").data("kendoGrid");
    maintenanceRecordLogGrid.dataSource.read();
}


function getMaintenanceRecordStepById(maintenanceRecordStepId){
    var maintenanceSteps = viewModel.get("maintenanceSteps");
    var maintenanceRecordStep = "";

    for(var i =0;i<maintenanceSteps.length;i++){
        if(maintenanceSteps[i].MaintenanceRecordStepId == maintenanceRecordStepId){
            maintenanceStep = maintenanceSteps[i];
        }
    }

    return maintenanceStep;
}

function saveMaintenanceRecordClick(maintenanceRecordStepId, status, repairRequired) {
    var maintenanceRecordStep = getMaintenanceRecordStepById(maintenanceRecordStepId);

    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        contentType: 'application/json',
        url: "/Maintenance/EditMaintenanceRecordStep",
        data:JSON.stringify(maintenanceRecordStep),
        success:function(){
            if(status){
                $.ajax({
                    type: 'POST',
                    dataType: 'json',
                    traditional: true,
                    contentType: 'application/json',
                    url: `/Maintenance/UpdateMaintenanceRecordStepStatus?maintenanceRecordStepId=${maintenanceRecordStepId}&status=${status}`,
                    success: function () {
                        if(repairRequired){
                            $.ajax({
                                type: 'POST',
                                dataType: 'json',
                                url: "/Maintenance/AddRepairStep",
                                data:{
                                    maintenanceRecordStepId: viewModel.get("maintenanceRecordStepId"),
                                    maintenanceRecordId: editMaintenanceRecordModel.maintenanceRecordId
                                },
                                success: function () {
                                    window.location.href = `/Maintenance/EditMaintenanceRecord?id=${editMaintenanceRecordModel.maintenanceRecordId}&tab=steps`;
                                }
                            });
                        } else{
                            window.location.href = `/Maintenance/EditMaintenanceRecord?id=${editMaintenanceRecordModel.maintenanceRecordId}&tab=steps`;
                        }
                    }
                });
            }else{
                window.location.href = `/Maintenance/EditMaintenanceRecord?id=${editMaintenanceRecordModel.maintenanceRecordId}&tab=steps`;
            }
        }
    });
}

function holdMaintenanceRecordClick(maintenanceRecordStepId) {
    saveMaintenanceRecordClick(maintenanceRecordStepId, editMaintenanceRecordModel.maintenanceStepResultConstantOnHold);
}

function removeHoldMaintenanceRecordClick(maintenanceRecordStepId) {
    saveMaintenanceRecordClick(maintenanceRecordStepId, editMaintenanceRecordModel.maintenanceStepResultConstantOnHoldRemoved);
}

function showRepairWindow(maintenanceRecordStepId) {
    viewModel.set("maintenanceRecordStepId", maintenanceRecordStepId);
    $("#repairWindow").data("kendoWindow").center().open();
}

$("#repairMaintenanceRecordClick").click(function () {
    var isRepairRequired = viewModel.get("isRepairRequired")

    var maintenanceRecordStepId = viewModel.get("maintenanceRecordStepId");
    saveMaintenanceRecordClick(maintenanceRecordStepId, editMaintenanceRecordModel.maintenanceStepResultConstantFail, isRepairRequired);
});

function passMaintenanceRecordClick(maintenanceRecordStepId) {
    saveMaintenanceRecordClick(maintenanceRecordStepId, editMaintenanceRecordModel.maintenanceStepResultConstantPass);
}

function failMaintenanceRecordClick(maintenanceRecordStepId) {
    saveMaintenanceRecordClick(maintenanceRecordStepId, editMaintenanceRecordModel.maintenanceStepResultConstantFail);
}

function deleteAttachment(maintenanceRecordStepId, documentId){
    var confirmDelete = confirm("Are you sure you wish to delete this attachment");

    if (confirmDelete) {
        $.ajax({
            type: 'POST',
            dataType: 'json',
            url: "/Maintenance/DeleteMaintenanceRecordStepDocument",
            data:{
                maintenanceRecordStepId: maintenanceRecordStepId,
                documentId: documentId
            },
            success: function () {
                alert('The Maintenance record step document has been deleted');
                window.location.href = `/Maintenance/EditMaintenanceRecord?id=${editMaintenanceRecordModel.maintenanceRecordId}&tab=steps`;
            }
        });
    }
}

var viewModel = new kendo.observable({
    totalMaintenanceRecords: 0,
    totalMaintenanceRecordLogs: 0,
    totalMaintenanceRecordDocuments: 0,
    totalMaintenanceBlueprintDocuments: 0,
    engineerUserId: editMaintenanceRecordModel.engineerUserId,
    maintenanceSteps: [],
    equipmentItemId: editMaintenanceRecordModel.equipmentItemId,
    maintenanceRecordStepId: "",
    isRepairRequired: false,
    isEditMaintenanceStripsVisible: false,

    stepsTabStripTitle: function(){
        return `<span class="k-link"><i class="fa fa-file-text mr-2"></i>Steps (<span data-bind="text:totalMaintenanceRecordSteps"></span>)</span>`;
    },
    mrAttachmentsTabStripTitle: function(){
        return `<span class="k-link"><i class="fa fa-file-text mr-2"></i>MR Attachments (<span data-bind="text: totalMaintenanceRecordDocuments"></span>)</span>`;
    },
    blueprintAttachmentsTabStripTitle: function(){
        return `<span class="k-link"><i class="fa fa-file-text mr-2"></i>Blueprint Attachments (<span data-bind="text: totalMaintenanceBlueprintDocuments"></span>)</span>`;
    },
    mrLogTabStripTitle: function(){
        return `<span class="k-link"><i class="fa fa-clock mr-2"></i>MR Log (<span data-bind="text: totalMaintenanceRecordLogs"></span>)</span>`;
    },
    tabStripHeaderDetails: function () {
        return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Details </span>`;
    },

    totalMaintenanceRecordSteps: function () {
        var maintenanceSteps = this.get("maintenanceSteps");
        return maintenanceSteps.length;
    },

    deleteMaintenanceRecord: function () {
        var confirmDelete = confirm("Are you sure you wish to delete this maintenance record");

        if (confirmDelete) {
            window.location.href = `/Maintenance/DeleteMaintenanceRecord?id=${editMaintenanceRecordModel.maintenanceRecordId}`
        }
    },

    selfAssignMRClick:function() {

        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url:"/Maintenance/SelfAssign",
            data: {
                id: editMaintenanceRecordModel.maintenanceRecordId,
            },
            success: function(){
                window.location.href = `/Maintenance/EditMaintenanceRecord?id=${editMaintenanceRecordModel.maintenanceRecordId}&tab=`;
            },
        });
    },

    closeMaintenancRecordClick:function() {
        var confirmClose = confirm("Are you sure you wish to close this maintenance record");

        if (confirmClose){
            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url:`/Maintenance/UpdateMaintenanceRecordStatus?id=${editMaintenanceRecordModel.maintenanceRecordId}&status=${editMaintenanceRecordModel.maintenanceConstantAwaitingApproval}`,
                data: {
                    id: editMaintenanceRecordModel.maintenanceRecordId,
                },
                success: function(){
                    window.location.href = `/Maintenance/EditMaintenanceRecord?id=${editMaintenanceRecordModel.maintenanceRecordId}&tab=`;
                },
            });
        }
    },

    completeMaintenancRecordClick:function() {
        var confirmComplete = confirm("Are you sure you wish to complete this maintenance record");

        if (confirmComplete){
            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url:`/Maintenance/UpdateMaintenanceRecordStatus?id=${editMaintenanceRecordModel.maintenanceRecordId}&status=${editMaintenanceRecordModel.maintenanceConstantClosed}`,
                data: {
                    id: editMaintenanceRecordModel.maintenanceRecordId,
                },
                success: function(){
                    window.location.href = `/Maintenance/EditMaintenanceRecord?id=${editMaintenanceRecordModel.maintenanceRecordId}&tab=`;
                },
            });
        }
    },

    acceptMaintenancRecordClick:function() {
        var confirmAccept = confirm("Are you sure you wish to accept this maintenance record");

        if (confirmAccept){
            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url:`/Maintenance/UpdateMaintenanceRecordStatus?id=${editMaintenanceRecordModel.maintenanceRecordId}&status=${editMaintenanceRecordModel.maintenanceConstantClosed}`,
                data: {
                    id: editMaintenanceRecordModel.maintenanceRecordId,
                },
                success: function(){
                    window.location.href = `/Maintenance/EditMaintenanceRecord?id=${editMaintenanceRecordModel.maintenanceRecordId}&tab=`;
                },
            });
        }
    },

    startMaintenancRecordClick:function() {
        var confirmStart = confirm("Are you sure you wish to start this maintenance record?");

        if (confirmStart){
            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url:`/Maintenance/UpdateMaintenanceRecordStatus?id=${editMaintenanceRecordModel.maintenanceRecordId}&status=${editMaintenanceRecordModel.maintenanceConstantInProgress}`,
                data: {
                    id: editMaintenanceRecordModel.maintenanceRecordId,
                },
                success: function(){
                    window.location.href = `/Maintenance/EditMaintenanceRecord?id=${editMaintenanceRecordModel.maintenanceRecordId}&tab=`;
                },
            });
        }
    },

    revertStartMaintenancRecordClick:function() {
        var confirmStart = confirm("Are you sure you wish to undo the start?");

        if (confirmStart){
            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url:`/Maintenance/RevertStart?id=${editMaintenanceRecordModel.maintenanceRecordId}`,
                data: {
                id: editMaintenanceRecordModel.maintenanceRecordId,
                },
            success: function(){
                window.location.href = `/Maintenance/EditMaintenanceRecord?id=${editMaintenanceRecordModel.maintenanceRecordId}&tab=`;
            },
            });
    }
},

    reOpenMaintenancRecordClick:function() {
        var confirmStart = confirm("Are you sure you wish to re-open this maintenance record?");

        if (confirmStart){
            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url:`/Maintenance/UpdateMaintenanceRecordStatus?id=${editMaintenanceRecordModel.maintenanceRecordId}&status=${editMaintenanceRecordModel.maintenanceConstantInProgress}`,
                data: {
                    id: editMaintenanceRecordModel.maintenanceRecordId,
                },
                success: function(){
                    window.location.href = `/Maintenance/EditMaintenanceRecord?id=${editMaintenanceRecordModel.maintenanceRecordId}&tab=`;
                },
            });
        }
    },

    showRejectWindow: function () {
        $("#rejectWindow").data("kendoWindow").center().open();
    },

    showAssignMaintenancRecordWindow: function () {
        $("#assignMaintenancRecordWindow").data("kendoWindow").center().open();
    },

    showReAssignMaintenancRecordWindow: function () {
        $("#reAssignMaintenancRecordWindow").data("kendoWindow").center().open();
    },

    onAttachmentSuccess:function(){
        window.location.href = `/Maintenance/EditMaintenanceRecord?id=${editMaintenanceRecordModel.maintenanceRecordId}&tab=steps`;
    }
});

kendo.bind(document.body.children, viewModel);