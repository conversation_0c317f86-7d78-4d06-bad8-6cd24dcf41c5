﻿namespace Centerpoint.Common.Constants
{
    public static class EquipmentConstant
    {

        public const string AcceptancePending = "ACP";
        public const string Operational = "OPE";
        public const string UnderMaintenance = "UMA";
        public const string UnderMaintenanceOnHold = "UOH";
        public const string Quarantined = "QUA";
        public const string Inactive = "IAC";
        public const string Lost = "LST";
        public const string Archived = "ARC";
        public const string InTransit = "In Transit";
        public const string Reserved = "Reserved";
        public const string Selected = "Selected";
        public const string BundleOK = "Bundle OK";
        public const string BundleCheck = "Bundle Check";
        public const string BundledItem = "Bundled Item";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && AllValuesAndDescriptions.ContainsKey(value) ? AllValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return AllValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ActiveValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {AcceptancePending,"Acceptance Pending"},
                    {Operational,"Operational"},
                    {UnderMaintenance,"Under Maintenance"},
                    {UnderMaintenanceOnHold,"Maintenance On Hold"},
                };
            }
        }

        public static Dictionary<string, string> AssetValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Operational,"Operational"},
                    {UnderMaintenance,"Under Maintenance"},
                    {UnderMaintenanceOnHold,"Maintenance On Hold"},
                };
            }
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {AcceptancePending,"Acceptance Pending"},
                    {Operational,"Operational"},
                    {UnderMaintenance,"Under Maintenance"},
                    {UnderMaintenanceOnHold,"Maintenance On Hold"},
                    {Quarantined, "Quarantined" },
                    {Inactive,"Inactive"},
                    {Lost,"Lost"}
                };
            }
        }
        public static Dictionary<string, string> AllValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {AcceptancePending,"Acceptance Pending"},
                    {Operational,"Operational"},
                    {UnderMaintenance,"Under Maintenance"},
                    {UnderMaintenanceOnHold,"Maintenance On Hold"},
                    {Quarantined, "Quarantined" },
                    {Inactive,"Inactive"},
                    {Lost,"Lost"},
                    {Archived, "Аrchived"}
                };
            }
        }

        public static Dictionary<string, string> OtherValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {InTransit, "In Transit"},
                    {Reserved, "Reserved" },
                    {Selected, "Selected" },
                    {BundleOK, "Bundle OK" },
                    {BundleCheck, "Bundle Check"},
                    {BundledItem, "Bundled Item"}
                };
            }
        }

        public static string GetStatusTemplate(string status, int equipmentPackingListId = 0, int equipmentShipmentId = 0)
        {
            if (status == EquipmentConstant.Selected && equipmentPackingListId != 0)
                return $"<a href='/Logistics/EditEquipmentPackingList/{equipmentPackingListId}' class='badge' style='background:{{0}};color:{{1}}'>{{2}}</a>";
            else if ((status == EquipmentConstant.Reserved || status == EquipmentConstant.InTransit) && equipmentShipmentId != 0)
                return $"<a href='/Logistics/EditEquipmentShipment/{equipmentShipmentId}' class='badge' style='background:{{0}};color:{{1}}'>{{2}}</a>";
            else
                return "<span class=\"badge\" style=\" background:{0}; color:{1};\">{2}</span>";
        }
        public static string GetStatusColour(string status)
        {
            switch (status)
            {
                case EquipmentConstant.AcceptancePending:
                    return "#BA55D3";
                case EquipmentConstant.Operational:
                    return "#7CBB00";
                case EquipmentConstant.UnderMaintenance:
                    return "#FF8000";
                case EquipmentConstant.UnderMaintenanceOnHold:
                    return "#B03060";
                case EquipmentConstant.Inactive:
                    return "#FF0080";
                case EquipmentConstant.Lost:
                    return "#FF0000";
                case EquipmentConstant.Quarantined:
                    return "#4A0000";

                case EquipmentConstant.Reserved:
                    return "#68217A";
                case EquipmentConstant.BundleOK:
                    return "#7CBB00";
                case EquipmentConstant.BundleCheck:
                    return "#ff8000";
                case EquipmentConstant.BundledItem:
                    return "#00A1F1";
                case EquipmentConstant.InTransit:
                    return "#3d3d00";
                case EquipmentConstant.Selected:
                    return "#FD8204";

                default:
                    return "#000";
            }
        }

        public static string GetStatusTextColour(string status)
        {
            if (status == EquipmentConstant.Operational || status == EquipmentConstant.UnderMaintenance || status == EquipmentConstant.UnderMaintenanceOnHold || status == EquipmentConstant.Inactive || status == EquipmentConstant.AcceptancePending || status == EquipmentConstant.Lost || status == EquipmentConstant.Quarantined)
                return "#ffffff";

            return "#000";
        }
    }
}
