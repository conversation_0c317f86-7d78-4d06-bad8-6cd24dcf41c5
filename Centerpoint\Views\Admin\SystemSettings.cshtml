﻿@model SystemSettingsModel

@Html.Partial("_GridNotification", EntityType.SystemSettings)

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-cogs"></i>
        System Settings
    </h4>
</div>
<hr />

<div class="row mb-2">
    <div class="col-md-4">
        <div class="form-group">
            <label>Event Duration</label>
            @(Html.Kendo().TextBoxFor(m => m.Event))
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            <label>Max Login Attempts</label>
            @(Html.Kendo().TextBoxFor(m => m.MaxLoginAttempts))
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            <label>Depreciation Years</label>
            @(Html.Kendo().TextBoxFor(m => m.DepreciationYears))
        </div>
    </div>
</div>
<span>
    <a id="confirmSettings" class="btn btn-primary btn-sm">Save</a>
</span>


<div class="card mt-2">
    <div class="card-header">Logo Upload</div>
    <div class="card-body">
        @(Html.Kendo().Upload()
            .Name("logoImage")
            .Messages(x=>x.Select("Select file..."))
            .Async(async => async.Save("UploadLogo", "Admin"))
            .Multiple(false)
            .Events(e => e.Select("onSelect").Complete("onComplete"))
            .Validation(validation => validation.AllowedExtensions(new string[] { ".jpg", ".png" })@*.MaxFileSize(2097152)*@)
        )
        <div class="demo-hint">You can only upload <strong>JPG</strong>, <strong>PNG</strong> files. File Size to <strong>2MB</strong>.</div>
    </div>
</div>

<div class="card mt-2 preview-container">
    <div class="card-header">
        Logo Preview
    </div>
    <div class="card-body logo-img-container">
        <img src="@Url.Action("LogoImage", "Admin")" id="preview-image" class="w-100" />
    </div>
    <div>
        <button class="btn btn-primary btn-sm m-3" onclick="deleteLogo()">Delete Logo</button>
    </div>
</div>

<script>
   
    function deleteLogo(){
        $.ajax({
            url:'/Admin/DeleteLogo',
            type: 'POST',
            success:(e)=>{
                updateMessage("Logo");
                window.location.reload();

            }
        });
    }
    
    var viewModel = new kendo.observable({
        logoImage: "",
    });
    kendo.bind(document.body.children, viewModel);



    $("#confirmSettings").click(function () {
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: '@Url.Action("UpdateSystemSettings", "Admin")',
            data: {
                event: $("#Event").val(),
                maxLoginAttempts: $("#MaxLoginAttempts").val(),
                depreciationYears: $("#DepreciationYears").val()
            },
            success: function () {
                window.location.reload();
            },
        });
    });

    function onSelect(e) {
        var fileInfo = e.files[0];
        var wrapper = this.wrapper
        setTimeout(function () {
            addPreview(fileInfo, wrapper);
        });
    }

    function addPreview(file, wrapper) {
        var raw = file.rawFile;
        var reader  = new FileReader();

        if (raw) {
          reader.onloadend = function () {
            
          var preview = $("#preview-image").attr("src", this.result);
          
           
          };

          reader.readAsDataURL(raw);

        }
    }

    function onComplete() {
        $("#logoImage").data("kendoUpload").clearAllFiles();
        getLogo();
        updateMessage("Logo");

    }
    

</script>
    
