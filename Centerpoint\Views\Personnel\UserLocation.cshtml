﻿@model List<UserLocationModel>

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-globe"></i>
        User Location
    </h4>
</div>
<hr />

<style>
    .k-i-marker-pin-target:before {
        display: none !important;
    }
   
</style>
<div class="row">
    <div class="col-md-12">
        @(Html.Kendo().Map()
        .Name("map")
        .HtmlAttributes(new { @style = "height:650px" })
        .Layers(layers => {
            layers.Add()
                .Type(MapLayerType.Bing)
                .ImagerySet(MapLayersImagerySet.Road)
                .Key("Ascdt6mylhFHwNXB-PKeNMKs0gQNKcdA0imOKJzpSKOTJnVUS__yGFCdF26kKpU4")
                .UrlTemplate("http://#= subdomain #.tile.openstreetmap.fr/hot/#= zoom #/#= x #/#= y #.png")
                .Subdomains("a", "b", "c")
                .Attribution("&copy; <a href='http://osm.org/copyright'>OpenStreetMap contributors</a>");

            layers.Add()
                .Type(MapLayerType.Marker)
                .DataSource(dataSource => dataSource.Read(read => read.Action("Location", "Personnel")))
                .LocationField("UserLocation")
                .Tooltip(t => t.Template("#=marker.dataItem.Location# - #=marker.dataItem.UserName#"))
                .Shape(MapMarkersShape.Pin);
        }))
    </div>
</div>
<br />

@if (Html.IsTestInstance()) {
    <div class="alert alert-dismissible alert-warning">
        <button type="button" class="close" data-bs-dismiss="alert">×</button>
        <h4 class="text-center mb-0"> CENTERPOINT UK TEST SMS Number : @ViewBag.PhoneNumber</h4>
    </div>    
} else {
    <div class="alert alert-dismissible alert-warning">
        <button type="button" class="close" data-bs-dismiss="alert">×</button>
        <h4 class="text-center mb-0"> CENTERPOINT UK SMS Number : @ViewBag.PhoneNumber</h4>
    </div>  
}
@(Html.Kendo().Grid<UserLocationModel>()
.Name("userLocationGrid")
.Columns(columns => {
columns.Bound(c => c.UserName).Title("User");
columns.Bound(c => c.ActionDescription).Title("Update");
columns.Bound(c => c.From).Title("From");
columns.Bound(c => c.Location).Title("Location");
columns.Bound(p => p.Created).Title("Date").Format(DateConstants.DateTimeFormat);
columns.Command(command => { 
    command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"}).Visible("isUserLocationEditable");
    command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}).Visible("isUserLocationDestroyable"); 
});
})
 .Events(e => e.DataBound("updateUserLocations"))
 .ColumnMenu(c => c.Columns(true))
  .ToolBar(t => {
         t.Create().Text("Add Update");
        })

 .Editable(editable => editable.Mode(GridEditMode.PopUp).TemplateName("UserLocation").Window(w => w.Name("userLocationWindow").Title("User Location").Width(800).Draggable(false)))
 .Filterable()
 .Sortable()
 .Groupable()
 .Reorderable(c => c.Columns(true))
 .Scrollable(s => s.Height("auto"))
 .DataSource(dataSource => dataSource
 .Ajax()
 .ServerOperation(false)
 .Model(model => {
     model.Id(m => m.UserLocationId);
 })
 .Read(read => read.Action("GetUsersLocation", "Personnel"))
 .Create(create => create.Action("UpdateUserLocation", "Personnel").Data("userLocationData"))
 .Update(update => update.Action("UpdateUserLocation", "Personnel").Data("userLocationData"))
 .Destroy(destroy => destroy.Action("DeleteUserLocation", "Personnel"))))


    <script>
        $(document).ready(function () {

            var userLocationGrid = $('#userLocationGrid').data("kendoGrid");
            var map = $("#map").getKendoMap();

            userLocationGrid.bind('dataBound', function (e) {
                this.element.find('.k-add').remove();
            });
        });

        function centerMapExtents(locations) {
            var map = $("#map").getKendoMap();
            var extent;

            for (var i = 0; i < locations.length; i++) {
                var loc = new kendo.dataviz.map.Location();
                loc.lat = locations[i].Latitude;
                loc.lng = locations[i].Longitude;

                if (!extent) {
                    extent = new kendo.dataviz.map.Extent(loc, loc);
                } else {
                    extent.include(loc);
                }
            }

            map.extent(extent);
        }

        function updateUserLocations() {
            var userLocationGrid = $("#userLocationGrid").data("kendoGrid");
            var userLocations = userLocationGrid.dataSource.total();

            refreshMarkers();
            
            viewModel.set("userLocations", userLocations);
        }

        function userLocationData(data) {
            data.Created = toUTCString(data.Created);
        }

        function isUserLocationEditable() {

            if(("@Html.IsGlobalAdmin()" === "True" || "@Html.IsPersonnelAdministrator()" === "true" && data.IsWebEntry) ||
                !("@Html.IsGlobalAdmin()" === "True" || "@Html.IsPersonnelAdministrator()" === "true") &&
                 (IsWebEntry && UserEmail == '@Html.AccountEmailAddress()')) {
                return true 
            }
        }

        function isUserLocationDestroyable() {
           if("@Html.IsGlobalAdmin()" === "True" || "@Html.IsPersonnelAdministrator()" === "true") {
              return true
           }
        }

        function refreshMarkers() {
            var map = $("#map").data("kendoMap");

            $.ajax({
                url: "@Url.Action("Location", "Personnel")",
                dataType:"json",
                success:function(result){
                    if (result) {
                        map.layers[1].dataSource.data(result);

                        if (result.length > 1) {
                            centerMapExtents(result);
                        } else {
                            var center = new kendo.dataviz.map.Location();
                            center.lat = result[0].Latitude;
                            center.lng = result[0].Longitude;

                            map.center(center);
                            map.zoom(5);
                        }
                    }
                }
            });
        }

        $("#map").bind("dblclick", function (e) {
            var map = $(this).data("kendoMap");
            var center = map.eventToLocation(e);
            map.center(center);
            map.zoom(map.zoom() + 1);
        });

        var viewModel = new kendo.observable({
            userLocations: 0,
        });

        kendo.bind(document.body.children, viewModel);
    </script>
