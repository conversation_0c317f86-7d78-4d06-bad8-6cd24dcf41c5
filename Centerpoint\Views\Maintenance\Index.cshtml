﻿@model MaintenanceRecordDashboardModel

<div class="header-container-between">
    <h4>
        <i class="fa fa-wrench"></i>
        Maintenance Records
        (<span data-bind="text: totalMaintenanceRecords"></span>)
    </h4>
    <div class="d-flex actionsContainer">
        @if (Html.IsMaintenanceAdmin() || Html.IsGlobalAdmin()) {
            <a class="btn btn-danger btn-sm" data-bind="click:mrTriggerClick"><i class="fa fa-recycle"></i>MR Trigger</a>
        }
        @if (!GlobalSettings.IsWellsense && (Html.IsJuniorFieldEngineer() || Html.IsFieldEngineer() || Html.IsSeniorFieldEngineer() || Html.IsAssetAdmin() || Html.IsMaintenanceAdmin() || Html.IsMaintenanceEngineer() || Html.IsOperationAdmin() || Html.IsGlobalAdmin())) {
            <a class="btn btn-primary btn-sm" href="@Url.Action("AddMaintenanceRecord", "Maintenance")"><i class="fa fa-plus"></i>Create MR</a>
        }
    </div> 
</div>
<hr />

<div class="row mb-3">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Current Maintenance Summary</h6>
            </div>
            <div class="card-body">
                    <div class="d-flex justify-content-between m-2">
                        <span>Total</span>
                        <span>
                            <a href="#maintenanceRecordGrid" data-bind="click:totalClick" class="btn card-list-item-count" style="background: black; color: #fff" title="Click here to show All MRs">@Model.TotalMR</a>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between m-2">
                        <span>Unassigned</span>
                        <span>
                            <a href="#maintenanceRecordGrid" data-bind="click:pendingClick" class="btn card-list-item-count" style="background: #BA55D3; color: #fff" title="Click here to show Total Pending">@Model.TotalPending</a>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between m-2">
                        <span>Assigned</span>
                        <span>
                            <a href="#maintenanceRecordGrid" data-bind="click:assignedClick" class="btn card-list-item-count" style="background: #55ADD3; color: #fff" title="Click here to show Total Assigned">@Model.TotalAssigned</a>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between m-2">
                        <span>In Progress</span>
                        <span>
                            <a href="#maintenanceRecordGrid" data-bind="click:inProgressClick" class="btn card-list-item-count" style="background: #B03060; color: #fff" title="Click here to show Total In Progress">@Model.TotalInProgress</a>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between m-2">
                        <span>On Hold</span>
                        <span>
                            <a href="#maintenanceRecordGrid" data-bind="click:onHoldClick" class="btn card-list-item-count" style="background: #F7A54A; color: #fff" title="Click here to show Total On Hold">@Model.TotalOnHold</a>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between m-2">
                        <span>Awaiting Approval</span>
                        <span>
                            <a href="#maintenanceRecordGrid" data-bind="click:awaitingApprovalClick" class="btn card-list-item-count" style="background: #FF0080; color: #fff" title="Click here to show Total Awaiting Approval">@Model.TotalAwaitingApproval</a>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between m-2">
                        <span>Average Maintenance Days</span>
                        <span>
                            <span class="btn disabled card-list-item-count" style="background: #16185F; color: #fff">@Model.AverageMaintenanceDays</span>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between m-2">
                        <span>SIF Related</span>
                        <span>
                            <a href="#maintenanceRecordGrid" data-bind="click:sifClick" class="btn card-list-item-count" style="background: #7ACCE4; color: #fff" title="Click here to show Total Related Sifs for this MR">@Model.TotalSifs</a>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between m-2">
                        <span>Full Maintenance</span>
                        <span>
                            <a href="#maintenanceRecordGrid" data-bind="click:notRunEnabledClick" class="btn card-list-item-count" style="background: #7CBB00; color: #fff" title="Click here to show Total MR that reset points">@Model.TotalNotRunEnabled</a>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between m-2">
                        <span>Post Run Maintenance</span>
                        <span>
                            <a href="#maintenanceRecordGrid" data-bind="click:runEnabledClick" class="btn card-list-item-count" style="background: #F2B660; color: #fff" title="Click here to show Total Post Run Maintenance">@Model.TotalRunEnabledOpen</a>
                        </span>
                    </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">MRs by Location</h6>
            </div>
            <div class="card-body">
                @foreach (var location in Model.TotalLocations) {
                    <div class="d-flex justify-content-between m-2">
                        <span>@location.Key.Name</span>
                        <span>
                            <a href="#maintenanceRecordGrid" class="btn card-list-item-count" style="background: #7CBB00; color: #fff" onclick="locationFilter('@location.Key.CompanyLocationId')">@location.Value</a>
                        </span>
                    </div>
                }
                @if(Model.TotalLocations.Count == 0){
                    <span>No locations found</span>
                }
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">MRs By Engineers</h6>
            </div>
            <div class="card-body">
                @foreach (var engineer in Model.TotalEngineers) {
                    <div class="d-flex justify-content-between m-2">
                        <span>@engineer.Key.Name</span>
                        <span>
                            <a href="#maintenanceRecordGrid" class="btn card-list-item-count" style="background: #7CBB00; color: #fff" onclick="engineerFilter('@engineer.Key.UserId')">@engineer.Value</a>
                        </span>
                    </div>
                }
                @if(Model.TotalEngineers.Count == 0){
                    <span>No engineers found</span>
                } 
            </div>
        </div>
    </div>
</div>


<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Monthly Summary</h6>
            </div>
            <div class="card-body" style="height:310px;">
                <div class="d-flex justify-content-between m-2">
                    <div>
                        @(Html.Kendo().DropDownListFor(m => m.Month)
                            .DataValueField("Key")
                            .DataTextField("Value")
                            .Filter("contains")
                            .DataSource(d => d.Read("GetMonths", "Lookup"))
                            .Events(e => e.Change("monthChanged"))
                            .HtmlAttributes(new { @data_bind = "value:month", @style = "width:100px; font-size: 14px;" }))
                    </div>
                    <div>
                        @(Html.Kendo().DropDownListFor(m => m.Year)
                            .Filter("contains")
                            .DataSource(d => d.Read("GetAllYears", "Lookup"))
                            .Events(e => e.Change("yearChanged"))
                            .HtmlAttributes(new { @data_bind = "value:year", @style = "width:100px; font-size: 14px;" }))
                    </div>
                </div>

                <div class="d-flex justify-content-between m-2">
                    <span>Total Carried Over</span>
                    <span>
                        <a data-bind="text:totalOutstanding, click:totalCarriedOverClick" class="btn" style="background: black; padding-top:0px; padding-right:8px;padding-bottom:0px; padding-left:8px; color: #fff"></a>
                    </span>
                </div>
                <div class="d-flex justify-content-between m-2">
                    <span>Total Raised</span>
                    <span>
                        <a data-bind="text:totalMrMonth, click:totalRaisedClick" class="btn" style="background: #BA55D3 ; padding-top:0px; padding-right:8px;padding-bottom:0px; padding-left:8px; color: #fff"></a>
                    </span>
                </div>
                <div class="d-flex justify-content-between m-2">
                    <span>Total Closed - All</span>
                    <span>
                        <a data-bind="text:totalClosed, click:totalClosedClick" class="btn" style="background: #7CBB00; padding-top:0px; padding-right:8px;padding-bottom:0px; padding-left:8px; color: #fff"></a>
                    </span>
                </div>
                <div class="d-flex justify-content-between m-2">
                    <span>Total to be Carried Over</span>
                    <span>
                        <a data-bind="text:totalCarriedOver" class="btn disabled" style="background: #B03060; padding-top:0px; padding-right:8px;padding-bottom:0px; padding-left:8px; color: #fff"></a>
                    </span>
                </div>

            </div>
        </div>
    </div>
    <div class="col-md-4">
        @if (Html.IsJuniorFieldEngineer() || Html.IsSeniorFieldEngineer() || Html.IsMaintenanceEngineer() || Html.IsMaintenanceAdmin() || Html.IsFieldEngineer() || Html.IsGlobalAdmin()) {
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">My MR</h6>
                </div>
                <div class="card-body" style=" height:310px">
                    <div class="d-flex justify-content-between m-2">
                        <span>Total Assigned</span>
                        <span>
                            <a data-bind="text:totalassignedByEngineer, click:assignedByEngineerClick" href="#maintenanceRecordGrid" class="btn card-list-item-count" style="background: #55ADD3; color: #fff" title="Click here to show Total Assigned"></a>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between m-2">
                        <span>In Progress</span>
                        <span>
                            <a data-bind="text:totalinProgressByEngineer, click:inProgressByEngineerClick" href="#maintenanceRecordGrid" class="btn card-list-item-count" style="background: #B03060; color: #fff" title="Click here to show Total In Progress"></a>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between m-2">
                        <span>On Hold</span>
                        <span>
                            <a data-bind="text:totalonHoldByEngineer, click:onHoldByEngineerClick" href="#maintenanceRecordGrid" class="btn card-list-item-count" style="background: #F7A54A; color: #fff" title="Click here to show Total On Hold"></a>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between m-2">
                        <span>Awaiting Approval</span>
                        <span>
                            <a data-bind="text:totalAwaitingApprovalByEngineer, click:awaitingApprovalByEngineerClick" href="#maintenanceRecordGrid" class="btn card-list-item-count" style="background: #FF0080; color: #fff" title="Click here to show Total Awaiting Approval"></a>
                        </span>
                    </div>
                </div>
            </div>
            }
    </div>
</div>

<hr />
@(Html.Kendo().Grid<MaintenanceRecordDashboardGridModel>()
            .Name("maintenanceRecordGrid")
            .Columns(columns => {
                columns.Bound(c => c.Number).Width(150).ClientTemplate("<a href='" + @Url.Action("EditMaintenanceRecord", "Maintenance", new { @id = "" }) + "/#=MaintenanceRecordId#'>#=Number#</a>");
                columns.Bound(c => c.MaintenanceBlueprintName).Width(150).Title("Blueprint").ClientTemplate("<a href='" + @Url.Action("EditMaintenanceBlueprint", "Admin", new { @id = "" }) + "/#=MaintenanceBlueprintId#'>#=MaintenanceBlueprintName#</a>");
                columns.Bound(c => c.EquipmentItemName).Width(150).Title("Equipment Item").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#'>#=EquipmentItemName#</a>");
                columns.Bound(c => c.RunName).Width(90).Title("Run").Hidden(true).ClientTemplate("<a href='" + @Url.Action("EditRun", "Operation", new { @id = "" }) + "/#=RunId#?tab=runTriggeredMRs'>#=RunName? RunName:''#</a>");
                columns.Bound(c => c.ServiceImprovementId).Width(90).Title("Sif").ClientTemplate("<a href='" + @Url.Action("EditServiceImprovement", "QHSE", new { @id = "" }) + "/#=ServiceImprovementId#'>#=ServiceImprovementName ? ServiceImprovementName : ''#</a>").Hidden(true);
                columns.Bound(c => c.PriorityDescription).Width(90).Title("Priority").Width(100);
                columns.Bound(c => c.UserName).Width(150).Title("Created By").ClientTemplate("#=UserName ? UserName : 'System Generated'#").Hidden(true);
                columns.Bound(c => c.EngineerUserName).Width(150).Title("Assigned Engineer").Hidden(true);
                columns.Bound(c => c.Created).Title("Created").EditorTemplateName("DateTemplate").Format(DateConstants.DateTimeFormat).Width(150);
                columns.Bound(c => c.Modified).Title("Modified").Format(DateConstants.DateTimeFormat).Hidden(true);
                columns.Bound(c => c.StartDate).Title("Start Date").EditorTemplateName("DateTemplate").Format(DateConstants.DateTimeFormat).Width(150);
                columns.Bound(c => c.CompletedDate).Title("Complete Date").EditorTemplateName("DateTemplate").Format(DateConstants.DateTimeFormat).Width(150);
                columns.Bound(c => c.ComplexityDescription).Title("Complexity").Hidden(true);
                columns.Bound(c => c.OnHoldHours).Width(150).Title("On Hold Hours").Hidden(true);
                columns.Bound(c => c.MaintenanceTimeString).Width(150).Title("Maintenance Time").Hidden(true);
                columns.Bound(c => c.CurrentCompany).Width(150).Title("Current Company").Hidden(true);
                columns.Bound(c => c.CurrentLocation).Width(150).Title("Current Location").Hidden(true);
                columns.Bound(c => c.TotalMaintenanceTimeString).Width(160).Title("Total Maintenance Time");
                columns.Bound(c => c.TimeSinceCreation).Title("Time Since Creation").Hidden(true);
                columns.Bound(c => c.Steps).Title("Total Steps").Hidden(true);
                columns.Bound(c => c.Comment).Title("Comment").Width(150).Hidden(true); ;
                columns.Bound(c => c.StatusDescription).Width(90).Title("Status").ClientTemplate("#if(MaintenanceRecordOnHold){#<span class='badge' style='background:\\#f7a54a;color:\\#fff'>#=NewOnHoldName#</span>#} else if(IsClosed && IsRepair && IsRepairFailed){#<span class='badge' style='background:\\#F2B660;color:\\#fff'>#=NewRepairFailedName#</span>#}else if(IsClosed && IsRepair && Pass){#<span class='badge' style='background:\\#7ACCE4;color:\\#fff'>#=NewRepairedName#</span>#}else if(Pass){#<span class='badge' style='background:#=StatusColour#;color:#=StatustextColour#'>#=NewPassedName#</span>#} else if(Fail){#<span class='badge' style='background:\\#FF0000;color:\\#fff'>#=NewFailedName#</span>#} else{#<span class='badge' style='background:#=StatusColour#;color:#=StatustextColour#'>#=StatusDescription#</span>#}#").Width(200);
                    columns.Command(command => { 
                        command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                    }).Width(90);
            })
                .ToolBar(t => {
                if (Html.IsMaintenanceAdmin() || Html.IsGlobalAdmin())
                {
                    t.Custom().HtmlAttributes(new { @class = "text-white bg-primary", @data_bind = "visible: selectedMaintenanceRecordId, click: editMaintenanceRecordWindow" }).Text("Edit Dates");
                }
                    t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetMaintenanceRecordGrid", @class="bg-danger text-white"});
                    t.Excel().Text("Export");
                }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
                .ColumnMenu(c => c.Columns(true))
                .Events(e => e.DataBound("updatedMaintenanceRecordGrid").Change("selectedMaintenanceRecord").ColumnReorder("saveMaintenanceRecordGrid").ColumnResize("saveMaintenanceRecordGrid").ColumnShow("saveMaintenanceRecordGrid").ColumnHide("saveMaintenanceRecordGrid"))
                .Sortable()
                .Selectable(selectable => selectable.Mode(GridSelectionMode.Multiple))
                .Filterable()
                .Editable(editable => editable.Mode(GridEditMode.InLine))
                .Excel(excel => excel
                    .FileName(string.Format("Centerpoint_MR_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                    .Filterable(true)
                    .ProxyURL(Url.Action("Export", "Qhse")))
                .Sortable()
                .Groupable()
                .Scrollable(s => s.Height("auto"))
                .Resizable(resize => resize.Columns(true))
                .Reorderable(reorder => reorder.Columns(true))
                .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Model(model => {
                    model.Id(m => m.MaintenanceRecordId);
                    model.Field(x => x.Modified).Editable(false);
                    })
                .Read(read => read.Action("GetMaintenanceRecords", "Maintenance").Data("maintenanceRecordData"))
                .Update(update => update.Action("UpdateMaintenanceRecords", "Maintenance", new { @maintenanceRecordId = Model.MaintenanceRecordId }).Data("maintenanceRecordGridUpdateData"))))

@(Html.Kendo().Window()
 .Name("editMaintenanceRecordWindowOpen")
 .Title("Edit Item")
 .Content(@<text>
               <partial name="EditSelectedMaintenanceRecord"/>
 </text>)
 .Width(800)
 .Modal(true)
 .Visible(false))


    @* <script type="text/x-kendo-tmpl" id="maintenanceRecordTemplate">
        <div class="col-md-12">
            <div class="row">
                <div class="col-md-5">
                    <a style="color:\\#0073d0" href="@Url.Action("EditMaintenanceRecord", "Maintenance")/#=MaintenanceRecordId#"><h4 style="color:\\#0073d0"><i class="fa fa-file-text"></i>#=Number#</h4></a>
                </div>
                <div class="col-md-4">
                    <a style="color:\\#0073d0" href="@Url.Action("EditMaintenanceRecord", "Maintenance")/#=MaintenanceRecordId#"><h4 style="color:\\#0073d0 ">#=TotalOutstandingDays# Day (s)</h4></a>
                </div>
            </div>
        </div>
        <br />
    </script> *@
    
    <script type="text/x-kendo-template" id="template">
        <p>#=OnHoldNotes#</p>
    </script>

    <script>
        const maintenanceModel = {
            month: @Model.Month,
            year: @Model.Year,
        }
    </script>

    <environment include="Development">
        <script src="~/js/views/maintenance/maintenance.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script src="~/js/views/maintenance/maintenance.min.js" asp-append-version="true"></script>
    </environment>