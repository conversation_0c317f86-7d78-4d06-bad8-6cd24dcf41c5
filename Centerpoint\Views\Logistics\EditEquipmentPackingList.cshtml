﻿@model EquipmentPackingListModel

<div id="page" style="position: relative;">
@if (Model.EquipmentShipmentId.HasValue) {
    <div class="alert alert-dismissible alert-warning">
        <button type="button" class="close" data-bs-dismiss="alert">×</button>
        <strong>Warning!</strong> This Packing List is tied to Shipment ( @Model.EquipmentShipmentNumber )
    </div>    
}
    <div style="display:none" class="alert alert-dismissible alert-warning" data-bind="visible:dangerousGoods">
        <button type="button" class="close" data-bs-dismiss="alert">×</button>
        <strong><i class="fa fa-warning"></i> Warning!</strong> There are items on the packing list which are Dangerous.
    </div>
    <br />

<div class="header-container-between">
    <h4>
        <i class="fa fa-file-text"></i>
        @(Model.EquipmentPackingListId.HasValue ? Model.PackingListName : "Create New Packing List")
    </h4>
    <div class="d-flex actionsContainer">
        <a class="btn btn-info btn-sm" href="@Url.Action("PackingList","Logistics" )"><i class="fa fa-refresh"></i>Return to Packing List Dashboard</a>
        @if (Model.EquipmentPackingListId.HasValue) {
            <a href='@Url.Action("PrintPackingListToPdf", "Report")/@Model.EquipmentPackingListId'  onclick="packingListPdf()" class="btn btn-primary btn-sm"><i class='fa fa-file-pdf'></i>Print PDF</a>
        }
    </div>
</div>
<hr />

@{Html.Kendo().TabStrip()
.Name("EditMaintenanceStrips")
.SelectedIndex(0)
.Animation(false)
.Items( tabstrip => {


tabstrip.Add().Text("")
        .HtmlAttributes(new { @data_bind = "html:tabStripHeaderDetails" })
    .Selected(true)
    .Content(@<text>

            <div id="details">
                <br />
                @using (Html.BeginForm("EditEquipmentPackingList", "Logistics", FormMethod.Post, new { @id = "editEquipmentPackingList" })) {
                    @Html.ValidationSummary(false)
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Title</label>
                                <br />
                                @(Html.Kendo().TextBoxFor(m => m.PackingListTitle)
                            .HtmlAttributes(new { @style = "font-size: 14px;" }))
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Packing List Created By</label>
                                <br />
                                @(Html.Kendo().DropDownListFor(m => m.CreatedByUserId)
                            .DataTextField("Text")
                            .DataValueField("Value")
                            .Filter("contains")
                            .DataSource(d => d.Read("GetUsers", "Lookup"))
                            .HtmlAttributes(new { @style = "font-size: 14px;", @disabled = "disabled" }))
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label> Associated Project</label>
                                <br />
                                @(Html.Kendo().DropDownListFor(m => m.ProjectId)
                            .DataValueField("Value")
                            .DataTextField("Text")
                            .Filter("contains")
                            .OptionLabel("Select Project")
                            .HtmlAttributes(new { @style = "font-size: 14px" })
                            .DataSource(d => d.Read("GetActiveOppurtunityProjects", "Lookup")))
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Date Required</label>
                                <br />
                                @Html.Kendo().DatePickerFor(m => m.DateRequired).Min(DateTime.Now.AddDays(1)).HtmlAttributes(new { @style = "font-size: 14px" })
                            </div>
                        </div>
                    </div>
                    <hr />
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label> Description</label>
                                <br />
                                @(Html.TextAreaFor(p => p.Description, new { @class = "form-control", @style = "width:100%", @rows = "5" }))
                            </div>
                        </div>
                    </div>
                    <hr />
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                @if(Model.EquipmentPackingListId.HasValue && (string.IsNullOrEmpty(Model.SignedOffBy) || !Model.SignedOffBy.Contains("System Signed Off"))) {
                                <label> Sign off Details <span class="text-danger" data-bind="visible:totalPackingListItems"> * </span></label>
                            } else {
                                <label> Sign off Details</label>
                            }
                                <br />
                                @(Html.TextAreaFor(p => p.SignOffDetails, new { @class = "form-control", @style = "width:100%", @rows = "5", @data_value_update = "keyup", @data_bind = "value:signOffDetail, enabled:totalPackingListItems" }))
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label> Signed Off By</label>
                                <br />
                                <p>@Model.SignedOffBy</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label> Signed Off Date</label>
                                <br />
                                <p>@(Model.SignedOffDate.HasValue ? Model.SignedOffDate.Value.ToString("dd/MM/yyyy") : "")</p>
                            </div>
                        </div>
                    </div>
                        <br />
                        <br />
                        <div class="card" data-bind="visible:totalPackingListItems">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fa fa-tags"></i> Equipment Packing List Items (<span data-bind="text: totalPackingListItems"></span>)</h6>
                            </div>
                            @(Html.Kendo().Grid<EquipmentPackingListItemModel>()
                        .Name("equipmentPackingListItemGridInDetails")
                        .Columns(columns => {
                            columns.Bound(c => c.EquipmentItemName).Title("Item Number").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#?packinListId=" + Model.EquipmentPackingListId + "'>#=EquipmentItemName#</a>").Width(150);
                            columns.Bound(c => c.EquipmentItem.ReceivedDate).Title("Manufacture Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                            columns.Bound(c => c.EquipmentItem.PurchasedDate).Title("Purchased Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                            columns.Bound(c => c.EquipmentItem.CurrencyName).Title("Currency").Width(100).Hidden(true);
                            columns.Bound(c => c.EquipmentItem.Price).Format("{0:n2}").Width(100).Hidden(true);
                            columns.Bound(c => c.EquipmentItem.DepreciatedPrice).Title("Net Book Value").Hidden(true).Format("{0:n2}").Width(100);
                            columns.Bound(c => c.EquipmentItem.PointsPerMonth).Title("Points Per Month").Hidden(true).Width(150);
                            columns.Bound(c => c.EquipmentItem.PointsPerRun).Title("Points Per Run").Hidden(true).Width(150);
                            columns.Bound(c => c.EquipmentItem.PointsPerMove).Title("Points Per Move").Hidden(true).Width(150);
                            columns.Bound(c => c.EquipmentItem.DivisionName).Title("Division").Hidden(true).Width(150);
                            columns.Bound(c => c.EquipmentItemTrackedNonAssetItem).Title("Tracked Non-Asset Item").ClientTemplate("#if(EquipmentItemTrackedNonAssetItem){#Yes#}else{#No#}#").Hidden(true).Width(100);
                            columns.Bound(c => c.CurrentClientLocationName).Title("Current Location").Width(100);
                            columns.Bound(c => c.EquipmentItem.CustomStatusCode).Title("Custom Status").Width(100).Hidden(true);
                            columns.Bound(c => c.EquipmentItem.Points).Title("Current Points").Width(100).Hidden(true);
                            columns.Bound(c => c.MaintenanceScheduleDetail).Title("Maintenance Schedule Date(s)").ClientTemplate("#if(MaintenanceScheduleDaysAlert){#<a style='color:\\#E31E33'href='\\#' onclick='scheduleDates(#=EquipmentItemId#)'>#=MaintenanceScheduleDetail#</a>#}else if(MaintenanceScheduleDetail != 'N/A'){#<a href='\\#' onclick='scheduleDates(#=EquipmentItemId#)'>#=MaintenanceScheduleDetail#</a>#}else{##=MaintenanceScheduleDetail##}#").Width(125);
                            columns.Bound(c => c.MRCount).Title("Active MRs").ClientTemplate("#if(MaintenanceRecordCount){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='maintenanceRecordCount(#=EquipmentItemId#)'>#=MRCount#</a>#} else {##=MRCount##}#").Width(75);
                            columns.Bound(c => c.EquipmentItem.EquipmentInfo).Title("Info").Width(100);
                            columns.Bound(c => c.HasDangerousGoods).Title("Dangerous Goods").ClientTemplate("#if(HasDangerousGoods){# <span class='badge badge-warning' style='font-size:14px'><i class='fa fa-warning' style='margin-right:0px' title='This item contains dangerous goods'></i></span> YES #}else{# #='NO'# #}#").Width(80);
                            columns.Bound(c => c.AllStatusDescription).Title("Status").Encoded(false).Filterable(f => f.Operators(o => o.ForString(str => str.Clear().Contains("Contains").DoesNotContain("Does not contain")))).Width(150);
                        })
                                .ColumnMenu(c => c.Columns(true))
                                .Filterable()
                                .Groupable()
                                .Sortable()
                                .Scrollable(s => s.Height(200))
                                .Resizable(resize => resize.Columns(true))
                                .Reorderable(reorder => reorder.Columns(true))
                                .Editable(editable => editable.Mode(GridEditMode.InLine))
                                .DataSource(dataSource => dataSource
                                .Ajax()
                                .ServerOperation(false)
                                .Model(model => {
                                    model.Id(m => m.EquipmentPackingListItemId);
                                })
                                    .Read(read => read.Action("GetEquipmentPackingListItems", "Logistics", new { @equipmentPackingListId = Model.EquipmentPackingListId }))))
                        </div>
                        <br />
                        <br />
                        @Html.HiddenFor(m => m.EquipmentPackingListId)
                        @Html.HiddenFor(m => m.ProjectId)
                        @Html.HiddenFor(m => m.CreatedByUserId)
                        <div class="d-flex actionsContainer">
                            @if (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsLogisticsAdmin() || Html.IsAssetAdmin() || Html.IsSeniorFieldEngineer() || Html.IsJuniorFieldEngineer() || Html.IsFieldEngineer())
                        {
                                <button type="submit" class="btn btn-sm btn-primary">Save Packing List Details</button>
                        }
                            @if (Model.EquipmentPackingListId.HasValue && !Model.EquipmentShipmentId.HasValue && (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsLogisticsAdmin() || Html.IsAssetAdmin() || Html.IsMaintenanceAdmin() || Html.IsSeniorFieldEngineer() || Html.IsJuniorFieldEngineer() || Html.IsFieldEngineer()))
                        {
                                <button type="button" class="btn btn-danger btn-sm" data-bind="click:deletePackingList"><i class="fa fa-thumbs-down"></i>Delete Packing List</button>
                        }
                            @if (string.IsNullOrEmpty(Model.SignedOffBy) && Model.EquipmentPackingListId.HasValue)
                        {
                                <button type="button" class="btn btn-primary btn-sm" data-bind="click:signOff, enabled:isSignedOffDetailsValid"><i class="fa fa-check"></i>Sign off</button>
                        }
                        </div>               
            }
            </div>

        </text>);

  if (Model.EquipmentPackingListId.HasValue) {
    tabstrip.Add().Text("")
        .HtmlAttributes(new { @data_bind = "html:tabStripHeaderManageListItems" })
        .Content(@<text>

        
                <br />
                <div id="packingListItems">
                    <br />
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fa fa-tags"></i> Equipment Categories</h6>
                                </div>
                                <div class="card-body">
                                    <div class="expandCollapseButtonsContainer">
                                        <button class="btn btn-sm btn-primary" id="expandAll" data-bind="click:expandAll" title="Expand All">
                                            <i class="fa fa-expand"></i>
                                        </button>
                                        <button class="btn btn-sm btn-primary" id="collapsAll" data-bind="click:collapseAll" title="Collapse All">
                                            <i class="fa fa-compress"></i>
                                        </button>
                                    </div>
                                    <input id="filterText" class="k-input k-textbox k-input-solid k-input-md k-rounded-md" type="text" placeholder="Search categories" />
                                    <br />
                                    <br />
                                    @(Html.Kendo().TreeView()
                                .Name("equipmentCategoryTreeView")
                                .TemplateId("equipmentCategoryTemplate")
                                .DataTextField("NewName")
                                .LoadOnDemand(false)
                                .Events(e => e.Change("equipmentCategorySelected").Drop("equipmentCategoryDropped").DataBound("equipmentCategoryLoaded"))
                                .DataSource(datasource => datasource
                                    .Events(e => e.RequestEnd("categoriesLoaded"))
                                    .Model(m => m.Id("EquipmentCategoryId").HasChildren("HasChildren").Children("Children"))
                                    .Read(r => r.Action("GetEquipmentCategories", "Admin"))
                                    .ServerFiltering(false)))
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fa fa-tags"></i> Equipment Items (<span data-bind="text: totalEquipmentItems"></span>)</h6>
                                </div>
                                @if (!Model.EquipmentShipmentId.HasValue || Model.IsPending && (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsLogisticsAdmin() || Html.IsAssetAdmin() || Html.IsMaintenanceAdmin() || Html.IsSeniorFieldEngineer() || Html.IsJuniorFieldEngineer() || Html.IsFieldEngineer())) {
                                    @(Html.Kendo().Grid<EquipmentItemModel>()
                                    .Name("equipmentItemGrid")
                                    .Columns(columns => {
                                        columns.Bound(c => c.EquipmentPackingListProjectName).Title("Assigned To").ClientTemplate("#=EquipmentPackingListProjectName ? NewProjectName : 'N/A'#").Width(150);
                                        columns.Bound(c => c.EquipmentItemName).Title("Item Number").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#?packingListId=" + Model.EquipmentPackingListId + "'>#=EquipmentItemName#</a>").Width(150);

                                        columns.Bound(c => c.ReceivedDate).Title("Received Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                        columns.Bound(c => c.PurchasedDate).Title("Purchased Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                        columns.Bound(c => c.CurrencyName).Title("Currency").Width(100).Hidden(true);
                                        columns.Bound(c => c.Price).Format("{0:n2}").Width(100).Hidden(true);
                                        columns.Bound(c => c.DepreciatedPrice).Title("Net Book Value").Hidden(true).Format("{0:n2}").Width(100);
                                        columns.Bound(c => c.PointsPerMonth).Title("Points Per Month").Hidden(true).Width(150);
                                        columns.Bound(c => c.PointsPerRun).Title("Points Per Run").Hidden(true).Width(150);
                                        columns.Bound(c => c.PointsPerMove).Title("Points Per Move").Hidden(true).Width(150);
                                        columns.Bound(c => c.DivisionName).Title("Division").Hidden(true).Width(150);
                                        columns.Bound(c => c.TrackedNonAssetItem).Title("Tracked Non-Asset Item").ClientTemplate("#if(TrackedNonAssetItem){#Yes#}else{#No#}#").Hidden(true).Width(100);

                                        columns.Bound(c => c.CurrentClientLocationName).Title("Current Location").ClientTemplate("#=CurrentClientLocationName ? CurrentClientLocationName : 'Not Yet Accepted'#").Width(100);
                                        columns.Bound(c => c.CustomStatusCode).Title("Custom Status").Width(100);
                                        columns.Bound(c => c.Points).Title("Current Points").Width(100);
                                        columns.Bound(c => c.MaintenanceScheduleDetail).Title("Maintenance Schedule Date(s)").ClientTemplate("#if(MaintenanceScheduleDaysAlert){#<a style='color:\\#E31E33'href='\\#' onclick='scheduleDates(#=EquipmentItemId#)'>#=MaintenanceScheduleDetail#</a>#}else if(MaintenanceScheduleDetail != 'N/A'){#<a href='\\#' onclick='scheduleDates(#=EquipmentItemId#)'>#=MaintenanceScheduleDetail#</a>#}else{##=MaintenanceScheduleDetail##}#").Width(125);
                                        columns.Bound(c => c.MRCount).Title("Active MRs").ClientTemplate("#if(MaintenanceRecordCount){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='maintenanceRecordCount(#=EquipmentItemId#)'>#=MRCount#</a>#} else {##=MRCount##}#").Width(75);
                                        columns.Bound(c => c.EquipmentInfo).Title("Info").Width(100);
                                        columns.Bound(c => c.AllStatusDescription).Title("Status").Encoded(false).Filterable(f => f.Operators(o => o.ForString(str => str.Clear().Contains("Contains").DoesNotContain("Does not contain")))).Width(150);
                                    })
                                    .ToolBar(toolbar => {
                                         toolbar.Custom().ClientTemplate("<button id='addPackingListBtn' class='btn btn-primary' data-bind='click:populateItemGridClick'>Add to Packing List</button>");
                                         toolbar.Search();
                                    })
                                    .Selectable(selectable => selectable.Mode(GridSelectionMode.Multiple))
                                    .Events(e => e.DataBound("updateEquipmentTotals"))
                                    .AutoBind(false)
                                    .ColumnMenu(c => c.Columns(true))
                                    .Filterable()
                                    .Sortable()
                                    .Search(s => {
                                        s.Field(o => o.EquipmentItemName, "contains");
                                    })
                                    .Groupable()
                                    .Scrollable(scrollable => scrollable.Endless(true).Height(535))
                                    .Resizable(resize => resize.Columns(true))
                                    .Reorderable(reorder => reorder.Columns(true))
                                    .DataSource(dataSource => dataSource
                                        .Ajax()
                                        .Model(model => {
                                            model.Id(m => m.EquipmentItemId);
                                        })
                                        .PageSize(100)
                                        .Read(read => read.Action("GetEquipmentItemsNotInPackingListId", "Logistics").Data("equipmentItemData"))))
                            } else {
                                    @(Html.Kendo().Grid<EquipmentItemModel>()
                                    .Name("equipmentItemGrid")
                                    .Columns(columns => {
                                        columns.Bound(c => c.EquipmentPackingListProjectName).Title("Assigned To").ClientTemplate("#=EquipmentPackingListProjectName ? NewProjectName : 'N/A'#").Width(150);
                                        columns.Bound(c => c.EquipmentItemName).Title("Item Number").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#?packingListId=" + Model.EquipmentPackingListId + "'>#=EquipmentItemName#</a>").Width(150);

                                        columns.Bound(c => c.ReceivedDate).Title("Received Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                        columns.Bound(c => c.PurchasedDate).Title("Purchased Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                        columns.Bound(c => c.CurrencyName).Title("Currency").Width(100).Hidden(true);
                                        columns.Bound(c => c.Price).Format("{0:n2}").Width(100).Hidden(true);
                                        columns.Bound(c => c.DepreciatedPrice).Title("Net Book Value").Hidden(true).Format("{0:n2}").Width(100);
                                        columns.Bound(c => c.PointsPerMonth).Title("Points Per Month").Hidden(true).Width(150);
                                        columns.Bound(c => c.PointsPerRun).Title("Points Per Run").Hidden(true).Width(150);
                                        columns.Bound(c => c.PointsPerMove).Title("Points Per Move").Hidden(true).Width(150);
                                        columns.Bound(c => c.DivisionName).Title("Division").Hidden(true).Width(150);                                        
                                        columns.Bound(c => c.TrackedNonAssetItem).Title("Tracked Non-Asset Item").ClientTemplate("#if(TrackedNonAssetItem){#Yes#}else{#No#}#").Hidden(true).Width(100);

                                        columns.Bound(c => c.CurrentClientLocationName).Title("Current Location").ClientTemplate("#=CurrentClientLocationName ? CurrentClientLocationName : 'Not Yet Accepted'#").Width(100);
                                        columns.Bound(c => c.CustomStatusCode).Title("Custom Status").Width(100);
                                        columns.Bound(c => c.Points).Title("Current Points").Width(100);
                                        columns.Bound(c => c.MaintenanceScheduleDetail).Title("Maintenance Schedule Date(s)").ClientTemplate("#=MaintenanceScheduleDetail#").Width(125);
                                        columns.Bound(c => c.EquipmentInfo).Title("Info").Width(100);
                                        columns.Bound(c => c.AllStatusDescription).Title("Status").Encoded(false).Filterable(f => f.Operators(o => o.ForString(str => str.Clear().Contains("Contains").DoesNotContain("Does not contain")))).Width(150);

                                    })
                                    .ToolBar(toolbar => {
                                         toolbar.Search();
                                     })
                                    .AutoBind(false)
                                    .ColumnMenu(c => c.Columns(true))
                                    .Events(e => e.DataBound("updateEquipmentTotals"))
                                    .Filterable()
                                    .Selectable(selectable => selectable.Mode(GridSelectionMode.Multiple))
                                    .Sortable()
                                    .Search(s => {
                                        s.Field(o => o.EquipmentItemName, "contains");
                                        })
                                    .Groupable()
                                    .Scrollable(scrollable => scrollable.Endless(true).Height(535))
                                    .Resizable(resize => resize.Columns(true))
                                    .Reorderable(reorder => reorder.Columns(true))
                                    .DataSource(dataSource => dataSource
                                    .Ajax()
                                    .Model(model => {
                                        model.Id(m => m.EquipmentItemId);
                                    })
                                    .PageSize(100)
                                .Read(read => read.Action("GetEquipmentItemsNotInPackingListId", "Logistics").Data("equipmentItemData"))))
                            }
                            </div>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-12">
                            <div>
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fa fa-tags"></i> Equipment Packing List Items (<span data-bind="text: totalPackingListItems"></span>)</h6>
                                </div>
                                @if (!Model.EquipmentShipmentId.HasValue || Model.IsPending) {
                                    @(Html.Kendo().Grid<EquipmentPackingListItemModel>()
                                .Name("equipmentPackingListItemGrid")
                                .Columns(columns => {
                                    columns.Bound(c => c.EquipmentItemName).Title("Item Number").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#?packingListId=" + Model.EquipmentPackingListId + "'>#=EquipmentItemName#</a>").Width(150);
                                    columns.Bound(c => c.EquipmentItem.ReceivedDate).Title("Manufacture Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                    columns.Bound(c => c.EquipmentItem.PurchasedDate).Title("Purchased Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                    columns.Bound(c => c.EquipmentItem.CurrencyName).Title("Currency").Width(100).Hidden(true);
                                    columns.Bound(c => c.EquipmentItem.Price).Format("{0:n2}").Width(100).Hidden(true);
                                    columns.Bound(c => c.EquipmentItem.DepreciatedPrice).Title("Net Book Value").Hidden(true).Format("{0:n2}").Width(100);
                                    columns.Bound(c => c.EquipmentItem.PointsPerMonth).Title("Points Per Month").Hidden(true).Width(150);
                                    columns.Bound(c => c.EquipmentItem.PointsPerRun).Title("Points Per Run").Hidden(true).Width(150);
                                    columns.Bound(c => c.EquipmentItem.PointsPerMove).Title("Points Per Move").Hidden(true).Width(150);
                                    columns.Bound(c => c.EquipmentItem.DivisionName).Title("Division").Hidden(true).Width(150);
                                    columns.Bound(c => c.EquipmentItem.TrackedNonAssetItem).Title("Tracked Non-Asset Item").ClientTemplate("#if(EquipmentItemTrackedNonAssetItem){#Yes#}else{#No#}#").Hidden(true).Width(100);
                                    columns.Bound(c => c.CurrentClientLocationName).Title("Current Location").Width(100);
                                    columns.Bound(c => c.EquipmentItem.CustomStatusCode).Title("Custom Status").Width(100).Hidden(true);
                                    columns.Bound(c => c.EquipmentItem.Points).Title("Current Points").Width(100).Hidden(true);
                                    columns.Bound(c => c.MaintenanceScheduleDetail).Title("Maintenance Schedule Date(s)").ClientTemplate("#if(MaintenanceScheduleDaysAlert){#<a style='color:\\#E31E33'href='\\#' onclick='scheduleDates(#=EquipmentItemId#)'>#=MaintenanceScheduleDetail#</a>#}else if(MaintenanceScheduleDetail != 'N/A'){#<a href='\\#' onclick='scheduleDates(#=EquipmentItemId#)'>#=MaintenanceScheduleDetail#</a>#}else{##=MaintenanceScheduleDetail##}#").Width(125);
                                    columns.Bound(c => c.MRCount).Title("Active MRs").ClientTemplate("#if(MaintenanceRecordCount){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='maintenanceRecordCount(#=EquipmentItemId#)'>#=MRCount#</a>#} else {##=MRCount##}#").Width(75);
                                    columns.Bound(c => c.EquipmentItem.EquipmentInfo).Title("Info").Width(100);
                                    columns.Bound(c => c.HasDangerousGoods).Title("Dangerous Goods").ClientTemplate("#if(HasDangerousGoods){# <span class='badge badge-warning' style='font-size:14px'><i class='fa fa-warning' style='margin-right:0px' title='This item contains dangerous goods'></i></span> YES #}else{# #='NO'# #}#").Width(80);
                                    columns.Bound(c => c.AllStatusDescription).Title("Status").Encoded(false).Filterable(f => f.Operators(o => o.ForString(str => str.Clear().Contains("Contains").DoesNotContain("Does not contain")))).Width(150);

                                    columns.Command(command => { 
                                        command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}).Visible("canDeleteEquipmentPackingListItemGrid"); 
                                    }).Width(100);
                                })
                        .Events(e => e.DataBound("updatePackingListItemsTotals"))
                        .Editable(editable => editable.Mode(GridEditMode.InLine))
                        .ColumnMenu(c => c.Columns(true))
                        .Filterable()
                        .Sortable()
                        .Groupable()
                        .Scrollable(s => s.Height(500))
                        .Resizable(resize => resize.Columns(true))
                        .Reorderable(reorder => reorder.Columns(true))
                        .DataSource(dataSource => dataSource
                        .Ajax()
                        .ServerOperation(false)
                        .Events(e => e.RequestEnd("packingListItemsRequestEnd"))
                        .Model(model => {
                            model.Id(m => m.EquipmentPackingListItemId);
                        })
                            .Read(read => read.Action("GetEquipmentPackingListItems", "Logistics", new { @equipmentPackingListId = Model.EquipmentPackingListId }))
                            .Destroy(destroy => destroy.Action("DeleteEquipmentPackingListItem", "Logistics"))))
                            } else {
                                    @(Html.Kendo().Grid<EquipmentPackingListItemModel>()
                                .Name("equipmentPackingListItemGrid")
                                .Columns(columns => {
                                    columns.Bound(c => c.EquipmentItemName).Title("Item Number").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#?packingListId=" + Model.EquipmentPackingListId + "'>#=EquipmentItemName#</a>").Width(150);
                                    columns.Bound(c => c.EquipmentItem.ReceivedDate).Title("Manufacture Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                    columns.Bound(c => c.EquipmentItem.PurchasedDate).Title("Purchased Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                    columns.Bound(c => c.EquipmentItem.CurrencyName).Title("Currency").Width(100).Hidden(true);
                                    columns.Bound(c => c.EquipmentItem.Price).Format("{0:n2}").Width(100).Hidden(true);
                                    columns.Bound(c => c.EquipmentItem.DepreciatedPrice).Title("Net Book Value").Hidden(true).Format("{0:n2}").Width(100);
                                    columns.Bound(c => c.EquipmentItem.PointsPerMonth).Title("Points Per Month").Hidden(true).Width(150);
                                    columns.Bound(c => c.EquipmentItem.PointsPerRun).Title("Points Per Run").Hidden(true).Width(150);
                                    columns.Bound(c => c.EquipmentItem.PointsPerMove).Title("Points Per Move").Hidden(true).Width(150);
                                    columns.Bound(c => c.EquipmentItem.DivisionName).Title("Division").Hidden(true).Width(150);
                                    columns.Bound(c => c.EquipmentItemTrackedNonAssetItem).Title("Tracked Non-Asset Item").ClientTemplate("#if(EquipmentItemTrackedNonAssetItem){#Yes#}else{#No#}#").Hidden(true).Width(100);
                                    columns.Bound(c => c.CurrentClientLocationName).Title("Current Location").Width(100);
                                    columns.Bound(c => c.EquipmentItem.CustomStatusCode).Title("Custom Status").Width(100).Hidden(true);
                                    columns.Bound(c => c.EquipmentItem.Points).Title("Current Points").Width(100).Hidden(true);
                                    columns.Bound(c => c.MaintenanceScheduleDetail).Title("Maintenance Schedule Date(s)").ClientTemplate("#if(MaintenanceScheduleDaysAlert){#<a style='color:\\#E31E33'href='\\#' onclick='scheduleDates(#=EquipmentItemId#)'>#=MaintenanceScheduleDetail#</a>#}else if(MaintenanceScheduleDetail != 'N/A'){#<a href='\\#' onclick='scheduleDates(#=EquipmentItemId#)'>#=MaintenanceScheduleDetail#</a>#}else{##=MaintenanceScheduleDetail##}#").Width(125);
                                    columns.Bound(c => c.MRCount).Title("Active MRs").ClientTemplate("#if(MaintenanceRecordCount){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='maintenanceRecordCount(#=EquipmentItemId#)'>#=MRCount#</a>#} else {##=MRCount##}#").Width(75);
                                    columns.Bound(c => c.EquipmentItem.EquipmentInfo).Title("Info").Width(100);
                                    columns.Bound(c => c.HasDangerousGoods).Title("Dangerous Goods").ClientTemplate("#if(HasDangerousGoods){# <span class='badge badge-warning' style='font-size:14px'><i class='fa fa-warning' style='margin-right:0px' title='This item contains dangerous goods'></i></span> YES #}else{# #='NO'# #}#").Width(80);
                                    columns.Bound(c => c.AllStatusDescription).Title("Status").Encoded(false).Filterable(f => f.Operators(o => o.ForString(str => str.Clear().Contains("Contains").DoesNotContain("Does not contain")))).Width(150);
                                })
                        .ColumnMenu(c => c.Columns(true))
                        .Events(e => e.DataBound("updatePackingListItemsTotals"))
                        .Filterable()
                        .Groupable()
                        .Sortable()
                        .Scrollable(s => s.Height(500))
                        .Resizable(resize => resize.Columns(true))
                        .Reorderable(reorder => reorder.Columns(true))
                        .Editable(editable => editable.Mode(GridEditMode.InLine))
                        .DataSource(dataSource => dataSource
                        .Ajax()
                        .ServerOperation(false)
                        .Events(e => e.RequestEnd("packingListItemsRequestEnd"))
                        .Model(model => {
                            model.Id(m => m.EquipmentPackingListItemId);
                        })
                            .Read(read => read.Action("GetEquipmentPackingListItems", "Logistics", new { @equipmentPackingListId = Model.EquipmentPackingListId }))
                            .Destroy(destroy => destroy.Action("DeleteEquipmentPackingListItem", "Logistics"))))
                            }
                            </div>
                        </div>
                    </div>
                </div>
        

            </text>);   
   };

}).Render();
}
@Html.HiddenFor(m => m.Created)



@(Html.Kendo().Window()
.Name("scheduleDatesWindow")
.Width(1000)
.Height(500)
.Title("Maintenance Schedule Dates")
.Visible(false)
.Modal(true)
.Events(e => e.Open("scheduleDatesWindowOpened"))
.Content(@<text>
        @(Html.Kendo().Grid<EquipmentItemMaintenanceScheduleModel>()
           .Name("equipmentItemMaintenanceScheduleGrid")
           .Columns(columns => {
               columns.Bound(c => c.MaintenanceBlueprintName).Title("Maintenance Blueprint").Width(150);
               columns.Bound(c => c.LastDate).Title("Last").Format(DateConstants.DateFormat).Width(125);
               columns.Bound(c => c.RecurringDays).Title("Recurring Days").Width(125).Visible(!GlobalSettings.IsWellsense);
               columns.Bound(c => c.RecurringMonths).Title("Recurring Months").Width(125).Visible(GlobalSettings.IsWellsense);
               columns.Bound(c => c.StartDate).Title("Next").ClientTemplate("#=CompanyLocationId!=null? 'N/A' : StartDateOnly #").Width(125);
           })
           .ColumnMenu(c => c.Columns(true))
           .Sortable()
           .AutoBind(false)
           .Scrollable(s => s.Height(400))
           .Resizable(resize => resize.Columns(true))
           .Reorderable(reorder => reorder.Columns(true))
           .DataSource(dataSource => dataSource
           .Ajax()
           .ServerOperation(false)
           .Model(model => {
               model.Id(m => m.EquipmentItemMaintenanceScheduleId);
           })
           .Read(read => read.Action("GetEquipmentItemMaintenanceSchedules", "Logistics").Data("equipmentItemMaintenanceScheduleData")))) </text>
         ))
@(Html.Kendo().Window()
.Name("maintenanceRecordWindow")
.Width(1000)
.Height(500)
.Title("Maintenance Record")
.Visible(false)
.Modal(true)
.Events(e => e.Open("maintenanceRecordWindowOpened"))
.Content(@<text>
        @(Html.Kendo().Grid<MaintenanceRecordModel>()
    .Name("maintenanceRecordGrid")
    .Columns(columns => {
        columns.Bound(c => c.Number).Title("Maintenancen Record").ClientTemplate("<a href='" + @Url.Action("EditMaintenanceRecord", "Maintenance", new { @id = "" }) + "/#=MaintenanceRecordId#'>#=Number#</a>");
        columns.Bound(c => c.MaintenanceBlueprintId).Title("Maintenancen Blueprint").ClientTemplate("<a href='" + @Url.Action("EditMaintenanceBlueprint", "Admin", new { @id = "" }) + "/#=MaintenanceBlueprintId#'>#=MaintenanceBlueprintName#</a>");
        columns.Bound(c => c.EquipmentItemName).Title("Equipment Item").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#'>#=EquipmentItemName#</a>");
        columns.Bound(c => c.PriorityDescription).Title("Priority");
        columns.Bound(c => c.UserName).Title("Created By").Hidden(true);
        columns.Bound(c => c.Created).Title("Created").Format(DateConstants.DateFormat);
        columns.Bound(c => c.Modified).Title("Modified").Format(DateConstants.DateFormat).Hidden(true);
        columns.Bound(c => c.StatusDescription).Title("Status").ClientTemplate("<span class='badge' style='background:#=StatusColour#;color:#=StatustextColour#'>#=StatusDescription#</span>");
    })
       .ColumnMenu(c => c.Columns(true))
       .Events(e => e.DataBound("updatedMaintenanceRecordGrid"))
       .Filterable()
       .Sortable()
       .Groupable()
       .Scrollable(s => s.Height(532))
       .Resizable(resize => resize.Columns(true))
       .Reorderable(reorder => reorder.Columns(true))
    .DataSource(dataSource => dataSource
    .Ajax()
    .ServerOperation(false)
    .Model(model => {
        model.Id(m => m.MaintenanceRecordId);
    })
    .Read(read => read.Action("GetMaintenanceRecordByEquipmentItemId", "Maintenance").Data("maintenanceRecordData"))))</text>
         ))
</div>
    <script>

        $(document).ready(function () {
            kendo.bind(document.body.children, viewModel);
            loadPackingEquipmentItemGrid();
            loadEquipmentPackingListItemGrid();
        });

        function loadPackingEquipmentItemGrid() {
            var grid = $("#equipmentItemGrid").data("kendoGrid");
            if (grid) {
                var options = localStorage["packingEquipmentItemGrid"];
                var toolBar = $("#equipmentItemGrid .k-grid-toolbar").html();
                viewModel.set("initialPackingEquipmentItemGrid", kendo.stringify(grid.getOptions()));
                if (options) {
                    grid.setOptions(JSON.parse(options));
                    $("#equipmentItemGrid .k-grid-toolbar").html(toolBar);
                    $("#equipmentItemGrid .k-grid-toolbar").addClass("k-grid-top");
                }
            }
        }
        function loadEquipmentPackingListItemGrid() {
            var grid = $("#equipmentPackingListItemGrid").data("kendoGrid");
            if (grid) {
                var options = localStorage["equipmentPackingListItemGrid"];
                var toolBar = $("#equipmentPackingListItemGrid .k-grid-toolbar").html();
                viewModel.set("initialEquipmentPackingListItemGrid", kendo.stringify(grid.getOptions()));
                if (options) {
                    grid.setOptions(JSON.parse(options));
                    $("#equipmentPackingListItemGrid .k-grid-toolbar").html(toolBar);
                    $("#equipmentPackingListItemGrid .k-grid-toolbar").addClass("k-grid-top");
                }
            }
        }

        // function saveEquipmentGrid(e) {
        //     setTimeout(function () {
        //         var grid = $("#equipmentItemGrid").data("kendoGrid");
        //         localStorage["packingEquipmentItemGrid"] = kendo.stringify(grid.getOptions());
        //     }, 10);
        // }

        // function saveEquipmentPackingListItemGrid(e) {
        //     setTimeout(function () {
        //         var grid = $("#equipmentPackingListItemGrid").data("kendoGrid");
        //         localStorage["equipmentPackingListItemGrid"] = kendo.stringify(grid.getOptions());
        //     }, 10);
        // }

        function packingListItemsRequestEnd(e) {
            if (e.type == "destroy") {
                refreshEquipmentPackingListItems();
                refreshEquipmentItems();
            }
        }


        function equipmentItemMaintenanceScheduleData() {
            return {
                eId: viewModel.get("selectedEquipmentItemId")
            }
        }

        function scheduleDates(equipmentItemId) {
            viewModel.set("selectedEquipmentItemId", equipmentItemId);

            $("#scheduleDatesWindow").data("kendoWindow").center().open();
        }

        function scheduleDatesWindowOpened() {
            var equipmentItemMaintenanceScheduleGrid = $("#equipmentItemMaintenanceScheduleGrid").data("kendoGrid");
            equipmentItemMaintenanceScheduleGrid.dataSource.read();
        }

        function maintenanceRecordData() {
            return {
                equipId: viewModel.get("selectedEquipmentItemId")
            }
        }

        function maintenanceRecordCount(equipmentItemId) {
            viewModel.set("selectedEquipmentItemId", equipmentItemId);

            $("#maintenanceRecordWindow").data("kendoWindow").center().open();
        }

        function maintenanceRecordWindowOpened() {
            var maintenanceRecordGrid = $("#maintenanceRecordGrid").data("kendoGrid");
            maintenanceRecordGrid.dataSource.read();
        }

        function updatedMaintenanceRecordGrid(e) {
            var maintenanceRecordGrid = $("#maintenanceRecordGrid").data("kendoGrid");

            var mrData = maintenanceRecordGrid.dataSource.data();

            var rows = e.sender.tbody.children();

            for (var j = 0; j < rows.length; j++) {
                var row = $(rows[j]);
                var dataItem = e.sender.dataItem(row);

                if (dataItem.get("Fail") == true) {
                    row.addClass("fail");
                }
            }
        }

        function equipmentItemData() {
            var equipmentCategory = viewModel.get("selectedEquipmentCategory");
            var equipmentPackingListId = viewModel.get("equipmentPackingListId");
            return {
                equipmentCategoryId: equipmentCategory ? equipmentCategory.EquipmentCategoryId : "",
                equipmentPackingListId: equipmentPackingListId
            };
        }

        function updateEquipmentTotals() {
            $("#resetPackingEquipmentItemsGrid").click(function (e) {
                e.preventDefault();
                viewModel.set("selectedEquipmentItemId", "")
                resetCustomGridView('equipmentItemGrid', 'packingEquipmentItemGrid', 'initialPackingEquipmentItemGrid');
                $(`#equipmentItemGrid`).data("kendoGrid").dataSource.read();
                kendo.bind($("#equipmentItemGrid"), viewModel);
            });

            var equipmentItemGrid = $("#equipmentItemGrid").data("kendoGrid");
            var totalEquipmentItems = equipmentItemGrid.dataSource.total();
            viewModel.set("totalEquipmentItems", totalEquipmentItems);

            var equipmentData = equipmentItemGrid.dataSource.data();

            $.each(equipmentData, function (i, item) {
                if (item.MaintenanceScheduleDaysAlert) {
                    $('tr[data-uid="' + item.uid + '"] td:nth-child(15)').css("color", "#E31E33 !important");
                }
                if (item.MaintenanceSchedulePointsAlert) {
                    $('tr[data-uid="' + item.uid + '"] td:nth-child(16)').css("color", "#E31E33 !important");
                }
                if (item.Fail) {
                    $('tr[data-uid="' + item.uid + '"] td:nth-child(19)').css("background-color", "#EA5C6B !important");
                }
            });
        }

        function updatePackingListItemsTotals() {
            $("#resetEquipmentPackingListItemGrid").click(function (e) {
                e.preventDefault();
                resetGridView('equipmentPackingListItemGrid', 'initialEquipmentPackingListItemGrid');
            });
            var equipmentPackingListItemGrid = $("#equipmentPackingListItemGrid").data("kendoGrid");
            var totalPackingListItems = equipmentPackingListItemGrid.dataSource.total();
            viewModel.set("totalPackingListItems", totalPackingListItems);

           var equipmentData = equipmentPackingListItemGrid.dataSource.data();

            var dangerousGoods = false;
        $.each(equipmentData, function (i, item) {
            if (item.HasDangerousGoods) {
                dangerousGoods = true;
            }
        });

            if(!$.cookie('equipmentCategory')){
                refreshEquipmentItems();
            }
        viewModel.set("dangerousGoods", dangerousGoods);
        }

        @* function handleGroups(groups) {
            for (var i = 0; i < groups.length; i++) {
                var gr = groups[i];
                offsetDateFields(gr); //handle the Key variable as well
                if (gr.HasSubgroups) {
                    handleGroups(gr.Items)
                } else {
                    loopRecords(gr.Items);
                }
            }
        } *@

        @* function loopRecords(persons) {
            for (var i = 0; i < persons.length; i++) {
                var person = persons[i];
                offsetDateFields(person);
            }
        } *@

        @* function offsetDateFields(obj) {
            for (var name in obj) {
                var prop = obj[name];
                if (typeof (prop) === "string" && prop.indexOf("/Date(") == 0) {
                    obj[name] = prop.replace(/\d+/, function (n) {
                        var offsetMiliseconds = new Date(parseInt(n)).getTimezoneOffset() * 60000;
                        return parseInt(n) + offsetMiliseconds
                    });
                }
            }
        } *@

        function refreshEquipmentCategories() {
            var equipmentCategory = $("#equipmentCategoryTreeView").data("kendoTreeView");
            equipmentCategory.dataSource.read();

            var path = viewModel.get("selectedEquipmentCategoryPath");
            equipmentCategory.expandPath(path);
        }

        function equipmentCategorySelected(e) {
            var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
            var node = equipmentCategoryTreeView.select();
            var selectedEquipmentCategory = equipmentCategoryTreeView.dataItem(node);

            if (selectedEquipmentCategory) {
                $.removeCookie('equipmentCategory');
                $.cookie('equipmentCategory', selectedEquipmentCategory.EquipmentCategoryId, { expires: 7, path: '/' });
                viewModel.set("selectedEquipmentCategory", selectedEquipmentCategory);

            var grid = $("#equipmentItemGrid").data("kendoGrid");
                grid.dataSource.options.endless = null;
                grid._endlessPageSize = grid.dataSource.options.pageSize;
                grid.dataSource.pageSize(grid.dataSource.options.pageSize);
            }
        }

        function equipmentCategoryLoaded() {
        $(".k-treeview-item").click(function (e) {
                var equipmentCategoryTree = $("#equipmentCategoryTreeView").data("kendoTreeView");
                var equipmentCategorySelected = equipmentCategoryTree.select();

                if (equipmentCategorySelected && $(e.currentTarget).attr("date-uid") == $(equipmentCategorySelected.context).attr("data-uid")) {
                    equipmentCategoryTree.select($());
                }
            });
        }

        function equipmentCategoryDropped(e) {
            var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
            var equipmentCategory = equipmentCategoryTreeView.dataItem(e.sourceNode);

            var parentEquipmentCategory = "";

            if (e.dropPosition == "over") {
                parentEquipmentCategory = equipmentCategoryTreeView.dataItem(e.destinationNode);
            } else {
                parentEquipmentCategory = equipmentCategoryTreeView.dataItem(equipmentCategoryTreeView.parent(e.destinationNode));
            }

            $.ajax({
                type: "POST",
                url: "@Url.Action("UpdateEquipmentCategoryParent", "Admin")",
                data: {
                    equipmentCategoryId: equipmentCategory.EquipmentCategoryId,
                    parentEquipmentCategoryId: parentEquipmentCategory ? parentEquipmentCategory.EquipmentCategoryId : ""
                },
                success: function (data) {
                    viewModel.set("selectedEquipmentCategory", equipmentCategory);
                    console.log(JSON.stringify(data));
                },
                dataType: "json"
            });
        }


        function refreshEquipmentItems() {
            var equipmentItemGrid = $("#equipmentItemGrid").data("kendoGrid");
            equipmentItemGrid.dataSource.read();
        }

        function refreshEquipmentPackingListItems() {
            var equipmentPackingListItemGrid = $("#equipmentPackingListItemGrid").data("kendoGrid");
            equipmentPackingListItemGrid.dataSource.read();
        }

        function refreshEquipmentPackingListItemsInDetails() {
            var equipmentPackingListItemGridInDetails = $("#equipmentPackingListItemGridInDetails").data("kendoGrid");
            equipmentPackingListItemGridInDetails.dataSource.read();
        }

        function canDeleteEquipmentPackingListItemGrid () {
               if( "@Html.IsGlobalAdmin()" === "True" ||
                    "@Html.IsOperationAdmin()" === "True" || 
                    "@Html.IsLogisticsAdmin()" === "True" || 
                    "@Html.IsAssetAdmin()" === "True" || 
                    "@Html.IsMaintenanceAdmin()" === "True" ||
                    "@Html.IsSeniorFieldEngineer()" === "True" || 
                    "@Html.IsJuniorFieldEngineer()" === "True" || 
                    "@Html.IsFieldEngineer()" === "True") {
                        return true;
                    }
                return false
        }

        $("#filterText").keyup(function (e) {

            let treeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
            let filterText = e.target.value;

            if (!filterText) {
                treeView.collapse(".k-treeview-item");
            } else {
                treeView.expand(".k-treeview-item");
                treeView.dataSource.filter(
                    { field: "NewName", operator: "contains", value: filterText }
                )
            }
        });

    function packingListPdf() {
        var element = $("#page");
        kendo.ui.progress(element, true);
        setTimeout(function () { kendo.ui.progress(element, false); }, 8000);
        $("<div id='dialog'></div>").kendoDialog({
            closable: false,
            title: '@Model.PackingListName',
            width: 500,
            buttonLayout: "normal",
            content: `The \'@Model.PackingListName\' document is been generated, please wait for it.`,
            actions: [{ text: "OK", cssClass: 'btn-primary' }]
        }).data("kendoDialog").open().center()
    }

        var viewModel = kendo.observable({
            dangerousGoods: false,
            equipmentPackingListId: "@Model.EquipmentPackingListId", selectedEquipmentCategory: "",
            selectedEquipmentCategoryPath: [],
            totalEquipmentItems: 0,
            selectedEquipmentItemId: 0,
            totalEquipmentPackingListItems: 0,
            signOffDetail: "@(Html.Raw(Html.Encode(!string.IsNullOrWhiteSpace(Model.SignOffDetails) ? Model.SignOffDetails.Replace(System.Environment.NewLine, @"\r\n").Replace("\"", "") : string.Empty)))",

            tabStripHeaderDetails: function () {
                return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Details </span>`;
            },
            tabStripHeaderManageListItems: function () {
                return `<span class="k-link"><i class="fa fa-tags mr-1"></i> Manage List Items </span>`;
            },
            isSignedOffDetailsValid: function () {
                var signedOffDetail = this.get("signOffDetail");

                return signedOffDetail;
            },

        deletePackingList: function () {
            var confirmDelete = confirm("Are you sure you wish to delete this packing list");
            var id = viewModel.get("equipmentPackingListId");

            if (confirmDelete) {
                $.ajax({
                    type: 'GET',
                    dataType: 'json',
                    url: `/Logistics/DeleteEquipmentPackingList/${id}`,
                    success: function (e) {
                        window.location.href = "/Logistics/PackingList";
                    }
                });
            }
        },

            signOff: function () {
                var confirmSignOff = confirm("Are you sure you wish to sign off this packing list");

                if (confirmSignOff) {
                    window.location.href = "@Url.Action("SignOffPackingList", "Logistics", new { @id = Model.EquipmentPackingListId })";
                }
            },

            signOff: function () {
                var confirmSignOff = confirm("Are you sure you wish to sign off this packing list");

                if (confirmSignOff) {
                    $.ajax({
                        type: 'POST',
                        dataType: 'json',
                        traditional: true,
                        url: "@Url.Action("SignOffPackingList", "Logistics", new { @id = Model.EquipmentPackingListId})",
                        data: {
                            id: "@Model.EquipmentPackingListId",
                        },
                        success: function () {
                            $("#editEquipmentPackingList").submit();
                        },
                        dataType: "json"
                    });
                }
            },

            expandAll: function () {
                var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
                equipmentCategoryTreeView.expand(".k-treeview-item");
            },
            collapseAll: function () {
                var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
                equipmentCategoryTreeView.collapse(".k-treeview-item");
            },

            populateItemGridClick: function () {
                $('#addPackingListBtn').prop('disabled', true);
                var equipmentItemGrid = $("#equipmentItemGrid").data("kendoGrid");

                var equipmentItemIds = [];

                equipmentItemGrid.select().each(function () {
                    var equipmentItem = equipmentItemGrid.dataItem($(this));

                    if (equipmentItem) {
                        equipmentItemIds.push(equipmentItem.EquipmentItemId);
                    }
                });

                $.ajax({
                    type: 'POST',
                    dataType: 'json',
                    traditional: true,
                    url: "@Url.Action("UpdatePackingListEquipmentItems", "Logistics")",
                    data: {
                        equipmentPackingListId: "@Model.EquipmentPackingListId",
                        equipmentItemIds: equipmentItemIds,
                        equipmentShipmentId: "@Model.EquipmentShipmentId"
                    },
                    success: function () {
                        $('#addPackingListBtn').prop('disabled', false);
                        refreshEquipmentPackingListItems();
                        refreshEquipmentItems();
                        refreshEquipmentPackingListItemsInDetails();
                    },
                    dataType: "json"
                });
            },
        });

    </script>
