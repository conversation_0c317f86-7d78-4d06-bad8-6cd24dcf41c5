﻿@model UserModel
<div class="container-fluid pl-0 pr-0">
    <div class="row mb-3">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">My Certificates</h6>
                </div>
                <div class="card-body">
                    <div class="card-list-item">
                        <span class="card-list-item-name">Expiring this month</span>
                        <span class="card-list-item-count pe-cursor" style="background: #DB8C8C" data-bind="click:thisMonthClick,text:totalExpiringThisMonth">0</span>
                    </div>
                    <div class="card-list-item">
                        <span class="card-list-item-name">Expiring within one month</span>
                        <span class="card-list-item-count" style="background: #DB8C8C" data-bind="click:oneMonthClick,text:totalExpiringOneMonth">0</span>
                    </div>
                    <div class="card-list-item">
                        <span class="card-list-item-name">Expiring within two months</span>
                        <span class="card-list-item-count" style="background: #DB8C8C" data-bind="click:twoMonthClick,text:totalExpiringTwoMonth">0</span>
                    </div>
                    <div class="card-list-item">
                        <span class="card-list-item-name">Expiring within three months</span>
                        <span class="card-list-item-count" style="background: #DB8C8C" data-bind="click:threeMonthClick,text:totalExpiringThreeMonth">0</span>
                    </div>
                    <div class="card-list-item d-flex justify-content-between">
                        <span class="card-list-item-name">Expiring this year</span>
                        <span class="card-list-item-count" style="background: #DB8C8C" data-bind="click:thisYearClick,text:totalExpiringThisYear">0</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @(Html.Kendo().Grid<PersonnelCertificateDocumentModel>()
       .Name("myPersonnelCertificateDocumentGrid")
       .Columns(c => {
           c.Template("#if(HasDocument){#<input type='checkbox' class='checkbox'/>#}#").Width(50);
           c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='" + @Url.Action("Index", "Document", new { @id = "#=DocumentId#" }) + "'>#=FileName ? FileName : ''#</a>").Width(250);
           c.Bound(p => p.CertificateCategoryName).EditorTemplateName("CertificateCategory").Title("Category").Width(250).ClientTemplate("#=CertificateCategoryName ? CertificateCategoryName : ''#").Width(150);
           c.Bound(p => p.Details).Title("Details").Width(200);
            c.Bound(p => p.AquisitionDate).EditorTemplateName("AquisitionDate").Title("Aquisition Date").Format(DateConstants.DateFormat).Width(150);
            c.Bound(p => p.ExpiryDate).EditorTemplateName("ExpiryDate").Title("Expiry Date").Format(DateConstants.DateFormat).Width(150);
            c.Bound(p => p.DateCreated).Title("Created").Format(DateConstants.DateFormat).Width(150);
       })
        .Events(e => e.ColumnReorder("saveCertificateGrid").ColumnResize("saveCertificateGrid").ColumnShow("saveCertificateGrid").ColumnHide("saveCertificateGrid"))
        .Events(e => e.DataBound("updateMyPersonnelCertificateDocumentGrid"))
        .Sortable()
        .ToolBar(t => {
            t.ClientTemplateId("myPersonnelCertificateHeaderTemplate");
        })
        .Resizable(r => r.Columns(true))
        .Reorderable(c => c.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .Filterable()
        .Groupable()
        .Excel(excel => excel
            .FileName(string.Format("Centerpoint_My_Personnel_Certificates_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Admin"))
        )
        .Scrollable(s => s.Height("auto"))
        .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Model(m => {
            m.Id(p => p.PersonnelCertificateDocumentId);
            })
        .Read(read => read.Action("GetAllMyPersonnelCertificates", "Home").Data("certificateData")))
    )
</div>

<script type="text/x-kendo-template" id="myPersonnelCertificateHeaderTemplate">
    <div class="d-flex justify-content-end w-100 toolbar-inline-padding">
        <button class="btn btn-primary btn-s margin-r" data-bind="click:emailWindow, enabled:documentIds.length">
            <i class="fa fa-envelope"></i>Email Certificates
        </button>
        <button class="btn btn-danger mr-2" id="resetMyPersonnelCertificateDocumentGrid">Reset Grid View</button>
        <button class="btn btn-success k-grid-excel">
            <i class="fa fa-file-excel"></i>
            Export
        </button>
    </div>
</script>

