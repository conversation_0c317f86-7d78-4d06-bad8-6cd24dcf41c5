﻿@model CompanyModel

@if (ViewBag.Revision == null && ViewBag.AnsaProjectId == null) {
    <h4>
        <i class="fa fa-tags"></i>
        Customer Assets
        (<span data-bind="text:totalCompanies"></span>)
    </h4>
    <hr />
    <div>
        @(Html.Kendo().Grid<CompanyModel>()
            .Name("companyGrid")
            .Columns(columns => {
                columns.Bound(c => c.Name).ClientTemplate("<a href='" + @Url.Action("ViewCustomerAssets", "Admin", new { @id = "" }) + "/#=CompanyId#'>#=Name#</a>").Width(200); ;
                columns.Bound(c => c.Categories).Title("Category").Width(200);
                columns.Bound(c => c.IsActive).Title("Active").ClientTemplate("#if(IsActive){#Yes#}else{#No#}#").Width(80);
                columns.Bound(c => c.CompanyLocationCount).Title("Locations").Width(80);
                columns.Bound(c => c.CompanyContactCount).Title("Contacts").Width(80);
                columns.Bound(c => c.CompanyFieldsCount).Title("Fields").Width(80);
                columns.Bound(c => c.CompanyWellsCount).Title("Wells").Width(80);
            })
            .Events(e => e.DataBound("updateCompanyTotals"))
            .ColumnMenu(c => c.Columns(true))
            .Filterable()
            .Sortable()
            .Groupable()
            .Reorderable(c => c.Columns(true))
            .Scrollable()
            .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Model(model => {
                    model.Id(m => m.CompanyId);
                })
                .Events(e => e.Error("onError"))
                .Read(read => read.Action("GetCompanies", "Admin"))
            )
        )
    </div>
}

<div class="header-container-single-item-with-hr mt-3 d-flex justify-content-between align-items-center">
    <h4 class="mb-0">
        <i class="fa fa-building"></i>
        @Model.Name
    </h4>
    @if (ViewBag.OpportunityId != null)
    {
        <a class="btn btn-info btn-sm" href="@Url.Action("Edit","Sales",new { @id = ViewBag.OpportunityId, @revision = ViewBag.Revision} )">
            <i class="fa fa-refresh"></i>
            Return to Opportunity
        </a>
    }
</div>
<hr />
<div class="row mt-2">
    <div class="col-md-4">
        <div class="header-container-single-item">
            <h4>
                Fields
                (<span data-bind="text:totalCompanyFields"></span>)
            </h4>
        </div>
        <div>
            <div>
                @(Html.Kendo().Grid<CompanyFieldModel>()
                    .Name("companyFieldGrid")
                    .Columns(c => {
                        c.Bound(p => p.Name);
                        c.Command(command => { 
                            command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                            command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
                        }).Width(200);
                    })
                    .ToolBar(t => {
                        t.ClientTemplateId("companyFieldGridheaderTemplate");
                    })
                    .Sortable()
                    .Filterable()
                    .Scrollable(s => s.Height(400))
                    .Resizable(c => c.Columns(true))
                    .ColumnMenu(c => c.Columns(true))
                    .Events(e => e.DataBound("updateCompanyFieldTotal"))
                    .DataSource(dataSource => dataSource
                        .Ajax()
                        .ServerOperation(false)
                        .Model(m => m.Id(p => p.CompanyFieldId))
                        .Read(read => read.Action("GetCompanyFields", "Admin", new { @companyId = Model.CompanyId }))
                        .Create(create => create.Action("UpdateCompanyField", "Admin", new { @compId = Model.CompanyId }))
                        .Update(update => update.Action("UpdateCompanyField", "Admin", new { @compId = Model.CompanyId }))
                        .Destroy(destroy => destroy.Action("DeleteCompanyField", "Admin"))
                    )
                )
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div>
            <div class="header-container-single-item">
                <h4>
                    Wells
                    (<span data-bind="text:totalCompanyWells"></span>)
                </h4>
            </div>
            <div>
                @(Html.Kendo().Grid<CompanyWellModel>()
                    .Name("companyWellGrid")
                    .Columns(c => {
                        c.Bound(p => p.Name).ClientTemplate("<a href='" + @Url.Action("EditCompanyWell", "Admin") + "/#=CompanyWellId#'>#=Name#</a>");
                        c.Bound(p => p.CompanyFieldName).Title("Field").ClientTemplate("#=CompanyFieldName#").ClientGroupHeaderTemplate("Field : #= value # (#= count#)");
                        c.Bound(p => p.MinimumId);
                        c.Bound(p => p.MaximumDeviation);
                        c.Bound(p => p.MaximumPressure).Hidden(true);
                        c.Bound(p => p.MaximumPressureUnits).Hidden(true);
                        c.Bound(p => p.MaximumTemperature).Hidden(true);
                        c.Bound(p => p.MaximumTemperatureDegrees).Hidden(true);
                        c.Bound(p => p.H2S).Hidden(true);
                        c.Bound(p => p.CO2).Hidden(true);
                        c.Bound(p => p.FluidTypes).Hidden(true);
                    })
                    .ToolBar(t => {
                        t.ClientTemplateId("companyWellGridHeaderTemplate");
                    })
                    .Sortable()
                    .Filterable()
                    .Scrollable(s => s.Height(400))
                    .Resizable(c => c.Columns(true))
                    .ColumnMenu(c => c.Columns(true))
                    .Events(e => e.Edit("onEditWell"))
                    .Events(e => e.DataBound("updateCompanyWellTotal"))
                    .DataSource(dataSource => dataSource
                        .Ajax()
                        .ServerOperation(false)
                        .Aggregates(aggregates => {
                            aggregates.Add(p => p.CompanyFieldName).Min().Max().Count();
                        })
                        .Group(group => group.Add(p => p.CompanyFieldName))
                        .Sort(sort => sort.Add(p => p.Name))
                        .Model(model => {
                            model.Id(m => m.CompanyWellId);
                        })
                        .Read(read => read.Action("GetCompanyWells", "Admin", new { @companyId = Model.CompanyId }))
                        .Destroy(destroy => destroy.Action("DeleteCompanyWell", "Admin"))
                    )
                )
            </div>
        </div>
    </div>
</div>
<script id="companyWellGridHeaderTemplate" type="text/x-kendo-template">
    @if (Model.CanAddAsset) {
        <a class="btn btn-primary" href="@Url.Action("AddCompanyWell", "Admin", new { @id = Model.CompanyId, @opportunityId = ViewBag.OpportunityId, @revision = ViewBag.Revision })">
        <i class="fa fa-plus"></i>Add New Well</a>
    }
</script>
<script id="companyFieldGridheaderTemplate" type="text/x-kendo-template">
    @if (Model.CanAddAsset) {
        <button class='btn btn-primary btn-sm' onclick='createFieldRow()'><i class='fa fa-plus'></i>Add Field</button>
    }
</script>

<environment include="Development">
    <script src="~/js/views/admin/viewCustomerAssets.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/admin/viewCustomerAssets.min.js" asp-append-version="true"></script>
</environment>


