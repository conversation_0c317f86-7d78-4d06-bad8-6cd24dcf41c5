﻿$("#expandButton").click(function () {
    var projectPanelBar = $("#projectPanelBar").data("kendoPanelBar");
    projectPanelBar.expand($("li", projectPanelBar.element));
});
$("#collapseButton").click(function () {
    var projectPanelBar = $("#projectPanelBar").data("kendoPanelBar");
    projectPanelBar.collapse($("li", projectPanelBar.element));
});

$(document).ready(function () {
    var closedGrid = $('#closedGrid').data("kendoGrid");
    var lostGrid = $('#lostGrid').data("kendoGrid")
    closedGrid.bind('dataBound', function (e) {
        this.element.find('.k-i-excel').remove();
    });
    lostGrid.bind('dataBound', function (e) {
        this.element.find('.k-i-excel').remove();
    });
});

//$('[data-toggle="tooltip"]').tooltip({
//    'placement': 'top'
//});
$(document).ready(function () {
    // getActiveOpportunityProjects();
    getActiveOpportunityProjectsNames();
});

function parseDate() {
    var now = new Date();
    return new Date(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), now.getUTCHours(), now.getUTCMinutes(), now.getUTCSeconds());

}

function onSelect(e) {
    if($("#projectPanelBar").data("kendoPanelBar")) {
        let isAllPanelsOpened = $("#projectPanelBar").children("li").length === ($("#projectPanelBar").children("li").filter(".k-state-active").length + 1) &&
                               !e.item.classList.contains("k-state-active");
        isAllPanelsOpened ? viewModel.set("expand", false) : null;
        let isAnyPanelsOpened =  $("#projectPanelBar").children("li").filter(".k-state-active").length === 1 && e.item.classList.contains("k-state-active");
        isAnyPanelsOpened ? viewModel.set("expand", true) : null;
    }
}

function getActiveOpportunityProjectsNames() {
    $.ajax({
        url: "/Lookup/GetProjectsNamesByStatus",
        dataType: "json",
        data: {
            status: viewModel.get("status")
        },
        success: function (result) {   
            result.map( item =>{ 
                item.children = true,
                item.content = " "
            } )
            viewModel.set("projects", result)

            var homogeneous = new kendo.data.HierarchicalDataSource({
                data: result,
                schema: {
                    model: {
                        id: "ProjectId",
                        hasChildren: "children",
                    }
                }
            });

            $("#projectPanelBar").kendoPanelBar({
                dataSource: homogeneous,
                template: "<div class='d-flex w-100 justify-content-between toolbar-inline-padding'><span>#=item.ProjectNameandObjectives#</span> <span>#=item.OppsName#</span></div>",
                select: onSelect,
                expand: onExpand
            });

            $("#projectPanelBar").data("kendoPanelBar").expand($("#projectPanelBar li:first"))
        }
    });
}


function onExpand(e) {
    let selectedItem = $("#projectPanelBar").data("kendoPanelBar").dataItem(e.item)
    if(selectedItem.hasExpanded) {
      return true
    }
    kendo.ui.progress($(e.item), true)
    $.ajax({
        url: `/Operation/GetProjectinfoById/${selectedItem.ProjectId}`,
        dataType: "json",
        success: function (result) {
            let projectTemplate = kendo.template($("#projectTemplateDetails").html()); // load detail template
            let projectHtml = projectTemplate(result); // fill template with data
            if(e && e.item) {
                if(!$(e.item).find(".k-content").length) {
                    $(e.item).append('<div class="k-panelbar-content k-content"></div>')
                }
                $(e.item).find(".k-panelbar-content.k-content").html(projectHtml)
            }
            selectedItem.hasExpanded = true
            kendo.ui.progress($(e.item), false) 
        }
    });        
   
}

jQuery(window).on("resize", function (event) {
    var chartDiv = jQuery("#jobsByServiceGrid");
    var chart = chartDiv.data("kendoChart");

    // Temporarily disable animation, yo!
    chart.options.transitions = false;

    // Temporarily hide, then set size of chart to container (which will naturally resize itself), then show it again
    chartDiv.css({ display: "none" });
    chartDiv.css({ width: chartDiv.parent().innerWidth(), display: "block" });

    chart.redraw();
});

function getLostProjects() {
    $("#lostGrid").data("kendoGrid").dataSource.read();
}

function getClosedProjects() {
    $("#closedGrid").data("kendoGrid").dataSource.read();
}

function updateClosedProjectTotal() {
    var closedGrid = $("#closedGrid").data("kendoGrid");
    var totalClosed = closedGrid.dataSource.total();
    viewModel.set("closedProjects", totalClosed);
}

function updateLostProjectTotal() {
    var lostGrid = $("#lostGrid").data("kendoGrid");
    var totalLost = lostGrid.dataSource.total();
    viewModel.set("lostProjects", totalLost);
}

var viewModel = kendo.observable({
    projects: [],
    expand: true,
    expandAll: function () {
        this.set("expand", false);
    },
    collapseAll: function () {
        this.set("expand", true);
    },
    totalProjects: function () {
        if (this.isActiveOpportunity()) {
            return this.activeOpportunityProjects();
        } else if (this.isClosed()) {
            return this.get("closedProjects");
        } else if (this.isLost) {
            return this.get("lostProjects");
        }
    },
    activeOpportunityProjects: function () {
        var projects = this.get("projects");
        return projects.length;
    },
    closedProjects: 0,
    lostProjects: 0,
    expandVisible: function () {
        var status = this.get("status");
        if (status == projectStatusConstant.opportunity || status == projectStatusConstant.active) {
            return true;
        } else {
            return false;
        }
    },

    activeBackground: function () {
        var status = this.get("status");
        if (status == projectStatusConstant.active) {
            return this.get("selectedBackground");
        } else {
            return this.get("normalBackground");
        }
    },
    opportunityBackground: function () {
        var status = this.get("status");
        if (status == projectStatusConstant.opportunity) {
            return this.get("selectedBackground");
        } else {
            return this.get("normalBackground");
        }
    },
    closedBackground: function () {
        var status = this.get("status");
        if (status == projectStatusConstant.closed) {
            return this.get("selectedBackground");
        } else {
            return this.get("normalBackground");
        }
    },
    lostBackground: function () {
        var status = this.get("status");
        if (status == projectStatusConstant.lost) {
            return this.get("selectedBackground");
        } else {
            return this.get("normalBackground");
        }
    },
    normalBackground: "#6eb6b4",
    selectedBackground: "#43ac6a",
    status: projectStatusConstant.active,
    statusDescription: statusDescription.active,
    opportunity: function () {
        viewModel.set("status", projectStatusConstant.opportunity);
        viewModel.set("statusDescription", statusDescription.opportunity);
        // getActiveOpportunityProjects();
        getActiveOpportunityProjectsNames();
    },
    active: function () {
        viewModel.set("status", projectStatusConstant.active);
        viewModel.set("statusDescription", statusDescription.active);
        // getActiveOpportunityProjects();
        getActiveOpportunityProjectsNames();
    },
    closed: function () {
        viewModel.set("status", projectStatusConstant.closed);
        viewModel.set("statusDescription", statusDescription.closed);
        getClosedProjects();
    },
    lost: function () {
        viewModel.set("status", projectStatusConstant.lost);
        viewModel.set("statusDescription", statusDescription.lost);
        getLostProjects();
    },
    isActiveOpportunity: function () {
        return this.get("status") == projectStatusConstant.active || this.get("status") == projectStatusConstant.opportunity;
    },
    isClosed: function () {
        return this.get("status") == projectStatusConstant.closed;
    },
    isLost: function () {
        return this.get("status") == projectStatusConstant.lost;
    }
});

kendo.bind(document.body.children, viewModel);