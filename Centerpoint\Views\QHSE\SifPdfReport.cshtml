﻿@{
    Layout = "~/Views/Shared/_PrintLayout.cshtml";
}
<style TYPE="text/css">
    .breakhere {
        page-break-after: always;
    }
</style>
@using Centerpoint.Common.Constants
@model ServiceImprovementPdfModel
<div class="logo">
    <div class="container pull-left">
        <a href="#"><img src="~/Content/img/logo.png" alt="Read Cased Hole"></a>
    </div>
</div>
<h2>@Model.NameTitleandStatus</h2>
<hr />
<div class="card breakhere" style="border:none">
    <div class="card-header" style="background: #67A2D5; color: #fff">
        <h6 class="mb-0">Details </h6>
    </div>
    <br />
    <div class="card-body">
        <div class="row">
            <div class="col-xs-4">
                <div class="form-group">
                    <h4>Creation Date</h4>
                    <br />
                    <p>@Model.CreatedDateOnly</p>
                </div>
                <div class="form-group">
                    <h4>Category</h4>
                    <br />
                    <p>@Model.SifCategoryDescription</p>
                </div>
            </div>
            <div class="col-xs-4">
                <div class="form-group">
                    <h4>Raised By</h4>
                    <br />
                    <p>@Model.CreatedByUserName</p>
                </div>

                <div class="form-group">
                    <h4>Sub Category</h4>
                    <br />
                    <p>@Model.SubCategories</p>
                </div>
            </div>
            <div class="col-xs-4">
                <div class="form-group">
                    <h4>SIF Admin</h4>
                    <br />
                    <p>@Model.SifAdminUserName</p>
                </div>
            </div>
        </div>
        <hr />
        <h2>Event Details</h2>
        <br />
        <div class="row">
            <div class="col-xs-8">
                <div class="form-group">
                    <h4> Associated Job</h4>
                    <br />
                    <p>@(string.IsNullOrWhiteSpace(Model.JobName) ? "Not Job Related" : Model.JobName)</p>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="form-group">
                    <h4>Title</h4>
                    <br />
                    <p>@Model.SifTitle</p>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-4">
                <div class="form-group">
                    <h4>Employee Base</h4>
                    <br />
                    <p>@Model.BaseCompanyLocationName</p>
                </div>
                <div class="form-group">
                    <h4>Company</h4>
                    <br />
                    <p>@Model.CompanyName</p>
                </div>
                <div class="form-group">
                    <h4>Equipment Related ?</h4>
                    <br />
                    <p>@Model.EquipmentRelatedDescription</p>
                </div>
            </div>
            <div class="col-xs-4">
                <div class="form-group">
                    <h4>Severity</h4>
                    <br />
                    <p>@Model.SeverityName</p>
                </div>
                <div class="form-group">
                    <h4>Client Location</h4>
                    <br />
                    <p>@Model.CompanyLocationName</p>
                </div>
                <div class="form-group">
                    <h4>Audit report number</h4>
                    <br />
                    <p>@Model.AuditReportNumber</p>
                </div>
            </div>
            <div class="col-xs-4">
                <div class="form-group">
                    <h4>Location</h4>
                    <br />
                    <p>@Model.ServiceImprovementLocationName</p>
                </div>
                <div class="form-group">
                    <h4>Client Contact</h4>
                    <br />
                    <p>@Model.CompanyContactName</p>
                </div>

                <div class="form-group">
                    <h4>Accident/Incident</h4>
                    <br />
                    <p>@Model.AccidentIncident</p>
                </div>
            </div>
        </div>
        <hr />
        <div class="row">
            <div class="col-xs-12">
                <div class="form-group">
                    <h4>Description</h4>
                    <br />
                    <p>@Model.Description</p>
                </div>
                <br />
                <div class="form-group">
                    <h4>Were any immediate corrective actions taken?</h4>
                    <br />
                    <p>@Model.ImmediateCorrectiveAction</p>
                </div>
                <br />
                @if (Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.Rejected) {
                <div class="form-group">
                    <h4>Reject Reason</h4>
                    <br />
                    <p>@Model.RejectReason</p>
                </div>
                }
            </div>
        </div>
    </div>
</div>

@if (Model.ServiceImprovementId.HasValue && (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.PendingApproval && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Draft && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Rejected && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Abandoned)) {
    <div class="card breakhere" style="border:none">
        <div class="card-header" style="background: #67A2D5; color: #fff">
            <h6 class="mb-0">Corrective Action </h6>
        </div>
        <br />
        <div class="card-body">
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-group">
                        <h4>Requested Corrective Actions</h4>
                        <br />
                        <p>@Model.RequestedCorrectiveActions</p>
                    </div>
                    <div class="form-group">
                        <h4> Action Party</h4>
                        <br />
                        <p>@Model.CorrectiveActionUserName</p>
                    </div>
                    <div class="form-group">
                        <h4>Corrective Action Target Date</h4>
                        <br />
                        <p>@Model.CorrectiveActionTargetDateOnly</p>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.NewCorrectiveActionTargetDateComment) && Model.NewCorrectiveActionTargetDate.HasValue) {
                        <div class="form-group">
                            <h4>New Corrective Action Target Date</h4>
                            <br />
                            <p>@Model.NewCorrectiveActionTargetDateOnly</p>
                        </div>
                        <div class="form-group">
                            <h4>New Corrective Action Target Date Comment</h4>
                            <br />
                            <p>@Model.NewCorrectiveActionTargetDateComment</p>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.NewCorrectiveActionTargetDateRejectComment)) {
                        <div class="form-group">
                            <br />
                            <h4>Target Date Extension Reject Reason</h4>
                            <br />
                            <p>@Model.NewCorrectiveActionTargetDateRejectComment</p>
                        </div>
                    }
                </div>
            </div>
            @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.PendingApproval && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Accepted) {
                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <br />
                            <h4>Corrective Action Comments </h4>
                            <br />
                            <p>@Model.CorrectiveActionsComments</p>
                        </div>
                    </div>
                </div>
            }
            <div class="row">
                <div class="col-xs-12">
                    @if (Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.CorrectiveActionReEvaluated) {
                        <div class="form-group">
                            <h4>Corrective Action Re-Evaluated Reason</h4>
                            <br />
                            <p>@Model.CorrectiveActionRejectReason</p>
                        </div>
                    }
                </div>
            </div>
            <hr />
            @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.PendingApproval) {
                <div class="row">
                    <div class="col-xs-6">
                        <div class="form-group">
                            <h4>Corrective Action Submitted By </h4>
                            <br />
                            <p>@Model.CorrectiveActionRequestedBy</p>
                        </div>
                    </div>
                    <div class="col-xs-6">
                        <div class="form-group">
                            <h4>Corrective Action Submitted Date </h4>
                            <br />
                            <p>@(Model.CorrectiveActionRequestedDateOnly)</p>
                        </div>
                    </div>
                </div>
            }
            <br />
            @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Accepted && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.CorrectiveActionRequested && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.CorrectiveActionReEvaluated && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.CorrectiveActionSubmitted) {
                <div class="row">
                    <div class="col-xs-6">
                        <div class="form-group">
                            <h4>Corrective Action Accepted By </h4>
                            <br />
                            <p>@Model.CorrectiveActionAcceptedBy</p>
                        </div>
                    </div>
                    <div class="col-xs-6">
                        <div class="form-group">
                            <h4>Corrective Action Accepted Date </h4>
                            <br />
                            <p>@(Model.CorrectiveActionAcceptedDateOnly)</p>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
}
@if (Model.ServiceImprovementId.HasValue && (ServiceImprovementStatusConstant.IsInvestigationStatus(Model.ServiceImprovementStatus))) {
    <div class="card breakhere">
        <div class="card-header" style="background: #67A2D5; color: #fff">
            <h6 class="mb-0">Investigation </h6>
        </div>
        <br />
        <div class="card-body">
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-group">
                        <h4>Requested Investigation Actions</h4>
                        <br />
                        <p>@Model.InvestigationActions</p>
                    </div>
                    <div class="form-group">
                        <h4> Action Party</h4>
                        <br />
                        <p>@Model.InvestigatorUserName</p>
                    </div>
                    <div class="form-group">
                        <h4>Investigation Target Date</h4>
                        <br />
                        <p>@Model.InvestigationTargetDateOnly</p>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.NewCorrectiveActionTargetDateComment) && Model.NewCorrectiveActionTargetDate.HasValue) {
                        <div class="form-group">
                            <h4>New Investigation Target Date</h4>
                            <br />
                            <p>@Model.NewInvestigationTargetDateOnly</p>
                        </div>
                        <div class="form-group">
                            <h4>New Investigation Target Date Comment</h4>
                            <br />
                            <p>@Model.NewTargetDateComment</p>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.NewTargetDateRejectComment)) {
                        <div class="form-group">
                            <br />
                            <h4>Target Date Extension Reject Reason</h4>
                            <br />
                            <p>@Model.NewTargetDateRejectComment</p>
                        </div>
                    }
                </div>
            </div>
            @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.CorrectiveActionAccepted) {
                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <h4>Investigation Results</h4>
                            <br />
                            <p>@Model.InvestigationResults</p>
                        </div>
                    </div>
                </div>
            }
            <div class="row">
                <div class="col-xs-12">
                    @if (Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.InvestigationReRequested) {
                        <div class="form-group">
                            <h4>Investigation Re-Requested Reason</h4>
                            <br />
                            <p>@Model.InvestigationRejectReason</p>
                        </div>
                    }
                </div>
            </div>
            <hr />
            @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.PendingApproval && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Accepted) {
                <div class="row">
                    <div class="col-xs-6">
                        <div class="form-group">
                            <h4>Investigation Submitted By </h4>
                            <br />
                            <p>@Model.InvestigationRequestedBy</p>
                        </div>
                    </div>
                    <div class="col-xs-6">
                        <div class="form-group">
                            <h4>Investigation Submitted Date </h4>
                            <br />
                            <p>@(Model.InvestigationRequestedDateOnly)</p>
                        </div>
                    </div>
                </div>
            }
            <br />
            @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.CorrectiveActionAccepted && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.InvestigationRequested && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.InvestigationReRequested && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.InvestigationSubmitted) {
                <div class="row">
                    <div class="col-xs-6">
                        <div class="form-group">
                            <h4>Investigation Accepted By </h4>
                            <br />
                            <p>@Model.InvestigationAcceptedBy</p>
                        </div>
                    </div>
                    <div class="col-xs-6">
                        <div class="form-group">
                            <h4>Investigation Accepted Date </h4>
                            <br />
                            <p>@(Model.InvestigationAcceptedDateOnly)</p>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
}
@if (Model.ServiceImprovementId.HasValue && (ServiceImprovementStatusConstant.IsPreventiveActionStatus(Model.ServiceImprovementStatus))) {
    <div class="card breakhere">
        <div class="card-header" style="background: #67A2D5; color: #fff">
            <h6 class="mb-0">Preventive Action </h6>
        </div>
        <br />
        <div class="card-body">
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-group">
                        <h4>Requested Preventive Actions</h4>
                        <br />
                        <p>@Model.RequestedPreventiveActions</p>
                    </div>
                    <div class="form-group">
                        <h4> Action Party</h4>
                        <br />
                        <p>@Model.PreventiveActionUserName</p>
                    </div>
                    <div class="form-group">
                        <h4>Preventive Action Target Date</h4>
                        <br />
                        <p>@Model.PreventiveActionTargetDateOnly</p>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.NewPreventiveActionTargetDateComment) && Model.NewPreventiveActionTargetDate.HasValue) {
                    <div class="form-group">
                        <h4>New Preventive Action Target Date</h4>
                        <br />
                        <p>@Model.NewPreventiveActionTargetDateOnly</p>
                    </div>
                    <div class="form-group">
                        <h4>New Preventive Action Target Date Comment</h4>
                        <br />
                        <p>@Model.NewPreventiveActionTargetDateComment</p>
                    </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.NewPreventiveActionTargetDateRejectComment)) {
                    <div class="form-group">
                        <br />
                        <h4>Target Date Extension Reject Reason</h4>
                        <br />
                        <p>@Model.NewPreventiveActionTargetDateRejectComment</p>
                    </div>
                    }
                </div>
            </div>
            @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.PendingApproval && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Accepted) {
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-group">
                        <br />
                        <h4>Preventive Action Comments </h4>
                        <br />
                        <p>@Model.PreventiveActionsComments</p>
                    </div>
                </div>
            </div>
            }
            <div class="row">
                <div class="col-xs-12">
                    @if (Model.ServiceImprovementStatus == ServiceImprovementStatusConstant.PreventiveActionReEvaluated) {
                    <div class="form-group">
                        <h4>Preventive Action Re-Evaluated Reason</h4>
                        <br />
                        <p>@Model.PreventiveActionRejectReason</p>
                    </div>
                    }
                </div>
            </div>
            <hr />
            @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.PendingApproval) {
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <h4>Preventive Action Submitted By </h4>
                        <br />
                        <p>@Model.PreventiveActionRequestedBy</p>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <h4>Preventive Action Submitted Date </h4>
                        <br />
                        <p>@(Model.PreventiveActionRequestedDateOnly)</p>
                    </div>
                </div>
            </div>
            }
            <br />
            @if (Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.Accepted && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.PreventiveActionRequested && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.PreventiveActionReEvaluated && Model.ServiceImprovementStatus != ServiceImprovementStatusConstant.PreventiveActionSubmitted) {
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <h4>Preventive Action Accepted By </h4>
                        <br />
                        <p>@Model.PreventiveActionAcceptedBy</p>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <h4>Preventive Action Accepted Date </h4>
                        <br />
                        <p>@(Model.PreventiveActionAcceptedDateOnly)</p>
                    </div>
                </div>
            </div>
            }
        </div>
    </div>
}
@if (Model.ServiceImprovementId.HasValue && (ServiceImprovementStatusConstant.IsPreventiveImplementationStatus(Model.ServiceImprovementStatus))) {
    <div class="card breakhere">
        <div class="card-header" style="background: #67A2D5; color: #fff">
            <h6 class="mb-0">Sign off </h6>
        </div>
        <br />
        <div class="card-body">
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <h4>Sign-off Comment</h4>
                        <br />
                        <p>@Model.SignOffComment</p>
                    </div>
                </div>
            </div>
            <br />
            <hr />
            @if (ServiceImprovementStatusConstant.IsPreventiveImplementationStatus(Model.ServiceImprovementStatus)) {
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <h4>Sign Off By </h4>
                        <br />
                        <p>@Model.SignOffBy</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <h4>Sign Off Date </h4>
                        <br />
                        <p>@(Model.SignOffDateOnly)</p>
                    </div>
                </div>
            </div>
        }
        </div>
    </div>
}

<div class="card">
    <div class="card-header" style="background: #67A2D5; color: #fff">
        <h6 class="mb-0">SIF Updates </h6>
    </div>
    <br />
    <div class="card-body">
        @if (Model.SifLogs != null) {
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Log</th>
                        <th>User Name</th>
                        <th>Date</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var log in Model.SifLogs) {
                        <tr>
                            <td>@log.Log</td>
                            <td>@log.UserName</td>
                            <td>@log.Date.ToString("dd-MM-yyyy HH:mm")</td>
                        </tr>
                    }
                </tbody>
            </table>
        }
    </div>
</div>
