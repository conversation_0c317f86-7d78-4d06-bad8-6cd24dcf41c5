﻿namespace Centerpoint.Common.Constants
{
    public static class OpportunityTypeConstant
    {

        public const string Lead = "L";
        public const string Opportunity = "O";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Lead,"Lead"},
                    {Opportunity,"Opportunity"}
                };
            }
        }
    }
}
