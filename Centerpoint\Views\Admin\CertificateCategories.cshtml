﻿@model CertificateCategoryModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-file-text"></i>
        Certificate Category
        (<span data-bind="text:totalCategories"></span>)
    </h4>
</div>
<hr />

<div class="grid-container">
        @(Html.Kendo().Grid<CertificateCategoryModel>()
            .Name("certificateCategoryGrid")
            .Columns(c => {
                c.Bound(p => p.Name);
                c.Command(command => { 
                    command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                    command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
                }).Width(200);
            })
            .Editable(editable => editable.Mode(GridEditMode.InLine))
            .ToolBar(t => {
                t.Create().Text("Add Category");
                t.Excel().Text("Export");
            }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
            .Sortable()
            .Filterable()
            .Scrollable()
            .Resizable(c => c.Columns(true))
            .ColumnMenu(c => c.Columns(true))
            .Events(e => e.DataBound("updateCertificateCategoryTotal"))
            .Excel(excel => excel
                .FileName(string.Format("Centerpoint_Personnel_CertificateCategory_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                .Filterable(true)
                .ProxyURL(Url.Action("Export", "Admin"))
            )
            .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Model(m => m.Id(p => p.CertificateCategoryId))
                .Events(e => e.Error("onError"))
                .Read(read => read.Action("GetCertificateCategories", "Admin"))
                .Create(create => create.Action("UpdateCertificateCategory", "Admin"))
                .Update(update => update.Action("UpdateCertificateCategory", "Admin"))
                .Destroy(destroy => destroy.Action("DeleteCertificateCategory", "Admin"))
            )
        )
</div>


    <script>
        $(document).ready(function () {
            var certificateCategoryGrid = $('#certificateCategoryGrid').data("kendoGrid");
            certificateCategoryGrid.bind('dataBound', function (e) {
                this.element.find('.k-add').remove();
                this.element.find('.k-i-excel').remove();
            });
        });

        function updateCertificateCategoryTotal() {
            var certificateCategoryGrid = $("#certificateCategoryGrid").data("kendoGrid");
            var totalCategories = certificateCategoryGrid.dataSource.total();
            viewModel.set("totalCategories", totalCategories);
        }

        function onError(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var certificateCategoryGrid = $("#certificateCategoryGrid").data("kendoGrid");
                certificateCategoryGrid.dataSource.cancelChanges();
            }
        }

        var viewModel = new kendo.observable({
            totalCategories: 0
        });

        kendo.bind(document.body.children, viewModel);
    </script>
