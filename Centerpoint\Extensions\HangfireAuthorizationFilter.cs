﻿using System.Linq;
using System.Security.Claims;
using Centerpoint.Common.Constants;
using Hangfire.Dashboard;

namespace Centerpoint.Authorization
{
    public class HangfireAuthorizationFilter : IDashboardAuthorizationFilter
    {
        public bool Authorize(DashboardContext context)
        {
            var httpContext = context.GetHttpContext();

            if (httpContext.User.Identity.IsAuthenticated)
            {
                var roleClaim = httpContext.User.Claims.Where(c => c.Type == ClaimTypes.Role);

                return roleClaim != null && roleClaim.Any(x => x.Value == UserRoleConstant.GlobalAdministrator);
            }
            else
            {
                return false;
            }
        }
    }
}
