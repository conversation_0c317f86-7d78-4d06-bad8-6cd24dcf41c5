﻿@model CompanyWellModel

<div class="header-container-between">
    <h4>
        <i class="fa fa-tags"></i>
        @(Model.CompanyWellId.HasValue ? string.Format("Edit - {0}", Model.Name) : "Add New Well")
    </h4>
    <div class="d-flex actionsContainer">
        @if (ViewBag.OpportunityId != null) {
            <a class="btn btn-info btn-sm" href="@Url.Action("Edit", "Sales", new { @id = ViewBag.OpportunityId, @revision = ViewBag.Revision })">
                <i class="fa fa-refresh"></i>
                Return to Opportunity
            </a>
            } else if (ViewBag.ProjectId != null) {
            <a class="btn btn-info btn-sm" href="@Url.Action("EditProject","Operation",new { @id = ViewBag.ProjectId} )">
                <i class="fa fa-refresh"></i>
                Go to Project
            </a>
            } else if (ViewBag.AnsaProjectId != null) {
            <a class="btn btn-info btn-sm" href="@Url.Action("EditAnsaProject","Ansa",new { @id = ViewBag.AnsaProjectId} )">
                <i class="fa fa-refresh"></i>
                Go to Ansa Project
            </a>
            } else {
            <a class="btn btn-info btn-sm" href="@Url.Action("EditCompany","Admin",new { @id = Model.CompanyId} )">
                <i class="fa fa-refresh"></i>
                Go to Company
            </a>
        }
    </div>
</div>
<hr />

<div>
    @(Html.Kendo().TabStrip()
            .Name("editCompanyWellTabStrip")
            .SelectedIndex(0)
            .HtmlAttributes(new { @data_bind="visible: isEditCompanyWellTabStripVisible" })
            .Animation(animation =>
            {
                animation.Enable(false);
            })
            .Items(tabstrip =>
            {
                tabstrip.Add()
                .Text("")
                .HtmlAttributes(new { @data_bind="html:detailsText" })
                .Selected(true)
                .Content(@<text>
                    @using (Html.BeginForm("EditCompanyWell", "Admin",FormMethod.Post, new { @companyId = Model.CompanyId, @id="editCompanyWellForm" })) {
                        @Html.ValidationSummary(false)
                        <div class="row">
                            <div class="col-md-4">
                                 <div class="form-group">
                                    @Html.LabelFor(m => m.CompanyFieldId)
                                    @(Html.Kendo().DropDownListFor(m => m.CompanyFieldId)
                                        .Filter("contains")
                                        .DataTextField("Name")
                                        .DataValueField("CompanyFieldId")
                                        .OptionLabel("Select Fields")
                                        .HtmlAttributes(new {  @data_value_primitive = "true", @tabindex = "666" })
                                        .DataSource(d => d.Read("GetFieldsByCompanyId", "Lookup", new { @companyId = Model.CompanyId }))
                                    )
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Well</label>
                                    @(Html.TextBoxFor(m => m.Name, new { @class = "form-control"}))
                                </div>
                            </div>
                            <div class="col-md-4">
                                 <div class="form-group">
                                    @Html.LabelFor(m => m.WellType)
                                    @(Html.Kendo().DropDownListFor(p => p.WellType)
                                        .Filter("contains")
                                        .DataValueField("Key")
                                        .DataTextField("Value")
                                        .OptionLabel("Select Well Type")
                                        .HtmlAttributes(new { @tabindex = "7" })
                                        .BindTo(Centerpoint.Common.Constants.WellTypeConstant.ValuesAndDescriptions.ToList())
                                    )
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    @if (GlobalSettings.IsAisus){
                                      <label>OD(Length)</label>
                                    } else {
                                    @Html.LabelFor(m => m.MinimumId)
                                    }
                                    @(Html.Kendo().NumericTextBoxFor(p => p.MinimumId).Spinners(false).Format("n3").Decimals(3))
                                </div>
                                @if (!GlobalSettings.IsAisus)
                                {
                                    <div class="d-flex">
                                        <div class="form-group col-md-8 pl-0">
                                            @Html.LabelFor(m => m.MaximumPressure)
                                            @(Html.Kendo().NumericTextBoxFor(p => p.MaximumPressure).Spinners(false).Format("n2").Decimals(2))
                                        </div>
                                        <div class="form-group col-md-4 p-0">
                                            <label>Units</label>
                                            @(Html.Kendo().DropDownListFor(m => m.MaximumPressureUnits)
                                                .Filter("contains")
                                                .DataTextField("Value")
                                                .DataValueField("Key")
                                                .BindTo(Centerpoint.Common.Constants.MaximumPressureUnitsConstant.ValuesAndDescriptions.ToList())
                                                .HtmlAttributes(new { @data_value_primitive = "true" })
                                                )
                                        </div>
                                    </div>
                                }
                            </div>
                            <div class="col-md-4">
                                @if (!GlobalSettings.IsAisus)
                                {
                                    <div class="form-group">
                                        @Html.LabelFor(m => m.MaximumDeviation)
                                        @(Html.Kendo().NumericTextBoxFor(p => p.MaximumDeviation).Spinners(false).Format("n0"))
                                    </div>
                                    <div class="d-flex">
                                        <div class="form-group col-md-8 pl-0">
                                            @Html.LabelFor(m => m.MaximumTemperature)
                                            @(Html.Kendo().NumericTextBoxFor(p => p.MaximumTemperature).Spinners(false).Format("n0"))
                                        </div>
                                        <div class="form-group col-md-4 p-0">
                                            <label>Degrees</label>
                                            @(Html.Kendo().DropDownListFor(m => m.MaximumTemperatureDegrees)
                                                .Filter("contains")
                                                .DataTextField("Value")
                                                .DataValueField("Key")
                                                .BindTo(Centerpoint.Common.Constants.MaximumTemperatureDegreesConstant.ValuesAndDescriptions.ToList())
                                                .HtmlAttributes(new { @data_value_primitive = "true" })
                                                )
                                        </div>
                                    </div>
                                }
                            </div>
                            <div class="col-md-4">
                                @if (!GlobalSettings.IsAisus)
                                {
                                    <div class="form-group">
                                        <label>H<sub>2</sub>S (ppm)</label>
                                        @(Html.Kendo().NumericTextBoxFor(m => m.H2S).Spinners(false).Format("n0"))
                                    </div>
                                    <div class="form-group">
                                        <label>CO<sub>2</sub> (%)</label>
                                        @(Html.Kendo().NumericTextBoxFor(m => m.CO2).Spinners(false).Format("n0"))
                                    </div>
                                }
                            </div>
                        </div>
                    @if (!GlobalSettings.IsAisus)
                    {
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Fluids</label>
                                    @(Html.Kendo().MultiSelectFor(m => m.FluidTypeIds)
                                        .Placeholder("Select Fluid(s)")
                                        .DataTextField("Name")
                                        .DataValueField("FluidTypeId")
                                        .DataSource(source => {
                                            source.Read(read => {
                                                read.Action("GetCompanyWellFluids", "Lookup");
                                            })
                                            .ServerFiltering(true);
                                        })
                                    )
                                </div>
                            </div>
                        </div>
                        }
                        
                        <button class="btn btn-sm btn-primary mt-3" onclick="validateEditCompanyWell(event)">Save Details</button>
                        @Html.HiddenFor(m => m.CompanyWellId)
                        @Html.HiddenFor(m => m.OpportunityId)
                        @Html.HiddenFor(m => m.Revision)
                        @Html.HiddenFor(m => m.AnsaProjectId)
                    }
                </text>);

                if (Model.CompanyWellId.HasValue) {
                    tabstrip.Add()
                    .Text("")
                    .HtmlAttributes(new {@data_bind="html:attachmentsText", @id="documents"})
                    .Content(@<text>
                        @if (Model.DevitationSurveyType != null || Model.SchematicType != null || Model.TallyType != null) {
                            <div class="mb-2">
                                <div class="card-header">
                                    <h6 class="mb-0">Latest Revisions</h6>
                                </div>
                                    <table class="table table-striped table-bordered attachments-container" cellspacing="0" id="table">
                                        <thead>
                                            <tr>
                                                <th style="width:40%">Document</th>
                                                <th style="width:20%">Type</th>
                                                <th style="width:20%">Created</th>
                                                <th style="width:20%">Created By</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @if (Model.DevitationSurveyType != null) {
                                                <tr>
                                                    <td style="width:40%"><a href="@Url.Action("Index", "Document", new { @id = Model.DevitationSurveyType.DocumentId })">@Model.DevitationSurveyType.FileName</a></td>
                                                    <td style="width:20%">@Model.DevitationSurveyType.CompanyWellDocumentTypeDescription</td>
                                                    <td style="width:20%">@Model.DevitationSurveyType.CreatedDate</td>
                                                    <td style="width:20%">@Model.DevitationSurveyType.Username</td>
                                                </tr>
                                                }
                                            @if (Model.SchematicType != null) {
                                                <tr>
                                                    <td style="width:40%"><a href="@Url.Action("Index", "Document", new { @id = Model.SchematicType.DocumentId })">@Model.SchematicType.FileName</a></td>
                                                    <td style="width:20%">@Model.SchematicType.CompanyWellDocumentTypeDescription</td>
                                                    <td style="width:20%">@Model.SchematicType.CreatedDate</td>
                                                    <td style="width:20%">@Model.SchematicType.Username</td>
                                                </tr>
                                                }
                                            @if (Model.TallyType != null) {
                                                <tr>
                                                    <td style="width:35%"><a href="@Url.Action("Index", "Document", new { @id = Model.TallyType.DocumentId })">@Model.TallyType.FileName</a></td>
                                                    <td style="width:20%">@Model.TallyType.CompanyWellDocumentTypeDescription</td>
                                                    <td style="width:20%">@Model.TallyType.CreatedDate</td>
                                                    <td style="width:20%">@Model.TallyType.Username</td>
                                                </tr>
                                                }
                                        </tbody>
                                    </table>
                                </div>    
                            <hr />
                        }
                         <div class="form-group">
                                    <label>Document Type</label>
                                    @(Html.Kendo().DropDownList()
                                        .Filter("contains")
                                        .Name("documentType")
                                        .DataValueField("Key")
                                        .DataTextField("Value")
                                        .OptionLabel("Select Type")
                                        .Events(e => {
                                            e.Change("documentTypeChanged");
                                        })
                                        .BindTo(Centerpoint.Common.Constants.CompanyWellDocumentTypeConstant.ValuesAndDescriptions.ToList())
                                    )
                        </div>
                        <div>
                            <div class="card-header">
                                <h6 class="mb-0">Past Revisions</h6>
                            </div>
                                <div data-bind="visible:canAttach">
                                    @(Html.Kendo().Upload()
                                        .Name("wellDocuments")
                                        .Messages(m => m.Select("Attach Documents"))
                                        .Multiple(true)
                                        .Events(e => e.Success("onWellDocumentAttached").Complete("onWellDocumentComplete").Upload("onWellDocumentUpload"))
                                        .Async(async => async.Save("AttachWellDocuments", "Admin").Batch(true))
                                    )
                                </div>
                                <div style="height: calc(100vh - 500px)">
                                    @(Html.Kendo().Grid<CompanyWellDocumentModel>()
                                        .Name("wellDocumentsGrid")
                                        .Columns(c => {
                                            c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                                            c.Bound(p => p.CompanyWellDocumentTypeDescription).Title("Type").ClientTemplate("#=CompanyWellDocumentTypeDescription ? CompanyWellDocumentTypeDescription : 'N/A'#");
                                    c.Bound(p => p.Created).Format(DateConstants.DateFormat).Title("Created");
                                            c.Bound(p => p.Username).Title("Created By");
                                            c.Command(c=>c.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"})).Width(150);
                                        })
                                        .Events(e => e.DataBound("updateWellDocumentsGrid"))
                                        .Sortable()
                                        .Resizable(r => r.Columns(true))
                                        .ColumnMenu(c => c.Columns(true))
                                        .Filterable()
                                        .Groupable()
                                        .Editable(e => e.Mode(GridEditMode.InLine))
                                        .Scrollable()
                                        .DataSource(dataSource => dataSource
                                            .Ajax()
                                            .Events(e => e.RequestEnd("onRequestEnd"))
                                            .ServerOperation(false)
                                            .Model(model => model.Id(p => p.CompanyWellDocumentId))
                                            .Read(read => read.Action("GetCompanyWellDocuments", "Admin", new { @companyWellId = Model.CompanyWellId }))
                                            .Destroy(destroy => destroy.Action("DeleteCompanyWellDocument", "Admin"))
                                        )
                                    )
                                </div>
                        </div>
                    </text>);
                }

            })
    )
</div>



<script>
    $(document).ready(function () {
        initialLoadSettings()
        $('#table').dataTable({
            "bPaginate": false,
            "bLengthChange": false,
            "bFilter": true,
            "bInfo": false,
            "bAutoWidth": false,
            "searching": false,
        });
    });

    function initialLoadSettings() {
        activateTabDependingOnUrlQuery();
        viewModel.set("isEditCompanyWellTabStripVisible", true)
    }

    function onRequestEnd(e) {
        var grid = $("#wellDocumentsGrid").data("kendoGrid");
        var data = grid.dataSource;
        if (e.type == "destroy") {
            window.location.reload();
        }
    }

    function documentTypeChanged(e) {
        var documentType = $("#documentType").data("kendoDropDownList");
        var documentTypeValue = documentType.value();

        if (documentTypeValue == "DEV") {
            viewModel.set("documentType", "DEV");
            viewModel.set("canAttach", true);
        } else if (documentTypeValue == "SCH") {
            viewModel.set("documentType", "SCH");
            viewModel.set("canAttach", true);
        } else if (documentTypeValue == "TAL") {
            viewModel.set("documentType", "TAL");
            viewModel.set("canAttach", true);
        } else if (documentTypeValue == "OTH") {
            viewModel.set("documentType", "OTH");
            viewModel.set("canAttach", true);
        } else if (documentTypeValue == "") {
            viewModel.set("canAttach", false);
        }
    }

    function fluidData() {
        return {
            companyWellId: "@Model.CompanyWellId"
        };
    }

    function onWellDocumentAttached() {
        var wellDocumentsGrid = $("#wellDocumentsGrid").data("kendoGrid");
        wellDocumentsGrid.dataSource.read();
    }

    function onWellDocumentUpload(e) {
        uploadValidation(e);

        var companyWellId = "@Model.CompanyWellId";
        var documentType = viewModel.get("documentType");
        e.data = { companyWellId: companyWellId, documentType: documentType };
        $(".k-upload-files.k-reset").show();
    }

    function onWellDocumentComplete(e) {
        $(".k-upload-files.k-reset").find("li").remove();
        $(".k-upload-files.k-reset").slideUp();
        if ("@ViewBag.OpportunityId") {
                    window.location.href = "/Admin/EditCompanyWell/@Model.CompanyWellId" + "?tab=" + "documents" + "&opportunityId=" + "@ViewBag.OpportunityId" + "&revision=" + "@ViewBag.revision";
            } else if ("@ViewBag.ProjectId") {
                    window.location.href = "/Admin/EditCompanyWell/@Model.CompanyWellId" + "?tab=" + "documents" + "&projectId=" + "@ViewBag.ProjectId";
            } else if ("@ViewBag.AnsaProjectId") {
                    window.location.href = "/Admin/EditCompanyWell/@Model.CompanyWellId" + "?tab=" + "documents" + "&ansaProjectId=" + "@ViewBag.AnsaProjectId";
            } else {
                    window.location.href = "/Admin/EditCompanyWell/@Model.CompanyWellId" + "?tab=documents";
            }
        }

    function updateWellDocumentsGrid() {
        var wellDocumentsGrid = $("#wellDocumentsGrid").data("kendoGrid");
        var totalWellDocuments = wellDocumentsGrid.dataSource.total();

        viewModel.set("totalWellDocuments", totalWellDocuments);

    }

    function validateEditCompanyWell(e){
        e.preventDefault();
        console.log($('#editCompanyWellForm').kendoValidator())
        if($('#editCompanyWellForm').kendoValidator().data('kendoValidator').validate()){
            $('#editCompanyWellForm').submit();
        }
    }

    function activateTabDependingOnUrlQuery () {
        let tabId = window.location.search.split("tab=")[1];
        if(tabId) {
            let tabToActivate = $(`#${tabId}`);
            if(tabToActivate) {
              $("#editCompanyWellTabStrip").data("kendoTabStrip").select(tabToActivate)
            }
        }
    }

    var viewModel = new kendo.observable({
        totalWellDocuments: 0,
        documentType: "",
        canAttach: false,
        isEditCompanyWellTabStripVisible: false,
        detailsText: function(){
            return `<span class="k-link"><i class="fa fa-file-text mr-2"></i> Details</span>`;   
        },
        attachmentsText: function(){
            return `<span class="k-link"><i class="fa fa-file-text mr-2"></i> Attachments (<span data-bind="text:totalWellDocuments"></span>) </span>`;
        }
    });
    kendo.bind(document.body.children, viewModel);
</script>
