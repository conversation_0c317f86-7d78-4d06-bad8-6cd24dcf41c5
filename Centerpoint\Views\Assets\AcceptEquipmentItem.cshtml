﻿<div class="form-group">
    <label>Current Location</label>
    <br />
    @(Html.Kendo().DropDownList()
      .Name("base")
      .OptionLabel("Select Location")
      .Filter("contains")
      .DataTextField("Text")
      .DataValueField("Value")
      .AutoBind(false)
      .DataSource(d => d.Read("GetBaseCompanyLocations", "Lookup"))
      .HtmlAttributes(new { @style = "width:250px;font-size: 14px" , @data_bind= "value:acceptCurrentCompanyLocationId"}))
</div>
<div class="form-group">
  <label>Acceptance Notes</label>
    @(Html.TextArea("AcceptanceNotes", null, new { @class = "form-control", @style = "width:100%", @rows = "5", @data_bind = "value:equipmentItemAcceptanceNote", @data_value_update = "keyup" }))
</div>
<hr />
<h4> Custom Status</h4>
<br />
<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label>Status</label>
            <br />
            @(Html.Kendo().DropDownList()
            .Name("customStatusName")
            .Filter("contains")
            .OptionLabel("Select Custom Status")
            .DataTextField("CustomStatusName")
            .DataValueField("CustomStatusCodeId")
            .DataSource(d => d.Read("GetCustomStatusCode", "Lookup"))
            .AutoBind(false)
            .HtmlAttributes(new { @style = "width:300px;font-size: 14px", @data_bind = "value:acceptCustomStatusId" }))
        </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label>Expiry Date</label>
                <br />
                @(Html.Kendo().DatePicker().Name("ExpiryDate").Min(DateTime.Now.Date).HtmlAttributes(new { @data_bind = "value:customStatusExpiryDate", @style = "width:220px; font-size: 14px;" }))
            </div>
        </div>
    </div>
<hr />
<div class="form-group">
    <label>Custom Status Comment</label>
    @(Html.TextArea("CustomStatusComment", null, new { @class = "form-control", @style = "width:100%", @rows = "5", @data_bind = "value:equipmentCustomStatusComment", @data_value_update = "keyup" }))
</div>
<button id="acceptEquipmentItemConfirm" data-bind="enabled:canAcceptEquipmentItem" class="btn btn-primary btn-sm mt-2">Accept</button>