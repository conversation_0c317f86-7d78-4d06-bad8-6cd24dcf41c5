﻿<?xml version="1.0" encoding="utf-8" ?>
<!-- XSD manual extracted from package NLog.Schema: https://www.nuget.org/packages/NLog.Schema-->
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd" xsi:schemaLocation="NLog NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogFile="C:\"
      internalLogLevel="Info" >

  <!-- enable asp.net core layout renderers -->
  <extensions>
    <add assembly="NLog.Web.AspNetCore"/>
  </extensions>

	<!-- the targets to write to -->
	<targets>
		<!-- write logs to file -->
		<target xsi:type="File" name="target1" fileName="${baseDir}\logs\${shortdate}.txt"
				layout="${date:universalTime=true} | ${level:uppercase=true} | ${message} | ${exception:format=ToString,StackTrace} | ${logger} | ${all-event-properties}${newline}" />
  </targets>

	<!-- rules to map from logger name to target -->
	<rules>
		<logger name="Microsoft.*" minlevel="Info" writeTo="" final="true" />
    <logger name="*" minlevel="Trace" writeTo="target1" />
	</rules>
</nlog>