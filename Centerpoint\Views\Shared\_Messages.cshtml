﻿@*@using Message
@if (TempData.ContainsKey("MessageType") && TempData.ContainsKey("Message")) {
    if ((MessageType)TempData["MessageType"] == MessageType.Error) {
        <div class="alert alert-danger">
            <a class="close" data-dismiss="alert" href="#">×</a>
            <p>
                @Html.Raw(TempData["Message"])
            </p>
        </div>
        }
    if ((MessageType)TempData["MessageType"] == MessageType.Warning) {
        <div class="alert alert-warning">
            <a class="close" data-dismiss="alert" href="#">×</a>
            <p>
                @Html.Raw(TempData["Message"])
            </p>
        </div>
        }
    if ((MessageType)TempData["MessageType"] == MessageType.Success) {
        <div class="alert alert-success">
            <a class="close" data-dismiss="alert" href="#">×</a>
            <p>
                @Html.Raw(TempData["Message"])
            </p>
        </div>
        }
    if ((MessageType)TempData["MessageType"] == MessageType.Info) {
        <div class="alert alert-info">
            <a class="close" data-dismiss="alert" href="#">×</a>
            <p>
                @Html.Raw(TempData["Message"])
            </p>
        </div>
        }
    }

@if (Html.ShowMessage()) {
    <div class="alert <EMAIL>().ToLower()">
        <a class="close" data-dismiss="alert" href="#">×</a>
        <p><strong>Attention:</strong> @Html.Raw(Html.Message())</p>
    </div>
    }

*@

