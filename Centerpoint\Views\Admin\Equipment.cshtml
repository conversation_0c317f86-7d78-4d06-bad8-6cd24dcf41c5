﻿@Html.Partial("_GridNotification", EntityType.EquipmentItem)

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-tags"></i>
        Equipment
    </h4>
</div>
<hr />
<div class="row">
    <div class="col-md-3">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fa fa-tags"></i>
                    Categories
                </h6>
            </div>
            <div class="card-body">
                <div class="expandCollapseButtonsContainer">
                    <button class="btn btn-sm btn-primary" id="expandAll" data-bind="click:expandAll" title="Expand All">
                        <i class="fa fa-expand m-0"></i>
                    </button>
                    <button class="btn btn-sm btn-primary" id="collapsAll" data-bind="click:collapseAll" title="Collapse All">
                        <i class="fa fa-compress m-0"></i>
                    </button>
                    <button class="btn btn-sm btn-primary" id="addEquipmentCategory" data-bind="click:addEquipmentCategory" title="Add New Equipment Category">
                        <i class="fa fa-plus m-0"></i>
                    </button>
                </div>
                <input id="filterText" class="k-input k-textbox k-input-solid k-input-md k-rounded-md" type="text" placeholder="Search categories" />
                <br />
                <br />
                @(Html.Kendo().TreeView()
                    .Name("equipmentCategoryTreeView")
                    .DataTextField("NewName")
                    .LoadOnDemand(false)
                    .DragAndDrop(true)
                    .Events(e => e
                        .Change("equipmentCategorySelected")
                        .Drop("equipmentCategoryDropped")
                        .DataBound("equipmentCategoryLoaded")
                        .Select("userEquipmentCategorySelected")
                    )
                    .DataSource(datasource => datasource
                        .Events(e => e.RequestEnd("categoriesLoaded"))
                        .Model(m => m.Id("EquipmentCategoryId").HasChildren("HasChildren").Children("Children"))
                        .Read(r => r.Action("GetEquipmentCategories", "Admin"))
                        .ServerFiltering(false)
                    )
                )
            </div>
        </div>
    </div>
    <div class="col-md-9" data-bind="visible:selectedEquipmentCategory">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0" data-bind="text:selectedAssetItemNameWithCount"></h6>
            </div>
            <div class="card-body">
                @(Html.Kendo().TabStrip()
                    .Name("adminEquipmentTabStrip")
                    .SelectedIndex(0)
                    .Animation(false)
                    .Items( tabstrip => {

                        tabstrip.Add().Text("Category")
                            .HtmlAttributes(new { @id="category"})
                            .Selected(true)
                            .Content(@<text>
                                <form>
                                    <div class="form-group">
                                        <label>Name</label>
                                        <input type="text" data-bind="value:selectedEquipmentCategory.Name" data-value-update="keyup" class="form-control"/>
                                    </div>
                                    <div class="form-group">
                                        <label>Details</label>
                                        <textarea data-bind="value:selectedEquipmentCategory.Details" data-value-update="keyup" class="form-control" rows="2"></textarea>
                                    </div>
                                    <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="d-inline-block mr-2">Division</label>
                                            <div class="d-inline-block">
                                                @(Html.Kendo().DropDownList()
                                                        .Name("Division")
                                                        .DataTextField("Text")
                                                        .DataValueField("Value")
                                                        .OptionLabel("Select Division")
                                                        .HtmlAttributes(new { @data_bind = "value:selectedEquipmentCategory.DivisionId" })
                                                        .DataSource(d => d.Read("GetDivisions", "Lookup"))
                                                        )
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <input type="checkbox" data-bind="disabled:totalEquipmentItems, checked:selectedEquipmentCategory.IsTopLevelOnly" />
                                                <label class="form-check-label">Top Level Only</label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group" data-bind="invisible:selectedEquipmentCategory.IsTopLevelOnly">
                                                <input type="checkbox" data-bind="checked:selectedEquipmentCategory.IsDangerousGoods" />
                                                <label class="form-check-label">Dangerous</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <input type="checkbox" data-bind="checked:selectedEquipmentCategory.DisplayInOpportunity" />
                                        <label class="form-check-label">Available as opportunity required equipment </label>
                                    </div>

                                    <div data-bind="invisible:selectedEquipmentCategory.IsTopLevelOnly">
                                        <div class="mb-2">
                                            <div class="card-header mb-3">
                                                <h6 class="mb-0">Dimensions</h6>
                                            </div>
                                            <div>
                                                <div class="d-flex">
                                                    <div class="d-flex w-50">
                                                        <div class="form-gruop">
                                                            <label class="editEquipmentLabelSmall">Length</label>
                                                            <input id="length" data-role="numerictextbox" data-spinners="false" data-min="0" data-format="n3" data-decimals="3" data-bind="value:selectedEquipmentCategory.Height" style="width:90px" />
                                                        </div>
                                                        <div class="form-group ml-2">
                                                            @(Html.Kendo().DropDownList()
                                                                .Name("HeightUnit")
                                                                .ValuePrimitive(true)
                                                                .Filter("contains")
                                                                .OptionLabel("Select")
                                                                .DataValueField("Key")
                                                                .DataTextField("Value")
                                                                .HtmlAttributes(new { @style = "width:100px", @data_bind = "value:selectedEquipmentCategory.HeightUnit", @id = "heightUint" })
                                                                .BindTo(Centerpoint.Common.Constants.UnitsConstant.EquipmentValuesAndDescriptions.ToList())
                                                            )
                                                        </div>
                                                    </div>
                                                    <div class="d-flex w-50">
                                                        <div class="d-flex">
                                                            <div class="form-group">
                                                                <label class="editEquipmentLabelSmall">OD</label>
                                                                <input id="OD" data-role="numerictextbox" style="width:90px" data-spinners="false" data-min="0" data-format="n3" data-decimals="3" data-bind="value:selectedEquipmentCategory.OuterDiameter" />
                                                            </div>
                                                            <div class="form-group ml-2">
                                                                @(Html.Kendo().DropDownList()
                                                                    .Name("OuterDiameterUnit")
                                                                    .ValuePrimitive(true)
                                                                    .Filter("contains")
                                                                    .OptionLabel("Select")
                                                                    .DataValueField("Key")
                                                                    .DataTextField("Value")
                                                                    .HtmlAttributes(new { @style = "width:100px", @data_bind = "value:selectedEquipmentCategory.OuterDiameterUnit", @id = "ODUint" })
                                                                    .BindTo(Centerpoint.Common.Constants.UnitsConstant.EquipmentValuesAndDescriptions.ToList())
                                                                )
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="d-flex">
                                                    <div class="d-flex w-50">
                                                        <div class="form-group">
                                                            <label class="editEquipmentLabelSmall">Width</label>
                                                            <input id="width" data-role="numerictextbox" data-spinners="false" data-min="0" data-format="n3" data-decimals="3" data-bind="value:selectedEquipmentCategory.Width" style="width:90px" />
                                                        </div>
                                                        <div class="form-group ml-2">
                                                            @(Html.Kendo().DropDownList()
                                                                .Name("WidthUnit")
                                                                .ValuePrimitive(true)
                                                                .Filter("contains")
                                                                .OptionLabel("Select")
                                                                .DataValueField("Key")
                                                                .DataTextField("Value")
                                                                .HtmlAttributes(new { @style = "width:100px", @data_bind = "value:selectedEquipmentCategory.WidthUnit", @id = "widthUint" })
                                                                .BindTo(Centerpoint.Common.Constants.UnitsConstant.EquipmentValuesAndDescriptions.ToList())
                                                            )
                                                        </div>
                                                    </div>
                                                    <div class="d-flex w-50">
                                                        <div class="form-group">
                                                            <label class="editEquipmentLabelSmall">Weight</label>
                                                            <input id="weight" data-role="numerictextbox" style="width:90px" data-spinners="false" data-min="0" data-format="n3" data-decimals="3" data-bind="value:selectedEquipmentCategory.Weight" />
                                                        </div>
                                                        <div class="form-group ml-2">
                                                            @(Html.Kendo().DropDownList()
                                                                .Name("WeightUnit")
                                                                .ValuePrimitive(true)
                                                                .Filter("contains")
                                                                .OptionLabel("Select")
                                                                .DataValueField("Key")
                                                                .DataTextField("Value")
                                                                .HtmlAttributes(new { @style = "width:100px", @data_bind = "value:selectedEquipmentCategory.WeightUnit", @id = "weightUint" })
                                                                .BindTo(Centerpoint.Common.Constants.UnitsConstant.EquipmentWeightValuesAndDescriptions.ToList())
                                                            )
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="d-flex">
                                                    <div class="form-group">
                                                        <label class="editEquipmentLabelSmall">Depth</label>
                                                        <input id="depth" data-role="numerictextbox" data-spinners="false" data-min="0" data-format="n3" data-decimals="3" data-bind="value:selectedEquipmentCategory.Depth" style="width:90px;" />
                                                    </div>
                                                    <div class="form-group ml-2">
                                                        @(Html.Kendo().DropDownList()
                                                            .Name("DepthUnit")
                                                            .ValuePrimitive(true)
                                                            .Filter("contains")
                                                            .OptionLabel("Select")
                                                            .DataValueField("Key")
                                                            .DataTextField("Value")
                                                            .HtmlAttributes(new { @style = "width:100px", @data_bind = "value:selectedEquipmentCategory.DepthUnit", @id = "depthUint" })
                                                            .BindTo(Centerpoint.Common.Constants.UnitsConstant.EquipmentValuesAndDescriptions.ToList())
                                                        )
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-header mb-3">
                                            <h6 class="mb-0">Maintenance</h6>
                                        </div>
                                            <div class="row" data-bind="visible:canEditEquipmentItems">
                                                <div class="col-md-12">
                                                    @(Html.Kendo().Grid<EquipmentCategoryMaintenanceStepModel>()
                                                        .Name("equipmentCategoryMaintenanceStepGrid")
                                                        .Columns(c =>
                                                        {
                                                            c.Bound(p => p.MaintenanceBlueprintId).EditorTemplateName("MaintenanceBlueprintName").ClientTemplate("#=MaintenanceBlueprintName ? MaintenanceBlueprintName : ''#").Title("Maintenance Blueprint");
                                                            c.Bound(p => p.RunEnabled).Title("Run Enabled").ClientTemplate("#if(RunEnabled){#Yes#}else{#No#}#");
                                                            if (Html.IsMaintenanceAdmin() || Html.IsMaintenanceEngineer() || Html.IsGlobalAdmin())
                                                            {
                                                                c.Template(@"
                                                                    <a href='javascript: void(0)' class='btn btn-primary btn-sm' onclick='editRow(this)' title='edit button'><i class='fa fa-pencil' style='margin-right: 0.3rem !important;'></i>Edit</a>
                                                                    <a href='javascript: void(0)' class='btn btn-danger btn-sm' onclick='deleteRow(this)' title='delete button'><i class='fa fa-trash' style='margin-right: 0.3rem !important;'></i>Delete</a>").Width(175);
                                                            }
                                                        })
                                                        .Editable(editable => editable
                                                        .Mode(GridEditMode.PopUp)
                                                        .TemplateName("MaintenanceStep")
                                                        .Window(w => w
                                                        .Name("maintenanceStep")
                                                        .Title("Select Maintenance Blueprint")
                                                        .Width(800)
                                                        .Draggable(false)
                                                        ))
                                                        .ToolBar(t => t
                                                        .Create().Text("Add Maintenance Blueprint"))
                                                        .Sortable()
                                                        .Selectable()
                                                        .Filterable()
                                                        .Groupable()
                                                        .Events(e => e
                                                         .DataBound("updateEquipmentCategoryMaintenanceStepTotal")
                                                        .Edit("runEnabledChanged"))
                                                        .Scrollable()
                                                        .Resizable(c => c.Columns(true))
                                                        .ColumnMenu(c => c.Columns(true))
                                                        .AutoBind(false)
                                                        .Height(300)
                                                        .DataSource(dataSource => dataSource
                                                        .Ajax()
                                                        .ServerOperation(false)
                                                        .Events(e => e.Error("onStepError").RequestEnd("onEquipmentCategoryMaintenanceRequestEnd"))
                                                        .Model(m =>
                                                        {
                                                            m.Id(p => p.EquipmentCategoryMaintenanceStepId);
                                                        })
                                                        .Read(read => read.Action("GetEquipmentCategoryMaintenanceSteps", "Admin").Data("equipmentCategoryData"))
                                                        .Create(create => create.Action("UpdateEquipmentCategoryMaintenanceStep", "Admin").Data("equipmentData"))
                                                        .Update(update => update.Action("UpdateEquipmentCategoryMaintenanceStep", "Admin").Data("equipmentData"))
                                                        .Destroy(destroy => destroy.Action("DeleteEquipmentCategoryMaintenanceStep", "Admin"))
                                                        ))
                                                </div>
                                            </div>
                                        </div>
                                    <div class="mt-4 d-flex actionsContainer">
                                        <button class="btn btn-success btn-sm" data-bind="click:saveEquipmentCategory, enabled:categorySaveEnabled">
                                            Save Equipment Category
                                        </button>
                                        <button class="btn btn-sm btn-danger" id="delete" data-bind="visible:canDelete, click:deleteItem">
                                            <i class="fa fa-trash"></i>Delete
                                        </button>
                                    </div>
                                </form>
                            </text>);

                        tabstrip.Add().Text("Equipment")
                            .HtmlAttributes(new {@data_bind="visible:canEditEquipmentItems", @id="equipment"})
                            .Content(@<text>
                                <div style="height: calc(100vh - 360px)">
                                    @(Html.Kendo().Grid<EquipmentItemModel>()
                                        .Name("equipmentItemGrid")
                                        .Columns(columns => {
                                            columns.Bound(c => c.EquipmentNumber).Title("Equipment Number").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#?returnUrl=" +  (@ViewBag.Url) + "&equipmentCategoryId=#=EquipmentCategoryId#'>#=EquipmentItemName#</a>").Width(150);
                                            columns.Bound(c => c.EquipmentPackingListProjectName).Title("Selected To").ClientTemplate("#if(EquipmentPackingListProjectName){#<a href='" + @Url.Action("EditProject", "Operation") + "/#=EquipmentPackingListProjectId#'>#=NewProjectName#</a>#}else{#N/A#}#").Width(150);
                                            columns.Bound(c => c.ProjectName).Title("Assigned To").ClientTemplate("#=ProjectName ? EquipmentProjectName : ''#").Width(150);
                                            columns.Bound(c => c.CountryOfOrigin).Title("Country Of Origin").Width(100).Hidden(true);
                                           if(GlobalSettings.IsWellsense ){
                                                columns.Bound(c => c.USHTSCommodityCode).Title("USHTS Commodity Code").ClientTemplate("#=USHTSCommodityCode ? USHTSCommodityCode : ''#").Width(100);
                                                columns.Bound(c => c.ImportCommodityCode).Title("Import Commodity Code").ClientTemplate("#=ImportCommodityCode ? ImportCommodityCode : ''#").Width(100);
                                                columns.Bound(c => c.ExportCommodityCode).Title("Export Commodity Code").ClientTemplate("#=ExportCommodityCode ? ExportCommodityCode : ''#").Width(100);
                                                columns.Bound(c => c.WaiverCode).Title("Waiver Code").ClientTemplate("#=WaiverCode ? WaiverCode : ''#").Width(100);
                                                columns.Bound(c => c.WaiverRequirement).Title("Waiver Requirement").ClientTemplate("#=WaiverRequirement ? WaiverRequirement : ''#").Width(100);
                                           }
                                            columns.Bound(c => c.ReceivedDate).Title("Received Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                            columns.Bound(c => c.PurchasedDate).Title("Purchased Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                            columns.Bound(c => c.CommodityCode).Title("Commodity Code").Width(100).Hidden(true);
                                            columns.Bound(c => c.CurrencyName).Title("Currency").Width(100).Hidden(true);
                                            columns.Bound(c => c.Price).Format("{0:n2}").Width(100).Hidden(true);
                                            columns.Bound(c => c.DepreciatedPrice).Title("Net Book Value").Hidden(true).Format("{0:n2}").Width(100);
                                            if(!GlobalSettings.IsWellsense ){
                                                columns.Bound(c => c.PointsPerMonth).Title("Points Per Month").Hidden(true).Width(150);
                                                columns.Bound(c => c.PointsPerRun).Title("Points Per Run").Hidden(true).Width(150);
                                                columns.Bound(c => c.PointsPerMove).Title("Points Per Move").Hidden(true).Width(150);
                                                columns.Bound(c => c.Points).Title("Current Points").ClientTemplate("#if(MaintenanceSchedulePointsAlert){#<span style='color:\\#E31E33'href='\\#'>#=Points#</span>#}else{#<span href='\\#'>#=Points#</span>#}#").Width(85);
                                            }
                                            columns.Bound(c => c.DivisionName).Title("Division").Hidden(true).Width(150);
                                            columns.Bound(c => c.InternalInvoiceNumber).Title("Internal Inv.Nbr").Hidden(true).Width(150);
                                            columns.Bound(c => c.ExternalInvoiceNumber).Title("External Inv.Nbr").Hidden(true).Width(150);
                                            columns.Bound(c => c.TrackedNonAssetItem).Title("Tracked Non-Asset Item").ClientTemplate("#if(TrackedNonAssetItem){#Yes#}else{#No#}#").Hidden(true).Width(100);
                                            columns.Bound(c => c.CurrentClientLocationName).Title("Current Location").ClientTemplate("#=CurrentClientLocationName ? CurrentClientLocationName : ''#").Width(125);
                                            columns.Bound(c => c.ManufacturerCompanyName).Title("Manufacturer").ClientTemplate("#=ManufacturerCompanyName ? ManufacturerCompanyName : ''#").Width(125);
                                            columns.Bound(c => c.MaintenanceScheduleDetail).Title("Maintenance Schedule Date(s)").ClientTemplate("#if(MaintenanceScheduleDaysAlert){#<a style='color:\\#E31E33'href='\\#' onclick='scheduleDates(#=EquipmentItemId#)'>#=MaintenanceScheduleDetail#</a>#}else if(MaintenanceScheduleDetail != ''){#<a href='\\#' onclick='scheduleDates(#=EquipmentItemId#)'>#=MaintenanceScheduleDetail#</a>#}else{##=MaintenanceScheduleDetail##}#").Width(125);
                                            columns.Bound(c => c.MRCount).Title("Active MRs").ClientTemplate("#if(MaintenanceRecordCount){#<a href='\\#' onclick='maintenanceRecordCount(#=EquipmentItemId#)'>#=MRCount#</a>#} else {##=MRCount##}#").Width(125);
                                            columns.Bound(c => c.EquipmentInfo).Title("Info").Width(150);
                                            columns.Bound(c => c.AllStatusDescription).Title("Status").Encoded(false).Filterable(f => f.Operators(o => o.ForString(str => str.Clear().Contains("Contains").DoesNotContain("Does not contain")))).Exportable(false).Width(150);
                                            columns.Bound(c => c.StatusNames).IncludeInMenu(false).Title("Status").Hidden(true).Exportable(true);
                                            columns.Bound(c => c.IsPartofBundle).Visible(false);
                                            columns.Bound(c => c.Height).Title("Length").Hidden(true).Width(100);
                                            columns.Bound(c => c.HeightUnitDescription).Title("Lenght Unit").Hidden(true).Width(100);
                                            columns.Bound(c => c.Width).Title("Width").Hidden(true).Width(100);
                                            columns.Bound(c => c.WidthUnitDescription).Title("Width Unit").Hidden(true).Width(100);
                                            columns.Bound(c => c.Depth).Title("Depth").Hidden(true).Width(100);
                                            columns.Bound(c => c.DepthUnitDescription).Title("Depth Unit").Hidden(true).Width(100);
                                            columns.Bound(c => c.OuterDiameter).Title("OD").Hidden(true).Width(100);
                                            columns.Bound(c => c.OuterDiameterUnitDescription).Title("OD Unit").Hidden(true).Width(100);
                                            columns.Bound(c => c.Weight).Title("Weight").Hidden(true).Width(100);
                                            columns.Bound(c => c.WeightUnitDescription).Title("Weight Unit").Hidden(true).Width(100);
                                        })
                                        .ColumnMenu(c => c.Columns(true))
                                        .ToolBar(t => {
                                            t.Custom().Text("Add New Equipment").HtmlAttributes(new {id = "createAssetButton", @data_bind = "invisible:newEquipmentVisible"});
                                            t.Search();
                                            t.Excel().Text("Export");
                                        }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
                                        .Search(s => { s.Field(o => o.EquipmentItemName, "contains"); })

                                        .Events(e => e.DataBound("updateEquipmentTotals").Edit("equipmentItemEdit"))
                                        .Sortable()
                                        .HtmlAttributes( new { @class="justify-toolbar-content-between", @data_bind="visible:canEditEquipmentItems"})
                                        .Groupable()
                                        .AutoBind(false)
                                        .Scrollable()
                                        .Resizable(resize => resize.Columns(true))
                                        .Reorderable(reorder => reorder.Columns(true))
                                        .Excel(excel => excel
                                            .FileName(string.Format("Centerpoint_Admin_Equipment_Items_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                                            .Filterable(true)
                                            .ProxyURL(Url.Action("Export", "Admin")))
                                        .DataSource(dataSource => dataSource
                                            .Ajax()
                                            .ServerOperation(false)
                                            .Events(e => e.Error("onError").RequestEnd("onRequestEnd"))
                                            .Model(model => {
                                                model.Id(m => m.EquipmentItemId);
                                                model.Field(m => m.DepreciationYears).DefaultValue(ViewBag.DepreciationYears);
                                            })
                                            .Read(read => read.Action("GetEquipmentItems", "Admin").Data("equipmentData"))
                                            @* .Create(update => update.Action("CreateEquipmentItem", "Admin").Data("equipmentCreateData")) *@
                                            .Destroy(destroy => destroy.Action("DeleteEquipmentItem", "Admin"))
                                        )
                                    )
                                </div>
                            </text>);
                    })
                )
            </div>
        </div>
    </div>
</div>

@(Html.Kendo().Window()
.Name("createAssetWindow")
        .Title("Equipment Item")
        .Position(x=>x.Top(50))
        .Width(1000)
        .Visible(false)
        .Draggable(true)
        .Content(@<text><partial name="EditorTemplates/EquipmentItem" /></text>)
    )

@(Html.Kendo().Window()
.Name("scheduleDatesWindow")
.Width(1000)
.Height(500)
.Title("Maintenance Schedule Dates")
.Visible(false)
.Modal(true)
.Events(e => e.Open("scheduleDatesWindowOpened"))
.Content(@<text>
    @(Html.Kendo().Grid<EquipmentItemMaintenanceScheduleModel>()
        .Name("equipmentItemMaintenanceScheduleGrid")
        .Columns(columns => {
            columns.Bound(c => c.MaintenanceBlueprintName).Title("Maintenance Blueprint").Width(150);
            columns.Bound(c => c.LastDate).Format(DateConstants.DateFormat).Title("Last").Width(125);
            columns.Bound(c => c.RecurringDays).Title("Recurring Day(s)").Width(125).Visible(!GlobalSettings.IsWellsense);
            columns.Bound(c => c.RecurringMonths).Title("Recurring Month(s)").Width(125).Visible(GlobalSettings.IsWellsense);
            columns.Bound(c => c.StartDate).Title("Next").ClientTemplate("#=CompanyLocationId != null ? 'N/A' : StartDateOnly #").Width(125);
        })
        .ColumnMenu(c => c.Columns(true))
        .Sortable()
        .AutoBind(false)
        .Scrollable(s => s.Height(400))
        .Resizable(resize => resize.Columns(true))
        .Reorderable(reorder => reorder.Columns(true))
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(model => {
                model.Id(m => m.EquipmentItemMaintenanceScheduleId);
            })
            .Read(read => read.Action("GetEquipmentItemMaintenanceSchedules", "Assets").Data("equipmentItemMaintenanceScheduleData"))
        )
    )
    </text>
))



@(Html.Kendo().Window()
.Name("maintenanceRecordWindow")
.Width(1000)
.Height(500)
.Title("Maintenance Record")
.Visible(false)
.Modal(true)
.Events(e => e.Open("maintenanceRecordWindowOpened"))
.Content(@<text>
    @(Html.Kendo().Grid<MaintenanceRecordModel>()
        .Name("maintenanceRecordGrid")
        .Columns(columns => {
            columns.Bound(c => c.Number).Title("Maintenancen Record").ClientTemplate("<a href='" + @Url.Action("EditMaintenanceRecord", "Maintenance", new { @id = "" }) + "/#=MaintenanceRecordId#'>#=Number#</a>");
            columns.Bound(c => c.MaintenanceBlueprintId).Title("Maintenancen Blueprint").ClientTemplate("<a href='" + @Url.Action("EditMaintenanceBlueprint", "Admin", new { @id = "" }) + "/#=MaintenanceBlueprintId#'>#=MaintenanceBlueprintName#</a>");
            columns.Bound(c => c.EquipmentItemName).Title("Equipment Item").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#'>#=EquipmentItemName#</a>");
            columns.Bound(c => c.PriorityDescription).Title("Priority");
            columns.Bound(c => c.UserName).Title("Created By").Hidden(true);
            columns.Bound(c => c.Created).Title("Created").Format(DateConstants.DateTimeFormat);
            columns.Bound(c => c.Modified).Title("Modified").Format(DateConstants.DateTimeFormat).Hidden(true);
            columns.Bound(c => c.StatusDescription).Title("Status").ClientTemplate("<span class='badge' style='background:#=StatusColour#;color:#=StatustextColour#'>#=StatusDescription#</span>");
        })
        .ColumnMenu(c => c.Columns(true))
        .Filterable()
        .Sortable()
        .Groupable()
        .Scrollable(s => s.Height(532))
        .Resizable(resize => resize.Columns(true))
        .Reorderable(reorder => reorder.Columns(true))
        .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Model(model => {
            model.Id(m => m.MaintenanceRecordId);
        })
        .Read(read => read.Action("GetMaintenanceRecordByEquipmentItemId", "Maintenance").Data("maintenanceRecordData"))))
</text>
))

<environment include="Development">
    <script src="~/js/views/assets/assetsAdmin.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/assets/assetsAdmin.min.js" asp-append-version="true"></script>
</environment>

