﻿@model CompanyContactModel
<div>
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                @Html.LabelFor(m => m.CompanyLocationName)<br />
                @(Html.Kendo().DropDownListFor(m => m.CompanyLocationId)
                    .OptionLabel("Select Location")
                    .Filter(FilterType.Contains)
                    .DataTextField("Text")
                    .DataValueField("Value")
                    .DataSource(d => d.Read("GetLocationsByCompanyId", "Lookup", new { @companyId = ViewBag.CompanyId, @partnerCompanyId = ViewBag.PartnerCompanyId }))
                    .HtmlAttributes(new { @data_value_primitive = "true", @style = "font-size: 14px;" }))
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label>Not Active</label>
                <br />
                @(Html.CheckBoxFor(m => m.NotActive))
            </div>
        </div>
    </div>
    <hr/>
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label>First Name</label>
                <br />
                @(Html.Kendo().TextBoxFor(m => m.FirstName)
                 .HtmlAttributes(new { @class = "form-control", @style = "width:100%; font-size: 14px;", tabindex = 1 }))
            </div>
            <div class="form-group">
                <label>Position</label>
                <br />
                @(Html.Kendo().TextBoxFor(m => m.Position)
                    .HtmlAttributes(new { @class = "form-control", @style = "width:100%; font-size: 14px;", tabindex = 3 }))
            </div>
            <div class="form-group">
                <label>Mobile</label>
                <br />
                @(Html.Kendo().TextBoxFor(m => m.Mobile)
                    .HtmlAttributes(new { @class = "form-control", @style = "width:100%; font-size: 14px;", tabindex = 5 }))
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label>Last Name</label>
                <br />
                @(Html.Kendo().TextBoxFor(m => m.LastName)
                    .HtmlAttributes(new { @class = "form-control", @style = "width:100%; font-size: 14px;", tabindex = 2 }))
            </div>
            <div class="form-group">
                <label>Email Address</label>
                <br />
                @(Html.Kendo().TextBoxFor(m => m.EmailAddress)
                    .HtmlAttributes(new { @class = "form-control", @style = "width:100%; font-size: 14px;", tabindex = 4 }))
            </div>
            <div class="form-group">
                <label>Work Telephone</label>
                <br />
                @(Html.Kendo().TextBoxFor(m => m.WorkTelephone)
                    .HtmlAttributes(new { @class = "form-control", @style = "width:100%; font-size: 14px;", tabindex = 6 }))
            </div>
        </div>
    </div>
    <div>
            <label>Comment</label>
            <br />
            @(Html.TextAreaFor(p => p.Comment, new { @class = "form-control", @style = "width:100%", @rows = "5", tabindex = 7 }))
    </div>
    <hr />
</div>


<script>
    $(document).ready(function () {
        removeValidation("checkbox");
    })
</script>
