﻿using System.Net;
using Centerpoint.Common;
using Centerpoint.Common.Constants;
using Centerpoint.Common.Enums;
using Centerpoint.Service.Interfaces;
using Centerpoint.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Centerpoint.Controllers
{
    public class ReportController : Controller
    {
        private readonly IReportService _reportService;
        private readonly ILogisticService _logisticService;
        private readonly string _contentType = "application/pdf";
        private readonly string formatFileName = "{0}.pdf";

        public ReportController(IReportService reportService,
            ILogisticService logisticService)
        {
            _reportService = reportService;
            _logisticService = logisticService;
        }

        [Authorize]
        public async Task<IActionResult> PrintPackingListToPdf(int id)
        {
            var resultModel = await _logisticService.ExportPackingListToPdf(id);

            var cookies = WebContext.GetAuthCookies(Request);
            var resultConverting = _reportService.ExportPackingListAsPdf(cookies,
                resultModel.Response.EquipmentPackingList.PackingListName, PDFType.PackingList, id);

            if (resultConverting.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(resultConverting.Errors);
            }

            string fileName = string.Format(formatFileName, resultModel.Response.FileName);

            return File(resultConverting.Response, _contentType, fileName);
        }
        public async Task<ActionResult> ViewPrintPackingListHTML(int id)
        {
            var resultModel = await _logisticService.ExportPackingListToPdf(id);
            return View("PDF_Reports/_PackingList", resultModel.Response);
        }
        [Authorize]
        public async Task<IActionResult> PackingListPDF(int id)
        {
            if (id <= 0)
            {
                return NotFound();
            }

            var resultModel = await _logisticService.ExportPackingListToPdf(id);

            return PartialView(resultModel.Response);
        }

        [Authorize]
        public async Task<IActionResult> EquipmentShipmentInvoice(int id)
        {
            var model = await _logisticService.GetEquipmentShipmentInvoiceById(id);

            var cookies = WebContext.GetAuthCookies(Request);

            var resultConverting = await _reportService.ExportInvoiceAsPdf(cookies,
                EquipmentShipmentPaperworkConstant.GetDescription(model.PaperworkType), PDFType.ShipmentInvoice, model);

            if (resultConverting.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(resultConverting.Errors);
            }

            string fileName = string.Format(formatFileName, model.FileName);

            return File(resultConverting.Response, _contentType, fileName);
        }
        [Authorize]
        public async Task<ActionResult> ViewEquipmentShipmentInvoiceHTML(int id)
        {
            try
            {
                var resultModel = await _logisticService.GetEquipmentShipmentInvoiceById(id);
                var viewName = ViewReportConstant.GetViewName(resultModel.PaperworkType);
                var viewPath = $"PDF_Reports/{viewName}";

                return View(viewPath, resultModel);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        [Authorize]
        public async Task<IActionResult> CommercialInvoicePDF(int id)
        {
            if (id <= 0)
            {
                return NotFound();
            }

            var resultModel = await _logisticService.GetEquipmentShipmentInvoiceById(id);

            return PartialView(resultModel);
        }

        [Authorize]
        public async Task<IActionResult> DeliveryNotePDF(int id)
        {
            if (id <= 0)
            {
                return NotFound();
            }

            var resultModel = await _logisticService.GetEquipmentShipmentInvoiceById(id);

            return PartialView(resultModel);
        }

        public async Task GenerateEquipmentReport()
        {
            if(!GlobalSettings.IsRegiis)
            await _reportService.GenerateEquipmentReport();
        }

    }
}
