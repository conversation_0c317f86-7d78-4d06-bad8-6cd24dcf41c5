﻿@model EquipmentItemMaintenanceScheduleModel
<div>
    <div class="row">
        <div class="col-md-4">
            <div class="form-group">
                <label>Maintenance Blueprint</label>
                @(Html.Kendo().DropDownListFor(m => m.MaintenanceBlueprintId)
                    .Filter("contains")
                    .OptionLabel("Select Maintenance Blueprint")
                    .DataTextField("Name")
                    .DataValueField("MaintenanceBlueprintId")
                    .DataSource(dataSource => dataSource.Read(read => read.Action("GetAllMaintenanceBlueprintsByCategoryId", "Lookup").Data("maintenanceBlueprintData")))
                )
            </div>
        </div>
        @if (GlobalSettings.IsWellsense)
        {
        <div class="col-md-4">
            <div class="form-group">
                <label>Company Location</label>
                @(Html.Kendo().DropDownListFor(m => m.CompanyLocationId)
                    .OptionLabel("Select Company Location")
                    .DataTextField("Name")
                    .DataValueField("CompanyLocationId")
                    .Filter("contains")
                        .Events(x => x.Change("onCompanyLocationIdChange").DataBound("onCompanyLocationIdChange"))
                    .HtmlAttributes( new { data_value_primitive = "true"})
                    .DataSource(source =>
                    {
                        source.Read(read =>
                        {
                            read.Action("GetLocationsWellsense", "Lookup");
                        });
                    }))
            </div>
        </div> 
        }
    </div>
    <div  class="row" id="maintenanceScheduleDates">
        <div class="col-md-4">
            <div class="form-group">
                <label>Next Date</label>
                @(Html.Kendo().DatePickerFor(m => m.StartDate))
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                @if (GlobalSettings.IsWellsense)
                {
                    <label>Recurring Months</label>
                    @(Html.Kendo().IntegerTextBoxFor(m => m.RecurringMonths)
                        .Spinners(false)
                        .Min(1))
                }
                else
                {
                    <label>Recurring Days</label>
                    @(Html.Kendo().IntegerTextBoxFor(m => m.RecurringDays)
                        .Spinners(false)
                        .Min(1))
                }
            </div>
        </div>  
    </div>
</div>



