﻿using Centerpoint.Common;
using Centerpoint.Extensions;
using Centerpoint.Model.Configuration;
using Centerpoint.Model.ViewModels;
using Centerpoint.Model.ViewModels.Logistics;
using Centerpoint.Service.Interfaces;
using Centerpoint.Services;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Net;

namespace Centerpoint.Controllers
{
    [Authorize]
    public class OperationController : Controller
    {
        private readonly ICurrentUserService _currentUser;
        private readonly IOperationService _operationService;

        public OperationController(ICurrentUserService currentUser, IOperationService operationService)
        {
            _currentUser = currentUser;
            _operationService = operationService;
        }

        public async Task<ActionResult> ClosedProject()
        {
            ViewBag.Title = "Closed Projects";
            ViewBag.Tab = "closed";
            return View("Status");
        }

        public async Task<ActionResult> LostProject()
        {
            ViewBag.Title = "Lost Projects";
            ViewBag.Tab = "lost";
            return View("Status");
        }

        public async Task<ActionResult> EmployeeOnSite()
        {
            ViewBag.Title = "Employee On-Site";
            return View("EmployeeOnSite");
        }

        public async Task<ActionResult> Index()
        {
            var model = await _operationService.GetOperationDashboardModel();

            this.SetTitle("Operations");

            return View(model);
        }

        public async Task<int> EmployeeOnSiteCount()
        {
            var count = await _operationService.EmployeeOnSiteCount();

            return count;
        }


        #region Project

        public async Task<ActionResult> GetProjects(string status)
        {
            var result = await _operationService.GetProjects(status);

            return Json(result);
        }

        public async Task<ActionResult> GetProjectInfoById(int id)
        {
            var result = await _operationService.GetProjectInfoById(id);

            return Json(result);
        }

        public async Task<ActionResult> GetProjectData([DataSourceRequest] DataSourceRequest project, string status)
        {
            var result = (await _operationService.GetProjectData(status)).ToDataSourceResult(project);

            return Json(result);
        }

        public async Task<ActionResult> AddProject()
        {
            var model = new ProjectModel
            {
                UserId = _currentUser.UserId,
                ProjectCreationDate = DateTime.UtcNow,

            };

            this.SetTitle("Add New Project");

            return View("EditProject", model);
        }

        public async Task<ActionResult> EditProject(int id, string tab, bool? isCreated)
        {
            var model = await _operationService.GetProject(id, _currentUser.UserId);
            this.SetTitle(string.Format("{0}", model.ProjectTitle));
            ViewBag.Tab = tab;

            return View(model);
        }
        //public async Task<ActionResult> GetProjectDropdownData(int id)
        //{
        //    var model = await _operationService.GetProjectDropdownData(id, _currentUser.UserId);

        //    return Json(model);
        //}

        public async Task<ActionResult> GetProjectMultiData(int id)
        {
            var model = await _operationService.GetProjectMultiData(id, _currentUser.UserId);

            return Json(model);
        }

        [HttpPost]
        public async Task<ActionResult> CreateProject(ProjectModel model)
        {
            if (model.IsOther && (model.OtherObjectives == null))
            {
                ModelState.AddModelError("OtherObjectives", "Extra objective(s) cannot be empty.");
            }
            if (!ModelState.IsValid)
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)));
            }

            var result = await _operationService.EditProject(model, _currentUser.UserId);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new { projectId = result.Response });
        }


        [HttpPost]
        public async Task<IActionResult> EditProject(ProjectModel model)
        {
            if (model.IsOther && (model.OtherObjectives == null))
                ModelState.AddModelError("OtherObjectives", "Extra objective(s) cannot be empty.");
            if (GlobalSettings.IsWellsense)
            {
                if (string.IsNullOrEmpty(model.ProjectTitle))
                {
                    ModelState.AddModelError("ProjectTitle", "The Project Title field is required.");
                }
            }
            if (!ModelState.IsValid)
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)));
            }


            var result = await _operationService.EditProject(model, _currentUser.UserId);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                return Json(result.Errors);
            }

            this.SetMessage(MessageType.Success, string.Format("'{0}' Project has been successfully updated", model.Name));

            return Json(result);
        }

        public async Task<ActionResult> DeleteProject(int id)
        {
            var project = await _operationService.DeleteProject(id);
            this.SetMessage(MessageType.Success, string.Format("'{0}' Project has been successfully deleted", project.Name));

            return RedirectToAction("Index");
        }

        #region Project Status

        public async Task<ActionResult> UpdateStatus(int id, string status)
        {
            await _operationService.UpdateStatus(id, status, _currentUser.UserId);

            return RedirectToAction("EditProject", new { @id = id });
        }

        #endregion

        #region Project Attachment Documents

        public async Task<ActionResult> GetProjectDocuments([DataSourceRequest] DataSourceRequest request, int projectId)
        {
            var result = (await _operationService.GetProjectDocuments(projectId)).ToDataSourceResult(request);

            return Json(result);
        }

        public async Task<ActionResult> AttachProjectDocuments(IEnumerable<IFormFile> projectAttachmentDocuments, int projectId)
        {
            await _operationService.AttachProjectDocuments(projectAttachmentDocuments, projectId, _currentUser.UserId);

            return Json(true);
        }

        public async Task<ActionResult> DeleteProjectDocument([DataSourceRequest] DataSourceRequest request, DocumentModel model, int projectId)
        {
            await _operationService.DeleteProjectDocument(model, projectId, _currentUser.UserId);

            return Json(ModelState.ToDataSourceResult());
        }

        #endregion

        #region Project Comment

        public async Task<ActionResult> GetProjectComments([DataSourceRequest] DataSourceRequest request, int projectId)
        {
            return Json((await _operationService.GetProjectComments(projectId)).ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateProjectComment([DataSourceRequest] DataSourceRequest request, ProjectCommentModel model, int pId)
        {
            var result = await _operationService.UpdateProjectComment(model, pId, _currentUser.UserId);

            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteProjectComment([DataSourceRequest] DataSourceRequest request, ProjectCommentModel model)
        {
            var result = await _operationService.DeleteProjectComment(model, _currentUser.UserId);

            return Json(result.ToDataSourceResult(request));
        }

        #endregion

        #region Crews

        public async Task<ActionResult> GetCrews([DataSourceRequest] DataSourceRequest request)
        {
            var result = await _operationService.GetCrews();

            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetCrewsbyProjectId([DataSourceRequest] DataSourceRequest request, int projectId)
        {
            var data = await _operationService.GetCrewsbyProjectId(projectId);
            return Json(data.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetCrewsOnsite([DataSourceRequest] DataSourceRequest request)
        {
            var result = await _operationService.GetCrewsOnsite();

            return Json(result.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateCrew([DataSourceRequest] DataSourceRequest request, CrewModel model, int projectId)
        {
            var (result, data) = await _operationService.UpdateCrew(model, projectId, _currentUser.UserId);
            if (!result.Succeeded)
            {
                return Json(new DataSourceResult
                {
                    Errors = result.Error
                });
            }

            return Json(data.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteCrew([DataSourceRequest] DataSourceRequest request, CrewModel model, int projectId)
        {
            var result = await _operationService.DeleteCrew(model, projectId, _currentUser.UserId);
            return Json(result.ToDataSourceResult(request));
        }

        #endregion

        public async Task<ActionResult> GetCompanyWellDocumentsByProjectId([DataSourceRequest] DataSourceRequest request, int pId)
        {
            var result = await _operationService.GetCompanyWellDocumentsByProjectId(pId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> CheckCrewExists(int crewId)
        {
            var result = await _operationService.CheckCrewExists(crewId);
            return Json(result);
        }

        #endregion

        #region Job

        public async Task<ActionResult> GetJobs(int projectId)
        {
            var result = await _operationService.GetJobs(projectId);
            return Json(result);
        }

        public async Task<ActionResult> GetJobListItem(int projectId)
        {
            var result = await _operationService.GetJobListItem(projectId);

            return Json(result);
        }

        public async Task<ActionResult> GetJobInfoById(int jobId)
        {
            var result = await _operationService.GetJobInfoById(jobId);
            return Json(result);
        }

        public async Task<ActionResult> AddJob(int id, int companyId)
        {
            var model = await _operationService.AddJob(id, companyId, _currentUser.UserId);
            this.SetTitle("Add New Job");
            return View("EditJob", model);
        }

        public async Task<ActionResult> EditJob(int id, string tab)
        {
            var model = await _operationService.EditJob(id, _currentUser.UserId);

            this.SetTitle(model.Name);

            ViewBag.Tab = tab;

            return View(model);
        }

        [HttpPost]
        public async Task<ActionResult> EditJob(JobModel model)
        {
            if (model.JobCreationDate.Value < model.ProjectProjectCreationDate)
            {
                ModelState.AddModelError("JobCreationDate", "Job Creation Date and time cannot be set before the Project Creation date and time");
            }

            if (!ModelState.IsValid)
            {
                return View(model);
            }

            var jobId = await _operationService.EditJob(model, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("'{0}' Job details have been successfully updated", model.JobName));

            return RedirectToAction("EditJob", new { @id = jobId });
        }

        public async Task<ActionResult> DeleteJob(int id)
        {
            var job = await _operationService.DeleteJob(id, _currentUser.UserId);

            return RedirectToAction("EditProject", new { @id = job.ProjectId });
        }

        #region Job Comment

        public async Task<ActionResult> GetJobComments([DataSourceRequest] DataSourceRequest request, int jobId)
        {
            var result = await _operationService.GetJobComments(jobId);

            return Json(result.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateJobComment([DataSourceRequest] DataSourceRequest request, JobCommentModel model, int jId)
        {
            if (string.IsNullOrEmpty(model.Comment))
            {
                ModelState.AddModelError("Comment", "Comment cannot be empty");
            }

            if (!ModelState.IsValid)
            {
                return View(model);
            }

            var result = await _operationService.UpdateJobComment(model, jId, _currentUser.UserId);

            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteJobComment([DataSourceRequest] DataSourceRequest request, JobCommentModel model)
        {
            var result = await _operationService.DeleteJobComment(model, _currentUser.UserId);

            return Json(result.ToDataSourceResult(request));
        }


        #endregion

        #endregion

        #region Run

        public async Task<ActionResult> GetRuns(int jobId)
        {
            var result = await _operationService.GetRuns(jobId);

            return Json(result);
        }

        public async Task<ActionResult> AddRun(int id)
        {
            var model = await _operationService.AddRun(id);

            this.SetTitle("Add New Run");

            return View("EditRun", model);
        }

        public async Task<ActionResult> EditRun(int id, string tab)
        {
            var model = await _operationService.GetRun(id, _currentUser.UserId);

            this.SetTitle(model.Name);

            ViewBag.Tab = tab;

            return View(model);
        }

        [HttpPost]
        public async Task<ActionResult> EditRun(RunModel model)
        {
            if (model.RunFinish < model.RunStart)
            {
                ModelState.AddModelError("RunFinish", "Run Finish date and time cannot be set before the Run Start date and time");
            } else if (model.RunStart > model.RunFinish)
            {
                ModelState.AddModelError("RunStart", "Run Start date and time cannot be set after the Run Finish date and time");
            }

            if (model.RunStart < model.JobCreationDate)
            {
                ModelState.AddModelError("RunStart", "Run Start date and time cannot be set before the Job Creation date and time");
            }

            if (model.Top > model.Bottom)
            {
                ModelState.AddModelError("Top", "Top Depth cannot be deeper than Bottom Depth");
            }

            if (!model.IsStandBy && (model.ObjectiveIds == null || !model.ObjectiveIds.Any()))
            {
                ModelState.AddModelError("ServiceIds", "At least one Service must be selected for Non-Standby Run");
            }

            if (!ModelState.IsValid)
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)));
            }

            var runId = await _operationService.EditRun(model, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("'{0}' Run details have been successfully updated", model.RunName));

            return Json(new { runId });
        }

        public async Task<ActionResult> DeleteRun(int id)
        {
            var jobId = await _operationService.DeleteRun(id, _currentUser.UserId);

            return RedirectToAction("EditJob", new { @id = jobId });
        }

        public async Task<ActionResult> CloseRun(int id)
        {
            await _operationService.CloseRun(id, _currentUser.UserId);

            return RedirectToAction("EditRun", new { @id = id });
        }

        public async Task<ActionResult> ReOpenRun(int id)
        {
            await _operationService.ReOpenRun(id);

            return RedirectToAction("EditRun", new { @id = id });
        }

        public async Task<ActionResult> GetEquipmentItemsByProjectId([DataSourceRequest] DataSourceRequest request, int projectId, int? runId)
        {
            var result = await _operationService.GetEquipmentItemsByProjectId(projectId, runId);

            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetRunEquipmentItems([DataSourceRequest] DataSourceRequest request, int runId, int projectId)
        {
            var result = await _operationService.GetRunEquipmentItems(runId, projectId);

            return Json(result.ToDataSourceResult(request));
        }


        [HttpPost]
        public async Task<ActionResult> UpdateRunEquipmentItems(int[] equipmentItemIds, int runId)
        {
            await _operationService.UpdateRunEquipmentItems(equipmentItemIds, runId, _currentUser.UserId);

            return Json(new { success = "true" });
        }

        public async Task<ActionResult> DeleteRunEquipmentItem([DataSourceRequest] DataSourceRequest request, RunEquipmentItemModel model)
        {
            var result = await _operationService.DeleteRunEquipmentItem(model, _currentUser.UserId);
            return Json(result.ToDataSourceResult(request));
        }

        #endregion

        #region Project Log

        public async Task<ActionResult> GetProjectLogByProjectId([DataSourceRequest] DataSourceRequest request, int pId)
        {
            var result = await _operationService.GetProjectLogByProjectId(pId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetProjectLogByJobId([DataSourceRequest] DataSourceRequest request, int jobId)
        {
            var result = await _operationService.GetProjectLogByJobId(jobId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetProjectLogByRunId([DataSourceRequest] DataSourceRequest request, int runId)
        {
            var result = await _operationService.GetProjectLogByRunId(runId);
            return Json(result.ToDataSourceResult(request));
        }

        #endregion

        #region Equipment Items

        [HttpPost]
        public async Task<ActionResult> AddEquipmentItemsToProject(int id, int divisionId)
        {
            await _operationService.AddEquipmentItemsToProject(id, divisionId, _currentUser.UserId);

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<ActionResult> CheckforEquipmentItems(int divisionId)
        {
            var result = await _operationService.CheckforEquipmentItems(divisionId);

            return Json(new { result });
        }

        public async Task<ActionResult> GetAllEquipmentItemsByProjectId([DataSourceRequest] DataSourceRequest request, int projectId)
        {
            var result = await _operationService.GetAllEquipmentItemsByProjectId(projectId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetAllNonAssetItemsByProjectId([DataSourceRequest] DataSourceRequest request, int pId)
        {
            var result = await _operationService.GetAllNonAssetItemsByProjectId(pId, _currentUser.UserId);
            return Json(result.ToDataSourceResult(request));
        }
        public async Task<ActionResult> GetAllEquipmentShipmentItemsByShipmentId([DataSourceRequest] DataSourceRequest request, int equipmentShipmentId)
        {
            var result = await _operationService.GetAllEquipmentShipmentItemsByShipmentId(equipmentShipmentId);
            return Json(result.ToDataSourceResult(request));
        }
        #endregion

        #region Lessons
        public async Task<ActionResult> GetLessonsByProjectId([DataSourceRequest] DataSourceRequest request, int lessonProjectId)
        {
            return Json((await _operationService.GetLessonsByProjectId(lessonProjectId)).ToDataSourceResult(request));
        }
        #endregion

        #region RISC

        public async Task<ActionResult> AddRisc(int id)
        {
            var riscNumber = await _operationService.AddRisc(id, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("RISC '{0}' have been successfully created", riscNumber));

            return RedirectToAction("EditJob", new { @id = id });
        }

        #endregion


        #region EquipmentItems

        [HttpPost]
        public async Task<ActionResult> GetOperationalEquipmentItemsNotLinkedToProjects([DataSourceRequest] DataSourceRequest request, int? equipmentCategoryId)
        {
            var result = await _operationService.GetOperationalEquipmentItemsNotLinkedToProjects(_currentUser.UserId, equipmentCategoryId, request.Page, request.PageSize);

            if (result.HasErrors())
                return Json(new DataSourceResult { Errors = result.Errors });

            request.Page = 1;
            var dataSourceResult = result.Response.ToDataSourceResult(request);
            dataSourceResult.Total = result.Count;

            return Json(dataSourceResult);
        }

        [HttpPost]
        public async Task<ActionResult> LinkEquipmentItemsToProject(int projectId, int[] equipmentItemIds)
        {
            var result = await _operationService.AddEquipmentItemsToProject(projectId, equipmentItemIds, _currentUser.UserId);

            if (result.HasErrors())
                return Json(new DataSourceResult { Errors = result.Errors });

            return Json("true");
        }

        [HttpPost]
        public async Task<ActionResult> ReceiveEquipmentItemsToProject(int projectId)
        {

            var result = await _operationService.ReceiveEquipmentItemsToProject(projectId, _currentUser.UserId);

            if (result.HasErrors())
                return Json(new DataSourceResult { Errors = result.Errors });

            return Json("true");
        }

        public async Task<ActionResult> UnlinkEquipmentItemFromProject(int equipmentItemId)
        {
            bool result = await _operationService.DeleteEquipmentItemFromProject(_currentUser.UserId, equipmentItemId);
            return Json(result);
        }

        #endregion

        public async Task<IActionResult> GetBackloadShipmentsByProjectId([DataSourceRequest] DataSourceRequest request, int id)
        {
            var result = await _operationService.GetBackloadShipmentsByProjectId(id);

            if (result.HasErrors())
                return Json(new DataSourceResult { Errors = result.Errors });

            return Json(result.Response.ToDataSourceResult(request));
        }

        [HttpGet]
        public async Task<IActionResult> GetProjectCompanyInfo(int id)
        {
            ProjectCompanyShipmentModel projectCompanyInfo = await _operationService.GetProjectCompanyInfo(id);
            return Json(projectCompanyInfo);
        }
    }
}
