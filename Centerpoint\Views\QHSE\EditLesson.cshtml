﻿@model LessonModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-bars"></i>
        @(Model.LessonId.HasValue ? Model.Number + "-" + Model.Name + "-" + Model.StatusDescription : "New Lesson")
    </h4>
</div>
<hr />


    @{Html.Kendo().TabStrip()
    .Name("salesHistoryStrips")
    .SelectedIndex(0)
    .Animation(false)
    .Items( tabstrip => {

    tabstrip.Add().Text("")
    .HtmlAttributes(new { @data_bind = "html:tabStripHeaderDetails" })
        .Selected(true)
        .Content(@<text>
            <div id="details">
                @using (Html.BeginForm("EditLesson", "Qhse", FormMethod.Post, new { @id = "lessonForm" })) {
                    @Html.ValidationSummary(false)
                    <div>
                        <div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        @Html.LabelFor(m => m.Name)
                                        <br />
                                        @(Html.Kendo().TextBoxFor(m => m.Name)
                                                        .HtmlAttributes(new { @style = "width:50%; font-size:14px", maxlength = 175 }))
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        @Html.LabelFor(m => m.Description)
                                        @Html.TextAreaFor(m => m.Description, new { @class = "form-control", @style = "width:100%;height:30vh" })
                                    </div>
                                </div>
                            </div>
                            <hr />
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        @Html.LabelFor(m => m.LessonCategoryId)
                                        <br />
                                        @(Html.Kendo().DropDownListFor(m => m.LessonCategoryId)
                                            .DataTextField("Text")
                                            .Filter(FilterType.Contains)
                                            .OptionLabel("Select Category")
                                            .DataValueField("Value")
                                            .AutoBind(false)
                                            .Events(e => e.Open("kendoDropdownOpen"))
                                            .DataSource(d => d.Read("GetLessonCategories", "Lookup")))
                                    </div>

                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Division</label>
                                        <br />
                                        @(Html.Kendo().DropDownListFor(m => m.DivisionId)
                                            .Filter(FilterType.Contains)
                                            .OptionLabel("Select Division")
                                            .DataTextField("Text")
                                            .DataValueField("Value")
                                            .Events(c => c.Change("divisionChange").Open("kendoDropdownOpen"))
                                            .AutoBind(false)
                                            .DataSource(d => d.Read("GetDivisions", "Lookup"))
                                            )
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Related Equipment</label>
                                        <br />
                                        @(Html.Kendo().MultiSelectFor(m => m.EquipmentCategoryIds)
                                            .DataTextField("Text")
                                            .DataValueField("Value")
                                            .Placeholder("Select Equipment(s)")
                                            .HtmlAttributes(new { @data_value_primitive = "true", @data_bind = "value:equipmentCategoryIds" })
                                            .AutoBind(false)
                                            .Events(e => e.Open("kendoDropdownOpen"))
                                            .DataSource(source => {
                                                source.Read(read => {
                                                    read.Action("GetEquipmentCategoryItemsByDivisionId", "Lookup").Data("divisionData");
                                                })
                                                .ServerFiltering(true);
                                            }))
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Related Services</label>
                                        <br />
                                        @(Html.Kendo().MultiSelectFor(m => m.ObjectiveIds)
                                            .Filter(FilterType.Contains)
                                            .DataTextField("Text")
                                            .DataValueField("Value")
                                            .Placeholder("Select Services...")
                                            .AutoBind(false)
                                            .Events(e => e.Open("kendoDropdownOpen"))
                                            .DataSource(source => {
                                                source.Read(read => {
                                                     read.Action("GetObjectives", "Lookup");
                                                }).ServerFiltering(true);
                                            }))
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Associated SIF</label>
                                        <br />
                                        @(Html.Kendo().DropDownListFor(m => m.ServiceImprovementId)
                                                .Filter(FilterType.Contains)
                                                .OptionLabel("Select SIF")
                                                .DataTextField("Text")
                                                .DataValueField("Value")
                                                .Events(e => e.Open("kendoDropdownOpen"))
                                                .AutoBind(false)
                                                .DataSource(d => d.Read("GetAllServiceImprovementsByStatus", "Lookup"))
                                                )
                                    </div>
                                </div>
                            </div>
                            <hr />
                            <h5 class="text-primary">Further Details</h5>
                                      <div class="row">
                                          <div class="col-md-6">
                                              <div class="form-group">

                                  @if (GlobalSettings.IsAisus)
                                  {
                                            <span class="fw-bold">Not Project Related</span>
                                  }else
                                  {
                                            <span class="fw-bold">Not Job Related</span>
                                  }
                                                    <span class="ml-2">@(
                                                     Html.CheckBoxFor(m => m.OnJob, new { @data_bind = "checked:notJobRelated", })
                                                    )</span>
                                              </div>
                                          </div>
                                      </div>
                                  
                                  <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        @Html.LabelFor(m => m.CompanyId)
                                        <br />
                                        @if (GlobalSettings.IsAisus)
                                        {
                                            @(
                                            Html.Kendo().DropDownListFor(m => m.CompanyId)
                                                        .Filter(FilterType.Contains)
                                                        .DataTextField("Text")
                                                        .DataValueField("Value")
                                                        .Events(x => x.Change("cascadeDropdownFilterHelper").Open("kendoDropdownOpen"))
                                                        .HtmlAttributes(new { @data_bind = "disabled:notJobRelated", @data_cascade_to = "ProjectId" })
                                                        .OptionLabel("Select Client")
                                                        .AutoBind(false)
                                                        .DataSource(d => d.Read("GetCompanies", "Lookup"))
                                          )
                                        }
                                        else
                                        {
                                            @(
                                            Html.Kendo().DropDownListFor(m => m.CompanyId)
                                            .Filter(FilterType.Contains)
                                            .DataTextField("Text")
                                            .DataValueField("Value")
                                            .Events(x => x.Change("cascadeDropdownFilterHelper").Open("kendoDropdownOpen"))
                                            .HtmlAttributes(new { @data_bind = "disabled:notJobRelated", @data_cascade_to = "JobId" })
                                            .OptionLabel("Select Client")
                                            .AutoBind(false)
                                            .DataSource(d => d.Read("GetCompanies", "Lookup"))
                                                )
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        @if (GlobalSettings.IsAisus)
                                        {
                                            @Html.LabelFor(m => m.ProjectId)
                                            <br />
                                            @(Html.Kendo().DropDownListFor(m => m.ProjectId)
                                            .Filter(FilterType.Contains)
                                            .DataTextField("Text")
                                            .DataValueField("Value")
                                            .OptionLabel("Select Project")
                                            .AutoBind(false)
                                            .Events(e => e.Open("kendoDropdownOpen"))
                                            .DataSource(source =>
                                            {
                                                source.Read(read =>
                                                {
                                                    read.Action("GetProjectsByCompanyId", "Lookup").Data("filterJobs");
                                                });
                                            })
                                            .HtmlAttributes(new { @data_bind = "disabled:notJobRelated" }))
                                        }
                                        else
                                        {
                                           @Html.LabelFor(m => m.JobId)
                                           <br />
                                            @(Html.Kendo().DropDownListFor(m => m.JobId)
                                           .Filter(FilterType.Contains)
                                           .DataTextField("Text")
                                           .DataValueField("Value")
                                           .OptionLabel("Select Job")
                                           .AutoBind(false)
                                           .Events(e => e.Open("kendoDropdownOpen"))
                                           .DataSource(source => {
                                               source.Read(read => {
                                                   read.Action("GetJobsByCompanyId", "Lookup").Data("filterJobs");
                                               });
                                           })
                                           .HtmlAttributes(new { @data_bind = "disabled:notJobRelated" }))
                                        }

                                        
                                    </div>
                                </div>
                            </div>
                            @if (Model.Status == LessonStatusConstant.Rejected) {
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            @Html.LabelFor(m => m.RejectReason)
                                            @Html.TextAreaFor(m => m.RejectReason, new { @rows = 5, @style = "width:100%", @disabled = "disabled" })
                                        </div>
                                    </div>
                                </div>
                            }
                            <div class="d-flex w-100 actionsContainer mt-2">
                                @if (!Model.LessonId.HasValue) {
                                    <button type="submit" class="btn btn-success btn-sm">Submit Lesson</button>
                                }
                                @if ((Html.IsGlobalAdmin() || Html.IsQhseAdmin() || Html.IsLessonsLearnedAdmin()) && Model.LessonId.HasValue) {
                                    <button type="submit" class="btn btn-primary btn-sm">Save</button>
                                }
                                @if (Model.Status == LessonStatusConstant.Rejected && Model.IsRaisedByUser) {
                                    <button class="btn btn-success btn-sm" data-bind="click:reSubmitLessonClick"><i class="fa fa-thumbs-up"></i>Re-Submit Lesson</button>
                                }
                                @if (Model.LessonId.HasValue && Model.Status == LessonStatusConstant.PendingApproval && (Html.IsGlobalAdmin() || Html.IsQhseAdmin() || Html.IsLessonsLearnedAdmin())) {
                                    <a class="btn btn-success btn-sm" data-bind="click:approveLessonClick"><i class="fa fa-thumbs-up"></i>Approve</a>
                                    <a class="btn btn-danger btn-sm" data-bind="click:showRejectWindow"><i class="fa fa-thumbs-down"></i>Reject</a>
                                }

                                @if (Model.Status == LessonStatusConstant.Rejected && Model.IsRaisedByUser) {
                                    <button class="btn btn-danger btn-sm" data-bind="click:deleteLesson"><i class="fa fa-thumbs-down"></i>Abandon Lesson</button>
                                }

                                @if (Model.Status == LessonStatusConstant.Approved && (Html.IsGlobalAdmin() || Html.IsQhseAdmin() || Html.IsLessonsLearnedAdmin())) {
                                    <a class="btn btn-danger btn-sm" data-bind="click:deleteLesson"><i class="fa fa-thumbs-down"></i>Delete Lesson</a>
                                }
                            </div>

                        </div>
                        @if(Model.LessonId.HasValue || Model.Status == LessonStatusConstant.Rejected || Model.Status == LessonStatusConstant.Approved) {
                            <hr />
                            <div>
                                    @if (Model.LessonId.HasValue) {
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Lesson Submitted By </label>
                                                    <br />
                                                    <h4>@Model.SubmittedBy</h4>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Lesson Submitted Date </label>
                                                    <br />
                                                    <h4 class="utcTimePicker">@Model.SubmittedDate.GetValueOrDefault()</h4>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                    @if (Model.Status == LessonStatusConstant.Rejected) {
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Lesson Rejected By </label>
                                                    <br />
                                                    <h4>@Model.RejectedBy</h4>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Lesson Rejected Date </label>
                                                    <br />
                                                    <h4 class="utcTimePicker">@Model.RejectedDate.GetValueOrDefault()</h4>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                    @if (Model.Status == LessonStatusConstant.Approved) {
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Lesson Approved By </label>
                                                    <br />
                                                    <h4>@Model.ApprovedBy</h4>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Lesson Approved Date </label>
                                                    <br />
                                                    <h4 class="utcTimePicker">@Model.ApprovedDate.GetValueOrDefault()</h4>
                                                </div>
                                            </div>
                                        </div>
                                    }
                            </div>
                        }
                    </div>

                    @Html.HiddenFor(m => m.LessonId)
                    @Html.HiddenFor(m => m.SubmittedDate)
                    @Html.HiddenFor(m => m.SubmittedBy)
                }
            </div>
        </text>);
        
        if (Model.LessonId.HasValue) {
            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind="html:attachmentsTabStrip"})
                .Content(@<text>

                    <div id="attachments">
                        @if ((!Html.IsGlobalAdmin() || !Html.IsLessonsLearnedAdmin()) && Model.Status == LessonStatusConstant.PendingApproval) {
                            <br />
                            <p>Click the link below to attach documents to this Lesson</p>
                            @(Html.Kendo().Upload()
                                        .Name("lessonAttachmentDocuments")
                                        .Messages(m => m.Select("Attach Lesson Documents"))
                                        .Multiple(true)
                                        .Events(e => e.Success("onLessonDocumentAttached").Complete("onLessonDocumentComplete").Upload("onLessonDocumentUpload"))
                                        .HtmlAttributes(new { @style = "width:300px" })
                                        .Async(async => async.Save("AttachLessonDocuments", "Qhse", new { @lessonId = Model.LessonId }).Batch(true)))
                        } else if (Html.IsGlobalAdmin() || Html.IsLessonsLearnedAdmin()) {
                            @(Html.Kendo().Upload()
                                        .Name("lessonAttachmentDocuments")
                                        .Messages(m => m.Select("Attach Lesson Documents"))
                                        .Multiple(true)
                                        .Events(e => e.Success("onLessonDocumentAttached").Complete("onLessonDocumentComplete").Upload("onLessonDocumentUpload"))
                                        .HtmlAttributes(new { @style = "width:300px" })
                                        .Async(async => async.Save("AttachLessonDocuments", "Qhse", new { @lessonId = Model.LessonId }).Batch(true)))
                        }
                        <br />
                        @(Html.Kendo().Grid<DocumentModel>()
                                .Name("lessonDocumentsGrid")
                                .Columns(c => {
                                c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                                c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat).Width(150);
                                c.Bound(p => p.UserName).Title("Created By").Width(200);
                                c.Command(command => { 
                                        command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
                                    });
                                })
                                .Events(e => e.DataBound("updateLessonDocumentGrid"))
                                .Sortable()
                                .Resizable(r => r.Columns(true))
                                .Filterable()
                                .Groupable()
                                .Editable(e => e.Mode(GridEditMode.InLine))
                                .Scrollable(s => s.Height(300))
                                .DataSource(dataSource => dataSource
                                .Ajax()
                                .ServerOperation(false)
                                .Model(model => model.Id(p => p.DocumentId))
                                .Read(read => read.Action("GetLessonDocuments", "Qhse", new { @lessonId = Model.LessonId }))
                                .Destroy(destroy => destroy.Action("DeleteLessonDocument", "Qhse", new { @lessonId = Model.LessonId }))))
                        <br />
                    </div>

                </text>);            
         }

    }).Render();
    }

@(Html.Kendo().Window().Name("rejectWindow")
    .Title("Reason for Rejection")
    .Content(@<text>
              <partial name='RejectLessonReason' model=Model />
            </text>)
    .Width(600)
    .Modal(true)
    .Visible(false))

<script>
    const editLessonModel = {
        modelLessonId: "@Model.LessonId",
        modelIsOnJob: @(Model.OnJob ? "true" : "false"),
        modelEquipmentCategoryIds: @Html.Raw(Json.Serialize(Model.EquipmentCategoryIds)),
        lessonStatusConstantApproved: "@LessonStatusConstant.Approved",
    }

    function kendoDropdownOpen(e) {
        if (!e.sender.requestFullfield) {
            e.sender.dataSource.read();
            e.sender.requestFullfield = true
        }
    }

    $(document).ready(function () {
        if (document.getElementById('LessonId').value) {
            $.ajax({
                url: `/Qhse/GetLessonDropdownData`,
                data: { id: "@(Model.LessonId)" },
                dataType: "json",
                method: 'GET',
                success: (e) => {
                    e.map(d => {
                        if (d.Item) {
                            $(`#${d.Name} `).data("kendoDropDownList")?.dataSource.add(d.Item);
                            $(`#${d.Name}`).data("kendoDropDownList")?.value(d.Item.Value);
                        } 
                        else if(d.Items) {
                            let values = [];
                            d.Items.map(i => {
                                $(`#${d.Name}`).data("kendoMultiSelect")?.dataSource.add(i);
                                values.push(i.Value)
                            })

                            $(`#${d.Name}`).data("kendoMultiSelect")?.value(values);
                        }
                    })
                },
                error: (e) => {
                    kendo.alert(e)
                }
            });
        }
    });

</script>    

<environment include="Development">
    <script src="~/js/views/qhse/editLesson.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/qhse/editLesson.min.js" asp-append-version="true"></script>
</environment>
