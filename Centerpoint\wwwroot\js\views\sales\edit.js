$(document).ready(function () {
    var companyContactGrid = $('#companyContactGrid').data("kendoGrid");

    $("#convertToProjectButton").kendoTooltip({
        content: "<div class='tooltip-content'><strong><div>Following fields must have a value</div> <div>before converting to a project :</div></strong><br/><p>Field</p><p>Well</p><p>Description</p><p>Rig Type</p><p>Conveyance Method</p><p>Value</p><p>FTA Required</p><p>New Personnel Visa Required</p><p>Objectives</p><p>Special Requirements</p><p>Qhse Consideration</p><p>Mobilsation Date</p><p>Primary Contact</p></div>",
        }
      );

    if(companyContactGrid){
        companyContactGrid.bind('dataBound', function (e) {
            this.element.find('.k-add').remove();
            this.element.find('.k-i-excel').remove();
        });
    }

    loadOpportunityEventsGrid();
    loadOpportunityActionsGrid()
    loadCompanyWellGrid();
    companyContactAddToLeadBtnDisabled(true)
    preventSubmitOnEnter();
});

function companyContactAddToLeadBtnDisabled(isDisable) {
    $("#companyContactAddToLeadBtn").attr("disabled", isDisable);
}

function onCompanyContactError(args) {
    if (args.errors) {
        var grid = this;
        grid.one("dataBinding", function (e) {
            alert(args.errors);
        });
    }
    var companyContactGrid = $("#companyContactGrid").data("kendoGrid");
    companyContactGrid.dataSource.read();
}

function companyWellData() {
    return {
        cwId: viewModel.get("companyWellId")
    }
}

function updateCompanyWellDocumentsGrid() {
    var wellDocumentsGrid = $("#companyWellDocumentsGrid").data("kendoGrid");
    var totalWellDocuments = wellDocumentsGrid.dataSource.total();
    viewModel.set("totalWellDocuments", totalWellDocuments);
}

function wellAttachmentCount(companyWellId) {
    viewModel.set("companyWellId", companyWellId);

    $("#wellAttachmentWindow").data("kendoWindow").center().open();
}

function wellAttachmentWindowOpened() {
    var wellDocumentsGrid = $("#companyWellDocumentsGrid").data("kendoGrid");

    wellDocumentsGrid.dataSource.read();
}

function showTopic() {
    var grid = $("#opportunityEventsGrid").data("kendoGrid");
    var model = grid.dataItem($(event.target).closest("tr"));
    var topic = model.Topic;
    swal({ html: true, title: '<i>Topic Details</i>', text: '<b> '+topic+' </b>' });

}

function saveOpportunityEventGrid(e) {
    setTimeout(function () {
        var grid = $("#opportunityEventsGrid").data("kendoGrid");
        localStorage["opportunityEventsGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}

function loadOpportunityEventsGrid() {
    var grid = $("#opportunityEventsGrid").data("kendoGrid");
    if(grid) {
        var toolBar = $("#opportunityEventsGrid .k-grid-toolbar").html();
        var options = localStorage["opportunityEventsGrid"];
        viewModel.set("initialOpportunityEventsGridOptions", kendo.stringify(grid.getOptions()));
        if (options) {
            grid.setOptions(JSON.parse(options));
            $("#opportunityEventsGrid .k-grid-toolbar").html(toolBar);
            $("#opportunityEventsGrid .k-grid-toolbar").addClass("k-grid-top");
        }
    }
}

function saveOpportunityActionGrid(e) {
    setTimeout(function () {
        var grid = $("#opportunityActionsGrid").data("kendoGrid");
        localStorage["opportunityActionsGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}

function loadOpportunityActionsGrid() {
    var grid = $("#opportunityActionsGrid").data("kendoGrid");
    if(grid) {
        var toolBar = $("#opportunityActionsGrid .k-grid-toolbar").html();
        var options = localStorage["opportunityActionsGrid"];
        viewModel.set("initialOpportunityActionsGridOptions", kendo.stringify(grid.getOptions()));
        if (options) {
            grid.setOptions(JSON.parse(options));
            $("#opportunityActionsGrid .k-grid-toolbar").html(toolBar);
            $("#opportunityActionsGrid .k-grid-toolbar").addClass("k-grid-top");
        }
    }
}

function saveCompanyWellGrid(e) {
    setTimeout(function () {
        var grid = $("#companyWellGrid").data("kendoGrid");
        localStorage["companyWellGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}

function loadCompanyWellGrid() {
    var grid = $("#companyWellGrid").data("kendoGrid");
    if(grid) {
        var toolBar = $("#companyWellGrid .k-grid-toolbar").html();
        var options = localStorage["companyWellGrid"];
        viewModel.set("initialCompanyWellGridOptions", kendo.stringify(grid.getOptions()));
        if (options) {
            grid.setOptions(JSON.parse(options));
            $("#companyWellGrid .k-grid-toolbar").html(toolBar);
            $("#companyWellGrid .k-grid-toolbar").addClass("k-grid-top");
        }
    }
}

function opportunityEventData() {
    return {
        opportunityEventId: viewModel.get("opportunityEventId")
    }
}

function updateCompanyWellTotal() {
    $("#resetCompanyWellGrid").click(function (e) {
        e.preventDefault();
        resetGridView('companyWellGrid', 'initialCompanyWellGridOptions')
    });

    var companyWellGrid = $("#companyWellGrid").data("kendoGrid");
    var totalWells = companyWellGrid.dataSource.total();
    viewModel.set("totalWells", totalWells);
}

function getHtmlNewLinesString(text) {
    var regexp = new RegExp('\n', 'g');
    return text ? text.replace(regexp, '<br>') : text;
}

function followUpActionCount(opportunityEventId) {
    viewModel.set("opportunityEventId", opportunityEventId);

    $("#followUpActionWindow").data("kendoWindow").center().open();
}

function followUpActionWindowOpened() {
    var followUpActionsGrid = $("#followUpActionsGrid").data("kendoGrid");
    followUpActionsGrid.dataSource.read();
}

function updateFollowUpActionsGrid() {
    var followUpActionsGrid = $("#followUpActionsGrid").data("kendoGrid");
    var totalFollowUpActions = followUpActionsGrid.dataSource.total();
    viewModel.set("totalFollowUpActions", totalFollowUpActions);
}

function onOpportunityDocumentAttached() {
    var opportunityDocumentsGrid = $("#opportunityDocumentsGrid").data("kendoGrid");
    opportunityDocumentsGrid.dataSource.read();
}

function onOpportunityDocumentUpload(e) {
    uploadValidation(e);
    $(".k-upload-files.k-reset").show();
}

function onOpportunityDocumentComplete(e) {
    $(".k-upload-files.k-reset").find("li").remove();
    $(".k-upload-files.k-reset").slideUp();
}

function updateOpportunityDocumentsGrid() {
    var opportunityDocumentsGrid = $("#opportunityDocumentsGrid").data("kendoGrid");
    var totalOpportunityDocuments = opportunityDocumentsGrid.dataSource.total();
    viewModel.set("totalOpportunityDocuments", totalOpportunityDocuments);

    refreshLogs();
}

function updateOpportunityEventDocumentsGrid() {
    var opportunityEventDocumentsGrid = $("#opportunityEventDocumentsGrid").data("kendoGrid");
    var totalOpportunityEventDocuments = opportunityEventDocumentsGrid.dataSource.total();
    viewModel.set("totalOpportunityEventDocuments", totalOpportunityEventDocuments);
}

function updateOpportunityActionDocumentsGrid() {
    var opportunityActionDocumentsGrid = $("#opportunityActionDocumentsGrid").data("kendoGrid");
    var totalOpportunityActionDocuments = opportunityActionDocumentsGrid.dataSource.total();
    viewModel.set("totalOpportunityActionDocuments", totalOpportunityActionDocuments);
}

function format(value) {
    return value.substring(0, 100);
}

function updateOpportunityEventsGrid() {
    $("#resetOpportunityEventsGrid").click(function (e) {
        e.preventDefault();
        resetGridView('opportunityEventsGrid', 'initialOpportunityEventsGridOptions')
    });

    var opportunityEventsGrid = $("#opportunityEventsGrid").data("kendoGrid");
    var totalOpportunityEvents = opportunityEventsGrid.dataSource.total();
    viewModel.set("totalOpportunityEvents", totalOpportunityEvents);
}

function updateWellDocumentsGrid() {
    var wellDocumentsGrid = $("#wellDocumentsGrid").data("kendoGrid");
    var totalOpportunityWellDocuments = wellDocumentsGrid.dataSource.total();

    viewModel.set("totalOpportunityWellDocuments", totalOpportunityWellDocuments);

}

function updateOpportunityActionsGrid() {
    $("#resetOpportunityActionsGrid").click(function (e) {
        e.preventDefault();
        resetGridView('opportunityActionsGrid', 'initialOpportunityActionsGridOptions')
    });

    var opportunityActionsGrid = $("#opportunityActionsGrid").data("kendoGrid");
    var totalOpportunityActions = opportunityActionsGrid.dataSource.total();
    viewModel.set("totalOpportunityActions", totalOpportunityActions);
}

function leadContactsRequestEnd(e) {
    if (!e.type || e.type == "destroy") {
        refreshLeadCompanyContacts();
        refreshCompanyContacts();
    }
}

function updateOpportunityLogs() {
    var opportunityLogGrid = $("#opportunityLogGrid").data("kendoGrid");
    var totalOpportunityLogs = opportunityLogGrid.dataSource.total();
    viewModel.set("totalOpportunityLogs", totalOpportunityLogs);
}

function refreshLogs(){
    var opportunityLogGrid = $("#opportunityLogGrid").data("kendoGrid");
    opportunityLogGrid.dataSource.read()
}

function updateCompanyContactTotals() {
    var companyContactGrid = $("#companyContactGrid").data("kendoGrid");
    var totalCompanyContacts = companyContactGrid.dataSource.total();
    viewModel.set("totalCompanyContacts", totalCompanyContacts);

    viewModel.set("selectedCompanyContactId", "");
    companyContactAddToLeadBtnDisabled(true)
}

function updateLeadContactTotals() {
    var leadContactGrid = $("#leadContactGrid").data("kendoGrid");
    var totalLeadContacts = leadContactGrid.dataSource.total();
    viewModel.set("totalLeadContacts", totalLeadContacts);
}

function selectedCompanyContact(e) {
    var selectedCompanyContactRow = this.select();

    if (selectedCompanyContactRow) {
        var selectedCompanyContact = this.dataItem(selectedCompanyContactRow);

        if (selectedCompanyContact) {
            viewModel.set("selectedCompanyContactId", selectedCompanyContact.CompanyContactId);
            companyContactAddToLeadBtnDisabled(false)
        } else {
            viewModel.set("selectedCompanyContactId", "");
            companyContactAddToLeadBtnDisabled(true)
        }
    } else {
        viewModel.set("selectedCompanyContactId", "");
        companyContactAddToLeadBtnDisabled(true)
    }
}

function refreshCompanyContacts() {
    var companyContactGrid = $("#companyContactGrid").data("kendoGrid");
    if(companyContactGrid) {
        companyContactGrid.dataSource.read();
    }
}

function refreshLeadCompanyContacts() {
    var leadContactGrid = $("#leadContactGrid").data("kendoGrid");
    if(leadContactGrid) {
        leadContactGrid.dataSource.read();
    }
}

function revisionSliderChange(e){
    window.location.href=`/Sales/Revision?id=${salesModel.linkOpportunityId}&revision=${e.value}`;
}
function canDeleteCompanyContact() {
    var stage = viewModel.get("stage");
   return stage == "CLS" ? false : true;
  }

$("#addContactConfirm").click(function () {
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: '/Sales/AddLeadContact',
        data: {
            companyContactId: viewModel.get("selectedCompanyContactId"),
            opportunityId: salesModel.linkOpportunityId,
            currentOpportunityId: salesModel.opportunityId,
            contactRole: $("#role").data("kendoDropDownList").value(),
        },
        success: function (response) {
            if (response.success) {
                $("#addContactRoleWindowOpen").data("kendoWindow").center().close();
                $("#role").data("kendoDropDownList").value("");
                 refreshCompanyContacts();
                refreshLeadCompanyContacts();
            }  else {
                alert(response.responseText)
                $("#addContactRoleWindowOpen").data("kendoWindow").center().close();
                $("#role").data("kendoDropDownList").value("");
            }
        },
    });
});

$("#closureReasonConfirm").click(function () {
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: '/Sales/ClosureReason',
        data: {
            opportunityId: salesModel.linkOpportunityId,
            reasonId: $("#reason").data("kendoDropDownList").value(),
            reasonText : $("#reason").data("kendoDropDownList").text(),
            comment: $("#ClosureReason").val()
        },
        success: function () {
            window.location.href= "/Sales/SalesHistory"
        },
    });
});


function companyContactEdit(e) {
    $(e.container).find(".k-edit-buttons").html("<a class='btn btn-primary btn-sm k-grid-update' href='#'>Update</a> " +
    "<a class='btn btn-primary btn-sm k-grid-cancel' href='#'>Cancel</a>");
}

function companyData() {
    return {
        companyId: viewModel.get("companyId"),
        partnerCompanyId: salesModel.partnerCompanyId,
        opportunityId: salesModel.linkOpportunityId
    };
}

function contactCompanyData() {
    return {
        companyId: viewModel.get("companyId"),
        partnerCompanyId: salesModel.partnerCompanyId,
        opportunityId: salesModel.opportunityId
    };
}

function refreshContacts() {
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: '/Sales/RefreshContacts',
        data: {
            opportunityId: salesModel.linkOpportunityId,
        },
        success: function () {
            refreshLeadCompanyContacts()
        },
    });
}

function customerChange() {
    refreshContacts();
}

function divisionChange(){
    var equipmentCategoryIds = $("#EquipmentCategoryIds").data("kendoMultiSelect");

    if(salesModel.type == salesModel.opportunity) {
        equipmentCategoryIds.dataSource.read();
    }
 }

function divisionData(e) {
    var divisionId = $("#DivisionId").data("kendoDropDownList").value();

    return {
        divisionId: divisionId,
        text: getTextValue(e)
    };
}

function companyChange(e) {
    var companyId = this.value();
    viewModel.set("companyId", companyId);

    refreshCompanyChange("CompanyCountry");
    cascadeDropdownFilterHelper(e);
}

function partnerCompanyChange(e) {
    var partnerCompanyId = this.value();
    viewModel.set("partnerCompanyId", partnerCompanyId);

    refreshCompanyChange("PartnerCountry");
    cascadeDropdownFilterHelper(e);
}

function refreshCompanyChange(countrySelector) {
    var serviceIds = $("#ServiceIds").data("kendoMultiSelect");
    var companyFieldIds = $("#CompanyFieldIds").data("kendoMultiSelect");
    var countryDropdown = $(`#${countrySelector}`).data("kendoDropDownList");

    if (companyFieldIds) {
        companyFieldIds.dataSource.read().then(() => {
            companyFieldIds.value(companyFieldIds.value());  
            companyFieldIds.trigger("change");  
        });
    }
    if (serviceIds) {
        serviceIds.dataSource.read();
    }
    if (countryDropdown) {
        countryDropdown.dataSource.read();
    }

    refreshContacts();
}

function companyFieldChange(e) {
    var companyWellIds = $("#CompanyWellIds").data("kendoMultiSelect");
    companyWellIds.dataSource.read();
}

function clientData() {
    var companyId = viewModel.get("companyId");

    return {
        companyId: companyId
      };
}

function clientDataObjectives(e) {
    var companyId = viewModel.get("companyId");
    return {
        companyId: companyId,
        text: getTextValue(e)
    };
}

function customerData(e) {
        var companyId = viewModel.get("companyId");
        var partnerCompanyId = viewModel.get("partnerCompanyId");

        return {
            companyId: companyId,
            partnerCompanyId: partnerCompanyId,
            text: getTextValue(e)
        };
    }

    function partnerData() {
        var partnerCompanyId = viewModel.get("partnerCompanyId");
        return {
            partnerCompanyId :partnerCompanyId,
        }
    }

function filterCompanyWells(e) {
    return {

        companyFieldIdString: $("#CompanyFieldIds").data('kendoMultiSelect').value().join(';'),//$("#CompanyFieldIds").data('kendoMultiSelect').value(),
        text: getTextValue(e)

    }
}

function companyWellChange(e) {
    var companyWellGrid = $("#companyWellGrid").data("kendoGrid");
    var companyWellDocumentGrid = $("#wellDocumentsGrid").data("kendoGrid");
    if(companyWellGrid) {
        companyWellGrid.dataSource.read();  
    }
    if(companyWellDocumentGrid) {
        companyWellDocumentGrid.dataSource.read();   
    }
}

function companyFieldData() {
    $.ajaxSetup({
        dataType: 'json',
        traditional: true,
        data: {
            companyWellIds: viewModel.get("companyWellIds") ? viewModel.get("companyWellIds").toJSON() : undefined,
        },
    });
}

 function addContactWindow() {
    $("#addContactRoleWindowOpen").data("kendoWindow").center().open();
}

var viewModel = new kendo.observable({
    companyId: salesModel.companyId,
    partnerCompanyId: salesModel.partnerCompanyId,
    projectId: salesModel.projectId,
    selectedCompanyContactId: 0,
    companyFieldIds: salesModel.companyFieldIds,
    companyWellIds: salesModel.companyWellIds,
    equipmentCategoryIds: salesModel.equipmentCategoryIds,
    totalCompanyContacts: 0,
    totalLeadContacts: 0,
    totalLeadContactsText: function(){
        return `<i class='fa fa-users mr-2'></i>Contacts (<span data-bind='text:totalLeadContacts'></span>)`;
    },
    salesEditDetailsText: function(){
        return `<i class="fa fa-file-text mr-2"></i>Details`;
    },
    totalOpportunityDocuments: 0,
    totalOpportunityEvents: 0,
    totalOpportunityEventsText: function(){
        return `<i class='fa fa-calendar mr-2'></i>Events (<span data-bind='text:totalOpportunityEvents'></span>)`;
    },
    totalOpportunityLogs: 0,
    totalOpportunityLogsText: function(){
        return `<i class='fa fa-clock mr-2'></i>Log (<span data-bind='text:totalOpportunityLogs'></span>)`;
    },
    totalOpportunityActions: 0,
    totalOpportunityActionsText: function(){
        return `<i class='fa fa-cogs mr-2'></i>Actions (<span data-bind='text:totalOpportunityActions'></span>)`;
    },
    stage: salesModel.modelStage,
    value: salesModel.value,
    opportunityEventId: 0,
    primaryContact :false,
    totalWells: 0,
    totalWellsText: function(){
        return `<i class='fa fa-cogs mr-2'></i>Wells (<span data-bind='text:totalWells'></span>)`;
    },
    probabilityOverridePercentage: salesModel.probabilityOverridePercentage,
    probablityPercentage: "",
    totalOpportunityActionDocuments: 0,
    totalOpportunityEventDocuments: 0,
    totalOpportunityWellDocuments: 0,
    companyContactId: 0,
    companyWellId: 0,
    totalWellDocuments: 0,
    hasProject: function () {
        var projectId = this.get("projectId");
        if (projectId > 0) {
            return false
        } else {
            return true
        }
    },
    customerVisible: function () {
        var companyId = this.get("companyId");
        var partnerCompanyId = this.get("partnerCompanyId");

        return companyId && partnerCompanyId;
    },

    totalDocuments : function(){
        return this.get("totalOpportunityDocuments") + this.get("totalOpportunityActionDocuments") + this.get("totalOpportunityEventDocuments") + this.get("totalOpportunityWellDocuments")
    },
    totalDocumentsText: function(){
        return `<i class='fa fa-file-text mr-2'></i>Attachments (<span data-bind='text:totalDocuments'></span>)`;
    },
    probabilityValue: function(){
        var projectId = this.get("projectId");
        var stage = this.get("stage");
        var value = this.get("value");
        var probabilityOverridePercentage = this.get("probabilityOverridePercentage");

        if(stage && !probabilityOverridePercentage){
            if (stage == "ENQ") {
                value = (value / 100) * 10;
                this.set("probablityPercentage", 10);
            } else if (stage == "PRP") {
                value = (value / 100) * 25;
                this.set("probablityPercentage", 25);
            } else if (stage == "REV") {
                value = (value / 100) * 50;
                this.set("probablityPercentage", 50);
            } else if (stage == "AWD") {
                value = (value / 100) * 90;
                this.set("probablityPercentage", 90);
            } else if (stage == "CLS") {
                value = projectId ? value : 0.0;
                this.set("probablityPercentage", 100);
            } else if (stage == "REL") {
                value = projectId ? value : 0.0;
                this.set("probablityPercentage", 100);
            }else {
                value = value;
            }
        } else if (probabilityOverridePercentage && stage){
            if (stage == "ENQ") {
                value = (value / 100) * probabilityOverridePercentage;
                this.set("probablityPercentage", 10);
            } else if (stage == "PRP") {
                value = (value / 100) * probabilityOverridePercentage;
                this.set("probablityPercentage", 25);
            } else if (stage == "REV") {
                value = (value / 100) * probabilityOverridePercentage;
                this.set("probablityPercentage", 50);
            } else if (stage == "AWD") {
                value = (value / 100) * probabilityOverridePercentage;
                this.set("probablityPercentage", 90);
            } else if (stage == "CLS") {
                value = (value / 100) * probabilityOverridePercentage;
                this.set("probablityPercentage", 100);
            } else if (stage == "REL") {
                value = (value / 100) * probabilityOverridePercentage;
                this.set("probablityPercentage", 100);
            } else {
                value = (value / 100) * probabilityOverridePercentage;
            }
        }

        return kendo.toString(value, 'n2');
    },
    showClosedReasonWindow: function () {
        $("#closedReasonWindowOpen").data("kendoWindow").center().open();
        $("#" + closeWindow).closest(".k-window").css({
            top: 55,
            left: 450
        });
    },
    deleteLead: function () {
        var confirmDelete = confirm("Are you sure you wish to delete this lead?");

        if (confirmDelete) {
            window.location.href = `/Sales/Delete/${salesModel.opportunityId}`;
        }
    },

    convertToProject: function () {
        var canConvertToProject = salesModel.canConvertToProject;

        if (canConvertToProject == "False") {
            $("<div id='dialog'></div>").kendoDialog({
                closable: false,
                title: "Warning!",
                close:()=>{
                    var dialog = $("#dialog").data("kendoDialog");
                    dialog.destroy();
                },
                width: 500,
                buttonLayout: "normal",
                content: "Please make sure Field, Well, Value and Objectives has value.",
                actions: [
                    {
                        text: "Close",
                        action: function(){
                        },
                        cssClass: 'btn-primary'
                    },
                ]
            }).data("kendoDialog").open().center()
        } else {
        var confirmToProject = confirm("Are you sure you wish convert this opportunity to project?");

        if (confirmToProject) {

            if(salesModel.opportunityId) {
                window.location.href = `/Sales/ConvertToProject/${salesModel.linkOpportunityId}?maxRevision=${salesModel.maxRevision}&oppsId=${salesModel.opportunityId}`;
            } else {
                window.location.href = `Sales/ConvertToProject?id=${salesModel.linkOpportunityId}&maxRevision=${salesModel.maxRevision}&oppsId=${salesModel.opportunityId}`;

            }
        }
       }
    },

    convertToOpportunity: function () {
        var confirmToOpportunity = confirm("Are you sure you wish this lead to opportunity?");

        if (confirmToOpportunity) {
            window.location.href = `/Sales/UpdateStatus?id=${salesModel.linkOpportunityId}`;
        }
    },

       revertToLead: function () {
        var confirmToLead = confirm("Are you sure you wish this revert this opportunity to lead?");

        if (confirmToLead) {
            window.location.href = `/Sales/RevertToLead?id=${salesModel.opportunityId}`;
        }
    },

    deleteOpportunity: function () {
        var confirmDelete = confirm("Are you sure you wish to delete this opportunity?");

        if (confirmDelete) {
            window.location.href = `/Sales/Delete?id=${salesModel.opportunityId}`;
        }
    },
    deleteCurrentOpportunity: function () {
        var confirmDelete = confirm("Are you sure you want to delete the current revision of this opportunity?");
        if (confirmDelete) {
            window.location.href = `/Sales/DeleteCurrentVersion?id=${salesModel.opportunityId}`;
        }
    },
});
kendo.bind(document.body.children, viewModel);