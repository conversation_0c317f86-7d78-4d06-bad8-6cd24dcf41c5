﻿@model ProjectEmail
<html>
<body>
    <p style="font-family:Arial,sans-serif">@Model.OpportunityNumber - @Model.CustomerCompany has been converted to an Upcoming Project</p>
    <p style="font-family:Arial,sans-serif">Expected Mobilisation Date : @Model.MobilisationDateOnly</p>
    @if (Model.Services.Count > 0) {
        <p style="font-family:Arial,sans-serif">Services : @Model.ServiceList</p>
        } else {
        <p style="font-family:Arial,sans-serif">Services : N/A</p>
        }
    <p style="font-family:Arial,sans-serif">Operator : @Model.CompanyName</p>
    <p style="font-family:Arial,sans-serif">Partner : @Model.PartnerCompany</p>
    @if (Model.Fields.Count > 0) {
        <p style="font-family:Arial,sans-serif">Fields : @Model.FieldList</p>
        } else {
        <p style="font-family:Arial,sans-serif">Fields : N/A</p>
        }
    @if (Model.Wells.Count > 0) {
        <p style="font-family:Arial,sans-serif">Wells : @Model.WellList</p>
        } else {
        <p style="font-family:Arial,sans-serif">Wells : N/A</p>
        }
    <p style="font-family:Arial,sans-serif">FTA Requried ? : @Model.IsFtaRequired</p>
    <p style="font-family:Arial,sans-serif">New Personnel Visa Required ? : @Model.IsNewPersonnelVisaRequired </p>
    @if (!string.IsNullOrWhiteSpace(Model.Description)) {
        <p style="font-family:Arial,sans-serif">Description : @Model.Description</p>
        } else {
        <p style="font-family:Arial,sans-serif">Description : N/A</p>
        }
    @if (!string.IsNullOrWhiteSpace(Model.Objectives)) {
        <p style="font-family:Arial,sans-serif">Objectives : @Model.Objectives</p>
        } else {
        <p style="font-family:Arial,sans-serif">Objectives : N/A</p>
        }
    @if (!string.IsNullOrWhiteSpace(Model.SpecialRequirements)) {
        <p style="font-family:Arial,sans-serif">Special Requirements : @Model.SpecialRequirements</p>
        } else {
        <p style="font-family:Arial,sans-serif">Special Requirements : N/A</p>
        }
    @if (!string.IsNullOrWhiteSpace(Model.QhseConsideration)) {
        <p style="font-family:Arial,sans-serif">QHSE Consideration : @Model.QhseConsideration</p>
        } else {
        <p style="font-family:Arial,sans-serif">QHSE Consideration : N/A</p>
        }
    <p style="font-family:Arial,sans-serif">System Integration Testing Required? : @Model.SystemIntegration</p>
    @if (!string.IsNullOrWhiteSpace(Model.Comments)) {
        <p style="font-family:Arial,sans-serif">Comments : @Model.Comments</p>
        } else {
        <p style="font-family:Arial,sans-serif">Comments : N/A</p>
        }
</body>
</html>