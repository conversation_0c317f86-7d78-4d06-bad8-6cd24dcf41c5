﻿@model OpportunityModel

<p>The following table is a log of all changes made.</p>

@(Html.Kendo().Grid<OpportunityLogModel>()
  .Name("opportunityLogGrid")
  .Columns(columns => {
      columns.Bound(c => c.Log).ClientTemplate("#=getHtmlNewLinesString(Log)#").Encoded(false);
      columns.Bound(c => c.UserName).Width(125);
      columns.Bound(c => c.Date).Title("Date").Format(DateConstants.DateTimeFormat).Width(150);
  })
  .Events(e => e.DataBound("updateOpportunityLogs"))
  .ColumnMenu(c => c.<PERSON>um<PERSON>(true))
  .Sortable()
  .Groupable()
  .Reorderable(reorder => reorder.Columns(true))
  .Resizable(resize => resize.Columns(true))
  .Scrollable(s => s.Height(400))
  .DataSource(dataSource => dataSource
    .Ajax()
    .ServerOperation(false)
    .Read(read => read
      .Action("GetOpportunityLogByOpportunityId", "Sales", new { @opportunityId = Model.LinkOpportunityId })
    )
  )
)