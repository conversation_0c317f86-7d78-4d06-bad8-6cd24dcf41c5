﻿<div class="header-container-between">
    <h4>
        <i class="fa fa-file-image"></i>
        Maintenance Blueprints
        (<span data-bind="text:totalBlueprints"></span>)
    </h4>
    <div>
       <a class="btn btn-sm btn-success" href="@Url.Action("AddMaintenanceBlueprint","Admin")">
            <i class="fa fa-plus"></i> 
            Add Maintenance Blueprint
        </a>
    </div>
</div>
<hr />

<div class="grid-container">
            @(Html.Kendo().Grid<MaintenanceBlueprintModel>()
                .Name("maintenanceBlueprintGrid")
                    .Columns(c => {
                    c.Bound(p => p.Name).ClientTemplate("<a href='" + @Url.Action("EditMaintenanceBlueprint", "Admin", new { @id = "" }) + "/#=MaintenanceBlueprintId#'>#=Name#</a>");
                    c.<PERSON>(p => p.ComplexityDescription);
                    c.<PERSON>und(p => p.EquipmentCategories).Title("Related Equipment(s)").ClientTemplate("#=EquipmentCategories ? EquipmentCategories : 'N/A'#");
                    c.<PERSON>(p => p.ApprovalRequired).ClientTemplate("#= ApprovalRequired ? 'Yes' : 'No' #");
                    c.Bound(p => p.ResetMaintenancePoints).ClientTemplate("#= ResetMaintenancePoints ? 'Yes' : 'No' #");
                    c.Bound(p => p.DivisionName).Title("Division");
                    c.Bound(p => p.UserName);
                    c.Bound(p => p.Modified).Format(DateConstants.DateTimeFormat);
                    c.Bound(p => p.Created).Format(DateConstants.DateTimeFormat);
                    c.Bound(p => p.Steps);
                    c.Template("<a class='btn btn-sm btn-primary' href='" + Url.Action("CloneMaintenanceBlueprint", "Admin") + "/#=MaintenanceBlueprintId#'><i class='fa fa-copy'></i><span class='ml-1'>Duplicate</span></a>");
                })
                .ToolBar(t => {
                    t.Custom().Text("Reset Grid View").HtmlAttributes(new { @id="resetMaintenanceBlueprintGrid", @class="bg-danger text-white"});
                    t.Excel().Text("Export");
                }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
                .Sortable()
                .Filterable()
                .Scrollable()
                .Resizable(c => c.Columns(true))
                .ColumnMenu(c => c.Columns(true))
                .Reorderable(c => c.Columns(true))
                .Events(e => e.DataBound("updateMaintenanceBlueprintTotal").ColumnReorder("saveMaintenanceBlueprintGrid").ColumnResize("saveMaintenanceBlueprintGrid").ColumnShow("saveMaintenanceBlueprintGrid").ColumnHide("saveMaintenanceBlueprintGrid"))
                .Excel(excel => excel
                    .FileName(string.Format("Centerpoint_Maintenance_Blueprints_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                    .Filterable(true)
                    .ProxyURL(Url.Action("Export", "Admin"))
                )
                .DataSource(dataSource => dataSource
                    .Ajax()
                    .ServerOperation(false)
                    .Read(read => read.Action("GetMaintenanceBlueprints", "Admin"))
                )
            )
</div>

<environment include="Development">
    <script src="~/js/views/maintenance/maintenanceBlueprintsAdmin.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/maintenance/maintenanceBlueprintsAdmin.min.js" asp-append-version="true"></script>
</environment>