﻿@model ObjectiveModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-list-alt"></i>
        Service
        (<span data-bind="text:totalObjectives"></span>)
    </h4>
</div>
<hr />

<div class="grid-container">
    @(Html.Kendo().Grid<ObjectiveModel>()
        .Name("objectiveGrid")
        .Columns(c => {
            c.<PERSON><PERSON>(p => p.Name);
            c.<PERSON>(p => p.<PERSON>);
            c.<PERSON>(p => p.CompanyId).EditorTemplateName("CompanyList").Title("Client").ClientTemplate("#=CompanyName ? CompanyName : 'N/A' #");
            c.Command(command => { 
                command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
            });
            })
        .Editable(editable => editable.Mode(GridEditMode.InLine))
        .ToolBar(t => {
            t.Create().Text("Add Service");
            t.Excel().Text("Export");
        }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
        .Sortable()
        .Filterable()
        .Scrollable()
        .Height("100%")
        .Resizable(c => c.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .Events(e => e.DataBound("updateObjectiveTotal"))
        .Excel(excel => excel
            .FileName(string.Format("Centerpoint_Operations_Services_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Admin"))
        )
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(m => m.Id(p => p.ObjectiveId))
            .Events(e => e.Error("onError"))
            .Read(read => read.Action("GetObjectives", "Admin"))
            .Create(create => create.Action("UpdateObjective", "Admin"))
            .Update(update => update.Action("UpdateObjective", "Admin"))
            .Destroy(destroy => destroy.Action("DeleteObjective", "Admin"))
        )
    )
</div>

    <script>
        function updateObjectiveTotal() {
            var objectiveGrid = $("#objectiveGrid").data("kendoGrid");
            var totalObjectives = objectiveGrid.dataSource.total();
            viewModel.set("totalObjectives", totalObjectives);
        }

        function onError(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var objectiveGrid = $("#objectiveGrid").data("kendoGrid");
                objectiveGrid.dataSource.cancelChanges();
            }
        }

        var viewModel = new kendo.observable({
            totalObjectives: 0
        });

        kendo.bind(document.body.children, viewModel);
    </script>
