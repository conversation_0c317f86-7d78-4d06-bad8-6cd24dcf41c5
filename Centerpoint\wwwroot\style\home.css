@import url('pageCorrections.css');
.card-list-item{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: .5rem;
}
.card-list-item-count {
    min-height: 25px;
    min-width: 25px;
    border-radius: 2px;
    font-size: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ffffff;
    padding: 4px;
    cursor: pointer;
}

/* edit and delete buttons in grid */

.grid-action-button {
    font-size: 1rem;
}

.small-switch {
    transform: scale(0.425);
    transform-origin: center;
}