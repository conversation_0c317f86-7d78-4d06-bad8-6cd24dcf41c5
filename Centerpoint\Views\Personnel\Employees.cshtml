﻿@model PersonnelEmployeeDashboardModel

<div class="header-container-between">
    <h4>
        <i class="fa fa-users"></i>
        Employees
        (<span data-bind="text:totalUsers"></span>)
    </h4>
    <div>
        <form class="form-horizontal" role="form">
            <div class="form-group d-flex align-items-center mb-0">
                <label for="userSearch" class="col-lg-5 control-label mb-0">Quick Search</label>
                <div>
                    <input id="userSearch" type="text" class="form-control" />
                </div>
            </div>
        </form> 
    </div>
</div>
<hr />

<div>
    <div class="row">
        <div class="col-md-6">
            <div class="row">
            @*  <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Employees</h6>
                        </div>
                        <div class="card-body">

                            <div class="card-list-item">
                                <span class="card-list-item-name">Total Employees</span>
                                <a class="card-list-item-count" href="#userGrid" data-bind="click:totalClick" style="background: black;" title="Click here to show All Employees">@Model.TotalEmployees</a>
                            </div>
                            <div class="card-list-item">
                                <span class="card-list-item-name">Total Active</span>
                                <a class="card-list-item-count" href="#userGrid" data-bind="click:activeClick" style="background: #999;" title="Click here to show Total Active">@Model.TotalActive</a>
                            </div>
                            <div class="card-list-item">
                                <span class="card-list-item-name">Total Inactive</span>
                                <a class="card-list-item-count" href="#userGrid" data-bind="click:inactiveClick" style="background: #BA141A;" title="Click here to show Total InActive">@Model.TotalInActive</a>
                            </div>
                        </div>
                    </div> 
                </div> *@
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <span class="mb-0">Employees By Base</span>
                        </div>
                        <div class="card-body">
                            @foreach (var readCompanyLocationBase in Model.TotalInBases) {
                                <div class="card-list-item">
                                    <span class="card-list-item-name">@readCompanyLocationBase.Key</span>
                                    <span class="card-list-item-count" onclick="baseCompanyLocationClick('@readCompanyLocationBase.Key')" style="background: #7CBB00">@readCompanyLocationBase.Value</span>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <hr />
    @if (Html.IsGlobalAdmin() || Html.IsQhseAdmin() || Html.IsPersonnelAdministrator()) {
        @(Html.Kendo().Grid<UserModel>()
            .Name("userGrid")
            .Columns(c => {
                c.Bound(p => p.Name).Title("Name");
                c.Bound(p => p.JobTitle);
                c.Bound(p => p.RoleDescription).Hidden(true);
                c.Bound(p => p.WorkTelephone);
                c.Bound(p => p.MobileTelephone);
                c.Bound(p => p.HomeTelephone).Hidden(true);
                c.Bound(p => p.EmailAddress).ClientTemplate("<a href='mailto:#=EmailAddress#'>#=EmailAddress#</a>");
                c.Bound(p => p.LastLogin).Format(DateConstants.DateTimeFormat);
                c.Bound(p => p.BaseCompanyLocationName).Title("Base").Hidden(true);
                c.Bound(p => p.IsEnabled).Title("Enabled").ClientTemplate("#if(IsEnabled){#Yes#}else{#No#}#").Hidden(true);
                c.Bound(p => p.IsNonEmployeeAccount).Title("Non-Employee Account").ClientTemplate("#if(IsNonEmployeeAccount){#Yes#}else{#No#}#").Hidden(true);
                })
                .ToolBar(t => {
                    t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetUserGrid", @class="bg-danger text-white"});
                    t.Excel().Text("Export");
                }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
                .Sortable()
                .Filterable()
                .Groupable()
                .Excel(excel => excel
                    .FileName(string.Format("Centerpoint_RISC_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                    .Filterable(true)
                    .ProxyURL(Url.Action("Export", "Admin")))
                .Events(e => e.DataBound("updateUserTotal").ColumnReorder("saveUserGrid").ColumnResize("saveUserGrid").ColumnShow("saveUserGrid").ColumnHide("saveUserGrid"))
                .Resizable(c => c.Columns(true))
                .ColumnMenu(c => c.Columns(true))
                .Reorderable(c => c.Columns(true))
                .DataSource(dataSource => dataSource
                    .Ajax()
                    .ServerOperation(false)
                    .Model(m => {
                        m.Id(p => p.UserId);
                    })
                .Read(read => read.Action("GetUsers", "Admin").Data("userData"))))
    } else {
        @(Html.Kendo().Grid<UserModel>()
            .Name("userGrid")
            .Columns(c => {
                c.Bound(p => p.Name).Title("Name").Width(200);
                c.Bound(p => p.JobTitle).Width(200);
                c.Bound(p => p.RoleDescription).Width(250).Hidden(true);
                c.Bound(p => p.WorkTelephone).Width(200);
                c.Bound(p => p.MobileTelephone).Width(200);
                c.Bound(p => p.EmailAddress).Width(200).ClientTemplate("<a href='mailto:#=EmailAddress#'>#=EmailAddress#</a>");
                c.Bound(p => p.BaseCompanyLocationName).Title("Base").Width(200).Hidden(true);
                })
            .ToolBar(t => {
            t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetUserGrid", @class="bg-danger text-white"});
            t.Excel().Text("Export");
            }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
            .Sortable()
            .Filterable()
            .Groupable()
            .Excel(excel => excel
            .FileName(string.Format("Centerpoint_RISC_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Admin")))
            .Events(e => e.DataBound("updateUserTotal"))
            .Resizable(c => c.Columns(true))
            .ColumnMenu(c => c.Columns(true))
            .Reorderable(c => c.Columns(true))
            .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Model(m => {
                    m.Id(p => p.UserId);
                })
            .Read(read => read.Action("GetUsers", "Admin").Data("userData"))))
    }
</div>

<environment include="Development">
    <script src="~/js/views/personnel/employees.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/personnel/employees.min.js" asp-append-version="true"></script>
</environment>