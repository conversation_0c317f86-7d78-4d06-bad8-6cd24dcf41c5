﻿using Microsoft.AspNetCore.Mvc.Rendering;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;


namespace Centerpoint.Common.Constants

{
    public static class UserRoleConstant
    {

        public const string GlobalAdministrator = "GAD";
        public const string OperationsAdministrator = "OAD";
        public const string LogisticsAdministrator = "LAD";
        public const string AssetsAdministrator = "AAD";
        public const string MaintenanceAdministrator = "MAD";
        public const string QHSEAdministrator = "QAD";
        public const string StandardUser = "STU";
        public const string MaintenanceEngineer = "MAE";
        public const string LessonsLearnedAdmin = "EAD";
        public const string PersonnelAdministrator = "HAD";
        public const string FieldEngineer = "FDE";
        public const string JuniorFieldEngineer = "JFE";
        public const string SeniorFieldEngineer = "SFE";
        public const string SifAdministrator = "SAD";
        public const string DownloadAdministrator = "DAD";
        public const string SalesAdministrator = "SLD";
        public const string AnsaAdministrator = "AND";
        public const string AnsaAnalyst = "ANA";
        public const string SeniorUSEngineer = "SUE";
        public const string SifManager = "SMG";
        public const string SifActionParty = "SAP";
        public const string ReportAdministrator = "RAD";




        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static string[] SidAdminRoles
        {
            get
            {
                return new string[] { GlobalAdministrator, SifAdministrator};
            }
        }
        public static string[] SaleAdminRoles
        {
            get
            {
                return new string[] { GlobalAdministrator, SalesAdministrator };
            }
        }
        public static string[] FieldEngineerRoles
        {
            get
            {
                return new string[] { FieldEngineer, JuniorFieldEngineer, SeniorFieldEngineer, SeniorUSEngineer };
            }
        }


        public static string[] EngineerRoles
        {
            get
            {
                return new string[] { FieldEngineer, JuniorFieldEngineer, SeniorFieldEngineer, SeniorUSEngineer };
            }
        }

        public static string[] MaintenanceEngineerRoles
        {
            get
            {
                return new string[] { JuniorFieldEngineer, SeniorFieldEngineer, MaintenanceEngineer, MaintenanceAdministrator, FieldEngineer };
            }
        }

        public static string[] ShipmentReceivers
        {
            get
            {
                return new string[] { GlobalAdministrator, LogisticsAdministrator, SeniorFieldEngineer, FieldEngineer };
            }
        }

        public static string[] AssetAdmins
        {
            get
            {
                return new string[] {
                    GlobalAdministrator,
                    AssetsAdministrator
                };
            }
        }

        public static string[] Admins
        {
            get
            {
                return new string[] {
                    GlobalAdministrator,
                    AssetsAdministrator,
                    LogisticsAdministrator,
                    MaintenanceAdministrator,
                    OperationsAdministrator,
                    QHSEAdministrator,
                    PersonnelAdministrator,
                    SifAdministrator,
                    DownloadAdministrator,
                    SalesAdministrator,
                    AnsaAdministrator
                };
            }
        }
        public const string AdminRoles = $@"{GlobalAdministrator},
                                                      {AssetsAdministrator},
                                                      {LogisticsAdministrator},
                                                      {MaintenanceAdministrator},
                                                      {OperationsAdministrator},
                                                      {QHSEAdministrator},
                                                      {PersonnelAdministrator},
                                                      {SifAdministrator},
                                                      {DownloadAdministrator},
                                                      {SalesAdministrator},
                                                      {AnsaAdministrator}";


        public const string CurrenyRoles = $@"{UserRoleConstant.GlobalAdministrator}, 
                                              {UserRoleConstant.AssetsAdministrator},
                                              {UserRoleConstant.LogisticsAdministrator},
                                              {UserRoleConstant.MaintenanceAdministrator},
                                              {UserRoleConstant.PersonnelAdministrator}";

        public const string AssetAdminRoles = $@"{UserRoleConstant.GlobalAdministrator},
                                                {UserRoleConstant.AssetsAdministrator},
                                                {UserRoleConstant.AnsaAdministrator}";
        public const string SifRoles = $@"{UserRoleConstant.GlobalAdministrator},
                {UserRoleConstant.QHSEAdministrator},
                {UserRoleConstant.SifAdministrator}";




        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {AnsaAdministrator,"ANSA Administrator"},
                    {AnsaAnalyst,"ANSA Analyst"},
                    {AssetsAdministrator,"Assets Administrator"},
                    {DownloadAdministrator,"Download Administrator"},
                    {FieldEngineer,"Field Engineer"},
                    {GlobalAdministrator,"Global Administrator"},
                    {JuniorFieldEngineer,"Junior Field Engineer"},
                    {LessonsLearnedAdmin,"Lessons Learned Administrator"},
                    {LogisticsAdministrator,"Logistics Administrator"},
                    {MaintenanceAdministrator,"Maintenance Administrator"},
                    {MaintenanceEngineer,"Maintenance Engineer"},
                    {OperationsAdministrator,"Operations Administrator"},
                    {PersonnelAdministrator,"Personnel Administrator"},
                    {SalesAdministrator,"Sales Administrator"},
                    {SeniorFieldEngineer,"Senior Field Engineer"},
                    {SeniorUSEngineer,"Senior US Engineer"},
                    {SifAdministrator,"SIF Administrator"},
                    {StandardUser,"Standard User"},
                    {QHSEAdministrator,"QHSE Administrator"},
                    {SifManager,"SIF Manager"},
                    {ReportAdministrator,"Report Administrator"},
                    {SifActionParty, "SIF Action Party" }
                };
            }
        }

        public static List<SelectListItem> GetRolesSelectListItems()
        {
            var selectList = new List<SelectListItem>();

            foreach (var item in ValuesAndDescriptions)
            {
                selectList.Add(new SelectListItem
                {
                    Value = item.Key,
                    Text = item.Value,
                });
            }
            return selectList;  
        }
        //public static Dictionary<string, UserRoleModel> ValuesAndDescriptions
        //{
        //    get
        //    {
        //        return new Dictionary<string, string> {
        //             {AnsaAdministrator,"ANSA Administrator"},
        //             {AnsaAnalyst,"ANSA Analyst"},
        //             {AssetsAdministrator,"Assets Administrator"},
        //             {DownloadAdministrator,"Download Administrator"},
        //             {FieldEngineer,"Field Engineer"},
        //             {GlobalAdministrator,"Global Administrator"},
        //             {JuniorFieldEngineer,"Junior Field Engineer"},
        //             {LessonsLearnedAdmin,"LessonsLearned Administrator"},
        //             {LogisticsAdministrator,"Logistics Administrator"},
        //             {MaintenanceAdministrator,"Maintenance Administrator"},
        //             {MaintenanceEngineer,"Maintenance Engineer"},
        //             {OperationsAdministrator,"Operations Administrator"},
        //             {PersonnelAdministrator,"Personnel Administrator"},
        //             {SalesAdministrator,"Sales Administrator"},
        //             {SeniorFieldEngineer,"Senior Field Engineer"},
        //             {SeniorUSEngineer,"Senior US Engineer"},
        //             {SifAdministrator,"SIF Administrator"},
        //             {StandardUser,"Standard User"},
        //             {QHSEAdministrator,"QHSE Administrator"},
        //        };
        //    }
        //}

        public static List<string> GetAllRoles()
        {
            return new List<string>
            {
                GlobalAdministrator ,
                OperationsAdministrator ,
                LogisticsAdministrator,
                AssetsAdministrator ,
                MaintenanceAdministrator,
                QHSEAdministrator,
                StandardUser ,
                MaintenanceEngineer ,
                LessonsLearnedAdmin ,
                PersonnelAdministrator,
                FieldEngineer ,
                JuniorFieldEngineer,
                SeniorFieldEngineer,
                SifAdministrator ,
                DownloadAdministrator,
                SalesAdministrator,
                AnsaAdministrator,
                AnsaAnalyst,
                SeniorUSEngineer,
                SifManager,
                ReportAdministrator
            };
        }
    }
}
