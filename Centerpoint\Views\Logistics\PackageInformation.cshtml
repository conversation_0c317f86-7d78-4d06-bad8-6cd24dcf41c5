﻿@using Centerpoint.Model.ViewModels.Logistics;
@model EquipmentShipmentModel;

<div class="row mt-3">
    <div class="col-md-12">
        <div>
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fa fa-tags"></i>
                    Shipment Packages (<span data-bind="text: totalPackageInformation"></span>)
                </h6>
            </div>

            @(
                Html.Kendo().Grid<EquipmentShipmentPackageModel>()
                    .Name("equipmentShipmentPackageGrid")
                    .Columns(c =>
                    {
                        c.Bound(p => p.PackageNumber).Title("Package Number / ID");
                        c.Bound(p => p.TypeOfPackage).Title("Type of Package");
                        c.Bound(p => p.Length).Title("Length (cm)").Format("{0:0.00}");
                        c.Bound(p => p.Width).Title("Width (cm)").Format("{0:0.00}");
                        c.Bound(p => p.Height).Title("Height (cm)").Format("{0:0.00}");
                        c.Bound(p => p.Weight).Title("Weight (kg)").Format("{0:0.00}").ClientFooterTemplate("Total Weight : #=sum#");
                        c.Command(command => {
                            command.Edit().HtmlAttributes(new { @class = "bg-primary text-white grid-action-button" });
                            command.Destroy().HtmlAttributes(new { @class = "bg-danger text-white grid-action-button" });
                        });
                    })
                    .Editable(editable => editable.Mode(GridEditMode.InLine))
                    .ToolBar(t => {
                        t.Create().Text("Add New Package");
                    })
                    .Sortable()
                    .Filterable()
                    .Scrollable(s => s.Height(500))
                    .Resizable(r => r.Columns(true))
                    .Reorderable(r => r.Columns(true))
                    .ColumnMenu(c => c.Columns(true))
                    .Events(e => e.DataBound("updateShipmentPackagesTotals"))
                    .DataSource(dataSource => dataSource
                    .Ajax()
                    .Aggregates(aggregates =>
                    {
                        aggregates.Add(p => p.Weight).Sum();
                    })
                    .Events(e => e.Error("onError"))
                    .ServerOperation(false)
                    .Model(model => {
                        model.Id(p => p.EquipmentShipmentPackageId);            
                    }) 
                    .Read(read => read.Action("GetEquipmentShipmentPackages", "Logistics", new { @equipmentShipmentId = Model.EquipmentShipmentId }))
                .Create(create => create.Action("UpdateEquipmentShipmentPackage", "Logistics", new { @sId = Model.EquipmentShipmentId }))
                .Update(update => update.Action("UpdateEquipmentShipmentPackage", "Logistics", new { @sId = Model.EquipmentShipmentId }))
                .Destroy(destroy => destroy.Action("DeleteEquipmentShipmentPackage", "Logistics")))
            )

        </div>
    </div>
</div>

<script type="text/javascript">

    function onError(e, status) {
        if (e.status == "customerror") {
            alert(e.errors);

            var equipmentShipmentPackageGrid = $("#equipmentShipmentPackageGrid").data("kendoGrid");
            equipmentShipmentPackageGrid.dataSource.cancelChanges();
        }
    }

</script>


<environment include="Development">
    <script src="~/js/views/logistics/editEquipmentShipment.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/logistics/editEquipmentShipment.min.js" asp-append-version="true"></script>
</environment>
