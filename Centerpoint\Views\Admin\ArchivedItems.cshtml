﻿<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-tags"></i>
        Assets
    </h4>
</div>
<hr />

<div>
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0" data-bind="text:selectedAssetItemNameWithCount"></h6>
        </div>
        <div class="card-body">
            <div>
                <div class="d-flex">
                    <div class="form-group w-50 mr-5">
                        <label>Filter by Equipment No/Category</label><br />
                        @(Html.Kendo().TextBox().Name("equipmentQuery"))
                    </div>
                </div>

                @(
                    Html.Kendo().Grid<EquipmentItemModel>()
                    .Name("archivedEquipmentItemGrid")
                    .Columns(columns =>
                    {
                        columns.Bound(c => c.EquipmentItemName).Title("Equipment Number").ClientTemplate("<a href='/Assets/EditEquipmentItem/#=EquipmentItemId#?returnUrl=#=window.location.href#'>#=EquipmentItemName#</a>").Width(150);
                        columns.Bound(c => c.CountryOfOrigin).Title("Country Of Origin").Width(100).Hidden(true);
                        if (GlobalSettings.IsWellsense)
                        {
                            columns.Bound(c => c.USHTSCommodityCode).Title("USHTS Commodity Code").ClientTemplate("#=USHTSCommodityCode ? USHTSCommodityCode : 'N/A'#").Width(100);
                            columns.Bound(c => c.ImportCommodityCode).Title("Import Commodity Code").ClientTemplate("#=ImportCommodityCode ? ImportCommodityCode : 'N/A'#").Width(100);
                            columns.Bound(c => c.ExportCommodityCode).Title("Export Commodity Code").ClientTemplate("#=ExportCommodityCode ? ExportCommodityCode : 'N/A'#").Width(100);
                            columns.Bound(c => c.WaiverCode).Title("Waiver Code").ClientTemplate("#=WaiverCode ? WaiverCode : 'N/A'#").Width(100);
                            columns.Bound(c => c.WaiverRequirement).Title("Waiver Requirement").ClientTemplate("#=WaiverRequirement ? WaiverRequirement : 'N/A'#").Width(100);
                        }
                        columns.Bound(c => c.ReceivedDate).Title("Received Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                        columns.Bound(c => c.PurchasedDate).Title("Purchased Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                        columns.Bound(c => c.CurrencyName).Title("Currency").Width(100).Hidden(true);
                        columns.Bound(c => c.Price).Format("{0:n2}").Width(100).Hidden(true);
                        columns.Bound(c => c.DepreciatedPrice).Title("Net Book Value").Hidden(true).Format("{0:n2}").Width(100);

                        columns.Bound(c => c.DivisionName).Title("Division").Hidden(true).Width(150);
                        columns.Bound(c => c.InternalInvoiceNumber).Title("Internal Inv.Nbr").Hidden(true).Width(150);
                        columns.Bound(c => c.ExternalInvoiceNumber).Title("External Inv.Nbr").Hidden(true).Width(150);
                        columns.Bound(c => c.TrackedNonAssetItem).Title("Tracked Non-Asset Item").ClientTemplate("#if(TrackedNonAssetItem){#Yes#}else{#No#}#").Hidden(true).Width(100);

                        columns.Bound(c => c.CurrentClientLocationName).Title("Current Location").ClientTemplate("#=CurrentClientLocationName ? CurrentClientLocationName : 'Not Yet Accepted'#").Width(125);
                        columns.Bound(c => c.ManufacturerCompanyName).Title("Manufacturer").ClientTemplate("#=ManufacturerCompanyName ? ManufacturerCompanyName : 'N/A'#").Width(125);
                        columns.Bound(c => c.MaintenanceScheduleDetail).Title("Maintenance Schedule Date(s)").ClientTemplate("#if(MaintenanceScheduleDaysAlert){#<a style='color:\\#E31E33'href='\\#' onclick='scheduleDates(#=EquipmentItemId#)'>#=MaintenanceScheduleDetail#</a>#}else if(MaintenanceScheduleDetail != 'N/A'){#<a href='\\#' onclick='scheduleDates(#=EquipmentItemId#)'>#=MaintenanceScheduleDetail#</a>#}else{##=MaintenanceScheduleDetail##}#").Width(125);
                        columns.Bound(c => c.MRCount).Title("Active MRs").ClientTemplate("#if(MaintenanceRecordCount){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='maintenanceRecordCount(#=EquipmentItemId#)'>#=MRCount#</a>#} else {##=MRCount##}#").Width(75);
                        columns.Bound(c => c.EquipmentInfo).Title("Info").Width(100);
                        columns.Bound(c => c.IsPartofBundle).Visible(false);

                        columns.Bound(c => c.Height).Title("Length").Hidden(true).Width(100);
                        columns.Bound(c => c.HeightUnitDescription).Title("Lenght Unit").Hidden(true).Width(100);
                        columns.Bound(c => c.Width).Title("Width").Hidden(true).Width(100);
                        columns.Bound(c => c.WidthUnitDescription).Title("Width Unit").Hidden(true).Width(100);
                        columns.Bound(c => c.Depth).Title("Depth").Hidden(true).Width(100);
                        columns.Bound(c => c.DepthUnitDescription).Title("Depth Unit").Hidden(true).Width(100);
                        columns.Bound(c => c.OuterDiameter).Title("OD").Hidden(true).Width(100);
                        columns.Bound(c => c.OuterDiameterUnitDescription).Title("OD Unit").Hidden(true).Width(100);
                        columns.Bound(c => c.Weight).Title("Weight").Hidden(true).Width(100);
                        columns.Bound(c => c.WeightUnitDescription).Title("Weight Unit").Hidden(true).Width(100);
                    })

                    .ColumnMenu(c => c.Columns(true))
                    .Events(e => e
                    .DataBound("updateEquipmentTotals")
                    .ColumnReorder("saveEquipmentGrid")
                    .ColumnResize("saveEquipmentGrid")
                    .ColumnShow("saveEquipmentGrid")
                    .ColumnHide("saveEquipmentGrid")
                    )
                    .Sortable()
                    .Scrollable(scrollable => scrollable.Endless(true).Height("auto"))
                    .Selectable(selectable => selectable.Mode(GridSelectionMode.Multiple))
                    .Filterable()
                    .Groupable()
                    .Excel(excel => excel
                    .FileName(string.Format("Centerpoint_Assets_Equipment_Items_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                    .Filterable(true)
                    .AllPages(true)
                    .ProxyURL(Url.Action("Export", "Asset"))
                    )
                    .ToolBar(t =>
                    {
                        t.Custom().Text("Reset Grid View").HtmlAttributes(new { @id = "resetArchivedEquipmentItemGrid", @class = "bg-danger text-white float-right" });
                        t.Excel().Text("Export");
                    })
                    .HtmlAttributes(new { @style = "height:80vh", @class = "justify-toolbar-content-to-end" })
                    .Resizable(resize => resize.Columns(true))
                    .Reorderable(reorder => reorder.Columns(true))
                    .DataSource(dataSource => dataSource
                    .Ajax()
                    .Model(model =>
                    {
                        model.Id(m => m.EquipmentItemId);
                    })
                    .PageSize(100)
                    .Read(read => read.Action("GetArchivedEquipmentItems", "Admin")))
                    )

            </div>
        </div>
    </div>
</div>




<script>
    const assetsModel = {
        statuses: [],
        equipmentFilterOptions: @Html.Raw(Json.Serialize(Html.DefaultEquipmentFilterOptions()))
        }

    @foreach (var status in EquipmentConstant.ValuesAndDescriptions.ToList())
    {
        @:assetsModel.statuses.push("@status.Key");
    }

</script>
<environment include="Development">
    <script src="~/js/views/admin/archivedAssets.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/admin/archivedAssets.min.js" asp-append-version="true"></script>
</environment>
