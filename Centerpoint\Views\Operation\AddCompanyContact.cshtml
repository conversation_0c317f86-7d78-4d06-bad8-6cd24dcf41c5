
<form id="customerContact">
<div>
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label>Location</label>
                @(Html.Kendo().DropDownList().Name("companyLocationId")
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .OptionLabel("Select Location")
                        .Filter(FilterType.Contains)
                        .HtmlAttributes( new { @required="required"})
                        .DataSource(source =>
                            {
                                source.Read(read =>
                                {
                                    read.Action("GetLocationsByCompanyId", "Lookup").Data("companyDataForCostomerContact");
                                });
                            }).AutoBind(false)
                        )
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label>Not Active</label>
                <br />
                @(Html.Kendo().CheckBox().Name("NotActive"))
            </div>
        </div>
    </div>
    <hr/>
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label>First Name</label>
                <br />
                @(Html.Kendo().TextBox().Name("FirstName")
                 .HtmlAttributes(new { @class = "form-control", @required="required", @style = "font-size: 14px;", tabindex = 1 }))
            </div>
            <div class="form-group">
                <label>Position</label>
                <br />
                @(Html.Kendo().TextBox().Name("Position")
                    .HtmlAttributes(new { @class = "form-control", @style = "font-size: 14px;", tabindex = 3 }))
            </div>
            <div class="form-group">
                <label>Mobile</label>
                <br />
                @(Html.Kendo().TextBox().Name("Mobile")
                    .HtmlAttributes(new { @class = "form-control", @style = "font-size: 14px;", tabindex = 5 }))
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label>Last Name</label>
                <br />
                @(Html.Kendo().TextBox().Name("LastName")
                    .HtmlAttributes(new { @class = "form-control", @required="required", @style = "font-size: 14px;", tabindex = 2 }))
            </div>
            <div class="form-group">
                <label>Email Address</label>
                <br />
                @(Html.Kendo().TextBox().Name("EmailAddress")
                    .HtmlAttributes(new { @class = "form-control", @style = "font-size: 14px;", tabindex = 4 }))
            </div>
            <div class="form-group">
                <label>Work Telephone</label>
                <br />
                @(Html.Kendo().TextBox().Name("WorkTelephone")
                    .HtmlAttributes(new { @class = "form-control", @style = "font-size: 14px;", tabindex = 6 }))
            </div>
        </div>
    </div>
    <hr />
    <div>
        <div>
            <label>Comment</label>
            <br/>
            @(Html.Kendo().TextArea().Name("Comment").HtmlAttributes( new { @class = "form-control", @style = "width:100%", @rows = "5", tabindex = 7 }))
        </div>
    </div>
</div>
<hr/>
<button type="button" onclick="addCompanyContact()" class="btn btn-sm btn-primary">save</button>
</form>