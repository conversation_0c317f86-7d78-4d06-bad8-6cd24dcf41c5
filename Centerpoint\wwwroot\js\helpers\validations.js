// checkbox with dropdownlist conection

function addOrRemoveDropDownListValidation(checked, id, inputType , formId) {
    $(id).data(inputType).value(null)
    $(id).data(inputType).element[0].required = checked
    var form = $(formId).data("kendoValidator");
    if(form) {
        form.reset();
        form.validate();
    }
}

function uploadValidation(e) {
    var files = e.files;
    var pattern = new RegExp(/[~`!#$%\^&*+=\[\]\\';,/{}|\\":<>\?]/);
    $.each(files, function () {
        if (pattern.test(this.name)) {
            alert('Your filename contains illegal characters.');
            e.preventDefault();
        }
    })
}

const compareDates = (d1, d2) => {
    let date1 = kendo.parseDate(d1).getTime();
    let date2 = kendo.parseDate(d2).getTime();
  
    if (date1 < date2) {
        return "d1IsSmaller"
    } else if (date1 > date2) {
        return "d2IsSmaller"
    } else {
        return "equal";
    }
};

$(document).ready(function () {
    removeValidation("checkbox");
});

function removeValidation(type) {
    $(`input[type="${type}"]`).removeAttr('data-val-required');
}