﻿@model CompanyWellModel
<div>
    <div class="row">
        <div class="col-md-5">
            <div class="form-group">
                <label>Field</label>
                <br />
                @(Html.Kendo().DropDownListFor(m => m.CompanyFieldId)
                .DataTextField("Name")
                .DataValueField("CompanyFieldId")
                .Filter("contains")
                .OptionLabel("Select Fields")
                .HtmlAttributes(new { @style = "width:100%", @data_value_primitive = "true" })
                .DataSource(d => d.Read("GetFieldsByCompanyId", "Lookup", new { @companyId = ViewBag.CompanyId })))
            </div>
            <div class="form-group">
                <label>Minimum</label>
                <br />
                @(Html.Kendo().NumericTextBoxFor(p => p.MinimumId)
                 .HtmlAttributes(new { @style = "width:100%" })
                 .Spinners(false))
            </div>
            <div class="form-group pull-left">
                @Html.LabelFor(m => m.MaximumPressure)
                <br />
                @(Html.Kendo().NumericTextBoxFor(p => p.MaximumPressure)
                 .HtmlAttributes(new { @style = "width:100px;margin-right:10px" })
                 .Spinners(false)
                 .Format("n2"))
            </div>
            <div class="form-group">
                <label>Units</label>
                <br />
                @(Html.Kendo().DropDownListFor(m => m.MaximumPressureUnits)
                  .DataTextField("Value")
                  .DataValueField("Key")
                  .Filter("contains")
                  .BindTo(Centerpoint.Common.Constants.MaximumPressureUnitsConstant.ValuesAndDescriptions.ToList())
                  .HtmlAttributes(new { @style = "width:60px", @data_value_primitive = "true" }))
            </div>
        </div>
        <div class="col-md-5">
            <div class="form-group">
                <label>Name</label>
                <br />
                @(Html.TextBoxFor(m => m.Name, new { @class = "k-textbox", @style = "width:100%" }))
            </div>
            <div class="form-group">
                @Html.LabelFor(m => m.MaximumDeviation)
                <br />
                @(Html.Kendo().NumericTextBoxFor(p => p.MaximumDeviation)
                  .HtmlAttributes(new { @style = "width:100%" })
                  .Spinners(false)
                  .Format("n2"))
            </div>
            <div class="form-group pull-left">
                @Html.LabelFor(m => m.MaximumTemperature)
                <br />
                @(Html.Kendo().NumericTextBoxFor(p => p.MaximumTemperature)
                  .HtmlAttributes(new { @style = "width:100px;" })
                  .Spinners(false)
                  .Format("n2"))
            </div>
            <div class="form-group">
                <label>Degrees</label>
                <br />
                @(Html.Kendo().DropDownListFor(m => m.MaximumTemperatureDegrees)
                  .DataTextField("Value")
                  .DataValueField("Key")
                  .Filter("contains")
                  .BindTo(Centerpoint.Common.Constants.MaximumTemperatureDegreesConstant.ValuesAndDescriptions.ToList())
                  .HtmlAttributes(new { @style = "width:60px", @data_value_primitive = "true" }))
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-5">
            <div class="form-group">
                <label>H<sub>2</sub>S (ppm)</label>
                <br />
                @(Html.Kendo().NumericTextBoxFor(m => m.H2S)
                  .HtmlAttributes(new { @style = "width:100%" })
                  .Spinners(false)
                  .Format("n2"))
            </div>
        </div>
        <div class="col-md-5">
            <div class="form-group">
                <label>CO<sub>2</sub> (%)</label>
                <br />
                @(Html.Kendo().NumericTextBoxFor(m => m.CO2)
                  .HtmlAttributes(new { @style = "width:100%" })
                  .Spinners(false)
                  .Format("n2"))
            </div>
        </div>
    </div>
</div>
