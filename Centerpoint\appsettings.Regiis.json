{"Logging": {"LogLevel": {"Default": "Information", "System": "Warning", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Hangfire": "Error"}, "ApplicationInsights": {"LogLevel": {"Default": "Information", "System": "Warning", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Warning", "Azure.Messaging.ServiceBus": "Warning", "Hangfire": "Error"}}}, "ConnectionStrings": {"DefaultConnection": "Server=tcp:centerpointapp.database.windows.net,1433;Initial Catalog=centerpoint-regiis;Persist Security Info=False;User ID=centerpoint;Password=**********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"}, "BlobStorage": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=centerpointstorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "Container": "regiis-live"}, "EmailSettings": {"Username": "<EMAIL>", "Password": "**********", "SmtpHost": "smtp.office365.com", "SmtpPort": "587", "From": "<EMAIL>", "UseSSL": true}, "Settings": {"webpages:Version": "*******", "webpages:Enabled": false, "ClientValidationEnabled": true, "UnobtrusiveJavaScriptEnabled": true, "EventTime": 30, "LoginAttempts": 5, "DownloadsFolderName": "Downloads", "DepreciationTotalYears": 8, "Client": "<PERSON><PERSON><PERSON>"}, "TwilioSMS": {"TWILIO_ACCOUNT_SID": "**********************************", "TWILIO_AUTH_TOKEN": "398a000c97d4b7829326b085bb333828", "TWILIO_FROM_PHONE": "+************"}, "AzureAdSettings": {"Enabled": true, "ClientId": "4e196ea8-2422-46cf-bf1e-a3169e51a3b9", "TenantId": "22acb312-3d52-4fe4-8dd0-9252e7507bb4", "Authority": "https://login.microsoftonline.com/common/"}, "AppUrl": " https://readcasedhole.centerpoint.pro"}