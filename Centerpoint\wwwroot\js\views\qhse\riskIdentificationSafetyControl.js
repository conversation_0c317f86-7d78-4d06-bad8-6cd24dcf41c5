
    $(document).ready(function () {
        refreshRiscGrid();
        getRiscMonthlySummary();
        getRiscContributorSummary();
        loadRiscGrid();
        var riscGrid = $('#riscGrid').data("kendoGrid");
        riscGrid.bind('dataBound', function (e) {
            this.element.find('.k-i-excel').remove();
        });
    });

    function loadRiscGrid() {
        var grid = $("#riscGrid").data("kendoGrid");
        var toolBar = $("#riscGrid .k-grid-toolbar").html();
        var options = localStorage["riscGrid"];
        viewModel.set("initialRiscGridOptions", kendo.stringify(grid.getOptions()));
        if (options) {
            grid.setOptions(JSON.parse(options));
            $("#riscGrid .k-grid-toolbar").html(toolBar);
            $("#riscGrid .k-grid-toolbar").addClass("k-grid-top");
        }
    }

    function saveRiscGrid(e) {
        setTimeout(function () {
            var grid = $("#riscGrid").data("kendoGrid");
            localStorage["riscGrid"] = kendo.stringify(grid.getOptions());
        }, 10);
    }

    function riscData() {
        return {
            type: viewModel.get("type"),
            month:viewModel.get("summaryMonth"),
            year:viewModel.get("year")
        };
    }

    function updateRiscGrid() {
        $("#resetRiscGrid").click(function (e) {
            e.preventDefault();
            resetGridView('riscGrid', 'initialRiscGridOptions')
        });

        var riscGrid = $("#riscGrid").data("kendoGrid");
        var total = riscGrid.dataSource.total();
        viewModel.set("riscCount", total);
    }

    function summaryMonthChanged(e){
        getRiscMonthlySummary();
        getRiscContributorSummary();
    }

    function yearChanged(e){
        getRiscMonthlySummary();
        getRiscContributorSummary();
    }

    function getRiscMonthlySummary() {
        $.ajax({
            url: "/Qhse/GetRiscMonthlySummary",
            data: {
                month:viewModel.get("summaryMonth"),
                year:viewModel.get("year")
            },
            success: function(result){
                if(result){
                    viewModel.set("totalCreatedMonth",result.TotalCreated);
                    viewModel.set("totalPositiveMonth",result.TotalPositive);
                    viewModel.set("totalNegativeMonth",result.TotalNegative);
                }
            },
            dataType: "json"
        });
        refreshRiscGrid();
    }

    function getRiscContributorSummary() {
        $.ajax({
            url: "/Qhse/GetRiscContributorSummary",
            data: {
                month:viewModel.get("summaryMonth"),
                year:viewModel.get("year")
            },
            success: function(result){
                if(result){
                    viewModel.set("contributorsTotal",result.ContributorsTotal);
                    viewModel.set("noContributorTotal",result.NoContributorTotal);
                    viewModel.set("onshoreTotal",result.OnshoreTotal);
                    viewModel.set("offshoreTotal",result.OffshoreTotal);
                }
            },
            dataType: "json"
        });
        refreshRiscGrid();
    }

    function refreshRiscGrid() {
        var riscGrid = $("#riscGrid").data("kendoGrid");
        riscGrid.dataSource.read();
    }

    var viewModel = new kendo.observable({
        riscCount: 0,
        contributorMonth: riskIdentificationSafetyControlModel.dateTimeNowMonth,
        summaryMonth: riskIdentificationSafetyControlModel.viewBagMonth,
        year: riskIdentificationSafetyControlModel.viewBagYear,
        totalCreatedMonth:0,
        totalPositiveMonth:0,
        totalNegativeMonth:0,
        contributorsTotal:0,
        noContributorTotal:0,
        onshoreTotal:0,
        offshoreTotal:0,
        type:"",

        totalClick:function(){
            viewModel.set("type", "total")
            refreshRiscGrid();
        },
        positiveMonthClick:function(){
            viewModel.set("type", "positive")
            refreshRiscGrid();
        },
        negativeMonthClick:function(){
            viewModel.set("type", "corrective")
            refreshRiscGrid();
        },
        contributorsClick:function(){
            viewModel.set("type", "contributors")
            refreshRiscGrid();
        },
        noContributorsClick:function(){
            viewModel.set("type", "noContributors")
            refreshRiscGrid();
        },
        onshoreClick:function(){
            viewModel.set("type", "onshore")
            refreshRiscGrid();
        },
        offshoreClick:function(){
            viewModel.set("type", "offshore")
            refreshRiscGrid();
        },
    });
    kendo.bind(document.body.children, viewModel);