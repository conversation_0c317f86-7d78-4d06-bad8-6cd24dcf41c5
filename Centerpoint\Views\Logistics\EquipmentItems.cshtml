@model EquipmentShipmentModel;
<div id="equipmentItems">

    @if (ViewBag.IsReceiving == null && Model.EquipmentShipmentStatus != EquipmentShipmentStatusConstant.Received) {
        <div class="row mb-5">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fa fa-tags"></i> Equipment Categories</h6>
                    </div>
                    <div class="card-body">
                        <div class="expandCollapseButtonsContainer">
                            <button class="btn btn-sm btn-primary" id="expandAll" data-bind="click:expandAll" title="Expand All">
                                <i class="fa fa-expand m-0"></i>
                            </button>
                            <button class="btn btn-sm btn-primary" id="collapsAll" data-bind="click:collapseAll" title="Collapse All">
                                <i class="fa fa-compress m-0"></i>
                            </button>
                        </div>
                        <input id="filterText" class="k-input k-textbox k-input-solid k-input-md k-rounded-md" type="text" placeholder="Search categories" />
                        <br />
                        <br />
                        @(Html.Kendo().TreeView()
                        .Name("equipmentCategoryTreeView")
                        .TemplateId("equipmentCategoryTemplate")
                        .DataTextField("NewName")
                        .LoadOnDemand(false)
                        .Events(e => e
                            .Change("equipmentCategorySelected")
                            .Drop("equipmentCategoryDropped")
                            .DataBound("equipmentCategoryLoaded")
                        )
                        .DataSource(datasource => datasource
                            .Events(e => e.RequestEnd("categoriesLoaded"))
                            .Model(m => m.Id("EquipmentCategoryId").HasChildren("HasChildren").Children("Children"))
                            .Read(r => r.Action("GetEquipmentCategories", "Admin"))
                            .ServerFiltering(false)))
                    </div>
                </div>
            </div>
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fa fa-tags"></i> Equipment Items (<span data-bind="text: totalEquipmentItems"></span>)
                        </h6>
                    </div>
                    <div class="card-body">
                        @(
                        Html.Kendo().Grid<EquipmentItemModel>()
                                .Name("equipmentItemGrid")
                                .Columns(columns => {
                                    columns.Bound(c => c.EquipmentPackingListProjectName).Title("Assigned To").ClientTemplate("#=EquipmentPackingListProjectName ? NewProjectName : 'N/A'#").Width(150);
                                    columns.Bound(c => c.EquipmentItemName).Title("Item Number").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#?shipmentId=" + Model.EquipmentShipmentId + "'>#=EquipmentItemName#</a>").Width(150);
                                    if (GlobalSettings.IsRegiis)
                                    {
                                        columns.Bound(c => c.CountryOfOrigin).Title("Country Of Origin").Width(150);
                                    }
                                    columns.Bound(c => c.ReceivedDate).Title("Manufacture Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                    columns.Bound(c => c.PurchasedDate).Title("Purchased Date").Format(DateConstants.DateFormat).Hidden(true).Width(125);
                                    columns.Bound(c => c.CurrencyName).Title("Currency").Width(100).Hidden(true);
                                    columns.Bound(c => c.Price).Format("{0:n2}").Width(100).Hidden(true);
                                    columns.Bound(c => c.DepreciatedPrice).Title("Net Book Value").Hidden(true).Format("{0:n2}").Width(100);
                                    columns.Bound(c => c.PointsPerMonth).Title("Points Per Month").Hidden(true).Width(150);
                                    columns.Bound(c => c.PointsPerRun).Title("Points Per Run").Hidden(true).Width(150);
                                    columns.Bound(c => c.PointsPerMove).Title("Points Per Move").Hidden(true).Width(150);
                                    columns.Bound(c => c.DivisionName).Title("Division").Hidden(true).Width(150);
                                    columns.Bound(c => c.TrackedNonAssetItem).Title("Tracked Non-Asset Item").ClientTemplate("#if(TrackedNonAssetItem){#Yes#}else{#No#}#").Hidden(true).Width(100);

                                    columns.Bound(c => c.CurrentClientLocationName).Title("Current Location").ClientTemplate("#=CurrentClientLocationName ? CurrentClientLocationName : 'Not Yet Accepted'#").Width(100);
                                    columns.Bound(c => c.CustomStatusCode).Title("Custom Status").Width(100);
                                    columns.Bound(c => c.Points).Title("Current Points").Width(100);
                                    columns.Bound(c => c.MaintenanceScheduleDetail).Title("Maintenance Schedule Date(s)").ClientTemplate("#if(MaintenanceScheduleDaysAlert){#<a style='color:\\#E31E33'href='\\#' onclick='scheduleDates(#=EquipmentItemId#)'>#=MaintenanceScheduleDetail#</a>#}else if(MaintenanceScheduleDetail != 'N/A'){#<a href='\\#' onclick='scheduleDates(#=EquipmentItemId#)'>#=MaintenanceScheduleDetail#</a>#}else{##=MaintenanceScheduleDetail##}#").Width(125);
                                    columns.Bound(c => c.MRCount).Title("Active MRs").ClientTemplate("#if(MaintenanceRecordCount){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='maintenanceRecordCount(#=EquipmentItemId#)'>#=MRCount#</a>#} else {##=MRCount##}#").Width(75);
                                    columns.Bound(c => c.EquipmentInfo).Title("Info").Width(100);
                                    columns.Bound(c => c.AllStatusDescription).Title("Status").Encoded(false).Filterable(f => f.Operators(o => o.ForString(str => str.Clear().Contains("Contains").DoesNotContain("Does not contain")))).Width(150);
                                })
                                .AutoBind(false)
                                .ColumnMenu(c => c.Columns(true))
                                .Events(e => e.DataBound("updateEquipmentTotals"))
                                .ToolBar(toolbar => {
                                    toolbar.ClientTemplateId(Model.EquipmentShipmentStatus == EquipmentShipmentStatusConstant.Pending && (Html.IsLogisticsAdmin() || Html.IsGlobalAdmin()) ? "equipmentItemGridHeader" : "equipmentItemGridSimpleHeader");
                                })
                                .Filterable()
                            .Selectable(selectable => selectable.Mode(GridSelectionMode.Multiple))
                            .Sortable()
                            .Groupable()
                            .Search(s =>
                            {
                                s.Field(o => o.EquipmentItemName, "contains");
                            })
                            .Scrollable(scrollable => scrollable.Endless(true).Height(535))
                            .Resizable(resize => resize.Columns(true))
                            .Reorderable(reorder => reorder.Columns(true))
                            .DataSource(dataSource => dataSource
                                .Ajax()
                                .Model(model => {
                                    model.Id(m => m.EquipmentItemId);
                                })
                                .PageSize(100)
                                .Read(read => read.Action("GetEquipmentItemsNotInEquipmentShipmentId", "Logistics").Data("equipmentItemData"))
                            )
                        )
                    </div>
                </div>
            </div>
        </div>
    }
    
    @if ((Model.EquipmentShipmentStatus == EquipmentShipmentStatusConstant.Pending && (Html.IsLogisticsAdmin() || Html.IsGlobalAdmin()))
         || (ViewBag.IsReceiving == true && Model.EquipmentShipmentStatus != EquipmentShipmentStatusConstant.Received)
      ) {    
        <div class="d-flex actionsContainer justify-content-end mt-4">
            @if (Model.EquipmentShipmentStatus == EquipmentShipmentStatusConstant.Pending && (Html.IsLogisticsAdmin() || Html.IsGlobalAdmin())) {
                <button class="btn btn-sm btn-primary" data-bind="click: showDePackinglistWindow">
                    <i class="fa fa-file-text"></i>
                    De-Select Packing List
                </button>
                <button class="btn btn-sm btn-primary" data-bind="click: showPackinglistWindow">
                    <i class="fa fa-file-text"></i>
                    Select Packing List
                </button>
            }
            @if (ViewBag.IsReceiving == true && Model.EquipmentShipmentStatus != EquipmentShipmentStatusConstant.Received) {
                <a class="btn btn-success btn-sm" data-bind="visible: anyEquipmentShipmentItems, click: receiveAllItemsClick">
                    <i class="fa fa-check"></i>
                    Receive All Items
                </a>
            }
        </div>
    } 
    

    <div class="row mt-3">
        <div class="col-md-12">
            <div>
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fa fa-tags"></i>
                        Equipment Shipment Items (<span data-bind="text: totalEquipmentShipmentItems"></span>)
                    </h6>
                </div>
                <div>
                    @(Html.Kendo().Grid<EquipmentShipmentItemModel>()
                        .Name("equipmentShipmentItemGrid")
                        .Columns(columns => {
                        if (ViewBag.IsReceiving == true && Model.EquipmentShipmentStatus != EquipmentShipmentStatusConstant.Received) {
                            columns.Template("#if(IsReceived){# <a href='\\#' onclick='receiveEquipmentShipmentItem(#=EquipmentShipmentItemId#)' class='btn btn-success btn-sm'><i class='fa fa-check'></i><span class='ml-1'>Received</span></a> #} else{# <a href='\\#' onclick='receiveEquipmentShipmentItem(#=EquipmentShipmentItemId#)' class='btn btn-danger btn-sm'> <span class='ml-1'>Not Received</span></a> #}#").Title("Item");
                        } else if (Model.EquipmentShipmentStatus == EquipmentShipmentStatusConstant.Received) {
                            columns.Template("#if(IsReceived){# <span class='badge badge-success'><i class='fa fa-check'></i><span class='ml-1'>Received</span></span> #} else{# <span class='badge badge-danger'><i class='fa fa-ban'></i> <span class='ml-1'>Not Received</span></span> #}#").Title("Item");
                            }
                            columns.Bound(c => c.EquipmentItemName).Title("Item Number").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#?shipmentId=" + Model.EquipmentShipmentId + "'>#=EquipmentItemName#</a>");
                            columns.Bound(c => c.EquipmentItemReceivedDate).Title("Manufacture Date").Format(DateConstants.DateFormat).Hidden(true);
                            columns.Bound(c => c.EquipmentItemPurchasedDate).Title("Purchased Date").Format(DateConstants.DateFormat).Hidden(true);
                            columns.Bound(c => c.EquipmentItemCurrencyName).Title("Currency").Hidden(true);
                            columns.Bound(c => c.EquipmentItemPrice).Format("{0:n2}").Hidden(true);
                            columns.Bound(c => c.EquipmentItemDepreciatedPrice).Title("Net Book Value").Hidden(true).Format("{0:n2}").Width(100);
                            columns.Bound(c => c.EquipmentItemPointsPerMonth).Title("Points Per Month").Hidden(true);
                            columns.Bound(c => c.EquipmentItemPointsPerRun).Title("Points Per Run").Hidden(true);
                            columns.Bound(c => c.EquipmentItemPointsPerMove).Title("Points Per Move").Hidden(true);
                            columns.Bound(c => c.EquipmentItemDivisionName).Title("Division").Hidden(true);
                            columns.Bound(c => c.EquipmentItemTrackedNonAssetItem).Title("Tracked Non-Asset Item").ClientTemplate("#if(EquipmentItemTrackedNonAssetItem){#Yes#}else{#No#}#").Hidden(true);
                            if (Model.EquipmentShipmentStatus == EquipmentShipmentStatusConstant.Pending) {
                                columns.Bound(c => c.CurrentClientLocationName).Title("Current Location").ClientTemplate("#if(!EquipmentItemAtFromCompanyLocation){# <span class='badge badge-warning' style='font-size:14px'><i class='fa fa-warning' style='margin-right:0px' title='This item is not currently located at the shipment from location'></i></span> #}# #=CurrentClientLocationName#");
                            } else {
                                columns.Bound(c => c.CurrentClientLocationName).Title("Current Location");
                            }
                            if (GlobalSettings.IsRegiis) {
                                columns.Bound(c => c.DescriptionName).Title("Description");
                            } else {
                                columns.Bound(c => c.EquipmentShipmentItemDescription).Title("Description");
                            }
                            columns.Bound(c => c.EquipmentItemCountryOfOrigin).Title("Country Of Origin").Visible(GlobalSettings.IsWellsense);
                            @*columns.Bound(c => c.EquipmentItemCustomStatusCodeId).Title("Custom Status").EditorTemplateName("EquipmentItemCustomStatusCode").Title("Custom Status").ClientTemplate("#=CustomStatusName ? CustomStatusName : ''#");*@
                            columns.Bound(c => c.EquipmentItemPoints).Title("Current Points").Hidden(true);
                            columns.Bound(c => c.MaintenanceScheduleDetail).Title("Maintenance Schedule Date(s)").ClientTemplate("#if(MaintenanceScheduleDaysAlert){#<a style='color:\\#E31E33'href='\\#' onclick='scheduleDates(#=EquipmentItemId#)'>#=MaintenanceScheduleDetail#</a>#}else if(MaintenanceScheduleDetail != 'N/A'){#<a href='\\#' onclick='scheduleDates(#=EquipmentItemId#)'>#=MaintenanceScheduleDetail#</a>#}else{##=MaintenanceScheduleDetail##}#").Width(125);
                            columns.Bound(c => c.MRCount).Title("Active MRs").ClientTemplate("#if(MaintenanceRecordCount){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='maintenanceRecordCount(#=EquipmentItemId#)'>#=MRCount#</a>#} else {##=MRCount##}#");
                            columns.Bound(c => c.EquipmentItemEquipmentInfo).Title("Info");
                            columns.Bound(c => c.HasDangerousGoods).Title("Dangerous Goods").ClientTemplate("#if(HasDangerousGoods){# <span class='badge badge-warning' style='font-size:14px'><i class='fa fa-warning' style='margin-right:0px' title='This item contains dangerous goods'></i></span> YES #}else{# #='NO'# #}#");
                            if (GlobalSettings.IsWellsense)
                            {
                                columns.Bound(c => c.IsNew).Title("New?").ClientTemplate("<input type='checkbox' #= IsNew ? 'checked' : '' # disabled='disabled'  />").EditorTemplateName("BooleanEditor").EditorViewData(new { FieldName = "IsNew" });
                                columns.Bound(c => c.IsPermanent).Title("Permanent?").ClientTemplate("<input type='checkbox' #= IsPermanent ? 'checked' : '' # disabled='disabled' />").EditorTemplateName("BooleanEditor").EditorViewData(new { FieldName = "IsPermanent" });
                            }
                            columns.Bound(c => c.AllStatusDescription).Title("Status").Encoded(false).Filterable(f => f.Operators(o => o.ForString(str => str.Clear().Contains("Contains").DoesNotContain("Does not contain")))).Width(150);
                           
                            if (Model.EquipmentShipmentStatus == EquipmentShipmentStatusConstant.Pending && (Html.IsLogisticsAdmin() || Html.IsGlobalAdmin()))
                            {
                                columns.Command(command => {
                                    if (!GlobalSettings.IsRegiis)
                                        command.Edit().HtmlAttributes(new { @class = "bg-primary text-white grid-action-button" });
                                    command.Destroy().HtmlAttributes(new {@class="bg-danger text-white grid-action-button"}); 
                                }).Width(200);
                            }
                        })
                        .ColumnMenu(c => c.Columns(true))
                        .ToolBar(t => {  
                            t.Search(); t.Excel().Text("Export"); t.Save();
                        })
                        .HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
                        .Events(e => e.DataBound("updateEquipmentShipmentTotals").BeforeEdit("saveEquipmentShipmentItemId"))
                        .Filterable()
                        .Groupable()
                        .Search(s => { s.Field(o => o.EquipmentItemName, "contains"); })
                        .Editable(editable => editable.Mode(GridEditMode.InLine))
                        .Scrollable(s => s.Height(500))
                        .Sortable()
                        .Resizable(resize => resize.Columns(true))
                        .Reorderable(reorder => reorder.Columns(true))
                        .Events(e => e.ExcelExport("excelExport"))
                        .Excel(excel => excel
                            .FileName(string.Format("Centerpoint_Shipment_Equipment_Items_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                            .Filterable(true)
                            .ProxyURL(Url.Action("Export", "Logistics"))
                        )
                        .DataSource(dataSource => dataSource
                            .Ajax()
                            .ServerOperation(false)
                            .Events(e => e.RequestEnd("shipmentItemsRequestEnd"))
                            .Model(model => {
                                model.Id(m => m.EquipmentShipmentItemId);

                                model.Field(m => m.EquipmentItemName).Editable(false);
                                model.Field(m => m.CurrentClientLocationName).Editable(false);
                                model.Field(m => m.EquipmentItemEquipmentInfo).Editable(false);
                                model.Field(m => m.HasDangerousGoods).Editable(false);
                                model.Field(m => m.MaintenanceRecordCount).Editable(false);
                                model.Field(m => m.MaintenanceScheduleDates).Editable(false);
                                model.Field(m => m.MRCount).Editable(false);
                                model.Field(m => m.EquipmentItemCountryOfOrigin).Editable(false);
                                model.Field(m => m.EquipmentItemReceivedDate).Editable(false);
                                model.Field(m => m.EquipmentItemPurchasedDate).Editable(false);
                                model.Field(m => m.EquipmentItemCurrencyName).Editable(false);
                                model.Field(m => m.EquipmentItemPrice).Editable(false);
                                model.Field(m => m.EquipmentItemDepreciatedPrice).Editable(false);
                                model.Field(m => m.EquipmentItemPointsPerMonth).Editable(false);
                                model.Field(m => m.EquipmentItemPointsPerRun).Editable(false);
                                model.Field(m => m.EquipmentItemPointsPerMove).Editable(false);
                                model.Field(m => m.EquipmentItemDivisionName).Editable(false);
                                model.Field(m => m.EquipmentItemTrackedNonAssetItem).Editable(false);
                                model.Field(m => m.MaintenanceScheduleDetail).Editable(false);
                                model.Field(m => m.MaintenanceScheduleDaysAlert).Editable(false);
                                model.Field(m => m.AllStatusDescription).Editable(false);

                                model.Field(m => m.EquipmentShipmentItemDescription).Editable(true);
                                model.Field(m => m.IsNew).Editable(true);
                                model.Field(m => m.IsPermanent).Editable(true);
                            })
                            .Read(read => read.Action("GetEquipmentShipmentItems", "Logistics", new { @equipmentShipmentId = Model.EquipmentShipmentId }))
                            .Update(update => update.Action("UpdateEquipmentShipmentItem", "Logistics").Data("equipmentShipmentItemData"))
                            .Destroy(destroy => destroy.Action("DeleteEquipmentShipmentItem", "Logistics"))
                        )
                    )
                </div>
            </div>
        </div>
    </div>
    @if (ViewBag.IsReceiving == true && Model.EquipmentShipmentStatus != EquipmentShipmentStatusConstant.Received) {
        <div class="task-template-item actionsContainer justify-content-end mt-4" data-bind="visible: anyEquipmentShipmentNonAssetItems">
            <a class="btn btn-success btn-sm" data-bind="click:receiveAllNonAssetItemsClick"><i class="fa fa-check"></i>Receive All Non-Asset Items</a>
        </div>
    }
    <div class="row mt-3">
        <div class="col-md-12">
            <div>
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fa fa-tags"></i>
                        Non-Asset Shipment Items (<span data-bind="text: totalEquipmentShipmentNonAssetItems"></span>)
                    </h6>
                </div>
                @if (Model.EquipmentShipmentStatus == EquipmentShipmentStatusConstant.Pending && (Html.IsLogisticsAdmin() || Html.IsGlobalAdmin())) {
                    @(Html.Kendo().Grid<EquipmentShipmentNonAssetItemModel>()
                    .Name("equipmentShipmentNonAssetItemGrid")
                    .Columns(c => {
                        c.Bound(p => p.Quantity).Width(100);
                        c.Bound(p => p.Description).Width(175);
                        c.Bound(p => p.CountryOfOrigin).Title("Country Of Origin").Width(175).Visible(GlobalSettings.IsWellsense);
                        @*c.Bound(p => p.CustomStatusCodeId).Title("Custom Status").EditorTemplateName("CustomStatusCode").Title("Custom Status").ClientTemplate("#=CustomStatusName ? CustomStatusName : ''#").Width(150);*@
                        c.Bound(p => p.UnitPrice).Title("Unit Price").Width(100).Format("{0:n2}");
                        c.Bound(p => p.Amount).Title("Amount").Width(100).Format("{0:n2}");
                        c.Bound(p => p.CurrencyId).Title("Currency").EditorTemplateName("Currency").Title("Currency").ClientTemplate("#=CurrencyName ? CurrencyName : ''#").Width(150);
                        c.Bound(p => p.Commodity).Title("Commodity").Width(175);
                        c.Bound(p => p.NetWeight).Title("Net Weight").Width(175).Format("{0:n2}").Visible(GlobalSettings.IsWellsense);
                        if (Model.EquipmentShipmentStatus == EquipmentShipmentStatusConstant.Pending) {
                            c.Command(command => { 
                                command.Edit().HtmlAttributes(new {@class="bg-primary text-white grid-action-button"});
                                command.Destroy().HtmlAttributes(new {@class="bg-danger text-white grid-action-button"}); 
                            }).Width(150);
                        };
                    })
                    .Editable(editable => editable.Mode(GridEditMode.InLine))
                    .ToolBar(t => {
                        t.Create().Text("Add New Item");
                    })
                    .Sortable()
                    .Filterable()
                    .Scrollable(s => s.Height(500))
                    .Resizable(r => r.Columns(true))
                    .Reorderable(r => r.Columns(true))
                    .ColumnMenu(c => c.Columns(true))
                    .Events(e => e.DataBound("updateEquipmentShipmentNonAssetItemTotals"))
                    .DataSource(dataSource => dataSource
                        .Ajax()
                        .ServerOperation(false)
                        .Model(m => m.Id(p => p.EquipmentShipmentNonAssetItemId))
                        .Read(read => read.Action("GetEquipmentShipmentNonAssetItems", "Logistics", new { @equipmentShipmentId = Model.EquipmentShipmentId }))
                        .Create(create => create.Action("UpdateEquipmentShipmentNonAssetItem", "Logistics", new { @sId = Model.EquipmentShipmentId }))
                        .Update(update => update.Action("UpdateEquipmentShipmentNonAssetItem", "Logistics", new { @sId = Model.EquipmentShipmentId }))
                        .Destroy(destroy => destroy.Action("DeleteEquipmentShipmentNonAssetItem", "Logistics"))
                        .Events(ev => ev.RequestEnd("onEquipmentShipmentNonAssetItemGridRequestEnd"))
                    ))
                } else {
                    @(Html.Kendo().Grid<EquipmentShipmentNonAssetItemModel>()
                    .Name("equipmentShipmentNonAssetItemGrid")
                    .Columns(c => {
                    if (ViewBag.IsReceiving == true && Model.EquipmentShipmentStatus != EquipmentShipmentStatusConstant.Received) {
                        c.Template("#if(IsReceived){# <a onclick='receiveEquipmentShipmentNonAssetItem(#=EquipmentShipmentNonAssetItemId#)' class='btn btn-success btn-sm'><i class='fa fa-check'></i><span class='ml-1'>Received</span></a> #} else{# <a onclick='receiveEquipmentShipmentNonAssetItem(#=EquipmentShipmentNonAssetItemId#)' class='btn btn-danger btn-sm'><span class='ml-1'>Not Received</span></a> #}#").Width(100);
                    } else if (Model.EquipmentShipmentStatus == EquipmentShipmentStatusConstant.Received) {
                        c.Template("#if(IsReceived){# <span class='badge badge-success'><i class='fa fa-check'></i><span class='ml-1'>Received</span></span> #} else{# <span class='badge badge-danger'><i class='fa fa-ban'></i><span class='ml-1'>Not Received</span></span> #}#").Width(100);
                        }
                        c.Bound(p => p.Quantity).Width(100);
                        c.Bound(p => p.Description).Width(175);
                        @*c.Bound(p => p.CustomStatusCodeId).Title("Custom Status").EditorTemplateName("CustomStatusCode").Title("Custom Status").ClientTemplate("#=CustomStatusName ? CustomStatusName : ''#").Width(150);*@
                        c.Bound(p => p.UnitPrice).Title("Unit Price").Width(100).Format("{0:n2}");
                        c.Bound(p => p.Amount).Title("Amount").Width(100).Format("{0:n2}");
                        c.Bound(p => p.CurrencyId).Title("Currency").EditorTemplateName("Currency").Title("Currency").ClientTemplate("#=CurrencyName ? CurrencyName : ''#").Width(150);
                        c.Bound(p => p.Commodity).Title("Commodity").Width(175);
                        c.Bound(p => p.CountryOfOrigin).Title("Country Of Origin").Width(175).Visible(GlobalSettings.IsWellsense); 
                    })
                    .Editable(editable => editable.Mode(GridEditMode.InLine))
                    .Sortable()
                    .Filterable()
                    .Scrollable(s => s.Height(500))
                    .Resizable(c => c.Columns(true))
                    .ColumnMenu(c => c.Columns(true))
                    .Events(e => e.DataBound("updateEquipmentShipmentNonAssetItemTotals"))
                    .DataSource(dataSource => dataSource
                    .Ajax()
                    .ServerOperation(false)
                    .Model(m => m.Id(p => p.EquipmentShipmentNonAssetItemId))
                    .Read(read => read.Action("GetEquipmentShipmentNonAssetItems", "Logistics", new { @equipmentShipmentId = Model.EquipmentShipmentId }))))

                }
            </div>
        </div>
    </div>
</div>

<script type="text/x-kendo-tmpl" id="equipmentItemGridHeader">
    <div style="display: flow; width: 100%;">
        <button id="addShipmentBtn" class="btn btn-primary" onclick="populateItemGridClick()">
            Add to Shipment
        </button>
        <span class="k-searchbox k-input k-input-md k-rounded-md k-input-solid k-grid-search float-right" style="width:250px; margin-right: 7px;">
            <span class="k-input-icon k-icon k-i-search"></span>
            <input autocomplete="off" placeholder="Search..." title="Search..." class="k-input-inner">
        </span>
    </div>
</script>

<script type="text/x-kendo-tmpl" id="equipmentItemGridSimpleHeader">
    <div style="display: flow; width: 100%;">
        <span class="k-searchbox k-input k-input-md k-rounded-md k-input-solid k-grid-search float-right" style="width:250px; margin-right: 7px;">
            <span class="k-input-icon k-icon k-i-search"></span>
            <input autocomplete="off" placeholder="Search..." title="Search..." class="k-input-inner">
        </span>
    </div>
</script>

<script>
    $("#filterText").keyup(function (e) {
        let treeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
        let filterText = e.target.value;

        if (!filterText) {
            treeView.collapse(".k-treeview-item");
        } else {
            treeView.expand(".k-treeview-item");
            treeView.dataSource.filter(
                { field: "NewName", operator: "contains", value: filterText }
            )
        }
    });
</script>