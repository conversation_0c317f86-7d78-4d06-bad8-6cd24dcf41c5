﻿@{var cardName = GlobalSettings.IsWellsense ? "SOC" : "RISC";}
@(Html.<PERSON>().TabStrip()
    .Name("riscsStrips")
    .SelectedIndex(0)
    .Animation(false)
    .Items( tabstrip => {

     tabstrip.Add().Text("")
         .HtmlAttributes(new { @data_bind = "click:refreshRiscMonthChart , html:tabStripHeaderRiscMonthly" })
         .Selected(true)
         .Content(@<text>
            <div class="card">
                <div class="card-header">
                    @if (GlobalSettings.IsWellsense)
                    {
                    <h6 class="mb-0">SOC Cards per Month</h6>
                    }
                    else
                    {
                    <h6 class="mb-0">RISC Cards per Month</h6>
                    }
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label>Period</label>
                        <input 
                           data-role="numerictextbox" 
                           data-spinners="false" 
                           data-bind="value:riscChartMonths, events:{change:refreshRiscMonthChart}" 
                           data-format="Last 0 months" 
                           data-min="1" 
                           />
                    </div>
                    @(Html.Kendo().Chart<StatisticsRiscModel>()
                    .Name("riscMonthChart")
                    .SeriesDefaults(series => series.Column())
                    .Series(series => {
                        series.Column(p => p.Onshore).Name("Onshore").Color("#7cbb00");
                        series.Column(p => p.Offshore).Name("Offshore");
                    })
                    .AutoBind(false)
                    .ValueAxis(v => v.Numeric().Title("Count").MajorGridLines(l => l.Visible(false)).MinorGridLines(l => l.Visible(false)))
                    .Legend(l => l.Position(ChartLegendPosition.Bottom))
                    .CategoryAxis(c => c.Categories(p => p.Category).Title("RISC"))
                    .Tooltip(t => t.Visible(true).Template("#=category# - #=value# #=series.name# RISCs"))
                    .DataSource(d => d.Read(read => read.Action("GetRiscMonthChartData", "Statistics").Data("riscChartData"))))
                </div>
            </div>
           </text>
         );

     tabstrip.Add().Text("")
        .HtmlAttributes(new { @data_bind = "click:refreshRiscYearChart, html:tabStripHeaderRiscYearly" })
        .Content(@<text>
            <div class="card">
                <div class="card-header">
                    @if (GlobalSettings.IsWellsense)
                    {
                        <h6 class="mb-0">SOC Cards per Year</h6>
                    }
                    else
                    {
                        <h6 class="mb-0">RISC Cards per Year</h6>
                    }
                </div>
                <div class="card-body">
                     <div class="mb-3">
                        <label>Period</label>
                        <input 
                           data-role="numerictextbox" 
                           data-spinners="false" 
                           data-bind="value:riscChartYears, events:{change:refreshRiscYearChart}" 
                           data-format="Last 0 years" 
                           data-min="1"
                           />
                    </div>
                    @(Html.Kendo().Chart<StatisticsRiscModel>()
                    .Name("riscYearChart")
                    .SeriesDefaults(series => series.Column())
                    .Series(series => {
                        series.Column(p => p.Onshore).Name("Onshore").Color("#7cbb00");
                        series.Column(p => p.Offshore).Name("Offshore");
                    })
                    .AutoBind(false)
                    .ValueAxis(v => v.Numeric().Title("Count").MajorGridLines(l => l.Visible(false)).MinorGridLines(l => l.Visible(false)))
                    .Legend(l => l.Position(ChartLegendPosition.Bottom))
                    .CategoryAxis(c => c.Categories(p => p.Year).Title($"{cardName}"))
                    .Tooltip(t => t.Visible(true).Template("#=category# - #=value# #=series.name# RISCs"))
                    .DataSource(d => d.Read(read => read.Action("GetRiscYearChartData", "Statistics").Data("riscYearChartData"))))
                </div>
            </div>
          </text>
         );
        }
      )
    );

