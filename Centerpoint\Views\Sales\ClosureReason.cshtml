﻿<p class="text-success">Please fill in a reason for closure</p>
<div>
    <div class="form-group">
        <label>Reason</label>
        @(Html.Kendo().DropDownList()
            .Name("reason")
            .Filter(FilterType.Contains)
            .OptionLabel("Select Reason")
            .DataValueField("OpportunityClosedReasonId")
            .DataTextField("Name")
            .AutoBind(false)
            .HtmlAttributes(new { @data_bind= "value:closureReason", @data_value_update = "keyup" })
            .DataSource(d => d.<PERSON>("GetClosureReasons", "Lookup"))
        )
    </div>
    <div class="form-group">
        <label>Comment</label>
        @Html.TextArea("ClosureReason",null, new { @class = "form-control", @rows = "5", })
    </div>
</div>
<div>
    <button id="closureReasonConfirm" class="btn btn-primary btn-sm" data-bind="visible:closureReason">Confirm</button>
</div>
