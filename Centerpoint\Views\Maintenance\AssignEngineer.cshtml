﻿<div class="row col-md-12">
    <h5 >Please select an Engineer to assign to this MR</h5>
</div>
<br />
<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>Assigned Engineer</label>
            <br />
            @(Html.Kendo().DropDownList()
             .Name("enginer")
             .Filter("contains")
             .OptionLabel("Select Engineer")
             .DataValueField("UserId")
             .DataTextField("Name")
             .HtmlAttributes(new { @data_bind = "value:engineerUserId" })
             .DataSource(d => d.<PERSON>("GetEngineers", "Lookup")))
        </div>
    </div>
</div>
<span>
    <button id="assignEngineerConfirm" data-bind="enabled:engineerUserId" class="btn btn-primary btn-sm">Confirm</button>
</span>

