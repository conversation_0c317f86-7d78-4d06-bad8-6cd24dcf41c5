﻿@model EquipmentItemCustomStatusCodeModel
<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label>Status</label>
            @(Html.Kendo().DropDownListFor(m => m.CustomStatusCodeId)
                            .Filter("contains")
                            .OptionLabel("Select Custom Status")
                            .DataTextField("CustomStatusName")
                            .DataValueField("CustomStatusCodeId")
                            .DataSource(d => d.Read("GetCustomStatusCode", "Lookup"))
            )
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label>Expiry Date</label>
            @(Html.Kendo().DatePickerFor(m => m.ExpiryDate)
            .Min(DateTime.Now.Date))
        </div>
    </div>
</div>
<hr />
<div class="form-group">
    <label>Comment</label>
    @(Html.TextAreaFor(m => m.Note, new { @class = "form-control", @style = "width:100%", @rows = "5" , required="required" }))
</div>