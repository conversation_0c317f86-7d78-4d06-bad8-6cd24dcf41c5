﻿namespace Centerpoint.Common.Constants
{
    public static class OpportunityTimePeriodConstant
    {

        public const string Days = "DAY";
        public const string Weeks = "WEK";
        public const string Months = "MON";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Days,"Day(s)"},
                    {Weeks,"Week(s)"},
                    {Months,"Month(s)"}
                };
            }
        }
    }
}
