@import 'variables.css';

@import 'home.css';

.body-wrapper {
    display: grid;
    gap: 0px 0px;
    grid-template-areas: "header main";
    grid-template-columns: 220px calc(100% - 220px);
    grid-template-rows: 100% auto;
    background-color: #e5e5e5;
    min-height: 100vh;
}
.body-wrapper[data-sidebar-open=false] {
    grid-template-columns: 0 100%;
}
header {
    background-color: var(--accent);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow: hidden;
    padding-bottom:10px;
    transition: 0.3s;
}
    header nav {
        padding: 10% 2% 10% 2%;
    }
header nav .logo {
    margin-bottom: 25%;
    margin-left: 10%;
}
header nav .main-list {
    padding: 0;
    overflow: auto;
}
header nav .main-list::-webkit-scrollbar {
    width: 6px;
}
header nav .main-list::-webkit-scrollbar-track  {
    background-color: white;
}
header nav .main-list::-webkit-scrollbar-thumb {
    box-shadow: inset 8px 8px 8px rgba(110,182,180, 0.8);
}
header nav ul .nav-item .collapse.show ul li{
    margin-bottom: 10px;
}
header nav ul .nav-item a {
    display: flex;
    align-items: center;
    transition: 0.3s;
    padding: 0.8rem 1.3rem 0.8rem 1.3rem;
}

header nav ul .nav-item .collapse a{
    display: flex;
    align-items: center;
    transition: 0.3s;
    padding: 0.3rem 0.7rem 0.3rem 0.7rem;
}
    header nav ul .nav-item a .nav-item-icon {
        height: 1rem;
        width: 0.9rem;
        margin-right: 5px;
        background-repeat: no-repeat;
        background-size: cover;
        display: flex;
        align-items: center;
    }
header nav ul .nav-item a span, header nav ul .nav-item i {
    color: #fff;
}

    header nav ul .nav-item:has(>a[aria-expanded="true"]) {
        background-color: #324952;
    }
    /* header nav .nav-item:hover > a,
header nav .nav-item:hover > a span,
header nav .nav-item.active > a span{
    color: var(--primary)!important;
} */
   /* header nav ul .nav-item:not(.nav-item-without-hover):hover {
        background-color: #324952;
    }*/
header nav ul .nav-item:hover > a .nav-item-icon path, header nav ul .nav-item > a.active .nav-item-icon path {
    fill: var(--primary);
}
header nav ul .nav-item:not(.nav-item-without-hover):hover > a, 
header nav ul .nav-item:not(.nav-item-without-hover):hover > a span, 
header nav ul .nav-item > .nav-item-icon a.active span, 
header nav ul .nav-item:not(.nav-item-without-hover):hover > a i {
    color: var(--primary)!important;
}

header ul .nav-item>.active,
header ul .nav-item>.active > i,
header ul .nav-item>.active > span{
    color: var(--primary)!important;
}

header .version-wrapper {
    color: #fff;
    text-align: center;
    display: block;
}

main .sidebar-control-btn {
    position: absolute;
    margin-top: 15px;
    margin-left: 3%;
    display: flex;
    padding-left: 8px;
    padding-right: 8px;
}

main #dataAndTime {
    position: absolute;
    margin-right: 8%;
    margin-top: 15px;
    right: 0;
    display: flex;
    padding-left: 8px;
    padding-right: 8px;
}

.main-content-header{
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.main-content-body {
    overflow: auto;
    overflow-x: hidden;
    margin: 0 2% 1% 2%;
    padding: 40px 20px;
    background-color: #fff;
    border-radius: 10px;
}

.main-content-wrapper
.main-content-header{
    padding-bottom: 15px;
    margin-bottom: 20px;
    border-bottom: 1px solid #E7E7E7;
}

.main-content-title{
    color: var(--primary);
    white-space: nowrap;
    width: 72%;
    text-overflow: ellipsis;
    overflow: hidden;
}

.main-content-wrapper
.main-content-header
.main-content-time-zone{
    display: flex;
}

.justify-toolbar-content-to-end > .k-toolbar {
    display: flex;
    justify-content: end;
}

.justify-toolbar-content-between > .k-toolbar {
    display: flex;
    justify-content: space-between;
}

.justify-toolbar-content-between > .k-toolbar::before{
    content: none;
}

.notificationWrapper {
    position: fixed;
    bottom: 15px;
    right: 15px;
    z-index: 10;
}
#banner {
    display: flex;
}