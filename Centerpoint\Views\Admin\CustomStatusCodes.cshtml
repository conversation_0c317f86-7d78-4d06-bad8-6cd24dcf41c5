﻿@using Centerpoint.Common.Enums
@model CustomStatusCodeModel

@Html.Partial( "_GridNotification", EntityType.CustomStatus)

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-flag"></i>
        Custom Status
        (<span data-bind="text:totalCustomStatusCodes"></span>)
    </h4>
</div>
<hr />

<div class="grid-container">
    @(Html.Kendo().Grid<CustomStatusCodeModel>()
        .Name("customStatusCodeGrid")
        .Columns(c => {
            c.Bound(p => p.Name);
            c.Bound(p => p.Country).EditorTemplateName("Country");
            c.Bound(p => p.Code);
            c.<PERSON>(command => { 
                command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
            }).Width(200);
        })
        .Editable(editable => editable.Mode(GridEditMode.InLine))
        .ToolBar(t => {
            t.Create().Text("Add Status");
            t.Excel().Text("Export");
        }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
        .Sortable()
        .Filterable()
        .Scrollable()
        .Resizable(c => c.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .Excel(excel => excel
            .FileName(string.Format("Centerpoint_Logistics_Statuses_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Admin"))
        )
        .Events(e => e.DataBound("updateCustomStatusCodeTotal"))
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(m => m.Id(p => p.CustomStatusCodeId))
            .Events(e => e.Error("onError").RequestEnd("onRequestEnd"))
            .Read(read => read.Action("GetCustomStatusCodes", "Admin"))
            .Create(create => create.Action("UpdateCustomStatusCode", "Admin"))
            .Update(update => update.Action("UpdateCustomStatusCode", "Admin"))
            .Destroy(destroy => destroy.Action("DeleteCustomStatusCode", "Admin"))
        )
    )
</div>


<script>
    function updateCustomStatusCodeTotal() {
        var customStatusCodeGrid = $("#customStatusCodeGrid").data("kendoGrid");
        var totalCustomStatusCodes = customStatusCodeGrid.dataSource.total();
        viewModel.set("totalCustomStatusCodes", totalCustomStatusCodes);
    }

    function onError(e, status) {
        if (e.status == "customerror") {
            alert(e.errors);

            var customStatusCodeGrid = $("#customStatusCodeGrid").data("kendoGrid");
            customStatusCodeGrid.dataSource.cancelChanges();
        }

        onErrorDeleteGridHandler(e, "#customStatusCodeGrid");
    }

    var viewModel = new kendo.observable({
        totalCustomStatusCodes: 0
    });

    kendo.bind(document.body.children, viewModel);
</script>
