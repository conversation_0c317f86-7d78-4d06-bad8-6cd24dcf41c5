﻿// ajax interceptor
$(document).ajaxError(function (event, jqXHR) {
    jqXHRErrors(jqXHR.responseJSON);
})

function showError(error) {
    const errorPopup = $("<div></div>").kendoNotification({
        appendTo: "#notificationWrapper",
        position: {
            bottom: 30,
            right: 30
        }
    }).data("kendoNotification");

    errorPopup.show(error, 'error');
}

function jqXHRErrors(errors) {
    if (errors != undefined && errors.length != undefined) {
        for (var i = 0; i < errors.length; i++) {
            showError(errors[i]);
        }
    }
}

$(document).ajaxSuccess(function (event) {
    //    console.log("ajaxSuccess", "this is the event -- ", event)
});

function handleSuccessMessages(tempName, updatedItemsName) {
    const errorPopup = $("<div></div>").kendoNotification({
        appendTo: "#notificationWrapper",
    }).data("kendoNotification");
    errorPopup.show(tempName.replace("_updatedItemsName", updatedItemsName), 'success');
}