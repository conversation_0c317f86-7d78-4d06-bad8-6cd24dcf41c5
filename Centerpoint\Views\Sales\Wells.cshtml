﻿@model OpportunityModel
@(Html.Ken<PERSON>().Grid<CompanyWellModel>()
    .Name("companyWellGrid")
    .Columns(c => {
        if (Model.ParentOpportunityId.HasValue) {
            c.Bound(p => p.CompanyWellId).Title("Name").ClientTemplate("<a href='" + @Url.Action("EditCompanyWell", "Admin", new { @id = "" }) + "/#=CompanyWellId#?opportunityId=" + Model.ParentOpportunityId + "&revision=" + ViewBag.Revision + "'>#=Name#</a>");
        } else {
            c.Bound(p => p.CompanyWellId).Title("Name").ClientTemplate("<a href='" + @Url.Action("EditCompanyWell", "Admin", new { @id = "" }) + "/#=CompanyWellId#?opportunityId=" + Model.OpportunityId + "&revision=" + ViewBag.Revision + "'>#=Name#</a>");
        }
        c.<PERSON>(p => p.CompanyFieldId).Title("Field").ClientTemplate("#=CompanyFieldName#");
        c.<PERSON>(p => p.MinimumId);
        c.Bound(p => p.MaximumDeviation);
        c.Bound(p => p.TotalAttachments).ClientTemplate("#if(TotalAttachments){#<a class='badge' style='background:\\#0073D0;color:\\#fff' href='\\#' onclick='wellAttachmentCount(#=CompanyWellId#)'>#=TotalAttachments#</a>#} else {##=TotalAttachments##}#"); ;
        c.Bound(p => p.MaximumPressure).Hidden(true);
        c.Bound(p => p.MaximumPressureUnits).Hidden(true);
        c.Bound(p => p.MaximumTemperature).Hidden(true);
        c.Bound(p => p.MaximumTemperatureDegrees).Hidden(true);
        c.Bound(p => p.H2S).Hidden(true);
        c.Bound(p => p.CO2).Hidden(true);
        c.Bound(p => p.FluidTypes).Hidden(true);
    })
    .ToolBar(t => {
        t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetCompanyWellGrid", @class="bg-danger text-white"});
    }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
    .Sortable()
    .Filterable()
    .Scrollable(s => s.Height(500))
    .Resizable(c => c.Columns(true))
    .ColumnMenu(c => c.Columns(true))
    .Events(e => e
        .DataBound("updateCompanyWellTotal")
        .ColumnReorder("saveCompanyWellGrid")
        .ColumnResize("saveCompanyWellGrid")
        .ColumnShow("saveCompanyWellGrid")
        .ColumnHide("saveCompanyWellGrid")
    )
    .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Model(model => {
            model.Id(m => m.CompanyWellId);
        })
    .Read(read => read.Action("GetCompanyWells", "Sales").Data("companyFieldData"))
    )
)
