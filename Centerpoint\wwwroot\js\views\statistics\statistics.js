
$(document).ready(function () {
    loadEquipmentItemGrid();
    var assetEquipmentItemStatsGrid = $('#assetEquipmentItemStatsGrid').data("kendoGrid");

    assetEquipmentItemStatsGrid.bind('dataBound', function (e) {
        this.element.find('.k-i-excel').remove();
    });

    $(window).on("resize orientationchange", function () {
        kendo.resize($("body"));
    });

    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        kendo.resize($("body"));
    });

    $("#assetValueChart").kendoTooltip({
        filter: ".k-leaf,.k-treemap-title",
        position: "top",
        content: function (e) {
            var treemap = $("#assetValueChart").data("kendoTreeMap");
            var item = treemap.dataItem(e.target.closest(".k-treemap-tile"));
            return item.Name + " - " + item.Value;
        }
    });

    var assetEquipmentFilterOptions = $.cookie('assetEquipmentFilterOptions');

    if(assetEquipmentFilterOptions){
        viewModel.set("assetEquipmentFilterOptions", JSON.parse(assetEquipmentFilterOptions));
    }

    assetEquipmentGridFilter();
    kendo.bind(document.body.children, viewModel);
});

function assetEquipmentGridFilter(){
    var assetEquipmentItemStatsGrid = $("#assetEquipmentItemStatsGrid").data("kendoGrid");
    var assetEquipmentFilterOptions = JSON.stringify(viewModel.get("assetEquipmentFilterOptions"));

    var filters = [];

    if (assetEquipmentFilterOptions != null && assetEquipmentFilterOptions.length > 0) {
        var statusFilters = [];

        for(var i=0;i<assetEquipmentFilterOptions.length;i++){
            let filterItem = getEquipmentFilter(assetEquipmentFilterOptions[i].Key);
            if(filterItem){
                statusFilters.push(getEquipmentFilter(assetEquipmentFilterOptions[i].Key));
            }
        }

        var statusFilter = {
            logic: "or",
            filters:statusFilters
        };

        filters.push(statusFilter);
    }

    var filter = {
        logic: "and",
        filters
    };

    assetEquipmentItemStatsGrid.dataSource.filter(filter);

    $.removeCookie('assetEquipmentFilterOptions');
    $.cookie('assetEquipmentFilterOptions', assetEquipmentFilterOptions, { expires: 7, path: '/' });
}

function getEquipmentFilter(option){
    var statuses = statisticsModel.statuses;
    if(statuses.indexOf(option)>-1){
        return { field: "Status", operator: "contains", value: option };
    } else if(option == "In Transit"){
        return { field: "IsTransit", operator: "eq", value: true };
    } else if(option == "Reserved"){
        return { field: "IsReserved", operator: "eq", value: true };
    } else if(option == "Selected"){
        return { field: "IsSelected", operator: "eq", value: true };
    }else if(option=="Bundle OK"){
        return {logic:"and", filters:[{ field: "IsOkBundle", operator: "eq", value: true }, {field:"HasBundle", operator:"eq",value:true}]};
    } else if(option=="Bundle Check"){
        return {logic:"and", filters:[{ field: "IsOkBundle", operator: "eq", value: false }, {field:"HasBundle", operator:"eq",value:true}]};
    } else if(option == "Bundled Item"){
        return { field: "IsPartOfBundle", operator: "eq", value: true};
    }
}

$(function () {
    var grid = $("#assetEquipmentItemStatsGrid").data("kendoGrid");

    $("#reset").click(function (e) {
        e.preventDefault();
        localStorage["assetEquipmentItemStatsGrid"] = "";
        window.location.reload();
    });
});

function refreshEquipmentCategoryMaintenanceStepGrid() {
    var equipmentCategoryMaintenanceStepGrid = $("#equipmentCategoryMaintenanceStepGrid").data("kendoGrid");
    equipmentCategoryMaintenanceStepGrid.dataSource.read();
}

function loadEquipmentItemGrid() {
    var grid = $("#assetEquipmentItemStatsGrid").data("kendoGrid");
    var toolBar = $("#assetEquipmentItemStatsGrid .k-grid-toolbar").html();
    var options = localStorage["assetEquipmentItemStatsGrid"];
    if (options) {
        grid.setOptions(JSON.parse(options));
        $("#assetEquipmentItemStatsGrid .k-grid-toolbar").html(toolBar);
        $("#assetEquipmentItemStatsGrid .k-grid-toolbar").addClass("k-grid-top");
    }
}

function saveEquipmentGrid(e) {
    setTimeout(function () {
        var grid = $("#assetEquipmentItemStatsGrid").data("kendoGrid");
        localStorage["assetEquipmentItemStatsGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}

function categoryData() {
    return {
        categoryId: viewModel.get("selectedEquipmentCategory").EquipmentCategoryId
    }
}

function statusFilter(element) {
    element.kendoAutoComplete({
        dataSource: {
            transport: {
                read: "/Lookup/GetEquipmentItemStatuses"
            }
        }
    });
}

function equipmentItemMaintenanceScheduleData() {
    return {
        eId: viewModel.get("selectedEquipmentItemId")
    }
}

function scheduleDates(equipmentItemId) {
    viewModel.set("selectedEquipmentItemId", equipmentItemId);

    $("#scheduleDatesWindow").data("kendoWindow").center().open();
}

function scheduleDatesWindowOpened() {
    var equipmentItemMaintenanceScheduleGrid = $("#equipmentItemMaintenanceScheduleGrid").data("kendoGrid");
    equipmentItemMaintenanceScheduleGrid.dataSource.read();
}

function maintenanceRecordData() {
    return {
        equipId: viewModel.get("selectedEquipmentItemId")
    }
}

function maintenanceRecordCount(equipmentItemId) {
    viewModel.set("selectedEquipmentItemId", equipmentItemId);

    $("#maintenanceRecordWindow").data("kendoWindow").center().open();
}

function maintenanceRecordWindowOpened() {
    var maintenanceRecordGrid = $("#maintenanceRecordGrid").data("kendoGrid");
    maintenanceRecordGrid.dataSource.read();
}

function userEquipmentCategorySelected(e) {
    if ($.DirtyForms.isDirty()) {
        var result = confirm("You have made changes to this page which are not saved. If you proceed now you will lose these changes.");

        if (!result) {
            e.preventDefault();
            var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
            var selectedEquipmentCategory = viewModel.get("selectedEquipmentCategory");
            var selectedEquipmentCategoryNode = equipmentCategoryTreeView.findByText(selectedEquipmentCategory.NewName);
            equipmentCategoryTreeView.select(selectedEquipmentCategoryNode);
        } else {
            $('form:dirty').dirtyForms('setClean');
        }
    }
}

function selectedEquipmentItem(e){
    var selectedEquipmentItemRow = this.select();

    if(selectedEquipmentItemRow){
        var selectedItem = this.dataItem(selectedEquipmentItemRow);

        if(selectedItem){
            viewModel.set("selectedEquipmentItemId", selectedItem.EquipmentItemId);
        } else{
            viewModel.set("selectedEquipmentItemId","");
        }
    } else{
        viewModel.set("selectedEquipmentItemId","");
    }
}

$("#moveAssetItemConfirm").click(function () {
    var assetEquipmentItemStatsGrid = $("#assetEquipmentItemStatsGrid").data("kendoGrid");

    var selectedEquipmentItemIds = [];

    assetEquipmentItemStatsGrid.select().each(function () {
        var selectedEquipmentItem = assetEquipmentItemStatsGrid.dataItem($(this));

        if (selectedEquipmentItem) {
            selectedEquipmentItemIds.push(selectedEquipmentItem.EquipmentItemId);
        }
    });

    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url:'/Assets/MoveAssetItem',
        data: {
            equipmentItemIds : selectedEquipmentItemIds,
            equipmentCategoryId : viewModel.get("selectedCategory")
        },
        success: function(){
            $("#equipmentCategory").val("")
            window.location.reload();
        },
    });
});


function selectedCategory(e){
    var assetItemTreeView = $("#assetItemTreeView").data("kendoTreeView");
    var node = assetItemTreeView.select();
    var selectedCategory = assetItemTreeView.dataItem(node);

    if (selectedCategory) {
        viewModel.set("selectedCategory", selectedCategory.EquipmentCategoryId);
    }else {
        viewModel.set("selectedCategory","")
    }
}

function moveAssetItemWindow(equipmentItemId) {
    viewModel.set("selectedEquipmentItemId", equipmentItemId);

    $("#moveAssetItemWindowOpen").data("kendoWindow").center().open();
}

function moveAssetItemWindowOpened() {
    var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
    equipmentCategoryTreeView.dataSource.read();
}

function equipmentItemEdit(e) {
    var equipmentItem = e.model;
    var equipmentCategory = viewModel.get("selectedEquipmentCategory");

    $(e.container).find(".k-edit-buttons").html("<a class='btn btn-primary btn-sm k-grid-update' href='#'>Update</a> " +
        "<a class='btn btn-primary btn-sm k-grid-cancel' href='#'>Cancel</a>");

    equipmentItem.set("PointsPerMonth", equipmentCategory.PointsPerMonth);
    equipmentItem.set("PointsPerRun", equipmentCategory.PointsPerRun);
    equipmentItem.set("PointsPerMove", equipmentCategory.PointsPerMove);
}

function equipmentItemData() {
    var equipmentCategory = viewModel.get("selectedEquipmentCategory");

    return {
        equipmentCategoryId: equipmentCategory ? equipmentCategory.EquipmentCategoryId : "",
        startDate:viewModel.get("assetStartDate"),
        endDate:viewModel.get("assetEndDate")
    };
}

function refreshAllEquipmentItems() {
    var assetEquipmentItemStatsGrid = $("#assetEquipmentItemStatsGrid").data("kendoGrid");
    assetEquipmentItemStatsGrid.dataSource.read();
}

function updateEquipmentTotals() {
    var assetEquipmentItemStatsGrid = $("#assetEquipmentItemStatsGrid").data("kendoGrid");
    var totalEquipmentItems = assetEquipmentItemStatsGrid.dataSource.total();
    viewModel.set("totalEquipmentItems", totalEquipmentItems);

}

function updateShipmentMRGrid() {
    var shipmentMRGrid = $("#shipmentMRGrid").data("kendoGrid");
    var totalShipmentItemsWithMR = shipmentMRGrid.dataSource.total();
    viewModel.set("totalShipmentItemsWithMR", totalShipmentItemsWithMR);
}

function refreshEquipmentCategories() {
    var equipmentCategory = $("#equipmentCategoryTreeView").data("kendoTreeView");
    equipmentCategory.dataSource.read();
}

function equipmentCategorySelected(e) {
    var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
    var node = equipmentCategoryTreeView.select();
    var originalEquipmentCategory = viewModel.get("selectedEquipmentCategory");
    var selectedEquipmentCategory = equipmentCategoryTreeView.dataItem(node);

    if (originalEquipmentCategory != selectedEquipmentCategory && selectedEquipmentCategory) {
        $.removeCookie('equipmentCategory');
        $.cookie('equipmentCategory', selectedEquipmentCategory.EquipmentCategoryId, { expires: 7, path: '/' });
        viewModel.set("selectedEquipmentCategory", selectedEquipmentCategory);

        var grid = $("#assetEquipmentItemStatsGrid").data("kendoGrid");
        grid.dataSource.options.endless = null;
        grid._endlessPageSize = grid.dataSource.options.pageSize;
        grid.dataSource.pageSize(grid.dataSource.options.pageSize);
    }
}

function equipmentCategoryLoaded(e) {
    $(".k-treeview-item").click(function (e) {
        var equipmentCategoryTree = $("#equipmentCategoryTreeView").data("kendoTreeView");
        var equipmentCategorySelected = equipmentCategoryTree.select();

        if (equipmentCategorySelected && $(e.currentTarget).attr("date-uid") == $(equipmentCategorySelected.context).attr("data-uid")) {
            equipmentCategoryTree.select($());
        }
    });
}

function equipmentCategoryDropped(e) {
    var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
    var equipmentCategory = equipmentCategoryTreeView.dataItem(e.sourceNode);

    var parentEquipmentCategory = "";

    if (e.dropPosition == "over") {
        parentEquipmentCategory = equipmentCategoryTreeView.dataItem(e.destinationNode);
    } else {
        parentEquipmentCategory = equipmentCategoryTreeView.dataItem(equipmentCategoryTreeView.parent(e.destinationNode));
    }

    $.ajax({
        type: "POST",
        url: "/Admin/UpdateEquipmentCategoryParent",
        data: {
            equipmentCategoryId: equipmentCategory.EquipmentCategoryId,
            parentEquipmentCategoryId: parentEquipmentCategory ? parentEquipmentCategory.EquipmentCategoryId : ""
        },
        success: function (data) {
            viewModel.set("selectedEquipmentCategory", equipmentCategory);
            console.log(JSON.stringify(data));
        },
        dataType: "json"
    });
}

jQuery(window).on("resize", function (event) {
    var chartDiv = jQuery("#jobChart");
    var chart = chartDiv.data("kendoChart");

    // Temporarily disable animation, yo!
    chart.options.transitions = false;

    // Temporarily hide, then set size of chart to container (which will naturally resize itself), then show it again
    chartDiv.css({ display: "none" });
    chartDiv.css({ width: chartDiv.parent().innerWidth(), display: "block" });

    chart.redraw();
});

jQuery(window).on("resize", function (event) {
    var chartDiv = jQuery("#jobServiceChart");
    var chart = chartDiv.data("kendoChart");

    // Temporarily disable animation, yo!
    chart.options.transitions = false;

    // Temporarily hide, then set size of chart to container (which will naturally resize itself), then show it again
    chartDiv.css({ display: "none" });
    chartDiv.css({ width: chartDiv.parent().innerWidth(), display: "block" });

    chart.redraw();
});


jQuery(window).on("resize", function (event) {
    var chartDiv = jQuery("#runChart");
    var chart = chartDiv.data("kendoChart");

    // Temporarily disable animation, yo!
    chart.options.transitions = false;

    // Temporarily hide, then set size of chart to container (which will naturally resize itself), then show it again
    chartDiv.css({ display: "none" });
    chartDiv.css({ width: chartDiv.parent().innerWidth(), display: "block" });

    chart.redraw();
});

jQuery(window).on("resize", function (event) {
    var chartDiv = jQuery("#runsByServiceChart");
    var chart = chartDiv.data("kendoChart");

    // Temporarily disable animation, yo!
    chart.options.transitions = false;

    // Temporarily hide, then set size of chart to container (which will naturally resize itself), then show it again
    chartDiv.css({ display: "none" });
    chartDiv.css({ width: chartDiv.parent().innerWidth(), display: "block" });

    chart.redraw();
});

jQuery(window).on("resize", function (event) {
    var chartDiv = jQuery("#riscMonthChart");
    var chart = chartDiv.data("kendoChart");

    // Temporarily disable animation, yo!
    chart.options.transitions = false;

    // Temporarily hide, then set size of chart to container (which will naturally resize itself), then show it again
    chartDiv.css({ display: "none" });
    chartDiv.css({ width: chartDiv.parent().innerWidth(), display: "block" });

    chart.redraw();
});

jQuery(window).on("resize", function (event) {
    var chartDiv = jQuery("#riscYearChart");
    var chart = chartDiv.data("kendoChart");

    // Temporarily disable animation, yo!
    chart.options.transitions = false;

    // Temporarily hide, then set size of chart to container (which will naturally resize itself), then show it again
    chartDiv.css({ display: "none" });
    chartDiv.css({ width: chartDiv.parent().innerWidth(), display: "block" });

    chart.redraw();
});

jQuery(window).on("resize", function (event) {
    var chartDiv = jQuery("#shipmentMonthChart");
    var chart = chartDiv.data("kendoChart");

    // Temporarily disable animation, yo!
    chart.options.transitions = false;

    // Temporarily hide, then set size of chart to container (which will naturally resize itself), then show it again
    chartDiv.css({ display: "none" });
    chartDiv.css({ width: chartDiv.parent().innerWidth(), display: "block" });

    chart.redraw();
});

function jobChartData() {
    return {
        months: viewModel.get("jobChartMonths"),
        showBy: viewModel.get("jobChartShowBy")
    }
}

function jobServiceChartData() {
    return {
        months: viewModel.get("jobServiceChartMonths"),
        showBy: viewModel.get("jobServiceChartShowBy")
    }
}

function runChartData() {
    return {
        months: viewModel.get("runChartMonths"),
        showBy: viewModel.get("runChartShowBy")
    }
}

function runServiceChartData() {
    return {
        months: viewModel.get("runServiceChartMonths"),
        showBy: viewModel.get("runServiceChartShowBy")
    }
}

function riscChartData() {
    return {
        months: viewModel.get("riscChartMonths")
    }
}

function riscYearChartData() {
    return {
        years: viewModel.get("riscChartYears")
    }
}

function shipmentMonthChartData() {
    return {
        months: viewModel.get("shipmentChartMonths")
    }
}

function assetValueDataRequestEnd() {
    viewModel.set("assetValueDataLoading", false);

    $("#assetValueChart").kendoTooltip({
        filter: ".k-leaf,.k-treemap-title",
        position: "top",
        content: function (e) {
            var treemap = $("#assetValueChart").data("kendoTreeMap");
            var item = treemap.dataItem(e.target.closest(".k-treemap-tile"));
            return item.Name + " - " + item.Value;
        }
    });
}

function onJobServiceChartDataBound(e) {
    console.log(e);
}

var viewModel = new kendo.observable({
    assetEquipmentFilterOptions: statisticsModel.assetEquipmentFilterOptions,
    totalShipmentItemsWithMR: 0,
    showByOptions: ["Month", "Quarter"],
    jobChartMonths: 12,
    jobChartShowBy:"Month",
    jobServiceChartMonths: 3,
    jobServiceChartShowBy: "Month",
    runChartMonths: 12,
    runChartShowBy: "Month",
    runServiceChartMonths: 3,
    runServiceChartShowBy: "Month",
    riscChartMonths: 12,
    riscChartYears: 4,
    shipmentChartMonths: 12,
    reDrawChart: function (e){
        let selector = $(e.currentTarget).attr("data-chart-selector");
        $(`#${selector}`).data("kendoChart").redraw();
    },
    tabStripHeaderDetails: function () {
        return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Details </span>`;
    },
    tabStripHeaderAssets: function () {
        return `<span class="k-link"><i class="fa fa-table mr-1"></i> Assets </span>`;
    },
    tabStripHeaderAssetsStats: function () {
        return `<span class="k-link"><i class="fa fa-table mr-1"></i> Asset Stats </span>`;
    },
    tabStripHeaderAssetValue: function () {
        return `<span class="k-link"><i class="fa fa-wrench mr-1"></i> Asset Value </span>`;
    },
    tabStripHeaderRiscs: function () {
        return `<span class="k-link"><i class="fa fa-bar-chart mr-1"></i> Riscs </span>`;
    },
    tabStripHeaderRiscMonthly: function () {
        return `<span class="k-link"><i class="fa fa-bar-chart mr-1"></i> ${settings.safetyCardName} (Monthly) </span>`;
    },
    tabStripHeaderRiscYearly: function () {
        return `<span class="k-link"><i class="fa fa-bar-chart mr-1"></i> ${settings.safetyCardName} (Yearly) </span>`;
    },
    tabStripHeaderRuns: function () {
        return `<span class="k-link"><i class="fa fa-bar-chart mr-1"></i> Runs </span>`;
    },
    tabStripHeaderRunsByService: function () {
        return `<span class="k-link"><i class="fa fa-bar-chart mr-1"></i> Runs by Service </span>`;
    },
    tabStripHeaderJobs: function () {
        return `<span class="k-link"><i class="fa fa-bar-chart mr-1"></i> Jobs </span>`;
    },
    tabStripHeaderJobsByService: function () {
        return `<span class="k-link"><i class="fa fa-bar-chart mr-1"></i> Jobs by Service </span>`;
    },
    tabStripHeaderShipments: function () {
        return `<span class="k-link"><i class="fa fa-bar-chart mr-1"></i> Shipments </span>`;
    },
    tabStripHeaderShipmentsMonthly: function () {
        return `<span class="k-link"><i class="fa fa-bar-chart mr-1"></i> Shipments (Monthly) </span>`;
    },
    tabStripHeaderConstructorOfShipmentsWithNumber: function () {
        return `<span class="k-link"><i class="fa fa-plane mr-2"></i>  Shipments (With active MRs) (<span data-bind="text: totalShipmentItemsWithMR"></span>)</span>`;   
    },
    refreshJobChart: function () {
        $("#jobChart").data("kendoChart").dataSource.read();
    },
    refreshJobServiceChart: function () {
        $("#jobServiceChart").data("kendoChart").dataSource.read();
        $("#jobServiceChart").data("kendoChart").redraw();
    },
    refreshRunChart: function () {
        $("#runChart").data("kendoChart").dataSource.read();
    },
    refreshRunServiceChart: function () {
        $("#runsByServiceChart").data("kendoChart").dataSource.read();
    },
    refreshRiscMonthChart: function () {
        $("#riscMonthChart").data("kendoChart").dataSource.read();
    },
    refreshRiscYearChart: function () {
        $("#riscYearChart").data("kendoChart").dataSource.read();
    },
    refreshShipmentChart: function () {
        $("#shipmentMonthChart").data("kendoChart").dataSource.read();
    },
    assetValueDataLoading:false,
    refreshAssetStats: function () {
        refreshAllEquipmentItems();
    },
    assetStartDate: statisticsModel.assetStartDate,
    assetEndDate: statisticsModel.assetEndDate,
    refreshAssetValueChart: function () {
        $("#assetValueChart").data("kendoTreeMap").dataSource.read();
    },
    units: statisticsModel.units,
    kg: statisticsModel.kg,
    selectedCategory: 0,
    selectedEquipmentCategory: "",
    selectedEquipmentCategoryPath: [],
    totalEquipmentCategories: 0,
    totalEquipmentItems: 0,
    selectedEquipmentItemId: 0,
    selectedMaintenanceRecordId: 0,
    totalEquipmentCategoryMaintenanceSteps: 0,
    equipmentQuery:"",

    newEquipmentVisible: function (){
        var equipmentCategory = this.get("selectedEquipmentCategory");
        return !equipmentCategory.EquipmentCategoryId;
    },
    expandAll: function () {
        var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
        equipmentCategoryTreeView.expand(".k-treeview-item");
    },
    collapseAll: function () {
        var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
        equipmentCategoryTreeView.collapse(".k-treeview-item");
    },
    expandAllItems: function () {
        var assetItemTreeView = $("#assetItemTreeView").data("kendoTreeView");
        assetItemTreeView.expand(".k-treeview-item");
    },
    collapseAllItems: function () {
        var assetItemTreeView = $("#assetItemTreeView").data("kendoTreeView");
        assetItemTreeView.collapse(".k-treeview-item");
    }
});
