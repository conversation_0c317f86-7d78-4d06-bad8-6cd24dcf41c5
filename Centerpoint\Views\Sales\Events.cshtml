﻿@model OpportunityModel

@(Html.Ken<PERSON>().Grid<OpportunityEventModel>()
    .Name("opportunityEventsGrid")
    .Columns(c => {
    c.Bound(p => p.NewName).Title("Event ID").ClientTemplate("<a href='" + @Url.Action("EditEvent", "Sales", new { @id = "" }) + "/#=OpportunityEventId#?revision=" + ViewBag.Revision + "'>#=NewName#</a>");
    c.<PERSON>und(p => p.CompanyContacts).Title("Contact (s)");
    c.Bound(p => p.TrimmedTopic).Title("Topic").ClientTemplate("#=opportunityEventsGridTopicGenerate(data)#");
    c.<PERSON>(p => p.TotalEventActionCount).Title("Follow-up Actions").ClientTemplate("#=totalEventActionCountTemplateGenerate(data)#");
    c.<PERSON>(p => p.EventDate).Title("Event Date").Format(DateConstants.DateTimeFormat);
    c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
    c.<PERSON>und(p => p.CreatedBy).Title("Created By");
    if(Model.Stage != OpportunityStageConstant.Closed) {
            c.Command(command => {
                command.Destroy().HtmlAttributes(new { @class = "bg-danger text-white grid-action-button" });
            }).Width(150);
        }
    })
    .ToolBar(t => t.ClientTemplate("#=opportunityEventsGridToolbarTemplate(data)#"))
    .Events(e => e
        .DataBound("updateOpportunityEventsGrid")
        .ColumnReorder("saveOpportunityEventGrid")
        .ColumnResize("saveOpportunityEventGrid")
        .ColumnShow("saveOpportunityEventGrid")
        .ColumnHide("saveOpportunityEventGrid")
    )
    .ColumnMenu(c => c.Columns(true))
    .Sortable()
    .Resizable(r => r.Columns(true))
    .Filterable()
    .Groupable()
    .Editable(e => e.Mode(GridEditMode.InLine))
    .Scrollable(s => s.Height(300))
    .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Model(model => model.Id(p => p.OpportunityEventId))
        .Read(read => read
            .Action("GetOpportunityEventsByOpportunityId", "Sales", new { @opportunityId = Model.LinkOpportunityId })
        )
        .Destroy(destroy => destroy.Action("DeleteOpportunityEvent", "Sales"))
    )
)


<script>
   function opportunityEventsGridToolbarTemplate(data) {
        let template = kendo.template($("#opportunityEventsGridToolbar").html())
        let result = template(data)
        return result
   }

   function totalEventActionCountTemplateGenerate(data) {
        let template = kendo.template($("#totalEventActionCountTemplate").html())
        let result = template(data)
        return result
   }

      function opportunityEventsGridTopicGenerate(data) {
        let template = kendo.template($("#opportunityEventsGridTopic").html())
        let result = template(data)
        return result
   }

</script>


<script type="text/x-kendo-template" id="opportunityEventsGridToolbar">
    
        <div class="d-flex justify-content-between w-100 toolbar-inline-padding">
            #if("@Model.Stage" != "@OpportunityStageConstant.Closed"){#
                <a class="btn btn-success d-flex align-items-center mr-2" href="@Url.Action("AddEvent", "Sales", new { @id = Model.ParentOpportunityId.HasValue ? Model.ParentOpportunityId : Model.OpportunityId, @revision = ViewBag.Revision })">
                    <i class="fa fa-plus mr-2"></i>
                    Add New Event
                </a>
            #} else {#
                <div></div>
            #}# 
            <button class="btn btn-danger" id="resetOpportunityEventsGrid">
                Reset Grid View
            </button>
        </div>

</script>

<script type="text/x-kendo-template" id="opportunityEventsGridTopic">
    #if(IsTopicLength){#
        <a href='\\#' onclick="showTopic()">
            #=TrimmedTopic#</a>
    #} else {#
        #=Topic#
    #}#
</script>

<script type="text/x-kendo-template" id="totalEventActionCountTemplate">
    #if(TotalEventActionCount && IsActionOverdue){#
            <span class='badge bg-danger' onclick='followUpActionCount(#=OpportunityEventId#)'>
                #=TotalEventActionCount#
            </span>
        #} else if(TotalEventActionCount && IsActionUpcoming){#
            <span class='badge' onclick='followUpActionCount(#=OpportunityEventId#)'>
                #=TotalEventActionCount#
            </span>
        #} else if(TotalEventActionCount && IsActionRegistered){#
            <span class='badge' onclick='followUpActionCount(#=OpportunityEventId#)'>
                #=TotalEventActionCount#
            </span>
        #} else if(TotalEventActionCount && IsActionCompleted) {#
            <span class='badge' onclick='followUpActionCount(#=OpportunityEventId#)'>
                #=TotalEventActionCount#
            </span>
        #} else {#
        #=TotalEventActionCount#
    #}#
</script>