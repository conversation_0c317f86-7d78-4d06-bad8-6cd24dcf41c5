﻿namespace Centerpoint.Common.Constants
{
    public static class OpportunitySourceConstant
    {

        public const string Call = "CAL";
        public const string Email = "EML";
        public const string Event = "EVE";
        public const string Other = "OTH";
        public const string Referall = "REF";
        public const string Website = "WEB";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Call,"Phone Call"},
                    {Email,"E-mail"},
                    {Event,"Event"},
                    {Referall,"Referall"},
                    {Website,"Website"},
                    {Other,"Other"}
                };
            }
        }
    }
}
