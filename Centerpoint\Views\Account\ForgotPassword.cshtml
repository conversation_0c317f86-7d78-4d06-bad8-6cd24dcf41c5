﻿@{
    Layout = "LoginLayout";
}

<div class="login-wrapper">
    <div class="container-fluid h-100 w-100">
        <div class="row h-100 ">
            <div class="col-xl-8 d-none d-lg-none d-xl-block p-0">
                <img src="/img/login-bg.png" alt="centerpoint login image" class="img-cover">
            </div>
            <div class="col-xl-4 bg-white forget-password-container">
                <div class="login-form-wrapper">
                    <img src="/img/logo.svg" alt="centerpoint logo"> 
                    <div id="forgetPasswordForm" class="forget-password-form">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Forgot Password</h5>
                            </div>            
                            <div class="card-body">
                                <p class="card-text">
                                    If you have forgotten your password you can create a new one. To reset your password, enter the email address you use to sign in below.
                                </p>
                                <div class="form-group mb-3">
                                    <label for="email">Email Address <span class="text-danger">*</span></label>
                                    <input class="form-control" name='mail' id='email' type="email" required/>
                                </div>
                                <div class="form-group">
                                    <button class="btn btn-danger" type="button" onclick="handleResetClick()">Reset Password</button>
                                    <a href="/Account/Login" class="btn btn-primary">Return to Login</a>
                                </div>
                            </div>
                        </div>
                    </div> 
                    <span class="copy-r">© Centrepoint @DateTime.Now.Year</span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        $("#email").kendoTextBox();
    })

    function handleResetClick () {
        var isValid = $("#forgetPasswordForm").kendoValidator({
            messages:{
                    email: "Please enter a valid email address"
            }
        }).data("kendoValidator").validate();
        const data = $("#email").data("kendoTextBox").value();

        if(isValid) {
            $.ajax({
                type: 'POST',
                url: '/Account/ForgotPassword',
                dataType: "json",
                data: {
                    Email: data
                },
                success: function (e) {
                    window.location.href = "/Account/Login/?sentEmail=true";   
                },
                error: function (e) {
                },
            })
        }
    }

</script>