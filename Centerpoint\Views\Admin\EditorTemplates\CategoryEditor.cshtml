﻿@using Centerpoint.Model.ViewModels;
@model IEnumerable<CategoryModel>

@(
    Html.Ken<PERSON>().MultiSelectFor(m => m)
        .Placeholder("Select Categories")
        .Filter(FilterType.Contains)
        .DataValueField("Value")
        .DataTextField("Text")
        .AutoBind(true)
        .DataSource(source => {
                        source.Read(read => {
                            read.Action("GetCategories", "Lookup");
                            }).ServerFiltering(true);
                        })
        @*.BindTo(Centerpoint.Common.Constants.UserRoleConstant.GetRolesSelectListItems())*@
)
@*
@(Html.Kendo().MultiSelectFor(m => m.)
                                        .DataTextField("Name")
                                        .DataValueField("CategoryId")
                                        .Placeholder("Select Categories")
                                        .DataSource(d => d.Read("GetCategories", "Lookup")))*@