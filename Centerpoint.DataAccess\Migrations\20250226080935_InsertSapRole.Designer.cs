﻿// <auto-generated />
using System;
using Centerpoint.DataAccess.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Centerpoint.DataAccess.Migrations
{
    [DbContext(typeof(DataContext))]
    [Migration("20250226080935_InsertSapRole")]
    partial class InsertSapRole
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Centerpoint.Model.Entities.ActionType", b =>
                {
                    b.Property<int>("ActionTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ActionTypeId"));

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("ActionTypeId");

                    b.ToTable("ActionTypes");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaAnalystHour", b =>
                {
                    b.Property<int>("AnsaAnalystHourId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaAnalystHourId"));

                    b.Property<int>("AnsaAnalystUserId")
                        .HasColumnType("int");

                    b.Property<double?>("AprHours")
                        .HasColumnType("float");

                    b.Property<double?>("AugHours")
                        .HasColumnType("float");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime");

                    b.Property<double?>("DecHours")
                        .HasColumnType("float");

                    b.Property<double?>("FebHours")
                        .HasColumnType("float");

                    b.Property<double?>("JanHours")
                        .HasColumnType("float");

                    b.Property<double?>("JulHours")
                        .HasColumnType("float");

                    b.Property<double?>("JunHours")
                        .HasColumnType("float");

                    b.Property<double?>("MarHours")
                        .HasColumnType("float");

                    b.Property<double?>("MayHours")
                        .HasColumnType("float");

                    b.Property<DateTime?>("Modified")
                        .HasColumnType("datetime");

                    b.Property<double?>("NovHours")
                        .HasColumnType("float");

                    b.Property<double?>("OctHours")
                        .HasColumnType("float");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<double?>("SepHours")
                        .HasColumnType("float");

                    b.HasKey("AnsaAnalystHourId");

                    b.HasIndex("AnsaAnalystUserId");

                    b.ToTable("AnsaAnalystHour", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaDeliverableBlueprint", b =>
                {
                    b.Property<int>("AnsaDeliverableBlueprintId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaDeliverableBlueprintId"));

                    b.Property<string>("Complexity")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime");

                    b.Property<bool?>("IsFta")
                        .HasColumnType("bit");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<double?>("TotalExpectedManHours")
                        .HasColumnType("float");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("AnsaDeliverableBlueprintId");

                    b.HasIndex("UserId");

                    b.ToTable("AnsaDeliverableBlueprint", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaDeliverableBlueprintEquipmentCategory", b =>
                {
                    b.Property<int>("AnsaDeliverableBlueprintEquipmentCategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaDeliverableBlueprintEquipmentCategoryId"));

                    b.Property<int>("AnsaDeliverableBlueprintId")
                        .HasColumnType("int");

                    b.Property<int?>("EquipmentCategoryId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("AnsaDeliverableBlueprintEquipmentCategoryId");

                    b.HasIndex("AnsaDeliverableBlueprintId");

                    b.HasIndex("EquipmentCategoryId");

                    b.ToTable("AnsaDeliverableBlueprintEquipmentCategory", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaDeliverableBlueprintStep", b =>
                {
                    b.Property<int>("AnsaDeliverableBlueprintStepId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaDeliverableBlueprintStepId"));

                    b.Property<int>("AnsaDeliverableBlueprintId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Entries")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<decimal?>("ExpectedManHours")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("Number")
                        .HasColumnType("int");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("Tasks")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Type")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.HasKey("AnsaDeliverableBlueprintStepId");

                    b.HasIndex("AnsaDeliverableBlueprintId");

                    b.ToTable("AnsaDeliverableBlueprintStep", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProject", b =>
                {
                    b.Property<int>("AnsaProjectId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaProjectId"));

                    b.Property<DateTime?>("AutomaticFeedbackCreated")
                        .HasColumnType("datetime");

                    b.Property<string>("CommunicationFromAnsaTeamRating")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("CompanyId")
                        .HasColumnType("int");

                    b.Property<int>("CompanyLocationId")
                        .HasColumnType("int");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("DateArchived")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("DateClosed")
                        .HasColumnType("datetime");

                    b.Property<int?>("FeedbackCompanyContactId")
                        .HasColumnType("int");

                    b.Property<string>("FeedbackCreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("FeedbackCreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("FeedbackNotes")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("FeedbackRequestedDate")
                        .HasColumnType("datetime");

                    b.Property<bool>("HasFta")
                        .HasColumnType("bit");

                    b.Property<bool>("IsClientAccessible")
                        .HasColumnType("bit");

                    b.Property<bool>("IsContentAgreedWithCustomer")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDddWithRevisedDocument")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDdsJobStatusArchived")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeliveryByAgreedMethod")
                        .HasColumnType("bit");

                    b.Property<bool>("IsFilesDeletedFromLiveLocation")
                        .HasColumnType("bit");

                    b.Property<bool>("IsManualFeedback")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPdfNoMarkupIncluded")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRequestFeedback")
                        .HasColumnType("bit");

                    b.Property<bool>("IsVersionHistoryUpdated")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LatestDeliveryDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("ManualFeedbackCreated")
                        .HasColumnType("datetime");

                    b.Property<string>("ManualFeedbackCreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("NotProjectRelated")
                        .HasColumnType("bit");

                    b.Property<string>("Notes")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Number")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("OperatorCompanyId")
                        .HasColumnType("int");

                    b.Property<string>("ParticularlyCommendable")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PerformanceOfAnalystRating")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("ProjectId")
                        .HasColumnType("int");

                    b.Property<string>("QualityOfFinalProductAndContentRating")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("QualityOfInterpretationRating")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("Reference")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("(newid())");

                    b.Property<string>("RevisionByCustomer")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("RevisionDueDate")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("Status")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<string>("TurnaroundTimeRating")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("WhatCanWeDoBetter")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("AnsaProjectId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("CompanyLocationId");

                    b.HasIndex("FeedbackCompanyContactId");

                    b.HasIndex("OperatorCompanyId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("UserId");

                    b.ToTable("AnsaProject", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectComment", b =>
                {
                    b.Property<int>("AnsaProjectCommentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaProjectCommentId"));

                    b.Property<int>("AnsaProjectId")
                        .HasColumnType("int");

                    b.Property<string>("Comment")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("Date")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("AnsaProjectCommentId");

                    b.HasIndex("AnsaProjectId");

                    b.HasIndex("UserId");

                    b.ToTable("AnsaProjectComment", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectCompanyContact", b =>
                {
                    b.Property<int>("AnsaProjectCompanyContactId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaProjectCompanyContactId"));

                    b.Property<int>("AnsaProjectId")
                        .HasColumnType("int");

                    b.Property<int>("CompanyContactId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("AnsaProjectCompanyContactId");

                    b.HasIndex("AnsaProjectId");

                    b.HasIndex("CompanyContactId");

                    b.ToTable("AnsaProjectCompanyContact", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectDeliverable", b =>
                {
                    b.Property<int>("AnsaProjectDeliverableId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaProjectDeliverableId"));

                    b.Property<DateTime?>("AnalysisStart")
                        .HasColumnType("datetime");

                    b.Property<int?>("AnsaDeliverableBlueprintId")
                        .HasColumnType("int");

                    b.Property<int>("AnsaProjectId")
                        .HasColumnType("int");

                    b.Property<int?>("AssignedUserId")
                        .HasColumnType("int");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("DataReceived")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("DateDelivered")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("DateRevisioned")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("DraftReportDueDate")
                        .HasColumnType("datetime");

                    b.Property<bool>("HasFta")
                        .HasColumnType("bit");

                    b.Property<int?>("JobId")
                        .HasColumnType("int");

                    b.Property<DateTime>("Modfied")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("AnsaProjectDeliverableId");

                    b.HasIndex("AnsaDeliverableBlueprintId");

                    b.HasIndex("AnsaProjectId");

                    b.HasIndex("AssignedUserId");

                    b.HasIndex("JobId");

                    b.ToTable("AnsaProjectDeliverable", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectDeliverableStep", b =>
                {
                    b.Property<int>("AnsaProjectDeliverableStepId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaProjectDeliverableStepId"));

                    b.Property<int>("AnsaProjectDeliverableId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("Checked")
                        .HasColumnType("datetime");

                    b.Property<string>("CheckedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("Completed")
                        .HasColumnType("datetime");

                    b.Property<string>("CompletedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("DocumentId")
                        .HasColumnType("int");

                    b.Property<string>("Entries")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("EntriesCompleted")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<double?>("ExpectedManHours")
                        .HasColumnType("float");

                    b.Property<double?>("ManHoursTaken")
                        .HasColumnType("float");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Note")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("Number")
                        .HasColumnType("int");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("Result")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("Status")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<string>("Tasks")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("TasksCompleted")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Type")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("AnsaProjectDeliverableStepId");

                    b.HasIndex("AnsaProjectDeliverableId");

                    b.HasIndex("DocumentId");

                    b.HasIndex("UserId");

                    b.ToTable("AnsaProjectDeliverableStep", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectDeliverableUser", b =>
                {
                    b.Property<int>("AnsaProjectDeliverableUserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaProjectDeliverableUserId"));

                    b.Property<int>("AnsaProjectDeliverableId")
                        .HasColumnType("int");

                    b.Property<int>("CompanyContactId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("AnsaProjectDeliverableUserId");

                    b.HasIndex("AnsaProjectDeliverableId");

                    b.HasIndex("CompanyContactId");

                    b.ToTable("AnsaProjectDeliverableUser", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectDeliverableWell", b =>
                {
                    b.Property<int>("AnsaProjectDeliverableWellId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaProjectDeliverableWellId"));

                    b.Property<int>("AnsaProjectDeliverableId")
                        .HasColumnType("int");

                    b.Property<int>("CompanyWellId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("AnsaProjectDeliverableWellId");

                    b.HasIndex("AnsaProjectDeliverableId");

                    b.HasIndex("CompanyWellId");

                    b.ToTable("AnsaProjectDeliverableWell", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectDocument", b =>
                {
                    b.Property<int>("AnsaProjectDocumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaProjectDocumentId"));

                    b.Property<int>("AnsaProjectId")
                        .HasColumnType("int");

                    b.Property<int>("DocumentId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("AnsaProjectDocumentId");

                    b.HasIndex("AnsaProjectId");

                    b.HasIndex("DocumentId");

                    b.ToTable("AnsaProjectDocument", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectFeedback", b =>
                {
                    b.Property<int>("AnsaProjectFeedbackId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaProjectFeedbackId"));

                    b.Property<int>("AnsaProjectId")
                        .HasColumnType("int");

                    b.Property<string>("Comment")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("Date")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("AnsaProjectFeedbackId");

                    b.HasIndex("AnsaProjectId");

                    b.HasIndex("UserId");

                    b.ToTable("AnsaProjectFeedback", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectField", b =>
                {
                    b.Property<int>("AnsaProjectFieldId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaProjectFieldId"));

                    b.Property<int>("AnsaProjectId")
                        .HasColumnType("int");

                    b.Property<int?>("CompanyFieldId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("AnsaProjectFieldId");

                    b.HasIndex("AnsaProjectId");

                    b.HasIndex("CompanyFieldId");

                    b.ToTable("AnsaProjectField", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectLog", b =>
                {
                    b.Property<int>("AnsaProjectLogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaProjectLogId"));

                    b.Property<int?>("AnsaProjectDeliverableId")
                        .HasColumnType("int");

                    b.Property<int?>("AnsaProjectDeliverableStepId")
                        .HasColumnType("int");

                    b.Property<int>("AnsaProjectId")
                        .HasColumnType("int");

                    b.Property<int?>("CompanyContactId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("Date")
                        .HasColumnType("datetime");

                    b.Property<string>("Details")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Log")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.HasKey("AnsaProjectLogId");

                    b.HasIndex("AnsaProjectDeliverableId");

                    b.HasIndex("AnsaProjectDeliverableStepId");

                    b.HasIndex("AnsaProjectId");

                    b.HasIndex("CompanyContactId");

                    b.HasIndex("UserId");

                    b.ToTable("AnsaProjectLog", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectService", b =>
                {
                    b.Property<int>("AnsaProjectServiceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaProjectServiceId"));

                    b.Property<int>("AnsaProjectId")
                        .HasColumnType("int");

                    b.Property<int>("AnsaServiceId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("AnsaProjectServiceId");

                    b.HasIndex("AnsaProjectId");

                    b.HasIndex("AnsaServiceId");

                    b.ToTable("AnsaProjectService", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectWell", b =>
                {
                    b.Property<int>("AnsaProjectWellId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaProjectWellId"));

                    b.Property<int>("AnsaProjectId")
                        .HasColumnType("int");

                    b.Property<int?>("CompanyWellId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("AnsaProjectWellId");

                    b.HasIndex("AnsaProjectId");

                    b.HasIndex("CompanyWellId");

                    b.ToTable("AnsaProjectWell", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaService", b =>
                {
                    b.Property<int>("AnsaServiceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaServiceId"));

                    b.Property<int?>("CompanyId")
                        .HasColumnType("int");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("AnsaServiceId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("UserId");

                    b.ToTable("AnsaService", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaServiceDeliverableBlueprint", b =>
                {
                    b.Property<int>("AnsaServiceDeliverableBlueprintId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaServiceDeliverableBlueprintId"));

                    b.Property<int?>("AnsaDeliverableBlueprintId")
                        .HasColumnType("int");

                    b.Property<int>("AnsaServiceId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("AnsaServiceDeliverableBlueprintId");

                    b.HasIndex("AnsaDeliverableBlueprintId");

                    b.HasIndex("AnsaServiceId");

                    b.ToTable("AnsaServiceDeliverableBlueprint", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaServiceDocument", b =>
                {
                    b.Property<int>("AnsaServiceDocumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaServiceDocumentId"));

                    b.Property<int>("AnsaServiceId")
                        .HasColumnType("int");

                    b.Property<int>("DocumentId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("AnsaServiceDocumentId");

                    b.HasIndex("AnsaServiceId");

                    b.HasIndex("DocumentId");

                    b.ToTable("AnsaServiceDocument", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaUnit", b =>
                {
                    b.Property<int>("AnsaUnitId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnsaUnitId"));

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("Symbol")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("AnsaUnitId");

                    b.ToTable("AnsaUnit", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Category", b =>
                {
                    b.Property<int>("CategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CategoryId"));

                    b.Property<bool>("CustomerAssets")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("CategoryId");

                    b.ToTable("Category", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CertificateCategory", b =>
                {
                    b.Property<int>("CertificateCategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CertificateCategoryId"));

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("CertificateCategoryId");

                    b.ToTable("CertificateCategory", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Company", b =>
                {
                    b.Property<int>("CompanyId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CompanyId"));

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsFocused")
                        .HasColumnType("bit");

                    b.Property<bool>("IsInternal")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("CompanyId");

                    b.ToTable("Company", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CompanyCategory", b =>
                {
                    b.Property<int>("CompanyCategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CompanyCategoryId"));

                    b.Property<int?>("CategoryId")
                        .HasColumnType("int");

                    b.Property<int>("CompanyId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("CompanyCategoryId");

                    b.HasIndex("CategoryId");

                    b.HasIndex("CompanyId");

                    b.ToTable("CompanyCategory", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CompanyContact", b =>
                {
                    b.Property<int>("CompanyContactId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CompanyContactId"));

                    b.Property<string>("Comment")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("CompanyLocationId")
                        .HasColumnType("int");

                    b.Property<string>("EmailAddress")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("FirstName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("LastName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Mobile")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("NotActive")
                        .HasColumnType("bit");

                    b.Property<string>("Position")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("WorkTelephone")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("CompanyContactId");

                    b.HasIndex("CompanyLocationId");

                    b.ToTable("CompanyContact", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CompanyField", b =>
                {
                    b.Property<int>("CompanyFieldId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CompanyFieldId"));

                    b.Property<int>("CompanyId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("CompanyFieldId");

                    b.HasIndex("CompanyId");

                    b.ToTable("CompanyField", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CompanyLocation", b =>
                {
                    b.Property<int>("CompanyLocationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CompanyLocationId"));

                    b.Property<string>("City")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Comment")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("CompanyId")
                        .HasColumnType("int");

                    b.Property<string>("Country")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("HouseNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("IsLocationActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsOffshore")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Postcode")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("Street")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Telephone")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("CompanyLocationId");

                    b.HasIndex("CompanyId");

                    b.ToTable("CompanyLocation", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CompanyWell", b =>
                {
                    b.Property<int>("CompanyWellId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CompanyWellId"));

                    b.Property<decimal?>("Co2")
                        .HasColumnType("decimal(18, 0)")
                        .HasColumnName("CO2");

                    b.Property<int>("CompanyFieldId")
                        .HasColumnType("int");

                    b.Property<string>("Conveyance")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<decimal?>("H2s")
                        .HasColumnType("decimal(18, 0)")
                        .HasColumnName("H2S");

                    b.Property<decimal?>("MaximumDeviation")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<decimal?>("MaximumPressure")
                        .HasColumnType("decimal(9, 2)");

                    b.Property<string>("MaximumPressureUnits")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<decimal?>("MaximumTemperature")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<string>("MaximumTemperatureDegrees")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<decimal?>("MinimumId")
                        .HasColumnType("decimal(5, 3)");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("WellType")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.HasKey("CompanyWellId");

                    b.HasIndex("CompanyFieldId");

                    b.ToTable("CompanyWell", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CompanyWellDocument", b =>
                {
                    b.Property<int>("CompanyWellDocumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CompanyWellDocumentId"));

                    b.Property<string>("CompanyWellDocumentType")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<int>("CompanyWellId")
                        .HasColumnType("int");

                    b.Property<int>("DocumentId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("CompanyWellDocumentId");

                    b.HasIndex("CompanyWellId");

                    b.HasIndex("DocumentId");

                    b.ToTable("CompanyWellDocument", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CompanyWellFluid", b =>
                {
                    b.Property<int>("CompanyWellFluidId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CompanyWellFluidId"));

                    b.Property<int>("CompanyWellId")
                        .HasColumnType("int");

                    b.Property<int>("FluidTypeId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("CompanyWellFluidId");

                    b.HasIndex("CompanyWellId");

                    b.HasIndex("FluidTypeId");

                    b.ToTable("CompanyWellFluid", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Crew", b =>
                {
                    b.Property<int>("CrewId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CrewId"));

                    b.Property<DateTime>("CurrentDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("DateIn")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("DateOut")
                        .HasColumnType("datetime");

                    b.Property<int>("ProjectId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("CrewId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("UserId");

                    b.ToTable("Crew", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Currency", b =>
                {
                    b.Property<int>("CurrencyId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CurrencyId"));

                    b.Property<string>("Format")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<double>("Multiple")
                        .HasColumnType("float");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("CurrencyId");

                    b.ToTable("Currency", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CustomStatusCode", b =>
                {
                    b.Property<int>("CustomStatusCodeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CustomStatusCodeId"));

                    b.Property<string>("Code")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Country")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("CustomStatusCodeId");

                    b.ToTable("CustomStatusCode", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Division", b =>
                {
                    b.Property<int>("DivisionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DivisionId"));

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<bool>("SimplifiedMobilisation")
                        .HasColumnType("bit");

                    b.HasKey("DivisionId");

                    b.ToTable("Division", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Document", b =>
                {
                    b.Property<int>("DocumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DocumentId"));

                    b.Property<string>("Category")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime");

                    b.Property<string>("FileName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("MimeType")
                        .HasMaxLength(75)
                        .IsUnicode(false)
                        .HasColumnType("varchar(75)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.HasKey("DocumentId");

                    b.HasIndex(new[] { "UserId" }, "IX_Document_UserId");

                    b.ToTable("Document", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.DownloadFolder", b =>
                {
                    b.Property<int>("DownloadFolderId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DownloadFolderId"));

                    b.Property<string>("FtpPath")
                        .HasMaxLength(800)
                        .HasColumnType("nvarchar(800)");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("ParentDownloadFolderId")
                        .HasColumnType("int");

                    b.Property<string>("Path")
                        .HasMaxLength(800)
                        .HasColumnType("nvarchar(800)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("DownloadFolderId");

                    b.HasIndex("ParentDownloadFolderId");

                    b.ToTable("DownloadFolder", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Equipment.EquipmentAdditionalProperty", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("EquipmentItemId")
                        .HasColumnType("int");

                    b.Property<string>("PropertyName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PropertyValue")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentItemId");

                    b.ToTable("EquipmentAdditionalProperty");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentCategory", b =>
                {
                    b.Property<int>("EquipmentCategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EquipmentCategoryId"));

                    b.Property<int?>("CompanyId")
                        .HasColumnType("int");

                    b.Property<double>("DepreciationFactor")
                        .HasColumnType("float");

                    b.Property<double?>("Depth")
                        .HasColumnType("float");

                    b.Property<string>("DepthUnit")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<string>("Details")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("DisplayInOpportunity")
                        .HasColumnType("bit");

                    b.Property<int?>("DivisionId")
                        .HasColumnType("int");

                    b.Property<double?>("Height")
                        .HasColumnType("float");

                    b.Property<string>("HeightUnit")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<bool>("IsDangerousGoods")
                        .HasColumnType("bit");

                    b.Property<bool>("IsTopLevelOnly")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<double>("OuterDiameter")
                        .HasColumnType("float");

                    b.Property<string>("OuterDiameterUnit")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<int?>("ParentEquipmentCategoryId")
                        .HasColumnType("int");

                    b.Property<int?>("PointsPerMonth")
                        .HasColumnType("int");

                    b.Property<int?>("PointsPerMove")
                        .HasColumnType("int");

                    b.Property<int?>("PointsPerRun")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<double?>("Weight")
                        .HasColumnType("float");

                    b.Property<string>("WeightUnit")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<double?>("Width")
                        .HasColumnType("float");

                    b.Property<string>("WidthUnit")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.HasKey("EquipmentCategoryId");

                    b.HasIndex(new[] { "CompanyId" }, "IX_EquipmentCategory_Company");

                    b.HasIndex(new[] { "DivisionId" }, "IX_EquipmentCategory_Division");

                    b.HasIndex(new[] { "ParentEquipmentCategoryId" }, "IX_EquipmentCategory_ParentEquipmentCategory");

                    b.ToTable("EquipmentCategory", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentCategoryMaintenanceStep", b =>
                {
                    b.Property<int>("EquipmentCategoryMaintenanceStepId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EquipmentCategoryMaintenanceStepId"));

                    b.Property<DateTime?>("Created")
                        .HasColumnType("datetime");

                    b.Property<int>("EquipmentCategoryId")
                        .HasColumnType("int");

                    b.Property<int>("MaintenanceBlueprintId")
                        .HasColumnType("int");

                    b.Property<int?>("Points")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<bool>("RunEnabled")
                        .HasColumnType("bit");

                    b.HasKey("EquipmentCategoryMaintenanceStepId");

                    b.HasIndex("EquipmentCategoryId");

                    b.HasIndex("MaintenanceBlueprintId");

                    b.ToTable("EquipmentCategoryMaintenanceStep", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentItem", b =>
                {
                    b.Property<int>("EquipmentItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EquipmentItemId"));

                    b.Property<DateTime?>("AcceptanceCreated")
                        .HasColumnType("datetime");

                    b.Property<string>("AcceptanceUser")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Comments")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("CommodityCode")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("CompanyId")
                        .HasColumnType("int");

                    b.Property<string>("CountryOfOrigin")
                        .HasMaxLength(59)
                        .HasColumnType("nvarchar(59)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime");

                    b.Property<int?>("CurrencyId")
                        .HasColumnType("int");

                    b.Property<int?>("CurrentCompanyLocationId")
                        .HasColumnType("int");

                    b.Property<int>("DepreciationYears")
                        .HasColumnType("int");

                    b.Property<double>("Depth")
                        .HasColumnType("float");

                    b.Property<string>("DepthUnit")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<int?>("DivisionId")
                        .HasColumnType("int");

                    b.Property<int?>("EquipmentCategoryId")
                        .HasColumnType("int");

                    b.Property<string>("EquipmentInfo")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("EquipmentNumber")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("EquipmentPackingListProjectId")
                        .HasColumnType("int");

                    b.Property<string>("ExternalInvoiceNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("H2sCo20to5")
                        .HasColumnType("int");

                    b.Property<int>("H2sCo210to15")
                        .HasColumnType("int");

                    b.Property<int>("H2sCo215Greater")
                        .HasColumnType("int");

                    b.Property<int>("H2sCo25to10")
                        .HasColumnType("int");

                    b.Property<bool>("HasDivison")
                        .HasColumnType("bit");

                    b.Property<double>("Height")
                        .HasColumnType("float");

                    b.Property<string>("HeightUnit")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<string>("InternalInvoiceNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("IsQuarantined")
                        .HasColumnType("bit");

                    b.Property<bool>("IsReserved")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("IsTransit")
                        .HasColumnType("bit");

                    b.Property<int?>("ManufacturerCompanyId")
                        .HasColumnType("int");

                    b.Property<int?>("ManufacturerCompanyLocationId")
                        .HasColumnType("int");

                    b.Property<double>("OuterDiameter")
                        .HasColumnType("float");

                    b.Property<string>("OuterDiameterUnit")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<int>("Points")
                        .HasColumnType("int");

                    b.Property<int>("PointsPerMonth")
                        .HasColumnType("int");

                    b.Property<int>("PointsPerMove")
                        .HasColumnType("int");

                    b.Property<int>("PointsPerRun")
                        .HasColumnType("int");

                    b.Property<DateTime?>("PointsUpdated")
                        .HasColumnType("datetime");

                    b.Property<decimal>("Price")
                        .HasColumnType("money");

                    b.Property<int?>("ProjectId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("PurchasedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("ReceivedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("RetestDate")
                        .HasColumnType("datetime2");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("SerialNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Status")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<bool?>("TrackedNonAssetItem")
                        .HasColumnType("bit");

                    b.Property<double>("Weight")
                        .HasColumnType("float");

                    b.Property<string>("WeightUnit")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<double>("Width")
                        .HasColumnType("float");

                    b.Property<string>("WidthUnit")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.HasKey("EquipmentItemId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("EquipmentPackingListProjectId");

                    b.HasIndex(new[] { "CurrencyId" }, "IX_EquipmentItem_Currency");

                    b.HasIndex(new[] { "CurrentCompanyLocationId" }, "IX_EquipmentItem_CurrentCompanyLocation");

                    b.HasIndex(new[] { "DivisionId" }, "IX_EquipmentItem_Division");

                    b.HasIndex(new[] { "EquipmentCategoryId" }, "IX_EquipmentItem_EquipmentCategory");

                    b.HasIndex(new[] { "EquipmentCategoryId" }, "IX_EquipmentItem_EquipmentCategoryId");

                    b.HasIndex(new[] { "ManufacturerCompanyId" }, "IX_EquipmentItem_ManufacturerCompany");

                    b.HasIndex(new[] { "ManufacturerCompanyLocationId" }, "IX_EquipmentItem_ManufacturerCompanyLocation");

                    b.HasIndex(new[] { "ProjectId" }, "IX_EquipmentItem_Project");

                    b.HasIndex(new[] { "Status" }, "IX_EquipmentItem_Status");

                    b.ToTable("EquipmentItem", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentItemBundle", b =>
                {
                    b.Property<int>("EquipmentItemBundleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EquipmentItemBundleId"));

                    b.Property<int?>("ChildEquipmentItemId")
                        .HasColumnType("int");

                    b.Property<int?>("ParentEquipmentItemId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("EquipmentItemBundleId");

                    b.HasIndex(new[] { "ChildEquipmentItemId" }, "IX_EquipmentItemBundle_ChildEquipmentItemId");

                    b.HasIndex(new[] { "ParentEquipmentItemId" }, "IX_EquipmentItemBundle_ParentEquipmentItemId");

                    b.ToTable("EquipmentItemBundle", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentItemCertificate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Category")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<int>("EquipmentItemId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FileName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("FilePath")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentItemId");

                    b.ToTable("EquipmentItemCertificate", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentItemCustomStatusCode", b =>
                {
                    b.Property<int>("EquipmentItemCustomStatusCodeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EquipmentItemCustomStatusCodeId"));

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime");

                    b.Property<int>("CustomStatusCodeId")
                        .HasColumnType("int");

                    b.Property<int>("EquipmentItemId")
                        .HasColumnType("int");

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Note")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("EquipmentItemCustomStatusCodeId");

                    b.HasIndex("CustomStatusCodeId");

                    b.HasIndex(new[] { "EquipmentItemId" }, "IX_EquipmentItemCustomStatusCode_EquipmentItemId");

                    b.ToTable("EquipmentItemCustomStatusCode", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentItemDocument", b =>
                {
                    b.Property<int>("EquipmentItemDocumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EquipmentItemDocumentId"));

                    b.Property<int>("DocumentId")
                        .HasColumnType("int");

                    b.Property<int>("EquipmentItemId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("EquipmentItemDocumentId");

                    b.HasIndex("DocumentId");

                    b.HasIndex("EquipmentItemId");

                    b.ToTable("EquipmentItemDocument", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentItemLog", b =>
                {
                    b.Property<int>("EquipmentItemLogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EquipmentItemLogId"));

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<int>("EquipmentItemId")
                        .HasColumnType("int");

                    b.Property<string>("Log")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Points")
                        .HasColumnType("int");

                    b.Property<string>("Reference")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("ReferenceId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("Type")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.HasKey("EquipmentItemLogId");

                    b.HasIndex("EquipmentItemId");

                    b.HasIndex("UserId");

                    b.ToTable("EquipmentItemLog", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentItemMaintenanceSchedule", b =>
                {
                    b.Property<int>("EquipmentItemMaintenanceScheduleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EquipmentItemMaintenanceScheduleId"));

                    b.Property<int?>("CompanyLocationId")
                        .HasColumnType("int");

                    b.Property<int>("EquipmentItemId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("LastDate")
                        .HasColumnType("datetime");

                    b.Property<int>("MaintenanceBlueprintId")
                        .HasColumnType("int");

                    b.Property<int>("RecurringDays")
                        .HasColumnType("int");

                    b.Property<int>("RecurringMonths")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime");

                    b.HasKey("EquipmentItemMaintenanceScheduleId");

                    b.HasIndex("CompanyLocationId");

                    b.HasIndex("MaintenanceBlueprintId");

                    b.HasIndex(new[] { "EquipmentItemId" }, "IX_EquipmentItemMaintenanceSchedule_EquipmentItemId");

                    b.ToTable("EquipmentItemMaintenanceSchedule", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentItemNote", b =>
                {
                    b.Property<int>("EquipmentItemNoteId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EquipmentItemNoteId"));

                    b.Property<string>("Comment")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime");

                    b.Property<int>("EquipmentItemId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("EquipmentItemNoteId");

                    b.HasIndex("EquipmentItemId");

                    b.HasIndex("UserId");

                    b.ToTable("EquipmentItemNote", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentItemShipmentView", b =>
                {
                    b.Property<int>("EquipmentItemId")
                        .HasColumnType("int");

                    b.Property<int>("EquipmentShipmentId")
                        .HasColumnType("int");

                    b.Property<bool>("IsReserved")
                        .HasColumnType("bit");

                    b.ToTable((string)null);

                    b.ToView("EquipmentItemShipmentView", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentItemsShippedWithActiveMr", b =>
                {
                    b.Property<int?>("ActiveMrs")
                        .HasColumnType("int")
                        .HasColumnName("ActiveMRs");

                    b.Property<string>("Category")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Equipment")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Project")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("ReceivedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("ShippedDate")
                        .HasColumnType("datetime");

                    b.ToTable((string)null);

                    b.ToView("Equipment Items Shipped with Active MRs", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentPackingList", b =>
                {
                    b.Property<int>("EquipmentPackingListId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EquipmentPackingListId"));

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime");

                    b.Property<int?>("CreatedByUserId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DateRequired")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("EquipmentShipmentId")
                        .HasColumnType("int");

                    b.Property<string>("Number")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PackingListTitle")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("ProjectId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("SignOffDetails")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("SignedOffBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("SignedOffDate")
                        .HasColumnType("datetime");

                    b.HasKey("EquipmentPackingListId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("EquipmentShipmentId");

                    b.HasIndex("ProjectId");

                    b.ToTable("EquipmentPackingList", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentPackingListItem", b =>
                {
                    b.Property<int>("EquipmentPackingListItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EquipmentPackingListItemId"));

                    b.Property<int>("EquipmentItemId")
                        .HasColumnType("int");

                    b.Property<int>("EquipmentPackingListId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("EquipmentPackingListItemId");

                    b.HasIndex("EquipmentItemId");

                    b.HasIndex("EquipmentPackingListId");

                    b.ToTable("EquipmentPackingListItem", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentShipment", b =>
                {
                    b.Property<int>("EquipmentShipmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EquipmentShipmentId"));

                    b.Property<int?>("CreatedByUserId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("CurrencyId")
                        .HasColumnType("int");

                    b.Property<string>("CustomStatus")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("DeliveryAddress")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Description")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("EquipmentShipmentStatus")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<string>("FromAddress")
                        .HasMaxLength(800)
                        .HasColumnType("nvarchar(800)");

                    b.Property<int?>("FromCompanyFieldId")
                        .HasColumnType("int");

                    b.Property<int>("FromCompanyId")
                        .HasColumnType("int");

                    b.Property<int>("FromCompanyLocationId")
                        .HasColumnType("int");

                    b.Property<int?>("FromProjectId")
                        .HasColumnType("int");

                    b.Property<bool>("IsProjectRelated")
                        .HasColumnType("bit");

                    b.Property<string>("Number")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PackingNotes")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("ProjectId")
                        .HasColumnType("int");

                    b.Property<string>("ReceivedByUserName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("ReceivedDate")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("SentByUserName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("SentDate")
                        .HasColumnType("datetime");

                    b.Property<int>("ShipmentMethodId")
                        .HasColumnType("int");

                    b.Property<string>("ShipmentNotes")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("ToAddress")
                        .HasMaxLength(800)
                        .HasColumnType("nvarchar(800)");

                    b.Property<int?>("ToCompanyFieldId")
                        .HasColumnType("int");

                    b.Property<int>("ToCompanyId")
                        .HasColumnType("int");

                    b.Property<int>("ToCompanyLocationId")
                        .HasColumnType("int");

                    b.HasKey("EquipmentShipmentId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("CurrencyId");

                    b.HasIndex("FromCompanyFieldId");

                    b.HasIndex("FromCompanyId");

                    b.HasIndex("FromCompanyLocationId");

                    b.HasIndex("FromProjectId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("ShipmentMethodId");

                    b.HasIndex("ToCompanyFieldId");

                    b.HasIndex("ToCompanyId");

                    b.HasIndex("ToCompanyLocationId");

                    b.ToTable("EquipmentShipment", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentShipmentDocument", b =>
                {
                    b.Property<int>("EquipmentShipmentDocumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EquipmentShipmentDocumentId"));

                    b.Property<int>("DocumentId")
                        .HasColumnType("int");

                    b.Property<int>("EquipmentShipmentId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("EquipmentShipmentDocumentId");

                    b.HasIndex("DocumentId");

                    b.HasIndex("EquipmentShipmentId");

                    b.ToTable("EquipmentShipmentDocument", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentShipmentInvoice", b =>
                {
                    b.Property<int>("EquipmentShipmentInvoiceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EquipmentShipmentInvoiceId"));

                    b.Property<string>("CommodityCodeOption")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("CompanyLocationId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("Created")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("CurrencyId")
                        .HasColumnType("int");

                    b.Property<int?>("DocumentId")
                        .HasColumnType("int");

                    b.Property<int>("EquipmentShipmentId")
                        .HasColumnType("int");

                    b.Property<string>("FileName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("IsNetWeight")
                        .HasColumnType("bit");

                    b.Property<bool>("IsNewUsed")
                        .HasColumnType("bit");

                    b.Property<bool>("IsTemproraryPermanent")
                        .HasColumnType("bit");

                    b.Property<string>("PaperworkType")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("ValueType")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.HasKey("EquipmentShipmentInvoiceId");

                    b.HasIndex("CompanyLocationId");

                    b.HasIndex("CurrencyId");

                    b.HasIndex("DocumentId");

                    b.HasIndex("EquipmentShipmentId");

                    b.ToTable("EquipmentShipmentInvoice", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentShipmentItem", b =>
                {
                    b.Property<int>("EquipmentShipmentItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EquipmentShipmentItemId"));

                    b.Property<int?>("EquipmentItemCustomStatusCodeId")
                        .HasColumnType("int");

                    b.Property<int>("EquipmentItemId")
                        .HasColumnType("int");

                    b.Property<int?>("EquipmentPackingListItemId")
                        .HasColumnType("int");

                    b.Property<int>("EquipmentShipmentId")
                        .HasColumnType("int");

                    b.Property<string>("EquipmentShipmentItemDescription")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("IsNew")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPackingList")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPermanent")
                        .HasColumnType("bit");

                    b.Property<bool>("IsReceived")
                        .HasColumnType("bit");

                    b.Property<bool>("IsReserved")
                        .HasColumnType("bit");

                    b.Property<double?>("Quantity")
                        .HasColumnType("float");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("EquipmentShipmentItemId");

                    b.HasIndex("EquipmentItemCustomStatusCodeId");

                    b.HasIndex("EquipmentItemId");

                    b.HasIndex("EquipmentPackingListItemId");

                    b.HasIndex("EquipmentShipmentId");

                    b.ToTable("EquipmentShipmentItem", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentShipmentNonAssetItem", b =>
                {
                    b.Property<int>("EquipmentShipmentNonAssetItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EquipmentShipmentNonAssetItemId"));

                    b.Property<string>("Commodity")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("CountryOfOrigin")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("CurrencyId")
                        .HasColumnType("int");

                    b.Property<int?>("CustomStatusCodeId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<int>("EquipmentShipmentId")
                        .HasColumnType("int");

                    b.Property<bool>("IsReceived")
                        .HasColumnType("bit");

                    b.Property<double?>("NetWeight")
                        .HasColumnType("float");

                    b.Property<int?>("ProjectNonAssetItemId")
                        .HasColumnType("int");

                    b.Property<double?>("Quantity")
                        .HasColumnType("float");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("money");

                    b.HasKey("EquipmentShipmentNonAssetItemId");

                    b.HasIndex("CurrencyId");

                    b.HasIndex("CustomStatusCodeId");

                    b.HasIndex("EquipmentShipmentId");

                    b.ToTable("EquipmentShipmentNonAssetItem", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentShipmentPackage", b =>
                {
                    b.Property<int>("EquipmentShipmentPackageId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EquipmentShipmentPackageId"));

                    b.Property<int>("EquipmentShipmentId")
                        .HasColumnType("int");

                    b.Property<double?>("Height")
                        .HasColumnType("float");

                    b.Property<double?>("Length")
                        .HasColumnType("float");

                    b.Property<string>("PackageNumber")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("TypeOfPackage")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<double?>("Weight")
                        .HasColumnType("float");

                    b.Property<double?>("Width")
                        .HasColumnType("float");

                    b.HasKey("EquipmentShipmentPackageId");

                    b.HasIndex("EquipmentShipmentId");

                    b.ToTable("EquipmentShipmentPackages");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentShipmentRunView", b =>
                {
                    b.Property<int>("Bottom")
                        .HasColumnType("int");

                    b.Property<string>("Category")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Company")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Country")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<string>("Field")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("IsStandBy")
                        .HasColumnType("bit");

                    b.Property<string>("Location")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("SerialNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ShipmentNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("Top")
                        .HasColumnType("int");

                    b.Property<string>("Well")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.ToTable((string)null);

                    b.ToView("EquipmentShipmentRunView", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentShpimentItemProject", b =>
                {
                    b.Property<string>("Category")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Number")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Project")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Shipment")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.ToTable((string)null);

                    b.ToView("EquipmentShpimentItemProject", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EventType", b =>
                {
                    b.Property<int>("EventTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EventTypeId"));

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("EventTypeId");

                    b.ToTable("EventType", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.FluidType", b =>
                {
                    b.Property<int>("FluidTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("FluidTypeId"));

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("FluidTypeId");

                    b.ToTable("FluidType", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Job", b =>
                {
                    b.Property<int>("JobId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("JobId"));

                    b.Property<decimal?>("Co2")
                        .HasColumnType("decimal(18, 0)")
                        .HasColumnName("CO2");

                    b.Property<int>("CompanyFieldId")
                        .HasColumnType("int");

                    b.Property<int>("CompanyWellId")
                        .HasColumnType("int");

                    b.Property<string>("Conveyance")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<decimal?>("H2s")
                        .HasColumnType("decimal(18, 0)")
                        .HasColumnName("H2S");

                    b.Property<string>("InvoiceNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("JobCreationDate")
                        .HasColumnType("datetime");

                    b.Property<decimal?>("MaximumDeviation")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<decimal?>("MaximumPressure")
                        .HasColumnType("decimal(9, 2)");

                    b.Property<string>("MaximumPressureUnits")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<decimal?>("MaximumTemperature")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<string>("MaximumTemperatureDegrees")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<decimal?>("MinimumId")
                        .HasColumnType("decimal(5, 3)");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("ProjectId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("WellType")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.HasKey("JobId");

                    b.HasIndex("CompanyFieldId");

                    b.HasIndex("CompanyWellId");

                    b.HasIndex("ProjectId");

                    b.ToTable("Job", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.JobComment", b =>
                {
                    b.Property<int>("JobCommentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("JobCommentId"));

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("Date")
                        .HasColumnType("datetime");

                    b.Property<int>("JobId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("JobCommentId");

                    b.HasIndex("JobId");

                    b.HasIndex("UserId");

                    b.ToTable("JobComment", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.JobCrew", b =>
                {
                    b.Property<int>("JobCrewId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("JobCrewId"));

                    b.Property<int>("CrewId")
                        .HasColumnType("int");

                    b.Property<int>("JobId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("JobCrewId");

                    b.HasIndex("CrewId");

                    b.HasIndex("JobId");

                    b.ToTable("JobCrew", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.JobFluid", b =>
                {
                    b.Property<int>("JobFluidId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("JobFluidId"));

                    b.Property<int>("FluidTypeId")
                        .HasColumnType("int");

                    b.Property<int>("JobId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("JobFluidId");

                    b.HasIndex("FluidTypeId");

                    b.HasIndex("JobId");

                    b.ToTable("JobFluid", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.JobObjective", b =>
                {
                    b.Property<int>("JobObjectiveId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("JobObjectiveId"));

                    b.Property<int>("JobId")
                        .HasColumnType("int");

                    b.Property<int>("ObjectiveId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("JobObjectiveId");

                    b.HasIndex("JobId");

                    b.HasIndex("ObjectiveId");

                    b.ToTable("JobObjective", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Lesson", b =>
                {
                    b.Property<int>("LessonId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LessonId"));

                    b.Property<string>("ApprovedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("ApprovedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("CompanyId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("DivisionId")
                        .HasColumnType("int");

                    b.Property<int?>("JobId")
                        .HasColumnType("int");

                    b.Property<int>("LessonCategoryId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Number")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("OnJob")
                        .HasColumnType("bit");

                    b.Property<int?>("ProjectId")
                        .HasColumnType("int");

                    b.Property<int?>("RaisedByUserId")
                        .HasColumnType("int");

                    b.Property<string>("RejectReason")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("RejectedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("RejectedDate")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int?>("ServiceImprovementId")
                        .HasColumnType("int");

                    b.Property<string>("Status")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<string>("SubmittedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("SubmittedDate")
                        .HasColumnType("datetime");

                    b.HasKey("LessonId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("DivisionId");

                    b.HasIndex("JobId");

                    b.HasIndex("LessonCategoryId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("RaisedByUserId");

                    b.HasIndex("ServiceImprovementId");

                    b.ToTable("Lesson", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.LessonCategory", b =>
                {
                    b.Property<int>("LessonCategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LessonCategoryId"));

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("LessonCategoryId");

                    b.ToTable("LessonCategory", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.LessonDocument", b =>
                {
                    b.Property<int>("LessonDocumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LessonDocumentId"));

                    b.Property<int>("DocumentId")
                        .HasColumnType("int");

                    b.Property<int>("LessonId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("LessonDocumentId");

                    b.HasIndex("DocumentId");

                    b.HasIndex("LessonId");

                    b.ToTable("LessonDocument", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.LessonEquipmentCategory", b =>
                {
                    b.Property<int>("LessonEquipmentCategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LessonEquipmentCategoryId"));

                    b.Property<int?>("EquipmentCategoryId")
                        .HasColumnType("int");

                    b.Property<int>("LessonId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("LessonEquipmentCategoryId");

                    b.HasIndex("EquipmentCategoryId");

                    b.HasIndex("LessonId");

                    b.ToTable("LessonEquipmentCategory", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.LessonObjective", b =>
                {
                    b.Property<int>("LessonObjectiveId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LessonObjectiveId"));

                    b.Property<int>("LessonId")
                        .HasColumnType("int");

                    b.Property<int?>("ObjectiveId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("LessonObjectiveId");

                    b.HasIndex("LessonId");

                    b.HasIndex("ObjectiveId");

                    b.ToTable("LessonObjective", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceBlueprint", b =>
                {
                    b.Property<int>("MaintenanceBlueprintId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaintenanceBlueprintId"));

                    b.Property<bool>("ApprovalRequired")
                        .HasColumnType("bit");

                    b.Property<string>("Complexity")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime");

                    b.Property<int?>("DivisionId")
                        .HasColumnType("int");

                    b.Property<double>("ExpectedTimeToComplete")
                        .HasColumnType("float");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("ResetMaintenancePoints")
                        .HasColumnType("bit");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("MaintenanceBlueprintId");

                    b.HasIndex("DivisionId");

                    b.HasIndex("UserId");

                    b.ToTable("MaintenanceBlueprint", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceBlueprintDocument", b =>
                {
                    b.Property<int>("MaintenanceBlueprintDocumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaintenanceBlueprintDocumentId"));

                    b.Property<int>("DocumentId")
                        .HasColumnType("int");

                    b.Property<int>("MaintenanceBlueprintId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("MaintenanceBlueprintDocumentId");

                    b.HasIndex("DocumentId");

                    b.HasIndex("MaintenanceBlueprintId");

                    b.ToTable("MaintenanceBlueprintDocument", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceBlueprintEquipmentCategory", b =>
                {
                    b.Property<int>("MaintenanceBlueprintEquipmentCategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaintenanceBlueprintEquipmentCategoryId"));

                    b.Property<int?>("EquipmentCategoryId")
                        .HasColumnType("int");

                    b.Property<int>("MaintenanceBlueprintId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("MaintenanceBlueprintEquipmentCategoryId");

                    b.HasIndex("EquipmentCategoryId");

                    b.HasIndex("MaintenanceBlueprintId");

                    b.ToTable("MaintenanceBlueprintEquipmentCategory", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceBlueprintLog", b =>
                {
                    b.Property<int>("MaintenanceBlueprintLogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaintenanceBlueprintLogId"));

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<string>("Log")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MaintenanceBlueprintId")
                        .HasColumnType("int");

                    b.Property<string>("Reference")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("ReferenceId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("Type")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.HasKey("MaintenanceBlueprintLogId");

                    b.HasIndex("MaintenanceBlueprintId");

                    b.HasIndex("UserId");

                    b.ToTable("MaintenanceBlueprintLog", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceBlueprintStep", b =>
                {
                    b.Property<int>("MaintenanceBlueprintStepId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaintenanceBlueprintStepId"));

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsRepair")
                        .HasColumnType("bit");

                    b.Property<int>("MaintenanceBlueprintId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("Number")
                        .HasColumnType("int");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("Tasks")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Type")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.HasKey("MaintenanceBlueprintStepId");

                    b.HasIndex("MaintenanceBlueprintId");

                    b.ToTable("MaintenanceBlueprintStep", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceRecord", b =>
                {
                    b.Property<int>("MaintenanceRecordId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaintenanceRecordId"));

                    b.Property<bool>("Approved")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("Closed")
                        .HasColumnType("datetime");

                    b.Property<string>("ClosedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("ClosedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Comment")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Complexity")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime");

                    b.Property<int?>("EngineerUserId")
                        .HasColumnType("int");

                    b.Property<int>("EquipmentItemId")
                        .HasColumnType("int");

                    b.Property<int>("MaintenanceBlueprintId")
                        .HasColumnType("int");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime");

                    b.Property<string>("Number")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("OnHold")
                        .HasColumnType("datetime");

                    b.Property<double>("OnHoldHours")
                        .HasColumnType("float");

                    b.Property<string>("Priority")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<string>("RejectReason")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Result")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int?>("RunId")
                        .HasColumnType("int");

                    b.Property<int?>("ServiceImprovementId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Status")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.HasKey("MaintenanceRecordId");

                    b.HasIndex("EngineerUserId");

                    b.HasIndex("MaintenanceBlueprintId");

                    b.HasIndex("RunId");

                    b.HasIndex("ServiceImprovementId");

                    b.HasIndex("UserId");

                    b.HasIndex(new[] { "EquipmentItemId" }, "IX_MaintenanceRecord_EquipmentItemId");

                    b.HasIndex(new[] { "Status" }, "IX_MaintenanceRecord_Status");

                    b.ToTable("MaintenanceRecord", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceRecordDocument", b =>
                {
                    b.Property<int>("MaintenanceRecordDocumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaintenanceRecordDocumentId"));

                    b.Property<int>("DocumentId")
                        .HasColumnType("int");

                    b.Property<int>("MaintenanceRecordId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("MaintenanceRecordDocumentId");

                    b.HasIndex("DocumentId");

                    b.HasIndex("MaintenanceRecordId");

                    b.ToTable("MaintenanceRecordDocument", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceRecordLog", b =>
                {
                    b.Property<int>("MaintenanceRecordLogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaintenanceRecordLogId"));

                    b.Property<DateTime?>("Date")
                        .HasColumnType("datetime");

                    b.Property<string>("Details")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Log")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MaintenanceRecordId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("MaintenanceRecordLogId");

                    b.HasIndex("MaintenanceRecordId");

                    b.HasIndex("UserId");

                    b.ToTable("MaintenanceRecordLog", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceRecordStep", b =>
                {
                    b.Property<int>("MaintenanceRecordStepId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaintenanceRecordStepId"));

                    b.Property<int?>("CompletedByUserId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ExpectedString")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("IsRepair")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRepairFailed")
                        .HasColumnType("bit");

                    b.Property<int>("MaintenanceRecordId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Note")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("Result")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("ResultString")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("Status")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<string>("Tasks")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TasksCompleted")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Type")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.HasKey("MaintenanceRecordStepId");

                    b.HasIndex("CompletedByUserId");

                    b.HasIndex(new[] { "MaintenanceRecordId" }, "IX_MaintenanceRecordStep_MaintenanceRecordId");

                    b.ToTable("MaintenanceRecordStep", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceRecordStepDocument", b =>
                {
                    b.Property<int>("MaintenanceRecordStepDocumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaintenanceRecordStepDocumentId"));

                    b.Property<int>("DocumentId")
                        .HasColumnType("int");

                    b.Property<int>("MaintenanceRecordStepId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .HasColumnType("varbinary(max)");

                    b.HasKey("MaintenanceRecordStepDocumentId");

                    b.HasIndex("DocumentId");

                    b.HasIndex("MaintenanceRecordStepId");

                    b.ToTable("MaintenanceRecordStepDocuments");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Manufacturer", b =>
                {
                    b.Property<int>("ManufacturerId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ManufacturerId"));

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("ManufacturerId");

                    b.ToTable("Manufacturer", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.NumberSequence", b =>
                {
                    b.Property<int>("NumberSequenceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("NumberSequenceId"));

                    b.Property<string>("Name")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<int>("Number")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("NumberSequenceId");

                    b.HasIndex(new[] { "Name" }, "IX_NumberSequence_Name")
                        .IsUnique()
                        .HasFilter("[Name] IS NOT NULL");

                    b.ToTable("NumberSequence", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Objective", b =>
                {
                    b.Property<int>("ObjectiveId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ObjectiveId"));

                    b.Property<string>("Alias")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("CompanyId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("ObjectiveId");

                    b.HasIndex("CompanyId");

                    b.ToTable("Objective", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Opportunity", b =>
                {
                    b.Property<int>("OpportunityId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpportunityId"));

                    b.Property<int?>("BaseCompanyLocationId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("Closed")
                        .HasColumnType("datetime");

                    b.Property<string>("ClosedReasonComment")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("CloseoutDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Comments")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("CompanyCountry")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("CompanyId")
                        .HasColumnType("int");

                    b.Property<string>("Conveyance")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("CreatedByUserId")
                        .HasColumnType("int");

                    b.Property<int?>("CurrencyId")
                        .HasColumnType("int");

                    b.Property<string>("Customer")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<int?>("CustomerCompanyId")
                        .HasColumnType("int");

                    b.Property<string>("CustomerCountry")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("DivisionId")
                        .HasColumnType("int");

                    b.Property<string>("Duration")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("EngineersRequired")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool?>("FtaRequired")
                        .HasColumnType("bit");

                    b.Property<bool>("IsLeadClosed")
                        .HasColumnType("bit");

                    b.Property<int?>("LastRevision")
                        .HasColumnType("int");

                    b.Property<string>("LeadStage")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<DateTime?>("MobilisationDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool?>("NewPersonnelVisaRequired")
                        .HasColumnType("bit");

                    b.Property<int?>("OpportunityClosedReasonId")
                        .HasColumnType("int");

                    b.Property<string>("OpportunityObjectives")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("ParentOpportunityId")
                        .HasColumnType("int");

                    b.Property<int?>("PartnerCompanyId")
                        .HasColumnType("int");

                    b.Property<string>("PartnerCountry")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<decimal?>("PoundProbability")
                        .HasColumnType("money");

                    b.Property<decimal?>("PoundValue")
                        .HasColumnType("money");

                    b.Property<decimal>("Probability")
                        .HasColumnType("money");

                    b.Property<double?>("ProbabilityOverride")
                        .HasColumnType("float");

                    b.Property<int?>("ProjectId")
                        .HasColumnType("int");

                    b.Property<string>("QhseConsideration")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("Revision")
                        .HasColumnType("int");

                    b.Property<string>("RigType")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("Source")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<string>("Stage")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<bool>("SystemIntegrationTestingRequired")
                        .HasColumnType("bit");

                    b.Property<string>("TimePeriod")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<string>("Type")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<decimal?>("Value")
                        .HasColumnType("money");

                    b.Property<string>("ValueProposition")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("OpportunityId");

                    b.HasIndex("BaseCompanyLocationId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("CurrencyId");

                    b.HasIndex("CustomerCompanyId");

                    b.HasIndex("DivisionId");

                    b.HasIndex("OpportunityClosedReasonId");

                    b.HasIndex("ParentOpportunityId");

                    b.HasIndex("PartnerCompanyId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("Revision")
                        .HasDatabaseName("IX_Opportunity_Revision");

                    b.HasIndex("Stage")
                        .HasDatabaseName("IX_Opportunity_Stage");

                    b.HasIndex("Type")
                        .HasDatabaseName("IX_Opportunity_Type");

                    b.ToTable("Opportunity", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityAction", b =>
                {
                    b.Property<int>("OpportunityActionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpportunityActionId"));

                    b.Property<int>("ActionTypeId")
                        .HasColumnType("int");

                    b.Property<int>("AssignedUserId")
                        .HasColumnType("int");

                    b.Property<int?>("CompanyId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("CompletedOpportunityEventId")
                        .HasColumnType("int");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Objective")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("OpportunityEventId")
                        .HasColumnType("int");

                    b.Property<int?>("OpportunityId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<DateTime>("TargetDate")
                        .HasColumnType("datetime");

                    b.HasKey("OpportunityActionId");

                    b.HasIndex("ActionTypeId");

                    b.HasIndex("AssignedUserId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("CompletedOpportunityEventId");

                    b.HasIndex("OpportunityEventId");

                    b.HasIndex("OpportunityId");

                    b.ToTable("OpportunityAction", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityActionDocument", b =>
                {
                    b.Property<int>("OpportunityActionDocumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpportunityActionDocumentId"));

                    b.Property<int>("DocumentId")
                        .HasColumnType("int");

                    b.Property<int>("OpportunityActionId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("OpportunityActionDocumentId");

                    b.HasIndex("DocumentId");

                    b.HasIndex("OpportunityActionId");

                    b.ToTable("OpportunityActionDocument", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityClosedReason", b =>
                {
                    b.Property<int>("OpportunityClosedReasonId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpportunityClosedReasonId"));

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("OpportunityClosedReasonId");

                    b.ToTable("OpportunityClosedReason", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityCompanyContact", b =>
                {
                    b.Property<int>("OpportunityCompanyContactId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpportunityCompanyContactId"));

                    b.Property<int>("CompanyContactId")
                        .HasColumnType("int");

                    b.Property<int>("OpportunityId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("Type")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.HasKey("OpportunityCompanyContactId");

                    b.HasIndex("CompanyContactId");

                    b.HasIndex("OpportunityId");

                    b.ToTable("OpportunityCompanyContact", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityConveyance", b =>
                {
                    b.Property<int>("OpportunityConveyanceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpportunityConveyanceId"));

                    b.Property<string>("Conveyance")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<int>("OpportunityId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("OpportunityConveyanceId");

                    b.HasIndex("OpportunityId");

                    b.ToTable("OpportunityConveyance", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityDocument", b =>
                {
                    b.Property<int>("OpportunityDocumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpportunityDocumentId"));

                    b.Property<int>("DocumentId")
                        .HasColumnType("int");

                    b.Property<int>("OpportunityId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("OpportunityDocumentId");

                    b.HasIndex("DocumentId");

                    b.HasIndex("OpportunityId");

                    b.ToTable("OpportunityDocument", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityEquipmentCategory", b =>
                {
                    b.Property<int>("OpportunityEquipmentCategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpportunityEquipmentCategoryId"));

                    b.Property<int?>("EquipmentCategoryId")
                        .HasColumnType("int");

                    b.Property<int>("OpportunityId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("OpportunityEquipmentCategoryId");

                    b.HasIndex("EquipmentCategoryId");

                    b.HasIndex("OpportunityId");

                    b.ToTable("OpportunityEquipmentCategory", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityEvent", b =>
                {
                    b.Property<int>("OpportunityEventId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpportunityEventId"));

                    b.Property<int?>("CompanyId")
                        .HasColumnType("int");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("EventDate")
                        .HasColumnType("datetime");

                    b.Property<int>("EventTypeId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("OpportunityId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("Topic")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("OpportunityEventId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("EventTypeId");

                    b.HasIndex("OpportunityId");

                    b.ToTable("OpportunityEvent", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityEventCompanyContact", b =>
                {
                    b.Property<int>("OpportunityEventCompanyContactId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpportunityEventCompanyContactId"));

                    b.Property<int>("CompanyContactId")
                        .HasColumnType("int");

                    b.Property<int>("OpportunityEventId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("OpportunityEventCompanyContactId");

                    b.HasIndex("CompanyContactId");

                    b.HasIndex("OpportunityEventId");

                    b.ToTable("OpportunityEventCompanyContact", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityEventDocument", b =>
                {
                    b.Property<int>("OpportunityEventDocumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpportunityEventDocumentId"));

                    b.Property<int>("DocumentId")
                        .HasColumnType("int");

                    b.Property<int>("OpportunityEventId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("OpportunityEventDocumentId");

                    b.HasIndex("DocumentId");

                    b.HasIndex("OpportunityEventId");

                    b.ToTable("OpportunityEventDocument", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityField", b =>
                {
                    b.Property<int>("OpportunityFieldId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpportunityFieldId"));

                    b.Property<int>("CompanyFieldId")
                        .HasColumnType("int");

                    b.Property<int>("OpportunityId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("OpportunityFieldId");

                    b.HasIndex("CompanyFieldId");

                    b.HasIndex("OpportunityId");

                    b.ToTable("OpportunityField", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityLog", b =>
                {
                    b.Property<int>("OpportunityLogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpportunityLogId"));

                    b.Property<DateTime?>("Date")
                        .HasColumnType("datetime");

                    b.Property<string>("Log")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("OpportunityId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("OpportunityLogId");

                    b.HasIndex("OpportunityId");

                    b.HasIndex("UserId");

                    b.ToTable("OpportunityLog", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityService", b =>
                {
                    b.Property<int>("OpportunityServiceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpportunityServiceId"));

                    b.Property<int>("ObjectiveId")
                        .HasColumnType("int");

                    b.Property<int>("OpportunityId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("OpportunityServiceId");

                    b.HasIndex("ObjectiveId");

                    b.HasIndex("OpportunityId");

                    b.ToTable("OpportunityService", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityWell", b =>
                {
                    b.Property<int>("OpportunityWellId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpportunityWellId"));

                    b.Property<int>("CompanyWellId")
                        .HasColumnType("int");

                    b.Property<int>("OpportunityId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("OpportunityWellId");

                    b.HasIndex("CompanyWellId");

                    b.HasIndex("OpportunityId");

                    b.ToTable("OpportunityWell", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.PersonnelCertificateDocument", b =>
                {
                    b.Property<int>("PersonnelCertificateDocumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PersonnelCertificateDocumentId"));

                    b.Property<DateTime?>("AquisitionDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("CertificateCategoryId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime");

                    b.Property<string>("Details")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("DocumentId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("WarningThreshold")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("PersonnelCertificateDocumentId");

                    b.HasIndex("CertificateCategoryId");

                    b.HasIndex("DocumentId");

                    b.HasIndex("UserId");

                    b.ToTable("PersonnelCertificateDocument", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Project", b =>
                {
                    b.Property<int>("ProjectId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProjectId"));

                    b.Property<int?>("BaseCompanyLocationId")
                        .HasColumnType("int");

                    b.Property<string>("ClientInductionTrainingRequirements")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Comments")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("CompanyContactId")
                        .HasColumnType("int");

                    b.Property<int>("CompanyId")
                        .HasColumnType("int");

                    b.Property<int>("CompanyLocationId")
                        .HasColumnType("int");

                    b.Property<int?>("CustomerCompanyId")
                        .HasColumnType("int");

                    b.Property<int?>("DivisionId")
                        .HasColumnType("int");

                    b.Property<bool?>("FtaRequired")
                        .HasColumnType("bit");

                    b.Property<bool>("IsClosedOut")
                        .HasColumnType("bit");

                    b.Property<bool>("IsOther")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("MobilisationDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool?>("NewPersonnelVisaRequired")
                        .HasColumnType("bit");

                    b.Property<string>("ObjectiveComments")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("OperatorCompanyId")
                        .HasColumnType("int");

                    b.Property<string>("OtherObjectives")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("PartnerCompanyId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ProjectCreationDate")
                        .HasColumnType("datetime");

                    b.Property<string>("ProjectTitle")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("QhseConsideration")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("RigType")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("SpecialRequirements")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Status")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<bool>("SystemIntegrationTestingRequired")
                        .HasColumnType("bit");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("ProjectId");

                    b.HasIndex("BaseCompanyLocationId");

                    b.HasIndex("CompanyContactId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("CompanyLocationId");

                    b.HasIndex("CustomerCompanyId");

                    b.HasIndex("DivisionId");

                    b.HasIndex("OperatorCompanyId");

                    b.HasIndex("PartnerCompanyId");

                    b.HasIndex("UserId");

                    b.ToTable("Project", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ProjectAdditionalProperty", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ProjectId")
                        .HasColumnType("int");

                    b.Property<string>("PropertyName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PropertyValue")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("ProjectId");

                    b.ToTable("ProjectAdditionalProperty");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ProjectComment", b =>
                {
                    b.Property<int>("ProjectCommentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProjectCommentId"));

                    b.Property<string>("Comment")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("Date")
                        .HasColumnType("datetime");

                    b.Property<int>("ProjectId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("ProjectCommentId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("UserId");

                    b.ToTable("ProjectComment", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ProjectConveyance", b =>
                {
                    b.Property<int>("ProjectConveyanceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProjectConveyanceId"));

                    b.Property<string>("Conveyance")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<int>("ProjectId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("ProjectConveyanceId");

                    b.HasIndex("ProjectId");

                    b.ToTable("ProjectConveyance", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ProjectDocument", b =>
                {
                    b.Property<int>("ProjectDocumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProjectDocumentId"));

                    b.Property<int>("DocumentId")
                        .HasColumnType("int");

                    b.Property<int>("ProjectId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("ProjectDocumentId");

                    b.HasIndex("DocumentId");

                    b.HasIndex("ProjectId");

                    b.ToTable("ProjectDocument", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ProjectEquipmentCategory", b =>
                {
                    b.Property<int>("ProjectEquipmentCategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProjectEquipmentCategoryId"));

                    b.Property<int?>("EquipmentCategoryId")
                        .HasColumnType("int");

                    b.Property<int>("ProjectId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("ProjectEquipmentCategoryId");

                    b.HasIndex("EquipmentCategoryId");

                    b.HasIndex("ProjectId");

                    b.ToTable("ProjectEquipmentCategory", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ProjectField", b =>
                {
                    b.Property<int>("ProjectFieldId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProjectFieldId"));

                    b.Property<int>("CompanyFieldId")
                        .HasColumnType("int");

                    b.Property<int>("ProjectId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("ProjectFieldId");

                    b.HasIndex("CompanyFieldId");

                    b.HasIndex("ProjectId");

                    b.ToTable("ProjectField", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ProjectLog", b =>
                {
                    b.Property<int>("ProjectLogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProjectLogId"));

                    b.Property<DateTime?>("Date")
                        .HasColumnType("datetime");

                    b.Property<int?>("JobId")
                        .HasColumnType("int");

                    b.Property<string>("Log")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ProjectId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int?>("RunId")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("ProjectLogId");

                    b.HasIndex("JobId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("RunId");

                    b.HasIndex("UserId");

                    b.ToTable("ProjectLog", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ProjectNonAssetItem", b =>
                {
                    b.Property<int>("ProjectNonAssetItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProjectNonAssetItemId"));

                    b.Property<int>("EquipmentShipmentNonAssetItemId")
                        .HasColumnType("int");

                    b.Property<bool>("IsShipped")
                        .HasColumnType("bit");

                    b.Property<int>("ProjectId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("ProjectNonAssetItemId");

                    b.HasIndex("EquipmentShipmentNonAssetItemId");

                    b.HasIndex("ProjectId");

                    b.ToTable("ProjectNonAssetItem", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ProjectObjective", b =>
                {
                    b.Property<int>("ProjectObjectiveId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProjectObjectiveId"));

                    b.Property<int>("ObjectiveId")
                        .HasColumnType("int");

                    b.Property<int>("ProjectId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("ProjectObjectiveId");

                    b.HasIndex("ObjectiveId");

                    b.HasIndex("ProjectId");

                    b.ToTable("ProjectObjective", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ProjectWell", b =>
                {
                    b.Property<int>("ProjectWellId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProjectWellId"));

                    b.Property<int>("CompanyWellId")
                        .HasColumnType("int");

                    b.Property<int>("ProjectId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("ProjectWellId");

                    b.HasIndex("CompanyWellId");

                    b.HasIndex("ProjectId");

                    b.ToTable("ProjectWell", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.RiskIdentificationSafetyControl", b =>
                {
                    b.Property<int>("RiskIdentificationSafetyControlId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RiskIdentificationSafetyControlId"));

                    b.Property<string>("ActionDetails")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Area")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("BehaviourCondition")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<string>("FollowUpDetails")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("HazardObservation")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("HseComment")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("HseDate")
                        .HasColumnType("datetime");

                    b.Property<string>("HseName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ItemStopped")
                        .HasMaxLength(2)
                        .IsUnicode(false)
                        .HasColumnType("char(2)")
                        .IsFixedLength();

                    b.Property<int?>("JobId")
                        .HasColumnType("int");

                    b.Property<string>("Location")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("NearMiss")
                        .HasColumnType("bit");

                    b.Property<bool>("NotRelated")
                        .HasColumnType("bit");

                    b.Property<string>("ObservationDetails")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("OnshoreOffshore")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PositiveNegative")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("ProjectId")
                        .HasColumnType("int");

                    b.Property<string>("RaisedByUser")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("RecordNumber")
                        .HasColumnType("int");

                    b.Property<string>("RiscNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("SafetyEnvironment")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Status")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("RiskIdentificationSafetyControlId");

                    b.HasIndex("JobId");

                    b.HasIndex("ProjectId");

                    b.ToTable("RiskIdentificationSafetyControl", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.RiskIdentificationSafetyControlTotal", b =>
                {
                    b.Property<int>("RiskIdentificationSafetyControlTotalId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RiskIdentificationSafetyControlTotalId"));

                    b.Property<int>("Month")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("Total")
                        .HasColumnType("int");

                    b.Property<int>("Year")
                        .HasColumnType("int");

                    b.HasKey("RiskIdentificationSafetyControlTotalId");

                    b.ToTable("RiskIdentificationSafetyControlTotal", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Run", b =>
                {
                    b.Property<int>("RunId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RunId"));

                    b.Property<int>("Bottom")
                        .HasColumnType("int");

                    b.Property<string>("BottomUnits")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("EngineerNotes")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("IsClosed")
                        .HasColumnType("bit");

                    b.Property<bool>("IsStandBy")
                        .HasColumnType("bit");

                    b.Property<int>("JobId")
                        .HasColumnType("int");

                    b.Property<int>("LostHrs")
                        .HasColumnType("int");

                    b.Property<int>("LostMin")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<DateTime?>("RunFinish")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("RunStart")
                        .HasColumnType("datetime");

                    b.Property<int>("Top")
                        .HasColumnType("int");

                    b.Property<string>("TopUnits")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.HasKey("RunId");

                    b.HasIndex("JobId");

                    b.ToTable("Run", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.RunEquipmentItem", b =>
                {
                    b.Property<int>("RunEquipmentItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RunEquipmentItemId"));

                    b.Property<int>("EquipmentItemId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("RunId")
                        .HasColumnType("int");

                    b.HasKey("RunEquipmentItemId");

                    b.HasIndex("EquipmentItemId");

                    b.HasIndex("RunId");

                    b.ToTable("RunEquipmentItem", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.RunObjective", b =>
                {
                    b.Property<int>("RunObjectiveId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RunObjectiveId"));

                    b.Property<int>("ObjectiveId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("RunId")
                        .HasColumnType("int");

                    b.HasKey("RunObjectiveId");

                    b.HasIndex("ObjectiveId");

                    b.HasIndex("RunId");

                    b.ToTable("RunObjective", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ServiceImprovement", b =>
                {
                    b.Property<int>("ServiceImprovementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ServiceImprovementId"));

                    b.Property<string>("AccidentIncident")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("AuditReportNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("BaseCompanyLocationId")
                        .HasColumnType("int");

                    b.Property<int?>("CompanyContactId")
                        .HasColumnType("int");

                    b.Property<int?>("CompanyId")
                        .HasColumnType("int");

                    b.Property<int?>("CompanyLocationId")
                        .HasColumnType("int");

                    b.Property<string>("CorrectiveActionAcceptedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("CorrectiveActionAcceptedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("CorrectiveActionRejectReason")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("CorrectiveActionRequestedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("CorrectiveActionRequestedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("CorrectiveActionTargetDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("CorrectiveActionUserId")
                        .HasColumnType("int");

                    b.Property<string>("CorrectiveActionsComments")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("CreatedByUserId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Engineer")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("EquipmentRelated")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<DateTime?>("FromDate")
                        .HasColumnType("datetime");

                    b.Property<string>("ImmediateCorrectiveAction")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("InvestigationAcceptedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("InvestigationAcceptedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("InvestigationActions")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("InvestigationRejectReason")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("InvestigationRequestedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("InvestigationRequestedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("InvestigationResults")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("InvestigationTargetDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("InvestigatorUserId")
                        .HasColumnType("int");

                    b.Property<bool>("IsRelated")
                        .HasColumnType("bit");

                    b.Property<int?>("JobId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("NewCorrectiveActionTargetDate")
                        .HasColumnType("datetime");

                    b.Property<string>("NewCorrectiveActionTargetDateComment")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NewCorrectiveActionTargetDateRejectComment")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("NewInvestigationTargetDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("NewPreventiveActionTargetDate")
                        .HasColumnType("datetime");

                    b.Property<string>("NewPreventiveActionTargetDateComment")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NewPreventiveActionTargetDateRejectComment")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NewTargetDateComment")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NewTargetDateRejectComment")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PreventiveActionAcceptedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("PreventiveActionAcceptedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("PreventiveActionRejectReason")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PreventiveActionRequestedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("PreventiveActionRequestedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("PreventiveActionTargetDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("PreventiveActionUserId")
                        .HasColumnType("int");

                    b.Property<string>("PreventiveActionsComments")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("ProjectId")
                        .HasColumnType("int");

                    b.Property<string>("RejectReason")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("RequestedCorrectiveActions")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("RequestedPreventiveActions")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int?>("ServiceImprovementLocationId")
                        .HasColumnType("int");

                    b.Property<string>("ServiceImprovementStatus")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<int?>("SeverityId")
                        .HasColumnType("int");

                    b.Property<int?>("SifAdminUserId")
                        .HasColumnType("int");

                    b.Property<string>("SifCategory")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<string>("SifTitle")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("SifValidatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("SifValidatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("SignOffBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("SignOffComment")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("SignOffDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("ToDate")
                        .HasColumnType("datetime");

                    b.HasKey("ServiceImprovementId");

                    b.HasIndex("BaseCompanyLocationId");

                    b.HasIndex("CompanyContactId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("CompanyLocationId");

                    b.HasIndex("CorrectiveActionUserId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("InvestigatorUserId");

                    b.HasIndex("JobId");

                    b.HasIndex("PreventiveActionUserId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("ServiceImprovementLocationId");

                    b.HasIndex("SeverityId");

                    b.HasIndex("SifAdminUserId");

                    b.ToTable("ServiceImprovement", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ServiceImprovementDocument", b =>
                {
                    b.Property<int>("ServiceImprovementDocumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ServiceImprovementDocumentId"));

                    b.Property<int>("DocumentId")
                        .HasColumnType("int");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("ServiceImprovementId")
                        .HasColumnType("int");

                    b.HasKey("ServiceImprovementDocumentId");

                    b.HasIndex("DocumentId");

                    b.HasIndex("ServiceImprovementId");

                    b.ToTable("ServiceImprovementDocument", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ServiceImprovementLocation", b =>
                {
                    b.Property<int>("ServiceImprovementLocationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ServiceImprovementLocationId"));

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("ServiceImprovementLocationId");

                    b.ToTable("ServiceImprovementLocation", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ServiceImprovementLog", b =>
                {
                    b.Property<int>("ServiceImprovementLogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ServiceImprovementLogId"));

                    b.Property<DateTime?>("Date")
                        .HasColumnType("datetime");

                    b.Property<string>("Details")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Log")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("ServiceImprovementId")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("ServiceImprovementLogId");

                    b.HasIndex("ServiceImprovementId");

                    b.HasIndex("UserId");

                    b.ToTable("ServiceImprovementLog", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ServiceImprovementSubCategory", b =>
                {
                    b.Property<int>("ServiceImprovementSubCategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ServiceImprovementSubCategoryId"));

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("ServiceImprovementId")
                        .HasColumnType("int");

                    b.Property<int>("SubCategoryId")
                        .HasColumnType("int");

                    b.HasKey("ServiceImprovementSubCategoryId");

                    b.HasIndex("ServiceImprovementId");

                    b.HasIndex("SubCategoryId");

                    b.ToTable("ServiceImprovementSubCategory", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Severity", b =>
                {
                    b.Property<int>("SeverityId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SeverityId"));

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("OrderNo")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("SeverityId");

                    b.ToTable("Severity", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ShipmentMethod", b =>
                {
                    b.Property<int>("ShipmentMethodId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ShipmentMethodId"));

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("ShipmentMethodId");

                    b.ToTable("ShipmentMethod", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.SubCategory", b =>
                {
                    b.Property<int>("SubCategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SubCategoryId"));

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("SubCategoryId");

                    b.ToTable("SubCategory", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.SystemSettings", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("DepreciationYears")
                        .HasColumnType("int");

                    b.Property<int?>("Event")
                        .HasColumnType("int");

                    b.Property<string>("LogoName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("LogoPath")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("MaxLoginAttempts")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("SystemSettings", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("Address")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Address1")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Address2")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("AllergiesAndReaction")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("BaseCompanyLocationId")
                        .HasColumnType("int");

                    b.Property<string>("BloodType")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("char(3)")
                        .IsFixedLength();

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("ContactName")
                        .HasMaxLength(300)
                        .IsUnicode(false)
                        .HasColumnType("varchar(300)");

                    b.Property<string>("ContactName1")
                        .HasMaxLength(300)
                        .IsUnicode(false)
                        .HasColumnType("varchar(300)");

                    b.Property<string>("ContactRelationship")
                        .HasMaxLength(300)
                        .IsUnicode(false)
                        .HasColumnType("varchar(300)");

                    b.Property<string>("ContactRelationship1")
                        .HasMaxLength(300)
                        .IsUnicode(false)
                        .HasColumnType("varchar(300)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("EmailAddress")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<bool>("EmailNotification")
                        .HasColumnType("bit");

                    b.Property<string>("HomeTelephone")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("HomeTelephone1")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("HomeTelephone2")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("bit");

                    b.Property<bool>("IsNonEmployeeAccount")
                        .HasColumnType("bit");

                    b.Property<string>("JobTitle")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("LastLogin")
                        .HasColumnType("datetime");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("LoginAttempts")
                        .HasColumnType("int");

                    b.Property<string>("MedicalConditions")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Medications")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("MobileNotification")
                        .HasColumnType("bit");

                    b.Property<string>("MobileTelephone")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("MobileTelephone1")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("MobileTelephone2")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("Name")
                        .HasMaxLength(300)
                        .IsUnicode(false)
                        .HasColumnType("varchar(300)");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("OrganDonor")
                        .HasColumnType("bit");

                    b.Property<string>("PasswordHash")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("SecurityStamp")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool?>("ShowAllProjectComments")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((1))");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("WorkTelephone")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("WorkTelephone1")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("WorkTelephone2")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.HasKey("Id");

                    b.HasIndex("BaseCompanyLocationId");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.HasIndex(new[] { "EmailAddress" }, "IX_User_EmailAddress")
                        .IsUnique()
                        .HasFilter("[EmailAddress] IS NOT NULL");

                    b.ToTable("User", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.UserLocation", b =>
                {
                    b.Property<int>("UserLocationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("UserLocationId"));

                    b.Property<string>("Action")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime");

                    b.Property<string>("From")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<double>("Latitude")
                        .HasColumnType("float");

                    b.Property<string>("Location")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<double>("Longitude")
                        .HasColumnType("float");

                    b.Property<string>("Message")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("UserLocationId");

                    b.HasIndex("UserId");

                    b.ToTable("UserLocation", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.UserNote", b =>
                {
                    b.Property<int>("UserNoteId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("UserNoteId"));

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<string>("Note")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("UserNoteId");

                    b.HasIndex("UserId");

                    b.ToTable("UserNote", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.UserQuery", b =>
                {
                    b.Property<int>("UserQueryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("UserQueryId"));

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("Public")
                        .HasColumnType("bit");

                    b.Property<Guid>("Query")
                        .HasColumnType("uniqueidentifier");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("UserQueryId");

                    b.HasIndex("UserId");

                    b.ToTable("UserQuery", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.UserRole", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("ClaimValue")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("ClaimValue")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("ProviderKey")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("ProviderDisplayName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Value")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaAnalystHour", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.User", "AnsaAnalystUser")
                        .WithMany("AnsaAnalystHours")
                        .HasForeignKey("AnsaAnalystUserId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaAnalystHour_AnsaAnalystUser");

                    b.Navigation("AnsaAnalystUser");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaDeliverableBlueprint", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("AnsaDeliverableBlueprints")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaDeliverableBlueprint_User");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaDeliverableBlueprintEquipmentCategory", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.AnsaDeliverableBlueprint", "AnsaDeliverableBlueprint")
                        .WithMany("AnsaDeliverableBlueprintEquipmentCategories")
                        .HasForeignKey("AnsaDeliverableBlueprintId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaDeliverableBlueprintEquipmentCategory_AnsaDeliverableBlueprint");

                    b.HasOne("Centerpoint.Model.Entities.EquipmentCategory", "EquipmentCategory")
                        .WithMany("AnsaDeliverableBlueprintEquipmentCategories")
                        .HasForeignKey("EquipmentCategoryId")
                        .HasConstraintName("FK_AnsaDeliverableBlueprintEquipmentCategory_EquipmentCategory");

                    b.Navigation("AnsaDeliverableBlueprint");

                    b.Navigation("EquipmentCategory");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaDeliverableBlueprintStep", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.AnsaDeliverableBlueprint", "AnsaDeliverableBlueprint")
                        .WithMany("AnsaDeliverableBlueprintSteps")
                        .HasForeignKey("AnsaDeliverableBlueprintId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaDeliverableBlueprintStep_AnsaDeliverableBlueprint");

                    b.Navigation("AnsaDeliverableBlueprint");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProject", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Company", "Company")
                        .WithMany("AnsaProjectCompanies")
                        .HasForeignKey("CompanyId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProject_Company");

                    b.HasOne("Centerpoint.Model.Entities.CompanyLocation", "CompanyLocation")
                        .WithMany("AnsaProjects")
                        .HasForeignKey("CompanyLocationId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProject_CompanyLocation");

                    b.HasOne("Centerpoint.Model.Entities.CompanyContact", "FeedbackCompanyContact")
                        .WithMany("AnsaProjects")
                        .HasForeignKey("FeedbackCompanyContactId")
                        .HasConstraintName("FK_AnsaProject_FeedbackCompanyContact");

                    b.HasOne("Centerpoint.Model.Entities.Company", "OperatorCompany")
                        .WithMany("AnsaProjectOperatorCompanies")
                        .HasForeignKey("OperatorCompanyId")
                        .HasConstraintName("FK_AnsaProject_OperatorCompany");

                    b.HasOne("Centerpoint.Model.Entities.Project", "Project")
                        .WithMany("AnsaProjects")
                        .HasForeignKey("ProjectId")
                        .HasConstraintName("FK_AnsaProject_Project");

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("AnsaProjects")
                        .HasForeignKey("UserId")
                        .HasConstraintName("FK_AnsaProject_User");

                    b.Navigation("Company");

                    b.Navigation("CompanyLocation");

                    b.Navigation("FeedbackCompanyContact");

                    b.Navigation("OperatorCompany");

                    b.Navigation("Project");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectComment", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.AnsaProject", "AnsaProject")
                        .WithMany("AnsaProjectComments")
                        .HasForeignKey("AnsaProjectId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProjectComment_AnsaProject");

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("AnsaProjectComments")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProjectComment_User");

                    b.Navigation("AnsaProject");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectCompanyContact", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.AnsaProject", "AnsaProject")
                        .WithMany("AnsaProjectCompanyContacts")
                        .HasForeignKey("AnsaProjectId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProjectCompanyContact_AnsaProject");

                    b.HasOne("Centerpoint.Model.Entities.CompanyContact", "CompanyContact")
                        .WithMany("AnsaProjectCompanyContacts")
                        .HasForeignKey("CompanyContactId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProjectCompanyContact_CompanyContact");

                    b.Navigation("AnsaProject");

                    b.Navigation("CompanyContact");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectDeliverable", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.AnsaDeliverableBlueprint", "AnsaDeliverableBlueprint")
                        .WithMany("AnsaProjectDeliverables")
                        .HasForeignKey("AnsaDeliverableBlueprintId")
                        .HasConstraintName("FK_AnsaProjectDeliverable_AnsaDeliverableBlueprint");

                    b.HasOne("Centerpoint.Model.Entities.AnsaProject", "AnsaProject")
                        .WithMany("AnsaProjectDeliverables")
                        .HasForeignKey("AnsaProjectId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProjectDeliverable_AnsaProject");

                    b.HasOne("Centerpoint.Model.Entities.User", "AssignedUser")
                        .WithMany("AnsaProjectDeliverables")
                        .HasForeignKey("AssignedUserId")
                        .HasConstraintName("FK_AnsaProjectDeliverable_AssignedUser");

                    b.HasOne("Centerpoint.Model.Entities.Job", "Job")
                        .WithMany("AnsaProjectDeliverables")
                        .HasForeignKey("JobId")
                        .HasConstraintName("FK_AnsaProjectDeliverable_Job");

                    b.Navigation("AnsaDeliverableBlueprint");

                    b.Navigation("AnsaProject");

                    b.Navigation("AssignedUser");

                    b.Navigation("Job");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectDeliverableStep", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.AnsaProjectDeliverable", "AnsaProjectDeliverable")
                        .WithMany("AnsaProjectDeliverableSteps")
                        .HasForeignKey("AnsaProjectDeliverableId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProjectDeliverableStep_AnsaProjectDeliverable");

                    b.HasOne("Centerpoint.Model.Entities.Document", "Document")
                        .WithMany("AnsaProjectDeliverableSteps")
                        .HasForeignKey("DocumentId")
                        .HasConstraintName("FK_AnsaProjectDeliverableStep_Document");

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("AnsaProjectDeliverableSteps")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProjectDeliverableStep_User");

                    b.Navigation("AnsaProjectDeliverable");

                    b.Navigation("Document");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectDeliverableUser", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.AnsaProjectDeliverable", "AnsaProjectDeliverable")
                        .WithMany("AnsaProjectDeliverableUsers")
                        .HasForeignKey("AnsaProjectDeliverableId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProjectDeliverableUser_AnsaProjectDeliverable");

                    b.HasOne("Centerpoint.Model.Entities.CompanyContact", "CompanyContact")
                        .WithMany("AnsaProjectDeliverableUsers")
                        .HasForeignKey("CompanyContactId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProjectDeliverableUser_CompanyContact");

                    b.Navigation("AnsaProjectDeliverable");

                    b.Navigation("CompanyContact");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectDeliverableWell", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.AnsaProjectDeliverable", "AnsaProjectDeliverable")
                        .WithMany("AnsaProjectDeliverableWells")
                        .HasForeignKey("AnsaProjectDeliverableId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProjectDeliverableWell_AnsaProjectDeliverable");

                    b.HasOne("Centerpoint.Model.Entities.CompanyWell", "CompanyWell")
                        .WithMany("AnsaProjectDeliverableWells")
                        .HasForeignKey("CompanyWellId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProjectDeliverableWell_CompanyWell");

                    b.Navigation("AnsaProjectDeliverable");

                    b.Navigation("CompanyWell");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectDocument", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.AnsaProject", "AnsaProject")
                        .WithMany("AnsaProjectDocuments")
                        .HasForeignKey("AnsaProjectId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProjectDocument_AnsaProject");

                    b.HasOne("Centerpoint.Model.Entities.Document", "Document")
                        .WithMany("AnsaProjectDocuments")
                        .HasForeignKey("DocumentId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProjectDocument_Document");

                    b.Navigation("AnsaProject");

                    b.Navigation("Document");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectFeedback", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.AnsaProject", "AnsaProject")
                        .WithMany("AnsaProjectFeedbacks")
                        .HasForeignKey("AnsaProjectId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProjectFeedback_AnsaProject");

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("AnsaProjectFeedbacks")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProjectFeedback_User");

                    b.Navigation("AnsaProject");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectField", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.AnsaProject", "AnsaProject")
                        .WithMany("AnsaProjectFields")
                        .HasForeignKey("AnsaProjectId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProjectField_AnsaProject");

                    b.HasOne("Centerpoint.Model.Entities.CompanyField", "CompanyField")
                        .WithMany("AnsaProjectFields")
                        .HasForeignKey("CompanyFieldId")
                        .HasConstraintName("FK_AnsaProjectField_CompanyField");

                    b.Navigation("AnsaProject");

                    b.Navigation("CompanyField");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectLog", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.AnsaProjectDeliverable", "AnsaProjectDeliverable")
                        .WithMany("AnsaProjectLogs")
                        .HasForeignKey("AnsaProjectDeliverableId")
                        .HasConstraintName("FK_AnsaProjectLog_AnsaProjectDeliverable");

                    b.HasOne("Centerpoint.Model.Entities.AnsaProjectDeliverableStep", "AnsaProjectDeliverableStep")
                        .WithMany("AnsaProjectLogs")
                        .HasForeignKey("AnsaProjectDeliverableStepId")
                        .HasConstraintName("FK_AnsaProjectLog_AnsaProjectDeliverableStep");

                    b.HasOne("Centerpoint.Model.Entities.AnsaProject", "AnsaProject")
                        .WithMany("AnsaProjectLogs")
                        .HasForeignKey("AnsaProjectId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProjectLog_AnsaProject");

                    b.HasOne("Centerpoint.Model.Entities.CompanyContact", "CompanyContact")
                        .WithMany("AnsaProjectLogs")
                        .HasForeignKey("CompanyContactId")
                        .HasConstraintName("FK_AnsaProjectLog_CompanyContact");

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("AnsaProjectLogs")
                        .HasForeignKey("UserId")
                        .HasConstraintName("FK_AnsaProjectLog_User");

                    b.Navigation("AnsaProject");

                    b.Navigation("AnsaProjectDeliverable");

                    b.Navigation("AnsaProjectDeliverableStep");

                    b.Navigation("CompanyContact");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectService", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.AnsaProject", "AnsaProject")
                        .WithMany("AnsaProjectServices")
                        .HasForeignKey("AnsaProjectId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProjectService_AnsaProject");

                    b.HasOne("Centerpoint.Model.Entities.AnsaService", "AnsaService")
                        .WithMany("AnsaProjectServices")
                        .HasForeignKey("AnsaServiceId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProjectService_AnsaService");

                    b.Navigation("AnsaProject");

                    b.Navigation("AnsaService");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectWell", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.AnsaProject", "AnsaProject")
                        .WithMany("AnsaProjectWells")
                        .HasForeignKey("AnsaProjectId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaProjectWell_AnsaProject");

                    b.HasOne("Centerpoint.Model.Entities.CompanyWell", "CompanyWell")
                        .WithMany("AnsaProjectWells")
                        .HasForeignKey("CompanyWellId")
                        .HasConstraintName("FK_AnsaProjectWell_CompanyWell");

                    b.Navigation("AnsaProject");

                    b.Navigation("CompanyWell");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaService", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Company", "Company")
                        .WithMany("AnsaServices")
                        .HasForeignKey("CompanyId")
                        .HasConstraintName("FK_AnsaService_Company");

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("AnsaServices")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaService_User");

                    b.Navigation("Company");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaServiceDeliverableBlueprint", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.AnsaDeliverableBlueprint", "AnsaDeliverableBlueprint")
                        .WithMany("AnsaServiceDeliverableBlueprints")
                        .HasForeignKey("AnsaDeliverableBlueprintId")
                        .HasConstraintName("FK_AnsaServiceDeliverableBlueprint_AnsaDeliverableBlueprint");

                    b.HasOne("Centerpoint.Model.Entities.AnsaService", "AnsaService")
                        .WithMany("AnsaServiceDeliverableBlueprints")
                        .HasForeignKey("AnsaServiceId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaServiceDeliverableBlueprint_AnsaService");

                    b.Navigation("AnsaDeliverableBlueprint");

                    b.Navigation("AnsaService");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaServiceDocument", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.AnsaService", "AnsaService")
                        .WithMany("AnsaServiceDocuments")
                        .HasForeignKey("AnsaServiceId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaServiceDocument_AnsaService");

                    b.HasOne("Centerpoint.Model.Entities.Document", "Document")
                        .WithMany("AnsaServiceDocuments")
                        .HasForeignKey("DocumentId")
                        .IsRequired()
                        .HasConstraintName("FK_AnsaServiceDocument_Document");

                    b.Navigation("AnsaService");

                    b.Navigation("Document");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CompanyCategory", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Category", "Category")
                        .WithMany("CompanyCategories")
                        .HasForeignKey("CategoryId")
                        .HasConstraintName("FK_CompanyCategory_Category");

                    b.HasOne("Centerpoint.Model.Entities.Company", "Company")
                        .WithMany("CompanyCategories")
                        .HasForeignKey("CompanyId")
                        .IsRequired()
                        .HasConstraintName("FK_CompanyCategory_Company");

                    b.Navigation("Category");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CompanyContact", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.CompanyLocation", "CompanyLocation")
                        .WithMany("CompanyContacts")
                        .HasForeignKey("CompanyLocationId")
                        .IsRequired()
                        .HasConstraintName("FK_CompanyContact_CompanyLocation");

                    b.Navigation("CompanyLocation");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CompanyField", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Company", "Company")
                        .WithMany("CompanyFields")
                        .HasForeignKey("CompanyId")
                        .IsRequired()
                        .HasConstraintName("FK_CompanyField_Company");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CompanyLocation", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Company", "Company")
                        .WithMany("CompanyLocations")
                        .HasForeignKey("CompanyId")
                        .IsRequired()
                        .HasConstraintName("FK_CompanyLocation_Company");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CompanyWell", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.CompanyField", "CompanyField")
                        .WithMany("CompanyWells")
                        .HasForeignKey("CompanyFieldId")
                        .IsRequired()
                        .HasConstraintName("FK_CompanyWell_CompanyField");

                    b.Navigation("CompanyField");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CompanyWellDocument", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.CompanyWell", "CompanyWell")
                        .WithMany("CompanyWellDocuments")
                        .HasForeignKey("CompanyWellId")
                        .IsRequired()
                        .HasConstraintName("FK_CompanyWellDocument_CompanyWell");

                    b.HasOne("Centerpoint.Model.Entities.Document", "Document")
                        .WithMany("CompanyWellDocuments")
                        .HasForeignKey("DocumentId")
                        .IsRequired()
                        .HasConstraintName("FK_CompanyWellDocument_Document");

                    b.Navigation("CompanyWell");

                    b.Navigation("Document");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CompanyWellFluid", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.CompanyWell", "CompanyWell")
                        .WithMany("CompanyWellFluids")
                        .HasForeignKey("CompanyWellId")
                        .IsRequired()
                        .HasConstraintName("FK_CompanyWellFluid_CompanyWell");

                    b.HasOne("Centerpoint.Model.Entities.FluidType", "FluidType")
                        .WithMany("CompanyWellFluids")
                        .HasForeignKey("FluidTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_CompanyWellFluid_FluidType");

                    b.Navigation("CompanyWell");

                    b.Navigation("FluidType");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Crew", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Project", "Project")
                        .WithMany("Crews")
                        .HasForeignKey("ProjectId")
                        .IsRequired()
                        .HasConstraintName("FK_Crew_Project");

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("Crews")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_Crew_User");

                    b.Navigation("Project");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Document", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("Documents")
                        .HasForeignKey("UserId")
                        .HasConstraintName("FK_Document_User");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.DownloadFolder", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.DownloadFolder", "ParentDownloadFolder")
                        .WithMany("InverseParentDownloadFolder")
                        .HasForeignKey("ParentDownloadFolderId")
                        .HasConstraintName("FK_DownloadFolder_ParentDownloadFolder");

                    b.Navigation("ParentDownloadFolder");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Equipment.EquipmentAdditionalProperty", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.EquipmentItem", "EquipmentItem")
                        .WithMany("AdditionalProperties")
                        .HasForeignKey("EquipmentItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EquipmentItem");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentCategory", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Company", "Company")
                        .WithMany("EquipmentCategories")
                        .HasForeignKey("CompanyId")
                        .HasConstraintName("FK_EquipmentCategory_Company");

                    b.HasOne("Centerpoint.Model.Entities.Division", "Division")
                        .WithMany("EquipmentCategories")
                        .HasForeignKey("DivisionId")
                        .HasConstraintName("FK_EquipmentCategory_Division");

                    b.HasOne("Centerpoint.Model.Entities.EquipmentCategory", "ParentEquipmentCategory")
                        .WithMany("InverseParentEquipmentCategory")
                        .HasForeignKey("ParentEquipmentCategoryId")
                        .HasConstraintName("FK_EquipmentCategory_ParentEquipmentCategory");

                    b.Navigation("Company");

                    b.Navigation("Division");

                    b.Navigation("ParentEquipmentCategory");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentCategoryMaintenanceStep", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.EquipmentCategory", "EquipmentCategory")
                        .WithMany("EquipmentCategoryMaintenanceSteps")
                        .HasForeignKey("EquipmentCategoryId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentCategoryMaintenanceStep_EquipmentCategory");

                    b.HasOne("Centerpoint.Model.Entities.MaintenanceBlueprint", "MaintenanceBlueprint")
                        .WithMany("EquipmentCategoryMaintenanceSteps")
                        .HasForeignKey("MaintenanceBlueprintId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentCategoryMaintenanceStep_MaintenanceBlueprint");

                    b.Navigation("EquipmentCategory");

                    b.Navigation("MaintenanceBlueprint");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentItem", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Company", null)
                        .WithMany("EquipmentItemCurrentCompanies")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Centerpoint.Model.Entities.Currency", "Currency")
                        .WithMany("EquipmentItems")
                        .HasForeignKey("CurrencyId")
                        .HasConstraintName("FK_EquipmentItem_Currency");

                    b.HasOne("Centerpoint.Model.Entities.CompanyLocation", "CurrentCompanyLocation")
                        .WithMany("EquipmentItemCurrentCompanyLocations")
                        .HasForeignKey("CurrentCompanyLocationId")
                        .HasConstraintName("FK_EquipmentItem_CurrentCompanyLocation");

                    b.HasOne("Centerpoint.Model.Entities.Division", "Division")
                        .WithMany("EquipmentItems")
                        .HasForeignKey("DivisionId")
                        .HasConstraintName("FK_EquipmentItem_Division");

                    b.HasOne("Centerpoint.Model.Entities.EquipmentCategory", "EquipmentCategory")
                        .WithMany("EquipmentItems")
                        .HasForeignKey("EquipmentCategoryId")
                        .HasConstraintName("FK_EquipmentItem_EquipmentCategory");

                    b.HasOne("Centerpoint.Model.Entities.Project", "EquipmentPackingListProject")
                        .WithMany("EquipmentItemEquipmentPackingListProjects")
                        .HasForeignKey("EquipmentPackingListProjectId")
                        .HasConstraintName("FK_EquipmentItem_EquipmentPackingListProject");

                    b.HasOne("Centerpoint.Model.Entities.Company", "ManufacturerCompany")
                        .WithMany("EquipmentItemManufacturerCompanies")
                        .HasForeignKey("ManufacturerCompanyId")
                        .HasConstraintName("FK_EquipmentItem_ManufacturerCompany");

                    b.HasOne("Centerpoint.Model.Entities.CompanyLocation", "ManufacturerCompanyLocation")
                        .WithMany("EquipmentItemManufacturerCompanyLocations")
                        .HasForeignKey("ManufacturerCompanyLocationId")
                        .HasConstraintName("FK_EquipmentItem_ManufacturerCompanyLocation");

                    b.HasOne("Centerpoint.Model.Entities.Project", "Project")
                        .WithMany("EquipmentItemProjects")
                        .HasForeignKey("ProjectId")
                        .HasConstraintName("FK_EquipmentItem_Project");

                    b.Navigation("Currency");

                    b.Navigation("CurrentCompanyLocation");

                    b.Navigation("Division");

                    b.Navigation("EquipmentCategory");

                    b.Navigation("EquipmentPackingListProject");

                    b.Navigation("ManufacturerCompany");

                    b.Navigation("ManufacturerCompanyLocation");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentItemBundle", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.EquipmentItem", "ChildEquipmentItem")
                        .WithMany("EquipmentItemBundleParentEquipmentItems")
                        .HasForeignKey("ChildEquipmentItemId")
                        .HasConstraintName("FK_EquipmentItemBundle_ChildEquipmentItem");

                    b.HasOne("Centerpoint.Model.Entities.EquipmentItem", "ParentEquipmentItem")
                        .WithMany("EquipmentItemBundleChildEquipmentItems")
                        .HasForeignKey("ParentEquipmentItemId")
                        .HasConstraintName("FK_EquipmentItemBundle_ParentEquipmentItem");

                    b.Navigation("ChildEquipmentItem");

                    b.Navigation("ParentEquipmentItem");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentItemCertificate", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.EquipmentItem", "EquipmentItem")
                        .WithMany("EquipmentItemCertificates")
                        .HasForeignKey("EquipmentItemId")
                        .IsRequired();

                    b.Navigation("EquipmentItem");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentItemCustomStatusCode", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.CustomStatusCode", "CustomStatusCode")
                        .WithMany("EquipmentItemCustomStatusCodes")
                        .HasForeignKey("CustomStatusCodeId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentItemCustomStatusCode_CustomStatusCode");

                    b.HasOne("Centerpoint.Model.Entities.EquipmentItem", "EquipmentItem")
                        .WithMany("EquipmentItemCustomStatusCodes")
                        .HasForeignKey("EquipmentItemId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentItemCustomStatusCode_EquipmentItem");

                    b.Navigation("CustomStatusCode");

                    b.Navigation("EquipmentItem");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentItemDocument", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Document", "Document")
                        .WithMany("EquipmentItemDocuments")
                        .HasForeignKey("DocumentId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentItemDocument_Document");

                    b.HasOne("Centerpoint.Model.Entities.EquipmentItem", "EquipmentItem")
                        .WithMany("EquipmentItemDocuments")
                        .HasForeignKey("EquipmentItemId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentItemDocument_EquipmentItem");

                    b.Navigation("Document");

                    b.Navigation("EquipmentItem");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentItemLog", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.EquipmentItem", "EquipmentItem")
                        .WithMany("EquipmentItemLogs")
                        .HasForeignKey("EquipmentItemId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentItemLog_EquipmentItem");

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("EquipmentItemLogs")
                        .HasForeignKey("UserId")
                        .HasConstraintName("FK_EquipmentItemLog_User");

                    b.Navigation("EquipmentItem");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentItemMaintenanceSchedule", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.CompanyLocation", "CompanyLocation")
                        .WithMany("EquipmentItemMaintenanceSchedules")
                        .HasForeignKey("CompanyLocationId")
                        .HasConstraintName("FK_EquipmentItemMaintenanceSchedule_CompanyLocation");

                    b.HasOne("Centerpoint.Model.Entities.EquipmentItem", "EquipmentItem")
                        .WithMany("EquipmentItemMaintenanceSchedules")
                        .HasForeignKey("EquipmentItemId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentItemMaintenanceSchedule_EquipmentItem");

                    b.HasOne("Centerpoint.Model.Entities.MaintenanceBlueprint", "MaintenanceBlueprint")
                        .WithMany("EquipmentItemMaintenanceSchedules")
                        .HasForeignKey("MaintenanceBlueprintId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentItemMaintenanceSchedule_MaintenanceBlueprint");

                    b.Navigation("CompanyLocation");

                    b.Navigation("EquipmentItem");

                    b.Navigation("MaintenanceBlueprint");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentItemNote", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.EquipmentItem", "EquipmentItem")
                        .WithMany("EquipmentItemNotes")
                        .HasForeignKey("EquipmentItemId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentItemNote_EquipmentItem");

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("EquipmentItemNotes")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentItemNote_User");

                    b.Navigation("EquipmentItem");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentPackingList", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.User", "CreatedByUser")
                        .WithMany("EquipmentPackingLists")
                        .HasForeignKey("CreatedByUserId")
                        .HasConstraintName("FK_EquipmentPackingList_CreatedByUser");

                    b.HasOne("Centerpoint.Model.Entities.EquipmentShipment", "EquipmentShipment")
                        .WithMany("EquipmentPackingLists")
                        .HasForeignKey("EquipmentShipmentId")
                        .HasConstraintName("FK_EquipmentPackingList_EquipmentShipment");

                    b.HasOne("Centerpoint.Model.Entities.Project", "Project")
                        .WithMany("EquipmentPackingLists")
                        .HasForeignKey("ProjectId")
                        .HasConstraintName("FK_EquipmentPackingList_Project");

                    b.Navigation("CreatedByUser");

                    b.Navigation("EquipmentShipment");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentPackingListItem", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.EquipmentItem", "EquipmentItem")
                        .WithMany("EquipmentPackingListItems")
                        .HasForeignKey("EquipmentItemId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentPackingListItem_EquipmentItem");

                    b.HasOne("Centerpoint.Model.Entities.EquipmentPackingList", "EquipmentPackingList")
                        .WithMany("EquipmentPackingListItems")
                        .HasForeignKey("EquipmentPackingListId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentPackingListItem_EquipmentPackingList");

                    b.Navigation("EquipmentItem");

                    b.Navigation("EquipmentPackingList");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentShipment", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.User", "CreatedByUser")
                        .WithMany("EquipmentShipments")
                        .HasForeignKey("CreatedByUserId")
                        .HasConstraintName("FK_EquipmentShipment_CreatedByUser");

                    b.HasOne("Centerpoint.Model.Entities.Currency", "Currency")
                        .WithMany("EquipmentShipments")
                        .HasForeignKey("CurrencyId")
                        .HasConstraintName("FK_EquipmentShipment_Currency");

                    b.HasOne("Centerpoint.Model.Entities.CompanyField", "FromCompanyField")
                        .WithMany("EquipmentShipmentFromCompanyFields")
                        .HasForeignKey("FromCompanyFieldId")
                        .HasConstraintName("FK_EquipmentShipment_FromCompanyField");

                    b.HasOne("Centerpoint.Model.Entities.Company", "FromCompany")
                        .WithMany("EquipmentShipmentFromCompanies")
                        .HasForeignKey("FromCompanyId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentShipment_FromCompany");

                    b.HasOne("Centerpoint.Model.Entities.CompanyLocation", "FromCompanyLocation")
                        .WithMany("EquipmentShipmentFromCompanyLocations")
                        .HasForeignKey("FromCompanyLocationId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentShipment_FromCompanyLocation");

                    b.HasOne("Centerpoint.Model.Entities.Project", "FromProject")
                        .WithMany("FromShipments")
                        .HasForeignKey("FromProjectId")
                        .HasConstraintName("FK_EquipmentShipment_FromProject");

                    b.HasOne("Centerpoint.Model.Entities.Project", "Project")
                        .WithMany("EquipmentShipments")
                        .HasForeignKey("ProjectId")
                        .HasConstraintName("FK_EquipmentShipment_Project");

                    b.HasOne("Centerpoint.Model.Entities.ShipmentMethod", "ShipmentMethod")
                        .WithMany("EquipmentShipments")
                        .HasForeignKey("ShipmentMethodId")
                        .IsRequired();

                    b.HasOne("Centerpoint.Model.Entities.CompanyField", "ToCompanyField")
                        .WithMany("EquipmentShipmentToCompanyFields")
                        .HasForeignKey("ToCompanyFieldId")
                        .HasConstraintName("FK_EquipmentShipment_ToCompanyField");

                    b.HasOne("Centerpoint.Model.Entities.Company", "ToCompany")
                        .WithMany("EquipmentShipmentToCompanies")
                        .HasForeignKey("ToCompanyId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentShipment_ToCompany");

                    b.HasOne("Centerpoint.Model.Entities.CompanyLocation", "ToCompanyLocation")
                        .WithMany("EquipmentShipmentToCompanyLocations")
                        .HasForeignKey("ToCompanyLocationId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentShipment_ToCompanyLocation");

                    b.Navigation("CreatedByUser");

                    b.Navigation("Currency");

                    b.Navigation("FromCompany");

                    b.Navigation("FromCompanyField");

                    b.Navigation("FromCompanyLocation");

                    b.Navigation("FromProject");

                    b.Navigation("Project");

                    b.Navigation("ShipmentMethod");

                    b.Navigation("ToCompany");

                    b.Navigation("ToCompanyField");

                    b.Navigation("ToCompanyLocation");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentShipmentDocument", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Document", "Document")
                        .WithMany("EquipmentShipmentDocuments")
                        .HasForeignKey("DocumentId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentShipmentDocument_Document");

                    b.HasOne("Centerpoint.Model.Entities.EquipmentShipment", "EquipmentShipment")
                        .WithMany("EquipmentShipmentDocuments")
                        .HasForeignKey("EquipmentShipmentId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentShipmentDocument_EquipmentShipment");

                    b.Navigation("Document");

                    b.Navigation("EquipmentShipment");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentShipmentInvoice", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.CompanyLocation", "CompanyLocation")
                        .WithMany("EquipmentShipmentInvoices")
                        .HasForeignKey("CompanyLocationId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentShipmentInvoice_CompanyLocation");

                    b.HasOne("Centerpoint.Model.Entities.Currency", "Currency")
                        .WithMany("EquipmentShipmentInvoices")
                        .HasForeignKey("CurrencyId")
                        .HasConstraintName("FK_EquipmentShipmentInvoice_Currency");

                    b.HasOne("Centerpoint.Model.Entities.Document", "Document")
                        .WithMany("EquipmentShipmentInvoices")
                        .HasForeignKey("DocumentId")
                        .HasConstraintName("FK_EquipmentShipmentInvoice_Document");

                    b.HasOne("Centerpoint.Model.Entities.EquipmentShipment", "EquipmentShipment")
                        .WithMany("EquipmentShipmentInvoices")
                        .HasForeignKey("EquipmentShipmentId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentShipmentInvoice_EquipmentShipment");

                    b.Navigation("CompanyLocation");

                    b.Navigation("Currency");

                    b.Navigation("Document");

                    b.Navigation("EquipmentShipment");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentShipmentItem", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.EquipmentItemCustomStatusCode", "EquipmentItemCustomStatusCode")
                        .WithMany("EquipmentShipmentItems")
                        .HasForeignKey("EquipmentItemCustomStatusCodeId")
                        .HasConstraintName("FK_EquipmentShipmentItem_EquipmentItemCustomStatusCode");

                    b.HasOne("Centerpoint.Model.Entities.EquipmentItem", "EquipmentItem")
                        .WithMany("EquipmentShipmentItems")
                        .HasForeignKey("EquipmentItemId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentShipmentItem_EquipmentItem");

                    b.HasOne("Centerpoint.Model.Entities.EquipmentPackingListItem", "EquipmentPackingListItem")
                        .WithMany("EquipmentShipmentItems")
                        .HasForeignKey("EquipmentPackingListItemId")
                        .HasConstraintName("FK_EquipmentShipmentItem_EquipmentPackingListItem");

                    b.HasOne("Centerpoint.Model.Entities.EquipmentShipment", "EquipmentShipment")
                        .WithMany("EquipmentShipmentItems")
                        .HasForeignKey("EquipmentShipmentId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentShipmentItem_EquipmentShipment");

                    b.Navigation("EquipmentItem");

                    b.Navigation("EquipmentItemCustomStatusCode");

                    b.Navigation("EquipmentPackingListItem");

                    b.Navigation("EquipmentShipment");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentShipmentNonAssetItem", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Currency", "Currency")
                        .WithMany("EquipmentShipmentNonAssetItems")
                        .HasForeignKey("CurrencyId")
                        .HasConstraintName("FK_EquipmentShipmentNonAssetItem_Currency");

                    b.HasOne("Centerpoint.Model.Entities.CustomStatusCode", "CustomStatusCode")
                        .WithMany("EquipmentShipmentNonAssetItems")
                        .HasForeignKey("CustomStatusCodeId")
                        .HasConstraintName("FK_EquipmentShipmentNonAssetItem_CustomStatusCode");

                    b.HasOne("Centerpoint.Model.Entities.EquipmentShipment", "EquipmentShipment")
                        .WithMany("EquipmentShipmentNonAssetItems")
                        .HasForeignKey("EquipmentShipmentId")
                        .IsRequired()
                        .HasConstraintName("FK_EquipmentShipmentNonAssetItem_EquipmentShipment");

                    b.Navigation("Currency");

                    b.Navigation("CustomStatusCode");

                    b.Navigation("EquipmentShipment");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentShipmentPackage", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.EquipmentShipment", "EquipmentShipment")
                        .WithMany("EquipmentShipmentPackages")
                        .HasForeignKey("EquipmentShipmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EquipmentShipment");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Job", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.CompanyField", "CompanyField")
                        .WithMany()
                        .HasForeignKey("CompanyFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Centerpoint.Model.Entities.CompanyWell", "CompanyWell")
                        .WithMany("Jobs")
                        .HasForeignKey("CompanyWellId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Centerpoint.Model.Entities.Project", "Project")
                        .WithMany("Jobs")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CompanyField");

                    b.Navigation("CompanyWell");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.JobComment", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Job", "Job")
                        .WithMany("JobComments")
                        .HasForeignKey("JobId")
                        .IsRequired()
                        .HasConstraintName("FK_JobComment_Job");

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("JobComments")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_JobComment_User");

                    b.Navigation("Job");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.JobCrew", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Crew", "Crew")
                        .WithMany("JobCrews")
                        .HasForeignKey("CrewId")
                        .IsRequired()
                        .HasConstraintName("FK_JobCrew_Crew");

                    b.HasOne("Centerpoint.Model.Entities.Job", "Job")
                        .WithMany("JobCrews")
                        .HasForeignKey("JobId")
                        .IsRequired()
                        .HasConstraintName("FK_JobCrew_Job");

                    b.Navigation("Crew");

                    b.Navigation("Job");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.JobFluid", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.FluidType", "FluidType")
                        .WithMany("JobFluids")
                        .HasForeignKey("FluidTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_JobFluid_FluidType");

                    b.HasOne("Centerpoint.Model.Entities.Job", "Job")
                        .WithMany("JobFluids")
                        .HasForeignKey("JobId")
                        .IsRequired()
                        .HasConstraintName("FK_JobFluid_Job");

                    b.Navigation("FluidType");

                    b.Navigation("Job");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.JobObjective", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Job", "Job")
                        .WithMany("JobObjectives")
                        .HasForeignKey("JobId")
                        .IsRequired()
                        .HasConstraintName("FK_JobObjective_Job");

                    b.HasOne("Centerpoint.Model.Entities.Objective", "Objective")
                        .WithMany("JobObjectives")
                        .HasForeignKey("ObjectiveId")
                        .IsRequired()
                        .HasConstraintName("FK_JobObjective_Objective");

                    b.Navigation("Job");

                    b.Navigation("Objective");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Lesson", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Company", "Company")
                        .WithMany("Lessons")
                        .HasForeignKey("CompanyId")
                        .HasConstraintName("FK_Lesson_Company");

                    b.HasOne("Centerpoint.Model.Entities.Division", "Division")
                        .WithMany("Lessons")
                        .HasForeignKey("DivisionId")
                        .HasConstraintName("FK_Lesson_Division");

                    b.HasOne("Centerpoint.Model.Entities.Job", "Job")
                        .WithMany("Lessons")
                        .HasForeignKey("JobId")
                        .HasConstraintName("FK_Lesson_Job");

                    b.HasOne("Centerpoint.Model.Entities.LessonCategory", "LessonCategory")
                        .WithMany("Lessons")
                        .HasForeignKey("LessonCategoryId")
                        .IsRequired()
                        .HasConstraintName("FK_Lesson_LessonCategory");

                    b.HasOne("Centerpoint.Model.Entities.Project", "Project")
                        .WithMany("Lessons")
                        .HasForeignKey("ProjectId")
                        .HasConstraintName("FK_Lesson_Project");

                    b.HasOne("Centerpoint.Model.Entities.User", "RaisedByUser")
                        .WithMany("Lessons")
                        .HasForeignKey("RaisedByUserId")
                        .HasConstraintName("FK_Lesson_RaisedByUser");

                    b.HasOne("Centerpoint.Model.Entities.ServiceImprovement", "ServiceImprovement")
                        .WithMany("Lessons")
                        .HasForeignKey("ServiceImprovementId")
                        .HasConstraintName("FK_Lesson_ServiceImprovement");

                    b.Navigation("Company");

                    b.Navigation("Division");

                    b.Navigation("Job");

                    b.Navigation("LessonCategory");

                    b.Navigation("Project");

                    b.Navigation("RaisedByUser");

                    b.Navigation("ServiceImprovement");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.LessonDocument", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Document", "Document")
                        .WithMany("LessonDocuments")
                        .HasForeignKey("DocumentId")
                        .IsRequired()
                        .HasConstraintName("FK_LessonDocument_Document");

                    b.HasOne("Centerpoint.Model.Entities.Lesson", "Lesson")
                        .WithMany("LessonDocuments")
                        .HasForeignKey("LessonId")
                        .IsRequired()
                        .HasConstraintName("FK_LessonDocument_Lesson");

                    b.Navigation("Document");

                    b.Navigation("Lesson");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.LessonEquipmentCategory", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.EquipmentCategory", "EquipmentCategory")
                        .WithMany("LessonEquipmentCategories")
                        .HasForeignKey("EquipmentCategoryId")
                        .HasConstraintName("FK_LessonEquipmentCategory_EquipmentCategory");

                    b.HasOne("Centerpoint.Model.Entities.Lesson", "Lesson")
                        .WithMany("LessonEquipmentCategories")
                        .HasForeignKey("LessonId")
                        .IsRequired()
                        .HasConstraintName("FK_LessonEquipmentCategory_Lesson");

                    b.Navigation("EquipmentCategory");

                    b.Navigation("Lesson");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.LessonObjective", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Lesson", "Lesson")
                        .WithMany("LessonObjectives")
                        .HasForeignKey("LessonId")
                        .IsRequired()
                        .HasConstraintName("FK_LessonObjective_Lesson");

                    b.HasOne("Centerpoint.Model.Entities.Objective", "Objective")
                        .WithMany("LessonObjectives")
                        .HasForeignKey("ObjectiveId")
                        .HasConstraintName("FK_LessonObjective_Objective");

                    b.Navigation("Lesson");

                    b.Navigation("Objective");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceBlueprint", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Division", "Division")
                        .WithMany("MaintenanceBlueprints")
                        .HasForeignKey("DivisionId")
                        .HasConstraintName("FK_MaintenanceBlueprint_Division");

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("MaintenanceBlueprints")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_MaintenanceBlueprint_User");

                    b.Navigation("Division");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceBlueprintDocument", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Document", "Document")
                        .WithMany("MaintenanceBlueprintDocuments")
                        .HasForeignKey("DocumentId")
                        .IsRequired()
                        .HasConstraintName("FK_MaintenanceBlueprintDocument_Document");

                    b.HasOne("Centerpoint.Model.Entities.MaintenanceBlueprint", "MaintenanceBlueprint")
                        .WithMany("MaintenanceBlueprintDocuments")
                        .HasForeignKey("MaintenanceBlueprintId")
                        .IsRequired()
                        .HasConstraintName("FK_MaintenanceBlueprintDocument_MaintenanceBlueprint");

                    b.Navigation("Document");

                    b.Navigation("MaintenanceBlueprint");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceBlueprintEquipmentCategory", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.EquipmentCategory", "EquipmentCategory")
                        .WithMany("MaintenanceBlueprintEquipmentCategories")
                        .HasForeignKey("EquipmentCategoryId")
                        .HasConstraintName("FK_MaintenanceBlueprintEquipmentCategory_EquipmentCategory");

                    b.HasOne("Centerpoint.Model.Entities.MaintenanceBlueprint", "MaintenanceBlueprint")
                        .WithMany("MaintenanceBlueprintEquipmentCategories")
                        .HasForeignKey("MaintenanceBlueprintId")
                        .IsRequired()
                        .HasConstraintName("FK_MaintenanceBlueprintEquipmentCategory_MaintenanceBlueprint");

                    b.Navigation("EquipmentCategory");

                    b.Navigation("MaintenanceBlueprint");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceBlueprintLog", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.MaintenanceBlueprint", "MaintenanceBlueprint")
                        .WithMany("MaintenanceBlueprintLogs")
                        .HasForeignKey("MaintenanceBlueprintId")
                        .IsRequired()
                        .HasConstraintName("FK_MaintenanceBlueprintLog_MaintenanceBlueprint");

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("MaintenanceBlueprintLogs")
                        .HasForeignKey("UserId")
                        .HasConstraintName("FK_MaintenanceBlueprintLog_User");

                    b.Navigation("MaintenanceBlueprint");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceBlueprintStep", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.MaintenanceBlueprint", "MaintenanceBlueprint")
                        .WithMany("MaintenanceBlueprintSteps")
                        .HasForeignKey("MaintenanceBlueprintId")
                        .IsRequired()
                        .HasConstraintName("FK_MaintenanceBlueprintStep_MaintenanceBlueprint");

                    b.Navigation("MaintenanceBlueprint");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceRecord", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.User", "EngineerUser")
                        .WithMany("MaintenanceRecordEngineerUsers")
                        .HasForeignKey("EngineerUserId")
                        .HasConstraintName("FK_MaintenanceRecord_EngineerUser");

                    b.HasOne("Centerpoint.Model.Entities.EquipmentItem", "EquipmentItem")
                        .WithMany("MaintenanceRecords")
                        .HasForeignKey("EquipmentItemId")
                        .IsRequired()
                        .HasConstraintName("FK_MaintenanceRecord_EquipmentItem");

                    b.HasOne("Centerpoint.Model.Entities.MaintenanceBlueprint", "MaintenanceBlueprint")
                        .WithMany("MaintenanceRecords")
                        .HasForeignKey("MaintenanceBlueprintId")
                        .IsRequired()
                        .HasConstraintName("FK_MaintenanceRecord_MaintenanceBlueprint");

                    b.HasOne("Centerpoint.Model.Entities.Run", "Run")
                        .WithMany("MaintenanceRecords")
                        .HasForeignKey("RunId")
                        .HasConstraintName("FK_MaintenanceRecord_Run");

                    b.HasOne("Centerpoint.Model.Entities.ServiceImprovement", "ServiceImprovement")
                        .WithMany("MaintenanceRecords")
                        .HasForeignKey("ServiceImprovementId")
                        .HasConstraintName("FK_MaintenanceRecord_ServiceImprovement");

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("MaintenanceRecordUsers")
                        .HasForeignKey("UserId")
                        .HasConstraintName("FK_MaintenanceRecord_User");

                    b.Navigation("EngineerUser");

                    b.Navigation("EquipmentItem");

                    b.Navigation("MaintenanceBlueprint");

                    b.Navigation("Run");

                    b.Navigation("ServiceImprovement");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceRecordDocument", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Document", "Document")
                        .WithMany("MaintenanceRecordDocuments")
                        .HasForeignKey("DocumentId")
                        .IsRequired()
                        .HasConstraintName("FK_MaintenanceRecordDocument_Document");

                    b.HasOne("Centerpoint.Model.Entities.MaintenanceRecord", "MaintenanceRecord")
                        .WithMany("MaintenanceRecordDocuments")
                        .HasForeignKey("MaintenanceRecordId")
                        .IsRequired()
                        .HasConstraintName("FK_MaintenanceRecordDocument_MaintenanceRecord");

                    b.Navigation("Document");

                    b.Navigation("MaintenanceRecord");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceRecordLog", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.MaintenanceRecord", "MaintenanceRecord")
                        .WithMany("MaintenanceRecordLogs")
                        .HasForeignKey("MaintenanceRecordId")
                        .IsRequired()
                        .HasConstraintName("FK_MaintenanceRecordLog_MaintenanceRecord");

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("MaintenanceRecordLogs")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_MaintenanceRecordLog_User");

                    b.Navigation("MaintenanceRecord");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceRecordStep", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.User", "CompletedByUser")
                        .WithMany("MaintenanceRecordSteps")
                        .HasForeignKey("CompletedByUserId")
                        .HasConstraintName("FK_MaintenanceRecordStep_CompletedByUser");

                    b.HasOne("Centerpoint.Model.Entities.MaintenanceRecord", "MaintenanceRecord")
                        .WithMany("MaintenanceRecordSteps")
                        .HasForeignKey("MaintenanceRecordId")
                        .IsRequired()
                        .HasConstraintName("FK_MaintenanceRecordStep_MaintenanceRecord");

                    b.Navigation("CompletedByUser");

                    b.Navigation("MaintenanceRecord");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceRecordStepDocument", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Document", "Document")
                        .WithMany()
                        .HasForeignKey("DocumentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Centerpoint.Model.Entities.MaintenanceRecordStep", "MaintenanceRecordStep")
                        .WithMany("MaintenanceRecordStepDocuments")
                        .HasForeignKey("MaintenanceRecordStepId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Document");

                    b.Navigation("MaintenanceRecordStep");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Objective", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Company", "Company")
                        .WithMany("Objectives")
                        .HasForeignKey("CompanyId")
                        .HasConstraintName("FK_Objective_Company");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Opportunity", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.CompanyLocation", "BaseCompanyLocation")
                        .WithMany("Opportunities")
                        .HasForeignKey("BaseCompanyLocationId")
                        .HasConstraintName("FK_Opportunity_BaseCompanyLocation");

                    b.HasOne("Centerpoint.Model.Entities.Company", "Company")
                        .WithMany("OpportunityCompanies")
                        .HasForeignKey("CompanyId")
                        .HasConstraintName("FK_Opportunity_Company");

                    b.HasOne("Centerpoint.Model.Entities.User", "CreatedByUser")
                        .WithMany("Opportunities")
                        .HasForeignKey("CreatedByUserId")
                        .HasConstraintName("FK_Opportunity_CreatedByUser");

                    b.HasOne("Centerpoint.Model.Entities.Currency", "Currency")
                        .WithMany("Opportunities")
                        .HasForeignKey("CurrencyId")
                        .HasConstraintName("FK_Opportunity_Currency");

                    b.HasOne("Centerpoint.Model.Entities.Company", "CustomerCompany")
                        .WithMany("OpportunityCustomerCompanies")
                        .HasForeignKey("CustomerCompanyId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("FK_Opportunity_CustomerCompany");

                    b.HasOne("Centerpoint.Model.Entities.Division", "Division")
                        .WithMany("Opportunities")
                        .HasForeignKey("DivisionId")
                        .HasConstraintName("FK_Opportunity_Division");

                    b.HasOne("Centerpoint.Model.Entities.OpportunityClosedReason", "OpportunityClosedReason")
                        .WithMany("Opportunities")
                        .HasForeignKey("OpportunityClosedReasonId")
                        .HasConstraintName("FK_Opportunity_OpportunityClosedReason");

                    b.HasOne("Centerpoint.Model.Entities.Opportunity", "ParentOpportunity")
                        .WithMany("InverseParentOpportunity")
                        .HasForeignKey("ParentOpportunityId")
                        .HasConstraintName("FK_Opportunity_ParentOpportunity");

                    b.HasOne("Centerpoint.Model.Entities.Company", "PartnerCompany")
                        .WithMany("OpportunityPartnerCompanies")
                        .HasForeignKey("PartnerCompanyId")
                        .HasConstraintName("FK_Opportunity_PartnerCompany");

                    b.HasOne("Centerpoint.Model.Entities.Project", "Project")
                        .WithMany("Opportunities")
                        .HasForeignKey("ProjectId")
                        .HasConstraintName("FK_Opportunity_Project");

                    b.Navigation("BaseCompanyLocation");

                    b.Navigation("Company");

                    b.Navigation("CreatedByUser");

                    b.Navigation("Currency");

                    b.Navigation("CustomerCompany");

                    b.Navigation("Division");

                    b.Navigation("OpportunityClosedReason");

                    b.Navigation("ParentOpportunity");

                    b.Navigation("PartnerCompany");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityAction", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.ActionType", "ActionType")
                        .WithMany("OpportunityActions")
                        .HasForeignKey("ActionTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityAction_ActionType");

                    b.HasOne("Centerpoint.Model.Entities.User", "AssignedUser")
                        .WithMany("OpportunityActions")
                        .HasForeignKey("AssignedUserId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityAction_AssignedUser");

                    b.HasOne("Centerpoint.Model.Entities.Company", "Company")
                        .WithMany("OpportunityActions")
                        .HasForeignKey("CompanyId")
                        .HasConstraintName("FK_OpportunityAction_Company");

                    b.HasOne("Centerpoint.Model.Entities.OpportunityEvent", "CompletedOpportunityEvent")
                        .WithMany("OpportunityActionCompletedOpportunityEvents")
                        .HasForeignKey("CompletedOpportunityEventId")
                        .HasConstraintName("FK_OpportunityAction_CompletedOpportunityEvent");

                    b.HasOne("Centerpoint.Model.Entities.OpportunityEvent", "OpportunityEvent")
                        .WithMany("OpportunityActionOpportunityEvents")
                        .HasForeignKey("OpportunityEventId")
                        .HasConstraintName("FK_OpportunityAction_OpportunityEvent");

                    b.HasOne("Centerpoint.Model.Entities.Opportunity", "Opportunity")
                        .WithMany("OpportunityActions")
                        .HasForeignKey("OpportunityId")
                        .HasConstraintName("FK_OpportunityAction_Opportunity");

                    b.Navigation("ActionType");

                    b.Navigation("AssignedUser");

                    b.Navigation("Company");

                    b.Navigation("CompletedOpportunityEvent");

                    b.Navigation("Opportunity");

                    b.Navigation("OpportunityEvent");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityActionDocument", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Document", "Document")
                        .WithMany("OpportunityActionDocuments")
                        .HasForeignKey("DocumentId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityActionDocument_Document");

                    b.HasOne("Centerpoint.Model.Entities.OpportunityAction", "OpportunityAction")
                        .WithMany("OpportunityActionDocuments")
                        .HasForeignKey("OpportunityActionId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityActionDocument_OpportunityAction");

                    b.Navigation("Document");

                    b.Navigation("OpportunityAction");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityCompanyContact", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.CompanyContact", "CompanyContact")
                        .WithMany("OpportunityCompanyContacts")
                        .HasForeignKey("CompanyContactId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityCompanyContact_CompanyContact");

                    b.HasOne("Centerpoint.Model.Entities.Opportunity", "Opportunity")
                        .WithMany("OpportunityCompanyContacts")
                        .HasForeignKey("OpportunityId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityCompanyContact_Opportunity");

                    b.Navigation("CompanyContact");

                    b.Navigation("Opportunity");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityConveyance", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Opportunity", "Opportunity")
                        .WithMany("OpportunityConveyances")
                        .HasForeignKey("OpportunityId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityConveyance_Opportunity");

                    b.Navigation("Opportunity");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityDocument", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Document", "Document")
                        .WithMany("OpportunityDocuments")
                        .HasForeignKey("DocumentId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityDocument_Document");

                    b.HasOne("Centerpoint.Model.Entities.Opportunity", "Opportunity")
                        .WithMany("OpportunityDocuments")
                        .HasForeignKey("OpportunityId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityDocument_Opportunity");

                    b.Navigation("Document");

                    b.Navigation("Opportunity");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityEquipmentCategory", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.EquipmentCategory", "EquipmentCategory")
                        .WithMany("OpportunityEquipmentCategories")
                        .HasForeignKey("EquipmentCategoryId")
                        .HasConstraintName("FK_OpportunityEquipmentCategory_EquipmentCategory");

                    b.HasOne("Centerpoint.Model.Entities.Opportunity", "Opportunity")
                        .WithMany("OpportunityEquipmentCategories")
                        .HasForeignKey("OpportunityId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityEquipmentCategory_Opportunity");

                    b.Navigation("EquipmentCategory");

                    b.Navigation("Opportunity");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityEvent", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Company", "Company")
                        .WithMany("OpportunityEvents")
                        .HasForeignKey("CompanyId")
                        .HasConstraintName("FK_OpportunityEvent_Company");

                    b.HasOne("Centerpoint.Model.Entities.EventType", "EventType")
                        .WithMany("OpportunityEvents")
                        .HasForeignKey("EventTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityEvent_EventType");

                    b.HasOne("Centerpoint.Model.Entities.Opportunity", "Opportunity")
                        .WithMany("OpportunityEvents")
                        .HasForeignKey("OpportunityId")
                        .HasConstraintName("FK_OpportunityEvent_Opportunity");

                    b.Navigation("Company");

                    b.Navigation("EventType");

                    b.Navigation("Opportunity");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityEventCompanyContact", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.CompanyContact", "CompanyContact")
                        .WithMany("OpportunityEventCompanyContacts")
                        .HasForeignKey("CompanyContactId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityEventCompanyContact_CompanyContact");

                    b.HasOne("Centerpoint.Model.Entities.OpportunityEvent", "OpportunityEvent")
                        .WithMany("OpportunityEventCompanyContacts")
                        .HasForeignKey("OpportunityEventId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityEventCompanyContact_OpportunityEvent");

                    b.Navigation("CompanyContact");

                    b.Navigation("OpportunityEvent");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityEventDocument", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Document", "Document")
                        .WithMany("OpportunityEventDocuments")
                        .HasForeignKey("DocumentId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityEventDocument_Document");

                    b.HasOne("Centerpoint.Model.Entities.OpportunityEvent", "OpportunityEvent")
                        .WithMany("OpportunityEventDocuments")
                        .HasForeignKey("OpportunityEventId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityEventDocument_OpportunityEvent");

                    b.Navigation("Document");

                    b.Navigation("OpportunityEvent");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityField", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.CompanyField", "CompanyField")
                        .WithMany("OpportunityFields")
                        .HasForeignKey("CompanyFieldId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityField_CompanyField");

                    b.HasOne("Centerpoint.Model.Entities.Opportunity", "Opportunity")
                        .WithMany("OpportunityFields")
                        .HasForeignKey("OpportunityId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityField_Opportunity");

                    b.Navigation("CompanyField");

                    b.Navigation("Opportunity");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityLog", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Opportunity", "Opportunity")
                        .WithMany("OpportunityLogs")
                        .HasForeignKey("OpportunityId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityLog_Opportunity");

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("OpportunityLogs")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityLog_User");

                    b.Navigation("Opportunity");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityService", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Objective", "Objective")
                        .WithMany("OpportunityServices")
                        .HasForeignKey("ObjectiveId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityService_Objective");

                    b.HasOne("Centerpoint.Model.Entities.Opportunity", "Opportunity")
                        .WithMany("OpportunityServices")
                        .HasForeignKey("OpportunityId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityService_Opportunity");

                    b.Navigation("Objective");

                    b.Navigation("Opportunity");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityWell", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.CompanyWell", "CompanyWell")
                        .WithMany("OpportunityWells")
                        .HasForeignKey("CompanyWellId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityWell_CompanyWell");

                    b.HasOne("Centerpoint.Model.Entities.Opportunity", "Opportunity")
                        .WithMany("OpportunityWells")
                        .HasForeignKey("OpportunityId")
                        .IsRequired()
                        .HasConstraintName("FK_OpportunityWell_Opportunity");

                    b.Navigation("CompanyWell");

                    b.Navigation("Opportunity");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.PersonnelCertificateDocument", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.CertificateCategory", "CertificateCategory")
                        .WithMany("PersonnelCertificateDocuments")
                        .HasForeignKey("CertificateCategoryId")
                        .HasConstraintName("FK_PersonnelCertificateDocument_CertificateCategory");

                    b.HasOne("Centerpoint.Model.Entities.Document", "Document")
                        .WithMany("PersonnelCertificateDocuments")
                        .HasForeignKey("DocumentId")
                        .HasConstraintName("FK_PersonnelCertificateDocument_Document");

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("PersonnelCertificateDocuments")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_PersonnelCertificateDocument_User");

                    b.Navigation("CertificateCategory");

                    b.Navigation("Document");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Project", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.CompanyLocation", "BaseCompanyLocation")
                        .WithMany("ProjectBaseCompanyLocations")
                        .HasForeignKey("BaseCompanyLocationId")
                        .HasConstraintName("FK_Project_BaseCompanyLocation");

                    b.HasOne("Centerpoint.Model.Entities.CompanyContact", "CompanyContact")
                        .WithMany("Projects")
                        .HasForeignKey("CompanyContactId")
                        .IsRequired()
                        .HasConstraintName("FK_Project_CompanyContact");

                    b.HasOne("Centerpoint.Model.Entities.Company", "Company")
                        .WithMany("ProjectCompanies")
                        .HasForeignKey("CompanyId")
                        .IsRequired()
                        .HasConstraintName("FK_Project_Company");

                    b.HasOne("Centerpoint.Model.Entities.CompanyLocation", "CompanyLocation")
                        .WithMany("ProjectCompanyLocations")
                        .HasForeignKey("CompanyLocationId")
                        .IsRequired()
                        .HasConstraintName("FK_Project_CompanyLocation");

                    b.HasOne("Centerpoint.Model.Entities.Company", "CustomerCompany")
                        .WithMany("ProjectCustomerCompanies")
                        .HasForeignKey("CustomerCompanyId")
                        .HasConstraintName("FK_Project_CustomerCompany");

                    b.HasOne("Centerpoint.Model.Entities.Division", "Division")
                        .WithMany("Projects")
                        .HasForeignKey("DivisionId")
                        .HasConstraintName("FK_Project_Division");

                    b.HasOne("Centerpoint.Model.Entities.Company", "OperatorCompany")
                        .WithMany("ProjectOperatorCompanies")
                        .HasForeignKey("OperatorCompanyId")
                        .HasConstraintName("FK_Project_OperatorCompany");

                    b.HasOne("Centerpoint.Model.Entities.Company", "PartnerCompany")
                        .WithMany("ProjectPartnerCompanies")
                        .HasForeignKey("PartnerCompanyId")
                        .HasConstraintName("FK_Project_PartnerCompany");

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("Projects")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_Project_User");

                    b.Navigation("BaseCompanyLocation");

                    b.Navigation("Company");

                    b.Navigation("CompanyContact");

                    b.Navigation("CompanyLocation");

                    b.Navigation("CustomerCompany");

                    b.Navigation("Division");

                    b.Navigation("OperatorCompany");

                    b.Navigation("PartnerCompany");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ProjectAdditionalProperty", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Project", "Project")
                        .WithMany("AdditionalProperties")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Project");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ProjectComment", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Project", "Project")
                        .WithMany("ProjectComments")
                        .HasForeignKey("ProjectId")
                        .IsRequired()
                        .HasConstraintName("FK_ProjectComment_Project");

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("ProjectComments")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_ProjectComment_User");

                    b.Navigation("Project");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ProjectConveyance", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Project", "Project")
                        .WithMany("ProjectConveyances")
                        .HasForeignKey("ProjectId")
                        .IsRequired()
                        .HasConstraintName("FK_ProjectConveyance_Project");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ProjectDocument", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Document", "Document")
                        .WithMany("ProjectDocuments")
                        .HasForeignKey("DocumentId")
                        .IsRequired()
                        .HasConstraintName("FK_ProjectDocument_Document");

                    b.HasOne("Centerpoint.Model.Entities.Project", "Project")
                        .WithMany("ProjectDocuments")
                        .HasForeignKey("ProjectId")
                        .IsRequired()
                        .HasConstraintName("FK_ProjectDocument_Project");

                    b.Navigation("Document");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ProjectEquipmentCategory", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.EquipmentCategory", "EquipmentCategory")
                        .WithMany("ProjectEquipmentCategories")
                        .HasForeignKey("EquipmentCategoryId")
                        .HasConstraintName("FK_ProjectEquipmentCategory_EquipmentCategory");

                    b.HasOne("Centerpoint.Model.Entities.Project", "Project")
                        .WithMany("ProjectEquipmentCategories")
                        .HasForeignKey("ProjectId")
                        .IsRequired()
                        .HasConstraintName("FK_ProjectEquipmentCategory_Project");

                    b.Navigation("EquipmentCategory");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ProjectField", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.CompanyField", "CompanyField")
                        .WithMany("ProjectFields")
                        .HasForeignKey("CompanyFieldId")
                        .IsRequired()
                        .HasConstraintName("FK_ProjectField_CompanyField");

                    b.HasOne("Centerpoint.Model.Entities.Project", "Project")
                        .WithMany("ProjectFields")
                        .HasForeignKey("ProjectId")
                        .IsRequired()
                        .HasConstraintName("FK_ProjectField_Project");

                    b.Navigation("CompanyField");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ProjectLog", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Job", "Job")
                        .WithMany("ProjectLogs")
                        .HasForeignKey("JobId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_ProjectLog_Job");

                    b.HasOne("Centerpoint.Model.Entities.Project", "Project")
                        .WithMany("ProjectLogs")
                        .HasForeignKey("ProjectId")
                        .IsRequired()
                        .HasConstraintName("FK_ProjectLog_Project");

                    b.HasOne("Centerpoint.Model.Entities.Run", "Run")
                        .WithMany("ProjectLogs")
                        .HasForeignKey("RunId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_ProjectLog_Run");

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("ProjectLogs")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_ProjectLog_User");

                    b.Navigation("Job");

                    b.Navigation("Project");

                    b.Navigation("Run");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ProjectNonAssetItem", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.EquipmentShipmentNonAssetItem", "EquipmentShipmentNonAssetItem")
                        .WithMany("ProjectNonAssetItems")
                        .HasForeignKey("EquipmentShipmentNonAssetItemId")
                        .IsRequired()
                        .HasConstraintName("FK_ProjectNonAssetItem_EquipmentShipmentNonAssetItem");

                    b.HasOne("Centerpoint.Model.Entities.Project", "Project")
                        .WithMany("ProjectNonAssetItems")
                        .HasForeignKey("ProjectId")
                        .IsRequired()
                        .HasConstraintName("FK_ProjectNonAssetItem_Project");

                    b.Navigation("EquipmentShipmentNonAssetItem");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ProjectObjective", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Objective", "Objective")
                        .WithMany("ProjectObjectives")
                        .HasForeignKey("ObjectiveId")
                        .IsRequired()
                        .HasConstraintName("FK_ProjectObjective_Objective");

                    b.HasOne("Centerpoint.Model.Entities.Project", "Project")
                        .WithMany("ProjectObjectives")
                        .HasForeignKey("ProjectId")
                        .IsRequired()
                        .HasConstraintName("FK_ProjectObjective_Project");

                    b.Navigation("Objective");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ProjectWell", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.CompanyWell", "CompanyWell")
                        .WithMany("ProjectWells")
                        .HasForeignKey("CompanyWellId")
                        .IsRequired()
                        .HasConstraintName("FK_ProjectWell_CompanyWell");

                    b.HasOne("Centerpoint.Model.Entities.Project", "Project")
                        .WithMany("ProjectWells")
                        .HasForeignKey("ProjectId")
                        .IsRequired()
                        .HasConstraintName("FK_ProjectWell_Project");

                    b.Navigation("CompanyWell");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.RiskIdentificationSafetyControl", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Job", "Job")
                        .WithMany("RiskIdentificationSafetyControls")
                        .HasForeignKey("JobId")
                        .HasConstraintName("FK_RiskIdentificationSafetyControl_Job");

                    b.HasOne("Centerpoint.Model.Entities.Project", "Project")
                        .WithMany()
                        .HasForeignKey("ProjectId");

                    b.Navigation("Job");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Run", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Job", "Job")
                        .WithMany("Runs")
                        .HasForeignKey("JobId")
                        .IsRequired()
                        .HasConstraintName("FK_Run_Job");

                    b.Navigation("Job");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.RunEquipmentItem", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.EquipmentItem", "EquipmentItem")
                        .WithMany("RunEquipmentItems")
                        .HasForeignKey("EquipmentItemId")
                        .IsRequired()
                        .HasConstraintName("FK_RunEquipmentItem_EquipmentItem");

                    b.HasOne("Centerpoint.Model.Entities.Run", "Run")
                        .WithMany("RunEquipmentItems")
                        .HasForeignKey("RunId")
                        .IsRequired()
                        .HasConstraintName("FK_RunEquipmentItem_Run");

                    b.Navigation("EquipmentItem");

                    b.Navigation("Run");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.RunObjective", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Objective", "Objective")
                        .WithMany("RunObjectives")
                        .HasForeignKey("ObjectiveId")
                        .IsRequired()
                        .HasConstraintName("FK_RunObjective_Objective");

                    b.HasOne("Centerpoint.Model.Entities.Run", "Run")
                        .WithMany("RunObjectives")
                        .HasForeignKey("RunId")
                        .IsRequired()
                        .HasConstraintName("FK_RunObjective_Run");

                    b.Navigation("Objective");

                    b.Navigation("Run");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ServiceImprovement", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.CompanyLocation", "BaseCompanyLocation")
                        .WithMany("ServiceImprovementBaseCompanyLocations")
                        .HasForeignKey("BaseCompanyLocationId")
                        .HasConstraintName("FK_ServiceImprovement_BaseCompanyLocation");

                    b.HasOne("Centerpoint.Model.Entities.CompanyContact", "CompanyContact")
                        .WithMany("ServiceImprovements")
                        .HasForeignKey("CompanyContactId")
                        .HasConstraintName("FK_ServiceImprovement_CompanyContact");

                    b.HasOne("Centerpoint.Model.Entities.Company", "Company")
                        .WithMany("ServiceImprovements")
                        .HasForeignKey("CompanyId")
                        .HasConstraintName("FK_ServiceImprovement_Company");

                    b.HasOne("Centerpoint.Model.Entities.CompanyLocation", "CompanyLocation")
                        .WithMany("ServiceImprovementCompanyLocations")
                        .HasForeignKey("CompanyLocationId")
                        .HasConstraintName("FK_ServiceImprovement_CompanyLocation");

                    b.HasOne("Centerpoint.Model.Entities.User", "CorrectiveActionUser")
                        .WithMany("ServiceImprovementCorrectiveActionUsers")
                        .HasForeignKey("CorrectiveActionUserId")
                        .HasConstraintName("FK_ServiceImprovement_CorrectiveActionUser");

                    b.HasOne("Centerpoint.Model.Entities.User", "CreatedByUser")
                        .WithMany("ServiceImprovementCreatedByUsers")
                        .HasForeignKey("CreatedByUserId")
                        .HasConstraintName("FK_ServiceImprovement_CreatedByUser");

                    b.HasOne("Centerpoint.Model.Entities.User", "InvestigatorUser")
                        .WithMany("ServiceImprovementInvestigatorUsers")
                        .HasForeignKey("InvestigatorUserId")
                        .HasConstraintName("FK_ServiceImprovement_InvestigatorUser");

                    b.HasOne("Centerpoint.Model.Entities.Job", "Job")
                        .WithMany("ServiceImprovements")
                        .HasForeignKey("JobId")
                        .HasConstraintName("FK_ServiceImprovement_Job");

                    b.HasOne("Centerpoint.Model.Entities.User", "PreventiveActionUser")
                        .WithMany("ServiceImprovementPreventiveActionUsers")
                        .HasForeignKey("PreventiveActionUserId")
                        .HasConstraintName("FK_ServiceImprovement_PreventiveActionUser");

                    b.HasOne("Centerpoint.Model.Entities.Project", "Project")
                        .WithMany()
                        .HasForeignKey("ProjectId");

                    b.HasOne("Centerpoint.Model.Entities.ServiceImprovementLocation", "ServiceImprovementLocation")
                        .WithMany("ServiceImprovements")
                        .HasForeignKey("ServiceImprovementLocationId")
                        .HasConstraintName("FK_ServiceImprovement_ServiceImprovementLocation");

                    b.HasOne("Centerpoint.Model.Entities.Severity", "Severity")
                        .WithMany("ServiceImprovements")
                        .HasForeignKey("SeverityId")
                        .HasConstraintName("FK_ServiceImprovement_Severity");

                    b.HasOne("Centerpoint.Model.Entities.User", "SifAdminUser")
                        .WithMany("ServiceImprovementSifAdminUsers")
                        .HasForeignKey("SifAdminUserId")
                        .HasConstraintName("FK_ServiceImprovement_SifAdminUser");

                    b.Navigation("BaseCompanyLocation");

                    b.Navigation("Company");

                    b.Navigation("CompanyContact");

                    b.Navigation("CompanyLocation");

                    b.Navigation("CorrectiveActionUser");

                    b.Navigation("CreatedByUser");

                    b.Navigation("InvestigatorUser");

                    b.Navigation("Job");

                    b.Navigation("PreventiveActionUser");

                    b.Navigation("Project");

                    b.Navigation("ServiceImprovementLocation");

                    b.Navigation("Severity");

                    b.Navigation("SifAdminUser");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ServiceImprovementDocument", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Document", "Document")
                        .WithMany("ServiceImprovementDocuments")
                        .HasForeignKey("DocumentId")
                        .IsRequired()
                        .HasConstraintName("FK_ServiceImprovementDocument_Document");

                    b.HasOne("Centerpoint.Model.Entities.ServiceImprovement", "ServiceImprovement")
                        .WithMany("ServiceImprovementDocuments")
                        .HasForeignKey("ServiceImprovementId")
                        .IsRequired()
                        .HasConstraintName("FK_ServiceImprovementDocument_ServiceImprovement");

                    b.Navigation("Document");

                    b.Navigation("ServiceImprovement");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ServiceImprovementLog", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.ServiceImprovement", "ServiceImprovement")
                        .WithMany("ServiceImprovementLogs")
                        .HasForeignKey("ServiceImprovementId")
                        .IsRequired()
                        .HasConstraintName("FK_ServiceImprovementLog_ServiceImprovement");

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("ServiceImprovementLogs")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_ServiceImprovementLog_User");

                    b.Navigation("ServiceImprovement");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ServiceImprovementSubCategory", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.ServiceImprovement", "ServiceImprovement")
                        .WithMany("ServiceImprovementSubCategories")
                        .HasForeignKey("ServiceImprovementId")
                        .IsRequired()
                        .HasConstraintName("FK_ServiceImprovementSubCategory_ServiceImprovement");

                    b.HasOne("Centerpoint.Model.Entities.SubCategory", "SubCategory")
                        .WithMany("ServiceImprovementSubCategories")
                        .HasForeignKey("SubCategoryId")
                        .IsRequired()
                        .HasConstraintName("FK_ServiceImprovementSubCategory_SubCategory");

                    b.Navigation("ServiceImprovement");

                    b.Navigation("SubCategory");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.User", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.CompanyLocation", "BaseCompanyLocation")
                        .WithMany("Users")
                        .HasForeignKey("BaseCompanyLocationId")
                        .HasConstraintName("FK_User_BaseCompanyLocation");

                    b.Navigation("BaseCompanyLocation");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.UserLocation", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("UserLocations")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_UserLocation_User");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.UserNote", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("UserNotes")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_UserNote_User");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.UserQuery", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("UserQueries")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_UserQuery_User");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.UserRole", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Role", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .IsRequired();

                    b.HasOne("Centerpoint.Model.Entities.User", "User")
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.Role", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.HasOne("Centerpoint.Model.Entities.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ActionType", b =>
                {
                    b.Navigation("OpportunityActions");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaDeliverableBlueprint", b =>
                {
                    b.Navigation("AnsaDeliverableBlueprintEquipmentCategories");

                    b.Navigation("AnsaDeliverableBlueprintSteps");

                    b.Navigation("AnsaProjectDeliverables");

                    b.Navigation("AnsaServiceDeliverableBlueprints");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProject", b =>
                {
                    b.Navigation("AnsaProjectComments");

                    b.Navigation("AnsaProjectCompanyContacts");

                    b.Navigation("AnsaProjectDeliverables");

                    b.Navigation("AnsaProjectDocuments");

                    b.Navigation("AnsaProjectFeedbacks");

                    b.Navigation("AnsaProjectFields");

                    b.Navigation("AnsaProjectLogs");

                    b.Navigation("AnsaProjectServices");

                    b.Navigation("AnsaProjectWells");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectDeliverable", b =>
                {
                    b.Navigation("AnsaProjectDeliverableSteps");

                    b.Navigation("AnsaProjectDeliverableUsers");

                    b.Navigation("AnsaProjectDeliverableWells");

                    b.Navigation("AnsaProjectLogs");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaProjectDeliverableStep", b =>
                {
                    b.Navigation("AnsaProjectLogs");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.AnsaService", b =>
                {
                    b.Navigation("AnsaProjectServices");

                    b.Navigation("AnsaServiceDeliverableBlueprints");

                    b.Navigation("AnsaServiceDocuments");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Category", b =>
                {
                    b.Navigation("CompanyCategories");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CertificateCategory", b =>
                {
                    b.Navigation("PersonnelCertificateDocuments");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Company", b =>
                {
                    b.Navigation("AnsaProjectCompanies");

                    b.Navigation("AnsaProjectOperatorCompanies");

                    b.Navigation("AnsaServices");

                    b.Navigation("CompanyCategories");

                    b.Navigation("CompanyFields");

                    b.Navigation("CompanyLocations");

                    b.Navigation("EquipmentCategories");

                    b.Navigation("EquipmentItemCurrentCompanies");

                    b.Navigation("EquipmentItemManufacturerCompanies");

                    b.Navigation("EquipmentShipmentFromCompanies");

                    b.Navigation("EquipmentShipmentToCompanies");

                    b.Navigation("Lessons");

                    b.Navigation("Objectives");

                    b.Navigation("OpportunityActions");

                    b.Navigation("OpportunityCompanies");

                    b.Navigation("OpportunityCustomerCompanies");

                    b.Navigation("OpportunityEvents");

                    b.Navigation("OpportunityPartnerCompanies");

                    b.Navigation("ProjectCompanies");

                    b.Navigation("ProjectCustomerCompanies");

                    b.Navigation("ProjectOperatorCompanies");

                    b.Navigation("ProjectPartnerCompanies");

                    b.Navigation("ServiceImprovements");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CompanyContact", b =>
                {
                    b.Navigation("AnsaProjectCompanyContacts");

                    b.Navigation("AnsaProjectDeliverableUsers");

                    b.Navigation("AnsaProjectLogs");

                    b.Navigation("AnsaProjects");

                    b.Navigation("OpportunityCompanyContacts");

                    b.Navigation("OpportunityEventCompanyContacts");

                    b.Navigation("Projects");

                    b.Navigation("ServiceImprovements");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CompanyField", b =>
                {
                    b.Navigation("AnsaProjectFields");

                    b.Navigation("CompanyWells");

                    b.Navigation("EquipmentShipmentFromCompanyFields");

                    b.Navigation("EquipmentShipmentToCompanyFields");

                    b.Navigation("OpportunityFields");

                    b.Navigation("ProjectFields");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CompanyLocation", b =>
                {
                    b.Navigation("AnsaProjects");

                    b.Navigation("CompanyContacts");

                    b.Navigation("EquipmentItemCurrentCompanyLocations");

                    b.Navigation("EquipmentItemMaintenanceSchedules");

                    b.Navigation("EquipmentItemManufacturerCompanyLocations");

                    b.Navigation("EquipmentShipmentFromCompanyLocations");

                    b.Navigation("EquipmentShipmentInvoices");

                    b.Navigation("EquipmentShipmentToCompanyLocations");

                    b.Navigation("Opportunities");

                    b.Navigation("ProjectBaseCompanyLocations");

                    b.Navigation("ProjectCompanyLocations");

                    b.Navigation("ServiceImprovementBaseCompanyLocations");

                    b.Navigation("ServiceImprovementCompanyLocations");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CompanyWell", b =>
                {
                    b.Navigation("AnsaProjectDeliverableWells");

                    b.Navigation("AnsaProjectWells");

                    b.Navigation("CompanyWellDocuments");

                    b.Navigation("CompanyWellFluids");

                    b.Navigation("Jobs");

                    b.Navigation("OpportunityWells");

                    b.Navigation("ProjectWells");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Crew", b =>
                {
                    b.Navigation("JobCrews");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Currency", b =>
                {
                    b.Navigation("EquipmentItems");

                    b.Navigation("EquipmentShipmentInvoices");

                    b.Navigation("EquipmentShipmentNonAssetItems");

                    b.Navigation("EquipmentShipments");

                    b.Navigation("Opportunities");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.CustomStatusCode", b =>
                {
                    b.Navigation("EquipmentItemCustomStatusCodes");

                    b.Navigation("EquipmentShipmentNonAssetItems");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Division", b =>
                {
                    b.Navigation("EquipmentCategories");

                    b.Navigation("EquipmentItems");

                    b.Navigation("Lessons");

                    b.Navigation("MaintenanceBlueprints");

                    b.Navigation("Opportunities");

                    b.Navigation("Projects");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Document", b =>
                {
                    b.Navigation("AnsaProjectDeliverableSteps");

                    b.Navigation("AnsaProjectDocuments");

                    b.Navigation("AnsaServiceDocuments");

                    b.Navigation("CompanyWellDocuments");

                    b.Navigation("EquipmentItemDocuments");

                    b.Navigation("EquipmentShipmentDocuments");

                    b.Navigation("EquipmentShipmentInvoices");

                    b.Navigation("LessonDocuments");

                    b.Navigation("MaintenanceBlueprintDocuments");

                    b.Navigation("MaintenanceRecordDocuments");

                    b.Navigation("OpportunityActionDocuments");

                    b.Navigation("OpportunityDocuments");

                    b.Navigation("OpportunityEventDocuments");

                    b.Navigation("PersonnelCertificateDocuments");

                    b.Navigation("ProjectDocuments");

                    b.Navigation("ServiceImprovementDocuments");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.DownloadFolder", b =>
                {
                    b.Navigation("InverseParentDownloadFolder");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentCategory", b =>
                {
                    b.Navigation("AnsaDeliverableBlueprintEquipmentCategories");

                    b.Navigation("EquipmentCategoryMaintenanceSteps");

                    b.Navigation("EquipmentItems");

                    b.Navigation("InverseParentEquipmentCategory");

                    b.Navigation("LessonEquipmentCategories");

                    b.Navigation("MaintenanceBlueprintEquipmentCategories");

                    b.Navigation("OpportunityEquipmentCategories");

                    b.Navigation("ProjectEquipmentCategories");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentItem", b =>
                {
                    b.Navigation("AdditionalProperties");

                    b.Navigation("EquipmentItemBundleChildEquipmentItems");

                    b.Navigation("EquipmentItemBundleParentEquipmentItems");

                    b.Navigation("EquipmentItemCertificates");

                    b.Navigation("EquipmentItemCustomStatusCodes");

                    b.Navigation("EquipmentItemDocuments");

                    b.Navigation("EquipmentItemLogs");

                    b.Navigation("EquipmentItemMaintenanceSchedules");

                    b.Navigation("EquipmentItemNotes");

                    b.Navigation("EquipmentPackingListItems");

                    b.Navigation("EquipmentShipmentItems");

                    b.Navigation("MaintenanceRecords");

                    b.Navigation("RunEquipmentItems");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentItemCustomStatusCode", b =>
                {
                    b.Navigation("EquipmentShipmentItems");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentPackingList", b =>
                {
                    b.Navigation("EquipmentPackingListItems");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentPackingListItem", b =>
                {
                    b.Navigation("EquipmentShipmentItems");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentShipment", b =>
                {
                    b.Navigation("EquipmentPackingLists");

                    b.Navigation("EquipmentShipmentDocuments");

                    b.Navigation("EquipmentShipmentInvoices");

                    b.Navigation("EquipmentShipmentItems");

                    b.Navigation("EquipmentShipmentNonAssetItems");

                    b.Navigation("EquipmentShipmentPackages");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EquipmentShipmentNonAssetItem", b =>
                {
                    b.Navigation("ProjectNonAssetItems");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.EventType", b =>
                {
                    b.Navigation("OpportunityEvents");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.FluidType", b =>
                {
                    b.Navigation("CompanyWellFluids");

                    b.Navigation("JobFluids");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Job", b =>
                {
                    b.Navigation("AnsaProjectDeliverables");

                    b.Navigation("JobComments");

                    b.Navigation("JobCrews");

                    b.Navigation("JobFluids");

                    b.Navigation("JobObjectives");

                    b.Navigation("Lessons");

                    b.Navigation("ProjectLogs");

                    b.Navigation("RiskIdentificationSafetyControls");

                    b.Navigation("Runs");

                    b.Navigation("ServiceImprovements");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Lesson", b =>
                {
                    b.Navigation("LessonDocuments");

                    b.Navigation("LessonEquipmentCategories");

                    b.Navigation("LessonObjectives");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.LessonCategory", b =>
                {
                    b.Navigation("Lessons");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceBlueprint", b =>
                {
                    b.Navigation("EquipmentCategoryMaintenanceSteps");

                    b.Navigation("EquipmentItemMaintenanceSchedules");

                    b.Navigation("MaintenanceBlueprintDocuments");

                    b.Navigation("MaintenanceBlueprintEquipmentCategories");

                    b.Navigation("MaintenanceBlueprintLogs");

                    b.Navigation("MaintenanceBlueprintSteps");

                    b.Navigation("MaintenanceRecords");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceRecord", b =>
                {
                    b.Navigation("MaintenanceRecordDocuments");

                    b.Navigation("MaintenanceRecordLogs");

                    b.Navigation("MaintenanceRecordSteps");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.MaintenanceRecordStep", b =>
                {
                    b.Navigation("MaintenanceRecordStepDocuments");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Objective", b =>
                {
                    b.Navigation("JobObjectives");

                    b.Navigation("LessonObjectives");

                    b.Navigation("OpportunityServices");

                    b.Navigation("ProjectObjectives");

                    b.Navigation("RunObjectives");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Opportunity", b =>
                {
                    b.Navigation("InverseParentOpportunity");

                    b.Navigation("OpportunityActions");

                    b.Navigation("OpportunityCompanyContacts");

                    b.Navigation("OpportunityConveyances");

                    b.Navigation("OpportunityDocuments");

                    b.Navigation("OpportunityEquipmentCategories");

                    b.Navigation("OpportunityEvents");

                    b.Navigation("OpportunityFields");

                    b.Navigation("OpportunityLogs");

                    b.Navigation("OpportunityServices");

                    b.Navigation("OpportunityWells");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityAction", b =>
                {
                    b.Navigation("OpportunityActionDocuments");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityClosedReason", b =>
                {
                    b.Navigation("Opportunities");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.OpportunityEvent", b =>
                {
                    b.Navigation("OpportunityActionCompletedOpportunityEvents");

                    b.Navigation("OpportunityActionOpportunityEvents");

                    b.Navigation("OpportunityEventCompanyContacts");

                    b.Navigation("OpportunityEventDocuments");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Project", b =>
                {
                    b.Navigation("AdditionalProperties");

                    b.Navigation("AnsaProjects");

                    b.Navigation("Crews");

                    b.Navigation("EquipmentItemEquipmentPackingListProjects");

                    b.Navigation("EquipmentItemProjects");

                    b.Navigation("EquipmentPackingLists");

                    b.Navigation("EquipmentShipments");

                    b.Navigation("FromShipments");

                    b.Navigation("Jobs");

                    b.Navigation("Lessons");

                    b.Navigation("Opportunities");

                    b.Navigation("ProjectComments");

                    b.Navigation("ProjectConveyances");

                    b.Navigation("ProjectDocuments");

                    b.Navigation("ProjectEquipmentCategories");

                    b.Navigation("ProjectFields");

                    b.Navigation("ProjectLogs");

                    b.Navigation("ProjectNonAssetItems");

                    b.Navigation("ProjectObjectives");

                    b.Navigation("ProjectWells");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Role", b =>
                {
                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Run", b =>
                {
                    b.Navigation("MaintenanceRecords");

                    b.Navigation("ProjectLogs");

                    b.Navigation("RunEquipmentItems");

                    b.Navigation("RunObjectives");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ServiceImprovement", b =>
                {
                    b.Navigation("Lessons");

                    b.Navigation("MaintenanceRecords");

                    b.Navigation("ServiceImprovementDocuments");

                    b.Navigation("ServiceImprovementLogs");

                    b.Navigation("ServiceImprovementSubCategories");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ServiceImprovementLocation", b =>
                {
                    b.Navigation("ServiceImprovements");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.Severity", b =>
                {
                    b.Navigation("ServiceImprovements");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.ShipmentMethod", b =>
                {
                    b.Navigation("EquipmentShipments");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.SubCategory", b =>
                {
                    b.Navigation("ServiceImprovementSubCategories");
                });

            modelBuilder.Entity("Centerpoint.Model.Entities.User", b =>
                {
                    b.Navigation("AnsaAnalystHours");

                    b.Navigation("AnsaDeliverableBlueprints");

                    b.Navigation("AnsaProjectComments");

                    b.Navigation("AnsaProjectDeliverableSteps");

                    b.Navigation("AnsaProjectDeliverables");

                    b.Navigation("AnsaProjectFeedbacks");

                    b.Navigation("AnsaProjectLogs");

                    b.Navigation("AnsaProjects");

                    b.Navigation("AnsaServices");

                    b.Navigation("Crews");

                    b.Navigation("Documents");

                    b.Navigation("EquipmentItemLogs");

                    b.Navigation("EquipmentItemNotes");

                    b.Navigation("EquipmentPackingLists");

                    b.Navigation("EquipmentShipments");

                    b.Navigation("JobComments");

                    b.Navigation("Lessons");

                    b.Navigation("MaintenanceBlueprintLogs");

                    b.Navigation("MaintenanceBlueprints");

                    b.Navigation("MaintenanceRecordEngineerUsers");

                    b.Navigation("MaintenanceRecordLogs");

                    b.Navigation("MaintenanceRecordSteps");

                    b.Navigation("MaintenanceRecordUsers");

                    b.Navigation("Opportunities");

                    b.Navigation("OpportunityActions");

                    b.Navigation("OpportunityLogs");

                    b.Navigation("PersonnelCertificateDocuments");

                    b.Navigation("ProjectComments");

                    b.Navigation("ProjectLogs");

                    b.Navigation("Projects");

                    b.Navigation("ServiceImprovementCorrectiveActionUsers");

                    b.Navigation("ServiceImprovementCreatedByUsers");

                    b.Navigation("ServiceImprovementInvestigatorUsers");

                    b.Navigation("ServiceImprovementLogs");

                    b.Navigation("ServiceImprovementPreventiveActionUsers");

                    b.Navigation("ServiceImprovementSifAdminUsers");

                    b.Navigation("UserLocations");

                    b.Navigation("UserNotes");

                    b.Navigation("UserQueries");

                    b.Navigation("UserRoles");
                });
#pragma warning restore 612, 618
        }
    }
}
