
$(document).ready(function () {
    var employeeOnSiteGrid = $('#employeeOnSiteGrid').data("kendoGrid");
    employeeOnSiteGrid.bind('dataBound', function (e) {
        this.element.find('.k-i-excel').remove();
    });
});
function updateEmployeeOnSiteGrid() {
    var employeeOnSiteGrid = $("#employeeOnSiteGrid").data("kendoGrid");
    var totalEmployeeOnSite = employeeOnSiteGrid.dataSource.total();
    viewModel.set("totalEmployeeOnSite", totalEmployeeOnSite);
}

var viewModel = new kendo.observable({
    totalEmployeeOnSite: 0,
});
kendo.bind(document.body.children, viewModel);