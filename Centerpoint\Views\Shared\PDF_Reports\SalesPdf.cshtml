﻿@{
    Layout = null;
}
<head>
    <script src="https://code.jquery.com/jquery-3.6.1.min.js"></script>
    <script src="~/lib/kendo/kendo.all.min.js"></script>
    <script src="~/lib/kendo/kendo.aspnetmvc.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Raleway&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="~/style/kendo-theme.css" />
    <link rel="stylesheet" href="~/style/salesPDF.css" />
    <link rel="stylesheet" href="~/style/home.css" />

</head>

@model SalesPdfModel

<hr style="margin-top:140px;" />
<div >
    <div class="panel panel-default">
        <div class="panel-heading">
            <h2 class="panel-title">Dashboard </h2>
        </div>
        <br />
        <div class="d-flex w-100 flex-wrap breakhere">
            <div style="padding-left:0; width:600px;">
                <div class="card">
                    <div class="card-header">
                        <h6>Opportunity Value</h6>
                    </div>
                    <div class="card-body">
                        <div class="card-list-item">
                            <span class="card-list-item-name">Enquiry</span>
                            <div class="d-flex justify-content-between card-list-item-count-exacrt-width">
                                <span class="card-list-item-count" style="background: #FF748C;">@Model.TotalEnquiry</span>
                                <span class="card-list-item-count" style="background: #000;">@Model.TotalEnquiries</span>
                            </div>
                        </div>
                        <div class="card-list-item">
                            <span class="card-list-item-name">Proposal</span>
                            <div class="d-flex justify-content-between card-list-item-count-exacrt-width">
                                <span class="card-list-item-count" style="background: #B03060;">@Model.TotalProposal</span>
                                <span class="card-list-item-count" style="background: #000;">@Model.TotalProposals</span>
                            </div>
                        </div>
                        <div class="card-list-item">
                            <span class="card-list-item-name">Review</span>
                            <div class="d-flex justify-content-between card-list-item-count-exacrt-width">
                                <span class="card-list-item-count" style="background: #cccc00;">@Model.TotalReview</span>
                                <span class="card-list-item-count" style="background: #000;">@Model.TotalReviews</span>
                            </div>
                        </div>
                        <div class="card-list-item">
                            <span class="card-list-item-name ">Award</span>
                            <div class="d-flex justify-content-between card-list-item-count-exacrt-width">
                                <span class="card-list-item-count" style="background: #008000;">@Model.TotalAward</span>
                                <span class="card-list-item-count" style="background: #000;">@Model.TotalAwards</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card" style="margin-left: 80px;">
                <div style="width:100%">
                    <div class="card-header">
                        <h6>Total Value</h6>
                    </div>
                    <div class="card-body" style=" padding: 0;">
                        @(Html.Kendo().Chart()
                            .Name("chart")
                            .Legend(legend => legend.Position(ChartLegendPosition.Bottom).Visible(true))
                            .HtmlAttributes(new { style = "height: 658px;width: 658px" })
                            .Series(series =>
                            {
                                series.Pie(new dynamic[] {
                                new {category = "Leads",value = Model.TotalLeadsValue},
                                new {category = "Opportunities",value = Model.TotalOpportunitiesValue},
                            })
                                .Labels(labels => labels
                                .Visible(true)
                                .Template("#if(value>0){##=category# - £ #= kendo.format('{0:n2}', value)##}#")
                                );
                            })
                            .Transitions(false)
                            .Tooltip(tooltip => tooltip
                            .Visible(true)
                            .Template("#=category# - £ #= kendo.format('{0:n2}', value)#")))
                    </div>
                </div>
                
            </div>
        </div>
        <br />
        <div class="d-flex w-100 flex-wrap breakhere">
            <div style="padding-left:0px; width:600px;">
                <div class="card">
                    <div class="card-header">
                        <h6>Actions by User</h6>
                    </div>
                    <div class="card-body">
                        @foreach (var userAction in Model.UserActions)
                        {
                            <div class="card-list-item">
                                <span class="card-list-item-name">@userAction.User</span>
                                <div class="d-flex w-50 justify-content-between">
                                    <span class="card-list-item-count" style="background: #FF0000;" title="Overdue">@userAction.Overdue</span>
                                    <span class="card-list-item-count" style="background: #F7A54A;" title="Upcoming">@userAction.Upcoming</span>
                                    <span class="card-list-item-count" style="background: #7CBB00;" title="Registered">@userAction.Registered</span>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
            <div style="padding-left:80px; width:600px;">
                <div class="card">
                    <div class="card-header">
                        <h6>Opportunities by Customer</h6>
                    </div>
                    <div class="card-body">
                        @foreach (var opportunityCustomer in Model.TotalOpportunitiesByCustomer)
                        {
                            <div class="card-list-item">
                                <span class="card-list-item-name">@opportunityCustomer.Key.Name</span>
                                <div class="d-flex justify-content-between">
                                    <span class="card-list-item-count" style="background: #7CBB00;">@opportunityCustomer.Value</span>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
           
        </div>
        
    </div>

    <div class="panel panel-default breakhere">
        <div class="panel-heading">
            <h2 class="panel-title">Leads </h2>
        </div>
        <br />
        <div class="panel-body">
            <div>
                @if (Model.Leads != null && Model.Leads.Any())
                {
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th style="text-align:center; width: 10%">Lead ID.</th>
                                <th style="text-align:center; width: 10%">Company</th>
                                <th style="text-align:center; width: 15%">Service</th>
                                <th style="text-align:center; width: 10%">Created Date</th>
                                <th style="text-align:center; width: 8%">Last Updated</th>
                                <th style="text-align:center; width: 10%">Contacts</th>
                                <th style="text-align:center; width: 8%">Stage</th>
                                <th style="text-align:center; width: 3%">Att.</th>
                                <th style="text-align:center; width: 3%">Ev.</th>
                                <th style="text-align:center; width: 3%">Act.</th>
                            <th style="text-align:center; width: 20%">Created By</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for (var i = 0; i < Model.Leads.Count; i++)
                            {
                                <tr>
                                    <td style="text-align:center; width: 10%">@Model.Leads[i].GridName</td>
                                    <td style="text-align:center; width: 10%">@Model.Leads[i].CompanyName</td>
                                    <td style="text-align:center; width: 10%">@Model.Leads[i].Services</td>
                                    <td style="text-align:center; width: 15%">@Model.Leads[i].CreatedDate</td>
                                    <td style="text-align:center; width: 8%">@Model.Leads[i].ModifiedDate</td>
                                    <td style="text-align:center; width: 10%">@Model.Leads[i].Contacts</td>
                                    <td style="text-align:center; width: 8%">@Model.Leads[i].LeadStageDescription</td>
                                    <td style="text-align:center; width: 3%">@Model.Leads[i].TotalLeadAttachments</td>
                                    <td style="text-align:center; width: 3%">@Model.Leads[i].OpportunityEventCount</td>
                                    <td style="text-align:center; width: 3%">@Model.Leads[i].OpportunityActionCount</td>
                                    <td style="text-align:center; width: 20%">@Model.Leads[i].CreatedBy</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                }
            </div>
        </div>
    </div>

    <div class="panel panel-default breakhere">
        <div class="panel-heading">
            <h2 class="panel-title">Opportunities </h2>
        </div>
        <br />
        <div class="panel-body">
            <div>
                @if (Model.Opportunities != null && Model.Opportunities.Any())
                {
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th style="text-align:center; width: 10%">Opportunity ID.</th>
                                <th style="text-align:center; width: 10%">Company</th>
                                <th style="text-align:center; width: 10%">Fields</th>
                                <th style="text-align:center; width: 10%">Wells</th>
                                <th style="text-align:center; width: 10%">Conveyance</th>
                                <th style="text-align:center; width: 8%">Closeout</th>
                                <th style="text-align:center; width: 8%">Mobilisation</th>
                                <th style="text-align:center; width: 10%">Contacts</th>
                                <th style="text-align:center; width: 8%">Stage</th>
                                <th style="text-align:center; width: 3%">Att.</th>
                                <th style="text-align:center; width: 3%">Ev.</th>
                                <th style="text-align:center; width: 3%">Act.</th>
                                <th style="text-align:center; width: 18%">Created By</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for (var i = 0; i < Model.Opportunities.Count; i++)
                            {
                                <tr>
                                    <td style="text-align:center; width: 10%">@Model.Opportunities[i].GridName</td>
                                    <td style="text-align:center; width: 10%">@Model.Opportunities[i].CompanyName</td>
                                    <td style="text-align:center; width: 10%">@Model.Opportunities[i].CompanyFields</td>
                                    <td style="text-align:center; width: 10%">@Model.Opportunities[i].CompanyWells</td>
                                    <td style="text-align:center; width: 10%">@Model.Opportunities[i].Conveyances</td>
                                    <td style="text-align:center; width: 8%">@Model.Opportunities[i].CloseoutDateOnly</td>
                                    <td style="text-align:center; width: 8%">@Model.Opportunities[i].MobilisationDateOnly</td>
                                    <td style="text-align:center; width: 10%">@Model.Opportunities[i].Contacts</td>
                                    <td style="text-align:center; width: 8%">@Model.Opportunities[i].StageDescription</td>
                                    <td style="text-align:center; width: 3%">@Model.Opportunities[i].TotalOpportunityAttachments</td>
                                    <td style="text-align:center; width: 3%">@Model.Opportunities[i].OpportunityEventCount</td>
                                    <td style="text-align:center; width: 3%">@Model.Opportunities[i].OpportunityActionCount</td>
                                    <td style="text-align:center; width: 18%">@Model.Opportunities[i].CreatedBy</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                }
            </div>
        </div>
    </div>

    <div class="panel panel-default breakhere">
        <div class="panel-heading">
            <h2 class="panel-title">Events </h2>
        </div>
        <br />
        <div class="panel-body">
            <div>
                @if (Model.OpportunityEvents != null && Model.OpportunityEvents.Any())
                {
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th style="text-align:center; width: 10%">Event ID.</th>
                                <th style="text-align:center; width: 10%">Lead / Opportunity ID</th>
                                <th style="text-align:center; width: 15%">Contact (s)</th>
                                <th style="text-align:center; width: 15%">Topic</th>
                                <th style="text-align:center; width: 10%">Event Date</th>
                                <th style="text-align:center; width: 15%">Follow-up Actions</th>
                                <th style="text-align:center; width: 10%">Created</th>
                                <th style="text-align:center; width: 15%">Created By</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for (var i = 0; i < Model.OpportunityEvents.Count; i++)
                            {
                                <tr>
                                    <td>@Model.OpportunityEvents[i].NewName</td>
                                    <td style="text-align:center; width: 10%">@Model.OpportunityEvents[i].OpportunityName</td>
                                    <td style="text-align:center; width: 10%">@Model.OpportunityEvents[i].CompanyContacts</td>
                                    <td style="text-align:center; width: 15%">@Model.OpportunityEvents[i].Topic</td>
                                    <td style="text-align:center; width: 15%">@Model.OpportunityEvents[i].EventDateTime</td>
                                    <td style="text-align:center; width: 10%">@Model.OpportunityEvents[i].TotalEventActionCount</td>
                                    <td style="text-align:center; width: 10%">@Model.OpportunityEvents[i].CreatedDate</td>
                                    <td style="text-align:center; width: 15%">@Model.OpportunityEvents[i].CreatedBy</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                }
            </div>
        </div>
    </div>

    <div class="panel panel-default">
        <div class="panel-heading">
            <h2 class="panel-title">Actions </h2>
        </div>
        <br />
        <div class="panel-body">
            <div>
                @if (Model.OpportunityActions != null && Model.OpportunityActions.Any())
                {
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th style="text-align:center; width:15%">Action ID.</th>
                                <th style="text-align:center; width:15%">Lead / Opportunity ID</th>
                                <th style="text-align:center; width:15%">Assignee</th>
                                <th style="text-align:center; width:10%">Target Date</th>
                                <th style="text-align:center; width:10%">Completed Date</th>
                                <th style="text-align:center; width:10%">Created</th>
                                <th style="text-align:center; width:15%">Created By</th>
                                <th style="text-align:center; width:10%">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for (var i = 0; i < Model.OpportunityActions.Count; i++)
                            {
                                <tr>
                                    <td>@Model.OpportunityActions[i].NewName</td>
                                    <td style="text-align:center; width:15%">@Model.OpportunityActions[i].OpportunityName</td>
                                    <td style="text-align:center; width:15%">@Model.OpportunityActions[i].AssignedUserName</td>
                                    <td style="text-align:center; width:15%">@Model.OpportunityActions[i].TargetDateOnly</td>
                                    <td style="text-align:center; width:10%">@Model.OpportunityActions[i].CompletedDateOnly</td>
                                    <td style="text-align:center; width:10%">@Model.OpportunityActions[i].CreatedDate</td>
                                    <td style="text-align:center; width:15%">@Model.OpportunityActions[i].CreatedBy</td>
                                    <td style="text-align:center; width:10%">@Model.OpportunityActions[i].Status</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                }
            </div>
        </div>
    </div>
</div>
