﻿@model UserModel

@{
    ViewData["Title"] = "Users";
    ViewData["ContentTitleIcon"] = "fa fa-users";
    var datetimeNow = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss");
}

@Html.Partial( "_GridNotification", EntityType.User)
<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-users"></i>
        Users
        (<span data-bind="text:totalUsers"></span>)
    </h4>
</div>
<hr />


<div class="mb-3">
    <div>
        @(Html.Kendo().Grid<UserModel>()
            .Name("userGrid")
            .Columns(c => {
                c.Bound(p => p.Name).Title("Name").ClientTemplate("<a href='" + @Url.Action("EditUser", "Admin", new { @id = "" }) + "/#=UserId#'>#=Name#</a>");
                c.<PERSON>(p => p.JobTitle);
                c.<PERSON>(p => p.RolesList).ClientTemplate("#=rolesTemplate(RolesList)#").Width(260);
                c.Bound(p => p.WorkTelephone);
                c.Bound(p => p.EmailAddress).ClientTemplate("<a href='mailto:#=EmailAddress#'>#=EmailAddress#</a>");
                c.Bound(p => p.LastLogin).Format(DateConstants.DateTimeFormat);
                c.Bound(p => p.BaseCompanyLocationName).Title("Base");
                c.Bound(p => p.IsEnabled).Title("Enabled").ClientTemplate("#if(IsEnabled){#Yes#}else{#No#}#");
                c.Bound(p => p.IsNonEmployeeAccount).Title("Non-Employee Account").ClientTemplate("#if(IsNonEmployeeAccount){#Yes#}else{#No#}#");
                c.Command(com=>{
                        com.Destroy().HtmlAttributes(new { @class = "bg-danger text-white grid-action-button" }).Visible("showCommand");
                });
            })
            .Sortable()
            .Filterable()
            .Groupable()
            .Editable(e => e.Mode(GridEditMode.InLine))
            .Scrollable()
            .Resizable(c => c.Columns(true))
            .ColumnMenu(c => c.Columns(true))
            .Events(e => e.DataBound("updateUserTotal"))
            .Excel(excel => excel
                .FileName(string.Format("Centerpoint_Personnel_Employees_Export_{0}.xlsx", DateTime.Now.ToString(DateConstants.DateTimeFormat)))
                .Filterable(true)
                .ProxyURL(Url.Action("Export", "Admin"))
            )
            .DataSource(dataSource => dataSource
                .Ajax()
                .Model(m => {
                    m.Id(p => p.UserId);
                })
                .Events(e => e.Error("onError").RequestEnd("onRequestEnd"))
                .Read(read => read.Action("GetAllUsers", "Admin"))
                .Destroy(destroy => destroy.Action("DeleteUser", "Admin"))
            )
        )
    </div>
</div>

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-user"></i>
        @(Model.UserId.HasValue ? string.Format("Edit User Details - {0}", Model.Name) : "Add New User")
    </h4>
</div>
<hr />

<div>
    <div>

            @(Html.Kendo().TabStrip()
                .Name("editUserTabStrip")
                .SelectedIndex(0)
                .Animation(animation =>{ 
                    animation.Enable(false); 
                    })
                .Items(tabstrip => {

                    tabstrip.Add()
                    .Text("Account Details")
                    .HtmlAttributes(new { @data_bind="html:accountDetailsText"})
                    .Selected(true)
                    .Content(@<text>
                                    <div id="addUserForm">                            
                                        <div class="row">
                                            <div class="col-md-4">
                                               <div class="form-group">
                                                    <label>Name</label>
                                                    @(Html.Kendo().TextBoxFor(m => m.Name))
                                                </div>
                                                <div class="form-group">
                                                    <label>Email Address</label>
                                                    @(Html.Kendo().TextBoxFor(m => m.EmailAddress))
                                                </div>
                                                <div class="form-group">
                                                    <div class="form-group input-group">
                                                        @Html.LabelFor(m => m.IsEnabled)
                                                        @Html.CheckBoxFor(m => m.IsEnabled, new { @class = "checkbox ml-2" })
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label>Job Title</label>
                                                    @(Html.Kendo().TextBoxFor(m => m.JobTitle))
                                                </div>
                                                <div class="form-group">
                                                    <label>Work Telephone</label>
                                                    @(Html.Kendo().TextBoxFor(m => m.WorkTelephone))
                                                </div>
                                                <div class="form-group">
                                                    <div class="form-group input-group">
                                                        <label for="IsNonEmployeeAccount">Non-Employee Account</label>
                                                        <input type="checkbox" id="IsNonEmployeeAccount" name="IsNonEmployeeAccount" class="checkbox ml-2">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                @if (Html.IsGlobalAdmin()||Html.IsPersonnelAdministrator()) {
                                                    <div class="form-group input-group">
                                                        @Html.LabelFor(m => m.Roles)
                                                        @(Html.Kendo().MultiSelectFor(m=>m.RolesList)
                                                                    .Placeholder("Select Role")
                                                                    .Filter(FilterType.Contains)
                                                                    .DataValueField("Value")
                                                                    .DataTextField("Text")                                                                    
                                                                    .AutoBind(true)
                                                                    .ValuePrimitive(true)
                                                                    .BindTo(UserRoleConstant.GetRolesSelectListItems()))
                                                    </div>      
                                                    } else {
                                                    <div class="form-group input-group">
                                                        @Html.LabelFor(m => m.Roles)
                                                        @(Html.Kendo().MultiSelectFor(m => m.Roles)
                                                        .HtmlAttributes(new { @data_value_primitive = "true", @readonly = "readonly"})
                                                        .DataValueField("Key")
                                                        .DataTextField("Value")
                                                        .BindTo(UserRoleConstant.ValuesAndDescriptions.ToList()))
                                                    </div>
                                                    }
                                                <div class="form-group input-group">
                                                    @Html.LabelFor(m => m.BaseCompanyLocationId)
                                                    @(Html.Kendo().DropDownListFor(m => m.BaseCompanyLocationId)
                                                    .Filter(FilterType.Contains)
                                                    .OptionLabel("Select Base Location")
                                                    .DataTextField("Text")
                                                    .DataValueField("Value")
                                                    .DataSource(d => d.Read("GetBaseCompanyLocations", "Lookup"))
                                                    )
                                                </div>
                                            </div>             
                                        </div>
                                    </div>        
                                    <button  onclick="saveUserHandler(event)" type="button" class="btn btn-primary btn-sm">Save User Details</button>
                                </text>);
                    
                    if (Model.UserId.HasValue) {
                        tabstrip.Add()
                        .Text("Personnel Details")
                        .HtmlAttributes(new { @data_bind="html:personnelDetailsText"})
                        .Content(@<text>
                                            <div>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-group input-group">
                                                            @Html.LabelFor(m => m.DateOfBirth)<br />
                                                            @(Html.Kendo().DatePickerFor(m => m.DateOfBirth).HtmlAttributes(new { @style = "font-size:14px" }))
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label>Home Telephone</label>
                                                            <br />
                                                            @(Html.Kendo().TextBoxFor(m => m.HomeTelephone).HtmlAttributes(new { @class = "form-control", @style = "font-size:14px" }))
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label>Mobile Telephone</label>
                                                            <br />
                                                            @(Html.Kendo().TextBoxFor(m => m.MobileTelephone).HtmlAttributes(new { @class = "form-control", @style = "font-size:14px" }))
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <div class="form-group input-group">
                                                            @Html.LabelFor(m => m.Address)  
                                                            @Html.TextAreaFor(m => m.Address, new { @class = "form-control", @style = "width:100%;height:141px" })
                                                        </div>
                                                    </div>
                                                </div>
                                                @Html.HiddenFor(m => m.UserId)
                                                <button  onclick="saveUserHandler(event)" type="button" class="btn btn-primary btn-sm">Save User Details</button>
                                            </div>
                                        </text>);

                        
                        tabstrip.Add()
                        .Text("Emergency Contact Details")
                        .HtmlAttributes(new { @data_bind="html: emergencyContactDetailsText"})
                        .Content(@<text>
                                    <div>
                                        <div class="card mb-2">
                                            <div class="card-header">
                                                    <h6 class="mb-0"> Emergency Contact Details (NOK 1)</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label>Contact Name</label>
                                                            <br />
                                                            @(Html.Kendo().TextBoxFor(m => m.ContactName).HtmlAttributes(new { @class = "form-control", @style = "font-size:14px" }))
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label>Contact Relationship</label>
                                                            <br />
                                                            @(Html.Kendo().TextBoxFor(m => m.ContactRelationship).HtmlAttributes(new { @class = "form-control", @style = "font-size:14px" }))
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label>Home Telephone</label>
                                                            <br />
                                                            @(Html.Kendo().TextBoxFor(m => m.HomeTelephone1).HtmlAttributes(new { @class = "form-control", @style = "font-size:14px" }))
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label>Work Telephone</label>
                                                            <br />
                                                            @(Html.Kendo().TextBoxFor(m => m.WorkTelephone1).HtmlAttributes(new { @class = "form-control", @style = "font-size:14px" }))
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label>Mobile Telephone</label>
                                                            <br />
                                                            @(Html.Kendo().TextBoxFor(m => m.MobileTelephone1).HtmlAttributes(new { @class = "form-control", @style = "font-size:14px" }))
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <div class="form-group input-group">
                                                            @Html.LabelFor(m => m.Address1)
                                                            @Html.TextAreaFor(m => m.Address1, new { @class = "form-control", @style = "width:100%;height:141px" })
                                                        </div>
                                                    </div>
                                                </div>                                            
                                            </div>
                                        </div>

                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0"> Emergency Contact Details (NOK 2)</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label>Contact Name</label>
                                                            <br />
                                                            @(Html.Kendo().TextBoxFor(m => m.ContactName1)
                                                            .HtmlAttributes(new { @class = "form-control", @style = "font-size:14px" }))
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label>Contact Relationship</label>
                                                            <br />
                                                            @(Html.Kendo().TextBoxFor(m => m.ContactRelationship1)
                                                                .HtmlAttributes(new { @class = "form-control", @style = "font-size:14px" }))
                                                        </div>
                                                    </div>
                                                </div>             
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label>Home Telephone</label>
                                                            <br />
                                                            @(Html.Kendo().TextBoxFor(m => m.HomeTelephone2)
                                                            .HtmlAttributes(new { @class = "form-control", @style = "font-size:14px" }))
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label>Work Telephone</label>
                                                            <br />
                                                            @(Html.Kendo().TextBoxFor(m => m.WorkTelephone2)
                                                            .HtmlAttributes(new { @class = "form-control", @style = "font-size:14px" }))
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label>Mobile Telephone</label>
                                                            <br />
                                                            @(Html.Kendo().TextBoxFor(m => m.MobileTelephone2)
                                                            .HtmlAttributes(new { @class = "form-control", @style = "font-size:14px" }))
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <div class="form-group input-group">
                                                            @Html.LabelFor(m => m.Address2)
                                                            @Html.TextAreaFor(m => m.Address2, new { @class = "form-control", @style = "width:100%;height:141px" })
                                                        </div>
                                                    </div>
                                                </div>
                                                @Html.HiddenFor(m => m.UserId)
                                                @Html.HiddenFor(m => m.LastLogin)
                                            </div>
                                        </div>
                                        <button  onclick="saveUserHandler(event)" type="button" class="btn btn-primary btn-sm mt-3">Save User Details</button>
                                    </div>
                                    
                            </text>
        );
                        
                            tabstrip.Add()
                            .Text("Medical")
                            .HtmlAttributes(Html.IsPersonnelAdministrator() ? new { @data_bind = "html: medicalText" } 
                                                                            : new { style = "display:none", @data_bind = "html: medicalText" })
                            .Content(
                            @<text>
                                <div id="medical">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-group input-group">
                                                <label>Blood Type</label>
                                                <br />
                                                    @(Html.Kendo().DropDownListFor(m => m.BloodType)
                                                    .HtmlAttributes(new {@style = "font-size:14px" })
                                                    .Filter(FilterType.Contains)
                                                    .OptionLabel("Select Type")
                                                    .DataValueField("Key")
                                                    .DataTextField("Value")
                                                    .BindTo(BloodTypeConstant.ValuesAndDescriptions.ToList()))
                                                </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group input-group">
                                                <label>Organ Donor</label>
                                                <br />
                                                    @(Html.Kendo().DropDownListFor(m => m.OrganDonor)
                                                    .DataValueField("Key")
                                                    .DataTextField("Value")
                                                    .Filter(FilterType.Contains)
                                                    .BindTo(Html.BooleanListValues().ToList())
                                                    .HtmlAttributes(new { @style = "font-size:14px" }))
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group input-group">
                                                <label>Medical Conditions</label>
                                                @Html.TextAreaFor(m => m.MedicalConditions, new { @class = "form-control", @style = "width:100%; height:141px" })
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group input-group">
                                                @Html.LabelFor(m => m.AllergiesAndReaction)
                                                @Html.TextAreaFor(m => m.AllergiesAndReaction, new { @class = "form-control", @style = "width:100%; height:141px" })
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group input-group">
                                                @Html.LabelFor(m => m.Medications)
                                                @Html.TextAreaFor(m => m.Medications, new { @class = "form-control", @style = "width:100%; height:141px" })
                                            </div>
                                        </div>
                                    </div>
                                    @Html.HiddenFor(m => m.UserId)
                                    @Html.HiddenFor(m => m.LastLogin)
                                    <button onclick="saveUserHandler(event)" type="button" class="btn btn-primary btn-sm">Save User Details</button>
                                </div>
                            </text>);
                        

                    tabstrip.Add()
                    .Text("Certificates")
                    .HtmlAttributes(new { @data_bind="html: certificatesText"})
                    .Content(@<text>

                                            @if (Model.UserId.HasValue) {
                                                <div id="certificates">
                                                    <p>Click the link below to attach personnel certificate</p>
                                                    <button type="button" class="btn btn-primary mb-2"  data-bind="click:showAttachmentPopup">Attach Documents</button>
                                                    <div>
                                                        @(Html.Kendo().Grid<PersonnelCertificateDocumentModel>()
                                            .Name("personnelCertificateDocumentGrid")
                                            .Columns(c => {
                                                c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='" + @Url.Action("Index", "Document", new { @id = "#=DocumentId#" }) + "'>#=FileName ? FileName : ''#</a>").Width(250);
                                                c.Bound(p => p.CertificateCategoryId).EditorTemplateName("CertificateCategory").Title("Category").Width(250).ClientTemplate("#=CertificateCategoryName ? CertificateCategoryName : ''#");
                                                c.Bound(p => p.Details).Title("Details").Width(200);
                                                c.Bound(p => p.WarningThreshold).EditorTemplateName("WarningThreshold").Title("Expiry warning (weeks)").Width(80);
                                        c.Bound(p => p.AquisitionDate).EditorTemplateName("AquisitionDate").Title("Aquisition Date").Format(DateConstants.DateFormat).Width(120);
                                        c.Bound(p => p.ExpiryDate).EditorTemplateName("ExpiryDate").Title("Expiry Date").ClientTemplateId("ExpiryDateTemplate").Format(DateConstants.DateFormat).Width(120);
                                        c.Bound(p => p.DateCreated).Format(DateConstants.DateFormat).Title("Created").Width(120);
                                                c.Command(c=>{
                                                    c.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                                                    c.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"});
                                                }).Width(200);
                                            })
                                            .ToolBar(t => {
                                                t.Create().Text("Add New Entry Without Certificate").HtmlAttributes(new { @style = "background-color:#39b3d7 !important" });
                                            })
                                            .Events(e => e.DataBound("updateUserCertificateTotal"))
                                            .Sortable()
                                            .Resizable(r => r.Columns(true))
                                            .ColumnMenu(c => c.Columns(true))
                                            .Filterable()
                                            .Groupable()
                                            .Editable(editable => editable.Mode(GridEditMode.InLine))
                                            .Scrollable(s => s.Height(300))
                                            .DataSource(dataSource => dataSource
                                            .Ajax()
                                            .ServerOperation(false)
                                            .Model(model => model.Id(p => p.PersonnelCertificateDocumentId))
                                            .Events(e => e.Error("onError"))
                                            .Read(read => read.Action("GetPersonnelCertificates", "Admin", new { @uId = Model.UserId }))
                                            .Create(create => create.Action("UpdatePersonnelCertificate", "Admin", new { @uId = Model.UserId }).Data("personnelCertificateDocumentData"))
                                            .Update(update => update.Action("UpdatePersonnelCertificate", "Admin", new { @uId = Model.UserId }).Data("personnelCertificateDocumentData"))
                                            .Destroy(destroy => destroy.Action("DeletePersonnelCertificate", "Admin"))))                                               
                                                    </div>
                                                </div>
                            }

                                        </text>);

                    tabstrip.Add()
                    .Text("Notes")
                    .HtmlAttributes(new { @data_bind="html: notesText"})
                    .Content(@<text>
                                <div id="notes">
                                    @(Html.Kendo().Grid<UserNoteModel>()
                                    .Name("userNoteGrid")
                                    .Columns(c => {
                                        c.Bound(p => p.Note).Encoded(false).ClientTemplate("#= getHtmlNewLinesString(Note) #");
                                        c.Bound(p => p.Date).Width(125).Format(DateConstants.DateTimeFormat);
                                        c.Command(c=>{
                                            c.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                                            c.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"});
                                        }).Width(175);
                                    })
                                    .Editable(editable => editable.Mode(GridEditMode.PopUp).DisplayDeleteConfirmation("Are you sure you want to delete this note?").TemplateName("UserNoteWindow")
                                    .Window(w => w.Name("UserNoteWindow")
                                                    .Title("User Notes")
                                                    .Width(850)
                                                    .Draggable(false)
                                                    ))
                                    .ToolBar(t => t.ClientTemplateId("userNoteGridToolbar"))
                                    .Sortable()
                                    .Filterable()
                                    .Groupable()
                                    .Events(e => e.Edit("userNoteEdit"))
                                    .Events(e => e.DataBound("updateUserNoteTotal"))
                                    .Scrollable(s => s.Height(500))
                                    .Resizable(c => c.Columns(true))
                                    .ColumnMenu(c => c.Columns(true))
                                    .DataSource(dataSource => dataSource
                                        .Ajax()
                                        .ServerOperation(false)
                                        .Model(m => m.Id(p => p.UserNoteId))
                                        .Read(read => read.Action("GetUserNotes", "Admin", new { @userId = Model.UserId }))
                                        .Create(create => create.Action("UpdateUserNote", "Admin", new { @uId = Model.UserId }).Data("usetNoteData"))
                                        .Update(update => update.Action("UpdateUserNote", "Admin", new { @uId = Model.UserId }).Data("usetNoteData"))
                                        .Destroy(destroy => destroy.Action("DeleteUserNote", "Admin"))
                                        ))
                                </div>
                            </text>);
                    }
                })
            )
            @Html.ValidationSummary(false)
            @Html.HiddenFor(m => m.UserId)
            @Html.HiddenFor(r => r.RoleDescription)  
    </div>
</div>



    @(Html.Kendo().Window().Name("attachDocumentWindow")
         .Title("Add New Entry With Certificate")
         .Content(@<text><partial name="AttachEntryWithCertificate"/></text>)
                       .Width(500)
                       .Modal(true)
                       .Draggable()
                       .Visible(false)
    )

    @(Html.Kendo().Window().Name("personnelReplacementWindow")
         .Title("Personnel Replacement")
         .Content(@<text><partial name="PersonnelReplacement" /></text>)
                       .Width(500)
                       .Modal(true)
                       .Draggable()
                       .Events(e => e.Close("cancelReplace"))
                       .Visible(false))

    <script>
        $(document).ready(function () {
                var personnelCertificateDocumentGrid = $('#personnelCertificateDocumentGrid').data("kendoGrid");
                var userGrid = $('#userGrid').data("kendoGrid");
                var userNoteGrid = $('#userNoteGrid').data("kendoGrid");

            if ("@Model.UserId"){
                personnelCertificateDocumentGrid.bind('dataBound', function (e) {
                    this.element.find('.k-add').remove();
                });
                userNoteGrid.bind('dataBound', function (e) {
                    this.element.find('.k-edit').remove();
                    this.element.find('.k-delete').remove();
                });
            }
                userGrid.bind('dataBound', function (e) {
                    this.element.find('.k-i-excel').remove();
                });
        });

        function showCommand(dataItem){
                return ("@Model.UserId" !== dataItem.Id)
        }

        function getHtmlNewLinesString(text) {
            var regexp = new RegExp('\n', 'g');
            return text ? text.replace(regexp, '<br>') : text;
        }

        function userNoteEdit(e) {
            $(e.container).find(".k-edit-buttons").html("<a class='btn btn-success btn-sm k-grid-update' href='#'><i class='fa fa-check-circle'></i>Update</a> " +
               "<a class='btn btn-warning btn-sm k-grid-cancel' href='#'><i class='fa fa-ban'></i>Cancel</a>");
        }

        function onError(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var personnelCertificateDocumentGrid = $("#personnelCertificateDocumentGrid").data("kendoGrid");
                personnelCertificateDocumentGrid.dataSource.cancelChanges();
            }

            onErrorDeleteGridHandler(e, "#userGrid");
        }

        function onPersonnelCertificateAttached() {
            var personnelCertificateDocumentGrid = $("#personnelCertificateDocumentGrid").data("kendoGrid");
            personnelCertificateDocumentGrid.dataSource.read();
        }

        function onPersonnelCertificateDocumentUpload(e) {
            uploadValidation(e);

            let dropdownlist = $("#certificateCategory").data("kendoDropDownList")
            e.data={
                certificateCategoryId: dropdownlist.value()
            }
            $(".k-upload-files.k-reset").show();
        }

        function onPersonnelCertificateDocumentComplete(e) {
            $("#attachDocumentWindow").data("kendoWindow").close();
            $("#certificateCategory").data("kendoDropDownList").select(0)
            $(".k-upload-files.k-reset").find("li").remove();
            $(".k-upload-files.k-reset").slideUp();
        }

        function updateUserTotal() {
            var userGrid = $("#userGrid").data("kendoGrid");
            var totalUsers = userGrid.dataSource.total();
            viewModel.set("totalUsers", totalUsers);
        }

        function updateUserNoteTotal() {
            var userNoteGrid = $("#userNoteGrid").data("kendoGrid");
            var totalUserNotes = userNoteGrid.dataSource.total();
            viewModel.set("totalUserNotes", totalUserNotes);
        }
        function usetNoteData(data) {
            data.Date = toUTCString(data.Date);
        }

        function updateUserCertificateTotal() {
            var personnelCertificateDocumentGrid = $("#personnelCertificateDocumentGrid").data("kendoGrid");
            var totalUserCertificates = personnelCertificateDocumentGrid.dataSource.total();
            viewModel.set("totalUserCertificates", totalUserCertificates);
        }

        function personnelCertificateDocumentData(e) {
            e.ExpiryDate = toUTCString(e.ExpiryDate);
            e.DateCreated = toUTCString(e.DateCreated);
            e.AquisitionDate = toUTCString(e.AquisitionDate);
        }


        function composeRoles() {
            viewModel.set("composedRoles", $("#RolesDropDown").data("kendoMultiSelect").value().join())
        }

        function changeDropdownValue(e) {
            const dropdownlist = e.sender.value();
            const fileUploader = $('#personnelCertificateAttachmentDocuments').data("kendoUpload")
            if(dropdownlist){
                fileUploader.enable();
            }else{
                fileUploader.disable();
            }
        }

        var viewModel = new kendo.observable({
            accountDetailsText:function(){
                return `<span><i class="fa fa-file-text mr-2"></i>Account Details</span>`;
            },
            personnelDetailsText:function(){
                return `<span><i class="fa fa-phone mr-2"></i>Personnel Details</span>`;
            },
            emergencyContactDetailsText:function(){
                return `<span><i class="fa fa-mobile-phone mr-2"></i>Emergency Contact Details</span>`;
            },
            medicalText:function(){
                return `<span><i class="fa fa-medkit mr-2"></i>Medical</span>`;
            },
            totalUsers: 0,
            totalUserCertificates: 0,
            composedRoles: null,
            userDetails: {},
            rolesFromModel: function(){
                let initialRoleString = "@Model.Roles"
                let arrayOfRoles = initialRoleString.split(',')
                return arrayOfRoles
            },
            certificatesText: function(){
                return `<span><i class="fa fa-file-archive mr-2"></i>Certificates (<span data-bind="text:totalUserCertificates"></span>)</span>`;
            },
            totalUserNotes: 0,
            notesText:function(){
                return `<span><i class="fa fa-file mr-2"></i>Notes (<span data-bind="text:totalUserNotes"></span>)</span>`;
            },
            showAttachmentPopup: function () {
                $("#attachDocumentWindow").data("kendoWindow").center().open();
            },
        });

        kendo.bind(document.body.children, viewModel);

    </script>
<script id="ExpiryDateTemplate" type="text/x-kendo-tmpl">
    #var currentDateTime = new Date();#
    #if(ExpiryDate && ExpiryDate > currentDateTime){#
        <span> #=kendo.toString(new Date(ExpiryDate), 'dd-MM-yyyy')# </span>
    #}else if(ExpiryDate){#
        <span style='color:\\#E31E33'>#=kendo.toString(new Date(ExpiryDate), 'dd-MM-yyyy')#</span>
    #}#
</script>

<script id="userNoteGridToolbar" type="text/x-kendo-tmpl">   
        <a href='/Admin/GetUserNotes/" + @Model.UserId + "?userId=" + @Model.UserId + "&userNoteGrid-mode=insert' class='btn btn-primary btn-sm k-grid-add'>
            <i class='fa fa-plus'></i>
            Add Notes
        </a>
    </script>    

    <script type="text/kendo" id="rolesTemplate">
        <ul class="rolesTemplateUl">
            #for(var i = 0; i< data.length; i++){#
                    <li>#:data[i].Text#</li>
            #}#
        </ul>
    </script>

    <script type="text/javascript">
        var rolesTemplate = kendo.template($("#rolesTemplate").html(), { useWithBlock: false });
    </script>

     <script type="text/javascript">

         $(document).ready( function(){
            submitSuccessHandler($("#Name").val());     

            $('#IsEnabled').change(function () {
                if (!$(this).is(':checked')) {
                    $("#personnelReplacementWindow").data("kendoWindow").center().open()
                }
            });
         });

        function saveUserHandler (event) {
            event.preventDefault();
            let name = $("#Name").val();
            const items = $("#RolesList").data("kendoMultiSelect").value();
            let rolesListData = $("#RolesList").data("kendoMultiSelect").dataSource.data().filter(r => items.includes(r.Value));
            const rolesList = [];
            rolesListData.forEach(i => {
                rolesList.push({
                    Selected: i.Selected,
                    Text: i.Text,
                    Value: i.Value,
                })
            })

            let isValid = $('#addUserForm').kendoValidator().data('kendoValidator').validate();
            if(isValid){
                if("@Model.UserId"){                    
                    updateUser(name, items, rolesListData, rolesList);
                }else{
                    createUser(name, items, rolesListData, rolesList);
            }
            }  
        }

            function updateUser(name, items, rolesListData, rolesList){
                let userId = "@Model.UserId";
                let model = {
                    UserId: Number(userId),
                    RolesList: rolesList,
                    Name: name,
                    EmailAddress: $("#EmailAddress").data("kendoTextBox").value(),
                    IsEnabled: $('#IsEnabled').is(':checked'),
                    JobTitle: $("#JobTitle").data("kendoTextBox").value(),
                    WorkTelephone: $("#WorkTelephone").data("kendoTextBox").value(),
                    IsNonEmployeeAccount: $('#IsNonEmployeeAccount').is(':checked'),
                    BaseCompanyLocationId: $("#BaseCompanyLocationId").data("kendoDropDownList").value(),

                    DateOfBirth: $("#DateOfBirth").val(),
                    HomeTelephone: $("#HomeTelephone").data("kendoTextBox").value(),
                    MobileTelephone: $("#MobileTelephone").data("kendoTextBox").value(),
                    Address: $("#Address").val(),

                    ContactName: $("#ContactName").data("kendoTextBox").value(),
                    ContactRelationship: $("#ContactRelationship").data("kendoTextBox").value(),
                    HomeTelephone1: $("#HomeTelephone1").val(),
                    WorkTelephone1: $("#WorkTelephone1").data("kendoTextBox").value(),
                    MobileTelephone1: $("#MobileTelephone1").data("kendoTextBox").value(),
                    Address1: $("#Address1").val(),
                    ContactName1: $("#ContactName1").data("kendoTextBox").value(),
                    ContactRelationship1: $("#ContactRelationship1").data("kendoTextBox").value(),
                    HomeTelephone2: $("#HomeTelephone2").data("kendoTextBox").value(),
                    WorkTelephone2: $("#WorkTelephone2").data("kendoTextBox").value(),
                    MobileTelephone2: $("#MobileTelephone2").data("kendoTextBox").value(),
                    Address2: $("#Address2").val(),

                    BloodType: $("#BloodType").data("kendoDropDownList").value(),
                    OrganDonor: $("#OrganDonor").data("kendoDropDownList").value(),
                    MedicalConditions: $("#MedicalConditions").val(),
                    AllergiesAndReaction: $("#AllergiesAndReaction").val(),
                    Medications: $("#Medications").val(),             
               }
               $.ajax({
                    type: 'POST',
                    url: '/Admin/EditUser',
                    dataType: "json",
                    data: {
                        model: model
                    },
                    success: function(e) {
                        updateMessage(name);
                        var userGrid = $("#userGrid").data("kendoGrid");
                        userGrid.dataSource.read();
                    },
                    error: function(e) {
                    },
                })
            }
                    
            
            function createUser(name, items, rolesListData, rolesList){
                let model = {
                    RolesList: rolesList,
                    Name: name,
                    EmailAddress: $("#EmailAddress").data("kendoTextBox").value(),
                    IsEnabled: $('#IsEnabled').is(':checked'),
                    JobTitle: $("#JobTitle").data("kendoTextBox").value(),
                    WorkTelephone: $("#WorkTelephone").data("kendoTextBox").value(),
                    IsNonEmployeeAccount: $('#IsNonEmployeeAccount').is(':checked'),
                    BaseCompanyLocationId: $("#BaseCompanyLocationId").data("kendoDropDownList").value(),
                }                

                $.ajax({
                    type: 'POST',
                    url: '/Admin/CreateUser',
                    dataType: "json",
                    data: {
                        model: model
                    },
                    success: function(e) {
                        window.location.href = "/Admin/EditUser/" + e.id + "?isCreated=true";                        
                    },
                })
             }



    </script>

