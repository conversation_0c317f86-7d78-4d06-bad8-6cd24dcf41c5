﻿@model ShipmentMethodModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-road"></i>
        Shipment Method
        (<span data-bind="text:totalShipmentMethods"></span>)
    </h4>
</div>
<hr />

<div class="grid-container">
    @(Html.Kendo().Grid<ShipmentMethodModel>()
        .Name("shipmentMethodGrid")
        .Columns(c => {
            c.Bound(p => p.Name);
            c.Command(command => { 
                command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
            }).Width(200);
        })
        .Editable(editable => editable.Mode(GridEditMode.InLine))
        .ToolBar(t => {
            t.Create().Text("Add Shipment Method");
            t.Excel().Text("Export");
        }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
        .Sortable()
        .Filterable()
        .Scrollable()
        .Resizable(c => c.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .Events(e => e.DataBound("updateShipmentMethodTotal"))
        .Excel(excel => excel
            .FileName(string.Format("Centerpoint_Logistics_Shipment_Method_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Admin"))
        )
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(m => m.Id(p => p.ShipmentMethodId))
            .Events(e => e.Error("onError"))
            .Read(read => read.Action("GetShipmentMethods", "Admin"))
            .Create(create => create.Action("UpdateShipmentMethod", "Admin"))
            .Update(update => update.Action("UpdateShipmentMethod", "Admin"))
            .Destroy(destroy => destroy.Action("DeleteShipmentMethod", "Admin"))
        )
    )
</div>


    <script>
        function updateShipmentMethodTotal() {
            var shipmentMethodGrid = $("#shipmentMethodGrid").data("kendoGrid");
            var totalShipmentMethods = shipmentMethodGrid.dataSource.total();
            viewModel.set("totalShipmentMethods", totalShipmentMethods);
        }

        function onError(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var shipmentMethodGrid = $("#shipmentMethodGrid").data("kendoGrid");
                shipmentMethodGrid.dataSource.cancelChanges();
            }
        }

        var viewModel = new kendo.observable({
            totalShipmentMethods: 0
        });

        kendo.bind(document.body.children, viewModel);
    </script>
