﻿<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-download"></i>
        Downloads
    </h4>
</div>
<hr />

<div class="row">
    <div class="col-md-4">
        @(Html.Kendo().TreeList<DownloadFolderModel>()
        .Name("downloadFolderTreeList")        
        .Columns(columns => {
            columns.Add().Field(e => e.Name).Title("Folder");           
        })
        .Events(e => e.Change("downloadFolderChanged"))
        .Sortable()
        .Filterable()
        .Scrollable(true)
        .Selectable(true)
        .Height(600)
        .ColumnMenu(c => c.Columns(true))
        .DataSource(dataSource => dataSource            
            .Read(read => read.Action("GetDownloadFolders", "Download"))
            .Model(m => {
                m.Id(f => f.DownloadFolderId);
                m.ParentId(f => f.ParentDownloadFolderId);
                m.<PERSON>(f => f.Modified).Editable(false);
                m.<PERSON>(f => f.ModifiedBy).Editable(false);
            })
        ))
    </div>
    <div class="col-md-8">
        <div id="downloadFolder" data-bind="visible:selectedDownloadFolder">
            <h2><i class="fa fa-folder-open"></i><span data-bind="text:selectedDownloadFolder.Name"></span> (<span data-bind="text:downloadFolderFiles"></span>)</h2>
            <hr />      
            <div data-bind="invisible:selectedDownloadFolder.FtpPath">
                @(Html.Kendo().Upload()
                .Name("downloadFiles")
                .Messages(m => m.Select("Upload Files"))
                .Multiple(true)
                .Events(e => e.Success("onDownloadFileSuccess").Complete("onDownloadFileComplete").Upload("onDownloadFileUploaded"))
                .HtmlAttributes(new { @style = "width:300px" })
                .Async(async => async.Save("UploadDownloadFiles", "Download")))
                <br />
            </div>      
            @(Html.Kendo().Grid<DownloadFileModel>()
            .Name("downloadFolderFilesGrid")
            .Columns(c => {
            c.Bound(p => p.Name).ClientTemplate("<a href='/Download/DownloadFile/#=DownloadFolderId#?name=#=encodeURIComponent(Name.trim())#' target='_blank'>#=Name#</a>");
            c.Bound(p => p.LengthMegabytes).Title("Size (MB)").Width(100);
            c.Bound(p => p.Created).Width(150).Format(DateConstants.DateTimeFormat);
            if (Html.IsGlobalAdmin() || Html.IsDownloadsAdmin()) {
                    c.Command(command => { 
                        command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
                    });
                }
            })
            .AutoBind(false)
            .Editable(editable => editable.Mode(GridEditMode.InLine))
            .Events(e => e.DataBound("updateDownloadFolderFilesTotal"))
            .Sortable()
            .Filterable()
            .ColumnMenu(c => c.Columns(true))
            .Scrollable(s => s.Height(430))
            .DataSource(dataSource => dataSource
                .Ajax()
                .Read(read => read.Action("GetDownloadFolderFiles", "Download").Data("downloadFolderData"))
                .Destroy(delete => delete.Action("DeleteDownloadFolderFile", "Download"))
                .Model(m => {
                    m.Id(f => f.Name);
                })
            ))           
        </div>
    </div>
</div>

    <script>
        function onDownloadFileSuccess() {
            var downloadFolderFilesGrid = $("#downloadFolderFilesGrid").data("kendoGrid");
            downloadFolderFilesGrid.dataSource.read();
        }

        function onDownloadFileUploaded(e) {
            uploadValidation(e);

            $(".k-upload-files.k-reset").show();

            var selectedDownloadFolder = viewModel.get("selectedDownloadFolder");

            e.data = {
                downloadFolderId: selectedDownloadFolder.DownloadFolderId
            };
        }

        function onDownloadFileComplete(e) {
            $(".k-upload-files.k-reset").find("li").remove();
            $(".k-upload-files.k-reset").slideUp();
        }

        function downloadFolderChanged() {
            var downloadFolderTreeList = $("#downloadFolderTreeList").data("kendoTreeList");
            var selectedFolderRows = downloadFolderTreeList.select();

            if (selectedFolderRows) {
                var selectedDownloadFolder = downloadFolderTreeList.dataItem(selectedFolderRows[0]);

                var downloadFolderFilesGrid = $("#downloadFolderFilesGrid").data("kendoGrid");

                if (selectedDownloadFolder.FtpPath) {                    
                    downloadFolderFilesGrid.hideColumn(3);
                } else {
                    downloadFolderFilesGrid.showColumn(3);
                }

                viewModel.set("selectedDownloadFolder", selectedDownloadFolder);
                refreshDownloadFolderFiles();
            }
        }

        function refreshDownloadFolderFiles() {
            var downloadFolderFilesGrid = $("#downloadFolderFilesGrid").data("kendoGrid");
            downloadFolderFilesGrid.dataSource.read();
        }

        function updateDownloadFolderFilesTotal() {
            var downloadFolderFilesGrid = $("#downloadFolderFilesGrid").data("kendoGrid");
            viewModel.set("downloadFolderFiles", downloadFolderFilesGrid.dataSource.total());
        }

        function downloadFolderData() {
            var selectedDownloadFolder = viewModel.get("selectedDownloadFolder");

            return {
                downloadFolderId: selectedDownloadFolder.DownloadFolderId
            };
        }
       
        var viewModel = new kendo.observable({
            selectedDownloadFolder: "",
            downloadFolderFiles: 0            
        });

        kendo.bind($("#downloadFolder"), viewModel);
    </script>