﻿using AutoMapper;
using Azure.Storage.Blobs;
using Centerpoint.Common;
using Centerpoint.Common.Constants;
using Centerpoint.DataAccess.Persistence;
using Centerpoint.DataAccess.Repositories.Interfaces;
using Centerpoint.Model;
using Centerpoint.Model.Entities;
using Centerpoint.Model.Entities.Equipment;
using Centerpoint.Model.ViewModels;
using Centerpoint.Model.ViewModels.Lookup;
using Centerpoint.Model.ViewModels.Project;
using Centerpoint.Service.Extensions;
using Centerpoint.Service.Interfaces;
using Centerpoint.Service.Validators.Interfaces;
using Centerpoint.Storage.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Syncfusion.Drawing;
using Syncfusion.XlsIO;
using System.Collections;
using System.IO.Compression;

namespace Centerpoint.Service
{
    public class AssetService : IAssetService
    {
        private readonly DataContext _context;
        private readonly IMapper _mapper;
        private readonly ILogger<AssetService> _logger;
        private readonly IIdentityService _identityService;
        private readonly IMaintenanceService _maintenanceService;
        private readonly IProjectRepository _projectRepository;
        private readonly ICompanyLocationRepository _companyLocationRepository;
        private readonly IEquipmentItemRepository _equipmentItemRepository;
        private readonly IEquipmentItemLogRepository _equipmentItemLogRepository;
        private readonly IMaintenanceRecordStepRepository _maintenanceRecordStepRepository;
        private readonly IEquipmentItemBundleRepository _equipmentItemBundleRepository;
        private readonly IEquipmentItemMaintenanceScheduleRepository _equipmentItemMaintenanceScheduleRepository;
        private readonly IEquipmentItemNoteRepository _equipmentItemNoteRepository;
        private readonly IEquipmentItemDocumentRepository _equipmentItemDocumentRepository;
        private readonly IDocumentRepository _documentRepository;
        private readonly IEquipmentCategoryMaintenanceStepRepository _equipmentCategoryMaintenanceStepRepository;
        private readonly IEquipmentItemCustomStatusCodeRepository _equipmentItemCustomStatusCodeRepository;
        private readonly ILogisticService _logisticService;
        private readonly IStorage _storage;
        private readonly IValidator<EquipmentItemModel> _equipmentItemValidator;
        private readonly IMaintenanceRecordRepository _maintenanceRecordRepository;

        public AssetService(DataContext context,
            IMapper mapper,
            ILogger<AssetService> logger,
            IIdentityService identityService,
            IProjectRepository projectRepository,
            ICompanyLocationRepository companyLocationRepository,
            IEquipmentItemRepository equipmentItemRepository,
            IEquipmentItemLogRepository equipmentItemLogRepository,
            IMaintenanceRecordStepRepository maintenanceRecordStepRepository,
            IMaintenanceService maintenanceService,
            IEquipmentItemBundleRepository equipmentItemBundleRepository,
            IEquipmentItemCustomStatusCodeRepository equipmentItemCustomStatusCodeRepository,
            IEquipmentItemMaintenanceScheduleRepository equipmentItemMaintenanceScheduleRepository,
            IEquipmentItemNoteRepository equipmentItemNoteRepository,
            IEquipmentItemDocumentRepository equipmentItemDocumentRepository,
            IDocumentRepository documentRepository,
            IEquipmentCategoryMaintenanceStepRepository equipmentCategoryMaintenanceStepRepository,
            IStorage storage,
            IValidator<EquipmentItemModel> equipmentItemValidator,
            ILogisticService logisticService,
            IMaintenanceRecordRepository maintenanceRecordRepository
            )
        {
            _context = context;
            _mapper = mapper;
            _logger = logger;
            _identityService = identityService;
            _projectRepository = projectRepository;
            _companyLocationRepository = companyLocationRepository;
            _equipmentItemRepository = equipmentItemRepository;
            _equipmentItemLogRepository = equipmentItemLogRepository;
            _maintenanceRecordStepRepository = maintenanceRecordStepRepository;
            _maintenanceService = maintenanceService;
            _equipmentItemBundleRepository = equipmentItemBundleRepository;
            _equipmentItemCustomStatusCodeRepository = equipmentItemCustomStatusCodeRepository;
            _equipmentItemMaintenanceScheduleRepository = equipmentItemMaintenanceScheduleRepository;
            _equipmentItemNoteRepository = equipmentItemNoteRepository;
            _equipmentItemDocumentRepository = equipmentItemDocumentRepository;
            _documentRepository = documentRepository;
            _equipmentCategoryMaintenanceStepRepository = equipmentCategoryMaintenanceStepRepository;
            _equipmentItemCustomStatusCodeRepository = equipmentItemCustomStatusCodeRepository;
            _storage = storage;
            _equipmentItemValidator = equipmentItemValidator;
            _logisticService = logisticService;
            _maintenanceRecordRepository = maintenanceRecordRepository;
        }

        public async Task<EquipmentItemModel> GetEquipmentItem(int id)
        {
            var equipmentItem = await _equipmentItemRepository.GetBaseEquipmentItemById(id);
            var parentBundleItem = await _equipmentItemBundleRepository.GetAllEquipmentItemBundles().Where(x => x.ChildEquipmentItemId == id).Include(x=>x.ParentEquipmentItem).FirstOrDefaultAsync();
                
            var result =  _mapper.Map<EquipmentItemModel>(equipmentItem);

            if (parentBundleItem != null)
                result.ParentEquipmentItem = parentBundleItem.ParentEquipmentItem.EquipmentNumber;
            return result;
        }

        public async Task<List<DropdownDataModel>> GetEquipmentItemDropdownData(int id)
        {
            var dropdownData = new List<DropdownDataModel>();
            var equipmentItem = await _equipmentItemRepository.GetBaseEquipmentItemById(id);

            #region Dropdowns

            var division = await _context.Divisions
                .Where(d => d.DivisionId == equipmentItem.DivisionId)
                .Select(s => new SelectItem { Text = s.Name, Value = s.DivisionId })
                .OrderBy(t => t.Text)
                .AsNoTracking().FirstOrDefaultAsync();

            var company = await _context.Companies
                .Where(x => x.CompanyId == equipmentItem.ManufacturerCompanyId)
                .Select(s => new SelectItem { Text = s.Name, Value = s.CompanyId })
                .AsNoTracking().FirstOrDefaultAsync();

            var loaction = await _context.CompanyLocations
                .Where(x => x.CompanyLocationId == equipmentItem.ManufacturerCompanyLocationId)
                .Select(s => new SelectItem { Text = s.Name, Value = s.CompanyLocationId })
                .AsNoTracking().FirstOrDefaultAsync();

            var currency = await _context.Currencies
                .Where(x => x.CurrencyId == equipmentItem.CurrencyId)
                .Select(s => new SelectItem { Text = s.Name, Value = s.CurrencyId })
                .AsNoTracking().FirstOrDefaultAsync();


            dropdownData.Add(new DropdownDataModel
            {
                Name = "DivisionId",
                Item = division,
                RequestFulfilled = false
            });
            dropdownData.Add(new DropdownDataModel
            {
                Name = "ManufacturerCompanyId",
                Item = company,
                RequestFulfilled = false
            });
            dropdownData.Add(new DropdownDataModel
            {
                Name = "ManufacturerCompanyLocationId",
                Item = loaction,
                RequestFulfilled = false
            });
            dropdownData.Add(new DropdownDataModel
            {
                Name = "CurrencyId",
                Item = currency,
                RequestFulfilled = false
            });

            #endregion

            return dropdownData;
        }
        public async Task<List<string>> GetEquipmentItemExistsErrors(EquipmentItemModel model, int equipmentCategoryId)
        {
            var errorList = new List<string>();

            if (await _equipmentItemRepository.CheckEquipmentItemExists(model.EquipmentItemId, model.EquipmentNumber, equipmentCategoryId))
            {
                errorList.Add(string.Format(ResourceMessages.EquipmentNumberIsExists, model.EquipmentNumber));
            }
            //if (!string.IsNullOrEmpty(model.SerialNumber) && await _equipmentItemRepository.CheckEquipmentItemSerialNumberExists(model.EquipmentItemId, model.SerialNumber))
            //{
            //    errorList.Add(string.Format(ResourceMessages.EquipmentSerialNumberIsExists, model.SerialNumber));
            //}

            return errorList;
        }

        public async Task<GenericResponse<EquipmentItemModel>> EditEquipmentItemAsync(EquipmentItemModel model, int userId, int equipmentCategoryId)
        {
            var errors = _equipmentItemValidator.Validate(model);
            var existErrors = await GetEquipmentItemExistsErrors(model, equipmentCategoryId);
            var response = new GenericResponse<EquipmentItemModel>() { Errors = errors.Concat(existErrors).ToList() };

            if (response.HasErrors())
                return response;

            var equipmentItem = await _equipmentItemRepository.CreateOrUpdateEquipmentItem(equipmentCategoryId, model.EquipmentItemId);

            int maxValue = Math.Max(Math.Max(model.H2sCo20to5, model.H2sCo25to10), Math.Max(model.H2sCo210to15, model.H2sCo215Greater));
            if (maxValue > 0)
                model.PointsPerRun += maxValue;

            equipmentItem = _mapper.Map<EquipmentItemModel, EquipmentItem>(model, equipmentItem);

            var additionalPropperties = new List<EquipmentAdditionalProperty>();

            SetAdditionalProperties(model, additionalPropperties);

            equipmentItem.AdditionalProperties = additionalPropperties;

            if (model.EquipmentItemId.HasValue)
                _equipmentItemLogRepository.LogEquipmentItemChanges(userId, equipmentItem.EquipmentItemId, equipmentItem.Points);
            else
                _equipmentItemLogRepository.CreateEquipmentItemLog(userId, equipmentItem.EquipmentItemId, equipmentItem.Points, EquipmentLogTypeConstant.Update, EquipmentLogConstant.EquipmentCreated);

            if (model.ReceivedDate.HasValue)
                equipmentItem.PointsUpdated = model.PointsUpdated.HasValue ? model.PointsUpdated : model.ReceivedDate;

            await _context.SaveChangesAsync();

            response.Response = model;
            return response;
        }

        public async Task<EquipmentItem> DeleteEquipmentItemAsync(int id)
        {
            var equipmentItem = await _equipmentItemRepository.DeleteEquipmentItem(id);
            await _context.SaveChangesAsync();
            return equipmentItem;
        }

        public async Task AcceptEquipmentItemAsync(int equipmentItemId, int currentCompanyLocationId, string acceptanceNote, int customStatusId, string acceptCustomStatusComment, DateTime expiryDate, int userId)
        {
            var now = DateTime.UtcNow.Date;
            var userName = await _identityService.GetUserNameAsync(userId);
            var equipmentItem = await _equipmentItemRepository.GetEquipmentItemWithCurrentLocationById(equipmentItemId);
            var equipmentItemCustomStatus = await _equipmentItemCustomStatusCodeRepository.CreateEquipmentItemStatusCode(equipmentItemId, customStatusId);

            equipmentItem.CurrentCompanyLocationId = currentCompanyLocationId;
            equipmentItem.Comments = acceptanceNote;
            equipmentItem.AcceptanceUser = userName;
            equipmentItem.AcceptanceCreated = now;
            equipmentItem.PointsUpdated = now;

            equipmentItemCustomStatus.Note = acceptCustomStatusComment ?? string.Empty;
            equipmentItemCustomStatus.ExpiryDate = expiryDate;

            _equipmentItemRepository.MarkEquipmentItemOperational(equipmentItem);

            await _context.SaveChangesAsync();
        }

        public async Task ChangeCurrentLocationAsync(int currentCompanyLocationId, int equipmentItemId, int userId, int? currentProjectId)
        {
            var equipmentItem = await _equipmentItemRepository.GetFullEquipmentItemById(equipmentItemId);
            var companyLocation = await _companyLocationRepository.GetCompanyLocationById(currentCompanyLocationId);

            if (equipmentItem.CurrentCompanyLocation != null && equipmentItem.CurrentCompanyLocation.Company != null)
                _equipmentItemLogRepository.CreateEquipmentItemLog(userId, equipmentItem.EquipmentItemId, equipmentItem.Points, EquipmentLogTypeConstant.Update, string.Format(EquipmentLogConstant.CurrentLocationChanged,
                    string.Format("{0} - {1}", equipmentItem.CurrentCompanyLocation.Company.Name, equipmentItem.CurrentCompanyLocation.Name),
                    string.Format("{0} - {1}", companyLocation.Company.Name, companyLocation.Name)));
            else
                _equipmentItemLogRepository.CreateEquipmentItemLog(userId, equipmentItem.EquipmentItemId, equipmentItem.Points, EquipmentLogTypeConstant.Update, string.Format(EquipmentLogConstant.CurrentLocationChangedTo,
                    string.Format("{0} - {1}", companyLocation.Company.Name, companyLocation.Name)));

            equipmentItem.CurrentCompanyLocationId = companyLocation.CompanyLocationId;
            equipmentItem.ProjectId = (equipmentItem.Project != null && equipmentItem.Project.CompanyLocation != companyLocation) || currentProjectId == 0 ? null : currentProjectId;

            if (equipmentItem.EquipmentItemBundleChildEquipmentItems.Any())
            {
                var equipmentItemIds = equipmentItem.EquipmentItemBundleChildEquipmentItems.Select(s => s.ChildEquipmentItemId.Value).ToArray();
                var childItems = await _equipmentItemRepository.GetEquipmentItemsWithProjectById(equipmentItemIds).ToListAsync();

                foreach (var childEquipmentItemId in equipmentItemIds)
                {
                    var childItem = childItems.FirstOrDefault(x => x.EquipmentItemId == childEquipmentItemId);
                    childItem.CurrentCompanyLocationId = companyLocation.CompanyLocationId;
                    childItem.ProjectId = (childItem.ProjectId != null && childItem.Project.CompanyLocationId != companyLocation.CompanyLocationId) || currentProjectId == 0 ? null : currentProjectId;
                }
            }
            await _context.SaveChangesAsync();
        }

        public async Task<IEnumerable<EquipmentItemModel>> GetEquipmentItemsAsync(int? equipmentCategoryId, int userId)
        {
            if (equipmentCategoryId.HasValue)
            {
                var bundleItems = await _context.EquipmentItemBundles.Include(bi => bi.ChildEquipmentItem).Include(bi => bi.ParentEquipmentItem).AsNoTracking().ToListAsync();
                var equipmentItems = await _equipmentItemRepository.GetFullEquipmentItemsByParentEquipmentCategoryId(userId, equipmentCategoryId.Value).AsNoTracking().ToListAsync();
                var equipmentItemIds = equipmentItems.Select(s => s.EquipmentItemId).ToList();

                var equipmentPackingListItems = await _equipmentItemRepository.GetFullEquipmentPackingListItemsDictionary(equipmentItemIds);
                var maintenanceRecords = await _equipmentItemRepository.GetMaintenanceRecordsDictionary(equipmentItemIds);
                var equipmentItemMaintenanceSchedules = await _equipmentItemRepository.GetEquipmentItemMaintenanceSchedulesDictionary(equipmentItemIds);
                var equipmentShipmentItems = await _equipmentItemRepository.GetFullEquipmentShipmentItemsDictionary(equipmentItemIds);

                foreach (var item in equipmentItems)
                {
                    item.EquipmentItemMaintenanceSchedules = equipmentItemMaintenanceSchedules.TryGetValue(item.EquipmentItemId, out var maintenanceSchedules) ? maintenanceSchedules : new List<EquipmentItemMaintenanceSchedule>();
                    item.MaintenanceRecords = maintenanceRecords.TryGetValue(item.EquipmentItemId, out var mrs) ? mrs : new List<MaintenanceRecord>();
                    item.EquipmentPackingListItems = equipmentPackingListItems.TryGetValue(item.EquipmentItemId, out var packingListItems) ? packingListItems : new List<EquipmentPackingListItem>();
                    item.EquipmentShipmentItems = equipmentShipmentItems.TryGetValue(item.EquipmentItemId, out var shipmentItems) ? shipmentItems : new List<EquipmentShipmentItem>();
                    item.EquipmentItemBundleChildEquipmentItems = bundleItems.Where(bi => bi.ParentEquipmentItemId == item.EquipmentItemId).Select(s => new EquipmentItemBundle { ChildEquipmentItem = new EquipmentItem { Status = s.ChildEquipmentItem.Status, CurrentCompanyLocationId = s.ChildEquipmentItem.CurrentCompanyLocationId } }).ToList();
                    item.EquipmentItemBundleParentEquipmentItems = bundleItems.Where(bi => bi.ChildEquipmentItemId == item.EquipmentItemId).Select(s => new EquipmentItemBundle { ParentEquipmentItemId = s.ParentEquipmentItemId }).ToList();
                }

                return equipmentItems.Select(x => _mapper.Map<EquipmentItemModel>(x));

            }

            return Array.Empty<EquipmentItemModel>();
        }

        public async Task<DuplicateEquipmentItemModel> DuplicateItemsAsync(int userId)
        {
            var equipmentItems = await _equipmentItemRepository.GetAllFullEquipmentItems(userId).AsNoTracking()
                                            .GroupBy(c => c.SerialNumber)
                                            .Where(g => g.Count() > 1)
                                            .Select(y => y.Key)
                                            .ToListAsync();

            return new DuplicateEquipmentItemModel { SerialNumbers = equipmentItems };
        }

        public async Task UpdateEquipmentItemInfoAsync(List<int> equipmentItemIds, string info)
        {
            await _context.EquipmentItems
                .Where(e => equipmentItemIds.Contains(e.EquipmentItemId))
                .ExecuteUpdateAsync(setters => setters.SetProperty(e => e.EquipmentInfo, info));
        }

        public async Task<IEnumerable<EquipmentItemStatisticsModel>> GetEquipmentItemsStatisticsAsync(int? equipmentCategoryId, DateTime startDate, DateTime endDate, int userId)
        {
            if (!equipmentCategoryId.HasValue)
                return Array.Empty<EquipmentItemStatisticsModel>();

            var totalDays = (endDate - startDate).TotalDays;
            var bundleItems = _context.EquipmentItemBundles.Include(bi => bi.ChildEquipmentItem).Include(bi => bi.ParentEquipmentItem);

            var equipmenItemModels = await _equipmentItemRepository.GetFullEquipmentItemsByParentEquipmentCategoryId(userId, equipmentCategoryId.Value).AsNoTracking()
                .Select(e => new EquipmentItem
                {
                    EquipmentItemId = e.EquipmentItemId,
                    EquipmentCategory = new EquipmentCategory { Name = e.EquipmentCategory.Name },
                    SerialNumber = e.SerialNumber,
                    Division = new Division { Name = e.Division.Name },
                    CurrentCompanyLocation = new CompanyLocation { Name = e.CurrentCompanyLocation.Name },
                    Price = e.Price,
                    PurchasedDate = e.PurchasedDate,
                    EquipmentItemBundleChildEquipmentItems = bundleItems.Where(bi => bi.ParentEquipmentItemId == e.EquipmentItemId).Select(s => new EquipmentItemBundle { ChildEquipmentItem = new EquipmentItem { Status = s.ChildEquipmentItem.Status, CurrentCompanyLocationId = s.ChildEquipmentItem.CurrentCompanyLocationId } }).ToList(),
                    EquipmentItemBundleParentEquipmentItems = bundleItems.Where(bi => bi.ChildEquipmentItemId == e.EquipmentItemId).Select(s => new EquipmentItemBundle { ParentEquipmentItemId = s.ParentEquipmentItemId }).ToList(),
                    IsSelected = e.IsSelected,
                    TrackedNonAssetItem = e.TrackedNonAssetItem,
                    Status = e.Status,
                    IsReserved = e.IsReserved,
                    IsTransit = e.IsTransit,
                })
                .Select(c => _mapper.Map<EquipmentItemStatisticsModel>(c))
                .ToListAsync();

            var equipmentItemIds = equipmenItemModels.Select(s => s.EquipmentItemId).ToList();

            var runs = await _context.Runs.Include(i => i.RunEquipmentItems).AsNoTracking()
                .Where(r => r.RunEquipmentItems.Any(i => i.Run.RunFinish.HasValue && i.Run.RunFinish >= startDate && i.Run.RunFinish <= endDate))
                .ToListAsync();

            var mrSteps = await _context.MaintenanceRecordSteps.Include(i => i.MaintenanceRecord).AsNoTracking()
                .Where(p => p.CompletedDate.HasValue && p.CompletedDate >= startDate && p.CompletedDate <= endDate && p.IsRepair)
                .ToListAsync();

            var shipments = await _context.EquipmentShipments
                .Include(i => i.EquipmentShipmentItems)
                .AsNoTracking().ToListAsync();

            var equipmentPackingListItems = await _equipmentItemRepository.GetEquipmentPackingListItemsDictionary(equipmentItemIds);
            var equipmentShipmentItems = await _equipmentItemRepository.GetFullEquipmentShipmentItemsDictionary(equipmentItemIds);

            foreach (var equipmentItemModel in equipmenItemModels)
            {
                var equipmentItemRuns = runs.Where(x => x.RunEquipmentItems.Any(i => i.EquipmentItemId == equipmentItemModel.EquipmentItemId));
                var equipmentItemRepairs = mrSteps.Where(x => x.MaintenanceRecord.EquipmentItemId == equipmentItemModel.EquipmentItemId).ToList();
                var completedEquipmentItemRepairs = equipmentItemRepairs.Where(e => e.Status == MaintenanceStepResultConstant.Repaired).ToList();

                equipmentItemModel.EquipmentPackingListId = equipmentPackingListItems.TryGetValue(equipmentItemModel.EquipmentItemId, out var packingListItems) ? packingListItems.LastOrDefault().EquipmentPackingListId : 0;
                equipmentItemModel.EquipmentShipmentId = equipmentShipmentItems.TryGetValue(equipmentItemModel.EquipmentItemId, out var shipmentItems) ? shipmentItems.LastOrDefault().EquipmentShipmentId : 0;
                equipmentItemModel.TotalDays = totalDays;
                equipmentItemModel.DaysUsed = Math.Round(_equipmentItemRepository.GetTotalDaysOnProject(equipmentItemModel.EquipmentItemId, startDate, endDate, shipments), 2);
                equipmentItemModel.Jobs = equipmentItemRuns.GroupBy(r => r.Job).Count();
                equipmentItemModel.Runs = equipmentItemRuns.Count();
                equipmentItemModel.MeanTimeBetweenFailure = equipmentItemModel.TimeInServiceDays > 0
                    ? Math.Round(equipmentItemRepairs.Count() / equipmentItemModel.TimeInServiceDays, 2) : 0.0;
                equipmentItemModel.MeanTimeToRepair = completedEquipmentItemRepairs.Any() && completedEquipmentItemRepairs.All(c => c.CompletedDate.HasValue)
                    ? Math.Round(completedEquipmentItemRepairs.Sum(c => (c.CompletedDate.Value - c.MaintenanceRecord.Created).TotalDays) / completedEquipmentItemRepairs.Count, 2) : 0.0;
            }

            return equipmenItemModels;
        }

        public async Task<IEnumerable<EquipmentItemInfoBundleModel>> GetEquipmentItemsNotInEquipmentItemBundleIdAsync(int equipmentItemId, int userId, int? сurrentCompanyLocationId)
        {
            var operItems = await _equipmentItemRepository.GetOperationalEquipmentItemsNotInEquipmentItemsBundleId(userId);
            return await operItems.AsNoTracking()
                .Where(m => m.EquipmentItemId != equipmentItemId && сurrentCompanyLocationId == m.CurrentCompanyLocationId || m.Status != EquipmentConstant.Inactive)
                .Select(e => new EquipmentItem
                {
                    EquipmentItemId = e.EquipmentItemId,
                    EquipmentCategory = new EquipmentCategory { Name = e.EquipmentCategory.Name },
                    EquipmentNumber = e.EquipmentNumber,
                    CurrentCompanyLocation = new CompanyLocation
                    {
                        Name = e.CurrentCompanyLocation.Name,
                        Company = new Company { Name = e.CurrentCompanyLocation.Company.Name },
                    },
                    Points = e.Points,
                    EquipmentItemMaintenanceSchedules = e.EquipmentItemMaintenanceSchedules.Select(s => new EquipmentItemMaintenanceSchedule
                    {
                        CompanyLocationId = s.CompanyLocationId,
                        StartDate = s.StartDate
                    }).OrderBy(o => o.StartDate).ToList(),
                    MaintenanceRecords = e.MaintenanceRecords.Select(mr => new MaintenanceRecord { Status = mr.Status }).ToList(),
                    EquipmentInfo = e.EquipmentInfo,
                    IsReserved = e.IsReserved,
                    IsTransit = e.IsTransit,
                    IsSelected = e.IsSelected,
                    Status = e.Status,
                    EquipmentItemBundleChildEquipmentItems = e.EquipmentItemBundleChildEquipmentItems.Select(s => new EquipmentItemBundle
                    {
                        ChildEquipmentItem = new EquipmentItem { Status = s.ChildEquipmentItem.Status, CurrentCompanyLocationId = s.ChildEquipmentItem.CurrentCompanyLocationId }
                    }).ToList(),
                    EquipmentItemBundleParentEquipmentItems = e.EquipmentItemBundleParentEquipmentItems.Select(s => new EquipmentItemBundle { ParentEquipmentItemId = s.ParentEquipmentItemId }).ToList(),
                    ReceivedDate = e.ReceivedDate,
                    PurchasedDate = e.PurchasedDate,
                    Currency = new Currency { Name = e.Currency.Name },
                    EquipmentPackingListItems = e.EquipmentPackingListItems.Select(x => new EquipmentPackingListItem { EquipmentPackingListId = x.EquipmentPackingListId }).ToList(),
                    Price = e.Price,
                    PointsPerMonth = e.PointsPerMonth,
                    PointsPerMove = e.PointsPerMove,
                    PointsPerRun = e.PointsPerRun,
                    Division = new Division { Name = e.Division.Name },
                    TrackedNonAssetItem = e.TrackedNonAssetItem
                })
                .Select(x => _mapper.Map<EquipmentItemInfoBundleModel>(x))
                .ToListAsync();
        }

        public async Task<IEnumerable<EquipmentItemBundleModel>> GetBundledEquipmentItemsAsync(int parentEquipmentItemId)
        {
            return await _equipmentItemBundleRepository.GetAllEquipmentItemBundlesByParentEquipmentItemId(parentEquipmentItemId).AsNoTracking()
                .Select(c => new EquipmentItemBundle
                {
                    ChildEquipmentItemId = c.ChildEquipmentItemId,
                    EquipmentItemBundleId = c.EquipmentItemBundleId,
                    ParentEquipmentItemId = c.ParentEquipmentItemId,
                    ParentEquipmentItem = new EquipmentItem { EquipmentNumber = c.ParentEquipmentItem.EquipmentNumber},
                    ChildEquipmentItem = new EquipmentItem
                    {
                        EquipmentItemId = c.ChildEquipmentItem.EquipmentItemId,
                        EquipmentCategory = new EquipmentCategory { Name = c.ChildEquipmentItem.EquipmentCategory.Name },
                        EquipmentNumber = c.ChildEquipmentItem.EquipmentNumber,
                        CurrentCompanyLocation = new CompanyLocation
                        {
                            Name = c.ChildEquipmentItem.CurrentCompanyLocation.Name,
                            Company = new Company { Name = c.ChildEquipmentItem.CurrentCompanyLocation.Company.Name },
                        },
                        Points = c.ChildEquipmentItem.Points,
                        EquipmentItemMaintenanceSchedules = c.ChildEquipmentItem.EquipmentItemMaintenanceSchedules.Select(s => new EquipmentItemMaintenanceSchedule
                        {
                            CompanyLocationId = s.CompanyLocationId,
                            StartDate = s.StartDate
                        }).OrderBy(o => o.StartDate).ToList(),
                        MaintenanceRecords = c.ChildEquipmentItem.MaintenanceRecords.Select(mr => new MaintenanceRecord { Status = mr.Status }).ToList(),
                        EquipmentInfo = c.ChildEquipmentItem.EquipmentInfo,
                        IsReserved = c.ChildEquipmentItem.IsReserved,
                        IsTransit = c.ChildEquipmentItem.IsTransit,
                        IsSelected = c.ChildEquipmentItem.IsSelected,
                        Status = c.ChildEquipmentItem.Status,
                        EquipmentItemBundleChildEquipmentItems = c.ChildEquipmentItem.EquipmentItemBundleChildEquipmentItems.Select(s => new EquipmentItemBundle
                        {
                            ChildEquipmentItem = new EquipmentItem { Status = s.ChildEquipmentItem.Status, CurrentCompanyLocationId = s.ChildEquipmentItem.CurrentCompanyLocationId }
                        }).ToList(),
                        EquipmentItemBundleParentEquipmentItems = c.ChildEquipmentItem.EquipmentItemBundleParentEquipmentItems.Select(s => new EquipmentItemBundle { ParentEquipmentItemId = s.ParentEquipmentItemId }).ToList(),
                        ReceivedDate = c.ChildEquipmentItem.ReceivedDate,
                        PurchasedDate = c.ChildEquipmentItem.PurchasedDate,
                        Currency = new Currency { Name = c.ChildEquipmentItem.Currency.Name },
                        Price = c.ChildEquipmentItem.Price,
                        EquipmentPackingListItems = c.ChildEquipmentItem.EquipmentPackingListItems.Select(x => new EquipmentPackingListItem { EquipmentPackingListId = x.EquipmentPackingListId }).ToList(),
                        EquipmentShipmentItems = c.ChildEquipmentItem.EquipmentShipmentItems.Select(x => new EquipmentShipmentItem { EquipmentShipmentId = x.EquipmentShipmentId }).ToList(),
                        PointsPerMonth = c.ChildEquipmentItem.PointsPerMonth,
                        PointsPerMove = c.ChildEquipmentItem.PointsPerMove,
                        PointsPerRun = c.ChildEquipmentItem.PointsPerRun,
                        Division = new Division { Name = c.ChildEquipmentItem.Division.Name },
                        TrackedNonAssetItem = c.ChildEquipmentItem.TrackedNonAssetItem
                    }
                })
                .Select(p => _mapper.Map<EquipmentItemBundleModel>(p))
                .ToListAsync();
        }


        public async Task UpdateBundledEquipmentItemsAsync(int[] equipmentItemIds, int parentEquipmentItemId, int userId)
        {
            var parentIsCild = _equipmentItemBundleRepository.GetAllEquipmentItemBundles().Where(x=>x.ChildEquipmentItemId == parentEquipmentItemId).Any();
            if (parentIsCild)
                throw new Exception("Current item is already in bundle as a child!");

            var logData = await _context.EquipmentItems.Where(c => equipmentItemIds.Contains(c.EquipmentItemId)).Select(c => new {c.EquipmentItemId,c.Points}).ToDictionaryAsync(c=>c.EquipmentItemId,c=>c.Points);
            foreach (var equipmentItemId in equipmentItemIds)
            {
                var equipmentItemBundle = new EquipmentItemBundle
                {
                    ParentEquipmentItemId = parentEquipmentItemId,
                    ChildEquipmentItemId = equipmentItemId
                };
                _context.EquipmentItemBundles.Update(equipmentItemBundle);
            }

            if (logData.Any())
                await _equipmentItemLogRepository.CreateEquipmentItemLogs(userId, logData, EquipmentLogTypeConstant.Update, $"Equipment Item Bundled as child item.");

            await _context.SaveChangesAsync();

            var parentEquipmentItemBundle = await _equipmentItemBundleRepository.GetEquipmentItemBundleByParentId(parentEquipmentItemId);
            var equipmentPackingListId = parentEquipmentItemBundle.ChildEquipmentItem?.EquipmentPackingListItems?.FirstOrDefault(x => !x.EquipmentPackingList.EquipmentShipmentId.HasValue || x.EquipmentPackingList?.EquipmentShipment?.EquipmentShipmentStatus == EquipmentShipmentStatusConstant.Pending)?.EquipmentPackingListId;
            var equipmentShipmentId = parentEquipmentItemBundle.ChildEquipmentItem?.EquipmentShipmentItems?.FirstOrDefault(x => x.EquipmentShipment.EquipmentShipmentStatus == EquipmentShipmentStatusConstant.Pending)?.EquipmentShipmentId;

            if (equipmentPackingListId != null && equipmentPackingListId.HasValue)
                await _logisticService.UpdatePackingListEquipmentItems(equipmentItemIds, (int)equipmentPackingListId, userId);

            if (equipmentShipmentId != null && equipmentShipmentId.HasValue)
                await _logisticService.UpdateShipmentEquipmentItems(equipmentItemIds, (int)equipmentShipmentId, userId);
        }

        public async Task DeleteBundleEquipmentItemAsync(int equipmentItemBundleId)
        {
            await _equipmentItemBundleRepository.DeleteEquipmentItemBundle(equipmentItemBundleId);
            await _context.SaveChangesAsync();
        }


        #region Equipment Item Maintenance Schedule

        public async Task<IEnumerable<EquipmentItemMaintenanceScheduleModel>> GetAllEquipmentItemMaintenanceSchedules()
        {
            return await _equipmentItemMaintenanceScheduleRepository.GetAllEquipmentItemMaintenanceSchedules().AsNoTracking()
                .Select(c => _mapper.Map<EquipmentItemMaintenanceScheduleModel>(c)).ToListAsync();
        }

        public async Task<IEnumerable<EquipmentItemMaintenanceScheduleModel>> GetEquipmentItemMaintenanceSchedules(int eId)
        {
            return (await _equipmentItemMaintenanceScheduleRepository.GetAllEquipmentItemMaintenanceSchedulesByEquipmentItemId(eId))
                .Select(c => _mapper.Map<EquipmentItemMaintenanceScheduleModel>(c)).ToList();
        }


        public async Task<IEnumerable<EquipmentItemMaintenanceScheduleModel>> UpdateEquipmentItemMaintenanceSchedule(EquipmentItemMaintenanceScheduleModel model, int eId)
        {
            var equipmentItemMaintenanceSchedule = await _equipmentItemMaintenanceScheduleRepository.CreateOrUpdateEquipmentItemMaintenanceSchedule(eId, model.EquipmentItemMaintenanceScheduleId, model.MaintenanceBlueprintId, model.CompanyLocationId);
            equipmentItemMaintenanceSchedule = _mapper.Map(model, equipmentItemMaintenanceSchedule);

            await _context.SaveChangesAsync();

            model = _mapper.Map<EquipmentItemMaintenanceScheduleModel>(equipmentItemMaintenanceSchedule);

            model.MaintenanceBlueprintName = equipmentItemMaintenanceSchedule.MaintenanceBlueprint.Name;
            if (model.CompanyLocationId != null)
                model.CompanyLocationName = await _context.CompanyLocations.Where(x => x.CompanyLocationId == model.CompanyLocationId).Select(x => x.Name).FirstOrDefaultAsync();

            return (new[] { model });
        }


        public async Task<IEnumerable<EquipmentItemMaintenanceScheduleModel>> DeleteEquipmentItemMaintenanceSchedule(EquipmentItemMaintenanceScheduleModel model)
        {
            if (model.EquipmentItemMaintenanceScheduleId.HasValue)
            {
                await _equipmentItemMaintenanceScheduleRepository.DeleteEquipmentItemMaintenanceSchedule(model.EquipmentItemMaintenanceScheduleId.Value);
                await _context.SaveChangesAsync();
            }

            return (new[] { model });
        }

        #endregion

        #region Equipment Item Note

        public async Task<IEnumerable<EquipmentItemNoteModel>> GetEquipmentItemNotes(int equipmentItemId)
        {
            return await _equipmentItemNoteRepository.GetEquipmentItemNotesByEquipmentItemId(equipmentItemId).AsNoTracking()
                    .Select(x => _mapper.Map<EquipmentItemNoteModel>(x))
                    .ToListAsync();
        }

        public async Task<EquipmentItemNoteModel> UpdateEquipmentItemNote(EquipmentItemNoteModel model, int equipmentItemId, int userId)
        {
            var equipmentItemNote = await _equipmentItemNoteRepository.CreateOrUpdateEquipmentItemNote(userId, equipmentItemId, model.EquipmentItemNoteId);
            equipmentItemNote = _mapper.Map<EquipmentItemNoteModel, EquipmentItemNote>(model, equipmentItemNote);
            equipmentItemNote.UserId = userId;

            await _context.SaveChangesAsync();

            return _mapper.Map<EquipmentItemNoteModel>(equipmentItemNote);
        }

        public async Task<IEnumerable<EquipmentItemNoteModel>> DeleteEquipmentItemNote(EquipmentItemNoteModel model)
        {
            if (model.EquipmentItemNoteId.HasValue)
            {
                await _equipmentItemNoteRepository.DeleteEquipmentItemNote(model.EquipmentItemNoteId.Value);
                await _context.SaveChangesAsync();
            }

            return (new[] { model });
        }

        #region Equipment Item Status


        public async Task<EquipmentItem> UpdateStatus(int id, string status, int userId)
        {
            var equipmentItem = await _equipmentItemRepository.GetEquipmentItemWithCategoryById(id);
            User user = await _identityService.GetUserAsync(userId);
            _equipmentItemRepository.SetEquipmentItemStatus(equipmentItem, status);
            var equipmentItemBundles = _context.EquipmentItemBundles.Where(x => x.ParentEquipmentItemId == id || x.ChildEquipmentItemId == id);
            var logData = new List<EquipmentItemLogModel>();

            if (status == EquipmentConstant.Inactive)
            {
                equipmentItem.PointsPerMonth = 0;
                if (GlobalSettings.IsRegiis)
                {

                    equipmentItem.Points = 0;
                    var equipmentMaintenanceRecords = await _maintenanceRecordRepository.GetAllMaintenanceRecordsByEquipmentId(id)
                        .Where(e => e.Status != MaintenanceConstant.Closed).ToListAsync();

                    equipmentMaintenanceRecords.ForEach(x =>
                    {
                        x.Status = MaintenanceConstant.Closed;
                        x.Closed = DateTime.UtcNow;
                        x.ClosedDate = DateTime.UtcNow;
                        x.ClosedBy = user.Name;

                        logData.Add(_equipmentItemLogRepository.GetEquipmentItemLogModel(x, userId, EquipmentLogConstant.MRClosedInactivity));
                    });
                    await equipmentItemBundles.ExecuteDeleteAsync();
                }
            }
            if (status == EquipmentConstant.Operational)
            {
                equipmentItem.PointsPerMonth = equipmentItem.EquipmentCategory.PointsPerMonth.HasValue ? equipmentItem.EquipmentCategory.PointsPerMonth.Value : 0;
            }

            if (logData.Any())
                _equipmentItemLogRepository.CreateEquipmentItemLogs(logData);

            _equipmentItemLogRepository.CreateEquipmentItemLog(userId, equipmentItem.EquipmentItemId, equipmentItem.Points, EquipmentLogTypeConstant.Update, string.Format(EquipmentLogConstant.MarkedAs, EquipmentConstant.GetDescription(status)));

            await _context.SaveChangesAsync();

            return equipmentItem;
        }


        #endregion

        #endregion

        #region Equipment Item Documents

        public async Task<IEnumerable<DocumentModel>> GetEquipmentItemDocuments(int equipmentItemId)
        {
            return await _equipmentItemDocumentRepository.GetEquipmentItemDocumentsByEquipmentItemId(equipmentItemId).AsNoTracking()
             .Select(p => _mapper.Map<Document, DocumentModel>(p.Document))
             .ToListAsync();
        }

        public async Task AttachEquipmentItemDocuments(IEnumerable<IFormFile> equipmentItemAttachmentDocuments, int equipmentItemId, int userId)
        {
            var equipmentItemAttachment = await _equipmentItemRepository.GetEquipmentItemById(equipmentItemId);
            var equipmentItem = await _equipmentItemRepository.GetEquipmentItemById(equipmentItemId);
            var logData = new List<EquipmentItemLogModel>();

            foreach (var equipmentItemAttachmentDocument in equipmentItemAttachmentDocuments)
            {
                var fileName = Path.GetFileName(equipmentItemAttachmentDocument.FileName);
                var mimeType = equipmentItemAttachmentDocument.ContentType;
                var data = await equipmentItemAttachmentDocument.GetBytes();

                var filePath = await _storage.UploadFileAsync(equipmentItemAttachmentDocument.FileName, data, mimeType);

                _equipmentItemDocumentRepository.CreateEquipmentItemDocument(equipmentItemAttachment, userId, fileName, mimeType, filePath);

                logData.Add(new EquipmentItemLogModel(userId, equipmentItem.EquipmentItemId, equipmentItem.Points, EquipmentLogTypeConstant.Update, string.Format(EquipmentLogConstant.FileAttached, fileName)));
            }

            if (logData.Any())
                _equipmentItemLogRepository.CreateEquipmentItemLogs(logData);

            await _context.SaveChangesAsync();
        }

        public async Task DeleteEquipmentItemDocument(DocumentModel model, int equipmentItemId, int userId)
        {
            if (!model.DocumentId.HasValue) return;

            var equipmentItem = await _equipmentItemRepository.GetEquipmentItemById(equipmentItemId);
            var document = await _documentRepository.DeleteDocumentById(model.DocumentId.Value);
            await _storage.DeleteBlobAsync(document.FilePath);
            _equipmentItemLogRepository.CreateEquipmentItemLog(userId, equipmentItem.EquipmentItemId, equipmentItem.Points, EquipmentLogTypeConstant.Update, string.Format(EquipmentLogConstant.FileDeleted, model.FileName));

            await _context.SaveChangesAsync();
        }

        public async Task<IEnumerable<EquipmentItemCertificateModel>> GetEquipmentItemCertificates(int equipmentItemId)
        {
            return await _context.EquipmentItemCertificates.Where(x => x.EquipmentItemId == equipmentItemId).AsNoTracking()
                .OrderByDescending(x => x.ExpiryDate)
                .Select(p => _mapper.Map<EquipmentItemCertificate, EquipmentItemCertificateModel>(p))
                .ToListAsync();
        }

        public async Task<string> GetEquipmentItemCertificate(int id)
        {
            var equipmentItemCertificate = await _context.EquipmentItemCertificates.FirstOrDefaultAsync(x => x.Id == id);
            var filePath = await _storage.GetBlobReferenceUri(equipmentItemCertificate.FilePath, equipmentItemCertificate.FileName);

            return filePath;
        }

        public async Task AttachEquipmentItemCertificates(IEnumerable<IFormFile> equipmentItemCertificatesFiles, int equipmentItemId, int userId)
        {
            var equipmentItem = await _equipmentItemRepository.GetEquipmentItemById(equipmentItemId);
            User user = await _identityService.GetUserAsync(userId);
            var equipmentItemCertificates = new List<EquipmentItemCertificate>();
            var logData = new List<EquipmentItemLogModel>();

            foreach (var equipmentItemCertificatefile in equipmentItemCertificatesFiles)
            {
                var fileName = Path.GetFileName(equipmentItemCertificatefile.FileName);
                var mimeType = equipmentItemCertificatefile.ContentType;
                var data = await equipmentItemCertificatefile.GetBytes();

                var filePath = await _storage.UploadFileAsync(equipmentItemCertificatefile.FileName, data, mimeType);

                equipmentItemCertificates.Add(new EquipmentItemCertificate
                {
                    Created = DateTime.UtcNow,
                    EquipmentItemId = equipmentItemId,
                    EquipmentItem = equipmentItem,
                    FileName = equipmentItemCertificatefile.FileName,
                    FilePath = filePath,
                    UserId = userId,
                    UserName = user.Name,
                });

                logData.Add(new EquipmentItemLogModel(userId, equipmentItem.EquipmentItemId, equipmentItem.Points, EquipmentLogTypeConstant.Update, string.Format(EquipmentLogConstant.FileAttached, fileName)));
            }

            if (logData.Any())
                _equipmentItemLogRepository.CreateEquipmentItemLogs(logData);

            await _context.EquipmentItemCertificates.AddRangeAsync(equipmentItemCertificates);

            await _context.SaveChangesAsync();
        }

        public async Task DeleteEquipmentItemCertificate(EquipmentItemCertificateModel model, int equipmentItemId, int userId)
        {
            if (model?.Id == null) return;

            var equipmentItem = await _equipmentItemRepository.GetEquipmentItemById(equipmentItemId);
            var document = await _context.EquipmentItemCertificates.FirstOrDefaultAsync(x => x.Id == model.Id);

            await _storage.DeleteBlobAsync(document.FilePath);
            _context.EquipmentItemCertificates.Remove(document);

            _equipmentItemLogRepository.CreateEquipmentItemLog(userId, equipmentItem.EquipmentItemId, equipmentItem.Points, EquipmentLogTypeConstant.Update, string.Format(EquipmentLogConstant.FileDeleted, model.FileName));
            await _context.SaveChangesAsync();
        }

        public async Task<EquipmentItemCertificateModel> EditEquipmentItemCertificate(EquipmentItemCertificateModel model, int equipmentItemId, int userId)
        {
            if (model?.Id == null) return model;

            var equipmentItem = await _equipmentItemRepository.GetEquipmentItemById(equipmentItemId);

            var document = await _context.EquipmentItemCertificates.FirstOrDefaultAsync(x => x.Id == model.Id);
            document.ExpiryDate = model.ExpiryDate;

            _equipmentItemLogRepository.CreateEquipmentItemLog(userId, equipmentItem.EquipmentItemId, equipmentItem.Points, EquipmentLogTypeConstant.Update, string.Format(EquipmentLogConstant.ExDateUpdated, model.FileName));
            await _context.SaveChangesAsync();

            return model;
        }

        #endregion

        #region Equipment Item Custom Status Codes

        public async Task<IEnumerable<EquipmentItemCustomStatusCodeModel>> GetEquipmentItemCustomStatusCodes(int equipmentItemId)
        {
            return await _equipmentItemCustomStatusCodeRepository.GetEquipmentItemCustomStatusCodeByEquipmentItemId(equipmentItemId).AsNoTracking()
                .Select(s => new EquipmentItemCustomStatusCode
                {
                    CustomStatusCode = new CustomStatusCode { Name = s.CustomStatusCode.Name, Country = s.CustomStatusCode.Country, Code = s.CustomStatusCode.Code },
                    CustomStatusCodeId = s.CustomStatusCodeId,
                    Note = s.Note,
                    Created = s.Created,
                    ExpiryDate = s.ExpiryDate,
                    EquipmentItemCustomStatusCodeId = s.EquipmentItemCustomStatusCodeId,
                    IsActive = s.IsActive
                })
                .Select(x => _mapper.Map<EquipmentItemCustomStatusCodeModel>(x))
                .ToListAsync();
        }

        public async Task<IEnumerable<EquipmentItemCustomStatusCodeModel>> UpdateEquipmentItemCustomStatusCode(EquipmentItemCustomStatusCodeModel model, int eId, int customStatusCodeId)
        {
            var equipmentItemCustomStatusCodes = await _equipmentItemCustomStatusCodeRepository.CreateOrUpdateEquipmentItemCustomStatusCode(model.EquipmentItemCustomStatusCodeId, eId, customStatusCodeId);
            equipmentItemCustomStatusCodes = _mapper.Map(model, equipmentItemCustomStatusCodes);

            await _context.SaveChangesAsync();

            model = _mapper.Map<EquipmentItemCustomStatusCodeModel>(equipmentItemCustomStatusCodes);

            return (new[] { model });
        }

        public async Task<IEnumerable<EquipmentItemCustomStatusCodeModel>> DeleteEquipmentItemCustomStatusCode(EquipmentItemCustomStatusCodeModel model)
        {

            if (model.EquipmentItemCustomStatusCodeId.HasValue)
            {
                await _equipmentItemCustomStatusCodeRepository.DeleteEquipmentItemCustomStatusCode(model.EquipmentItemCustomStatusCodeId.Value);
                await _context.SaveChangesAsync();

            }
            return (new[] { model });
        }
        #endregion

        #region Equipment Item Log

        public async Task<IEnumerable<EquipmentItemLogModel>> GetEquipmentItemLogsByEquipmentItemId(int eId)
        {
            return await _equipmentItemLogRepository.GetAllEquipmentItemLogsByEquipmentItemId(eId)
                .Select(p => _mapper.Map<EquipmentItemLogModel>(p)).ToListAsync();
        }

        #endregion

        public async Task MoveAssetItem(int[] equipmentItemIds, int equipmentCategoryId)
        {
            await _context.EquipmentItems
                .Where(x => equipmentItemIds.Contains(x.EquipmentItemId))
                .ExecuteUpdateAsync(setters => setters.SetProperty(e => e.EquipmentCategoryId, equipmentCategoryId));
        }

        public async Task<IEnumerable<EquipmentCategoryMaintenanceStepModel>> GetEquipmentCategoryMaintenanceSteps(int? categoryId)
        {
            if (categoryId.HasValue)
            {
                return await _equipmentCategoryMaintenanceStepRepository.GetEquipmentCategoryMaintenanceStepsByEquipmentCategoryId(categoryId.Value).AsNoTracking()
                 .Select(x => _mapper.Map<EquipmentCategoryMaintenanceStepModel>(x))
                 .ToListAsync();
            }
            return Array.Empty<EquipmentCategoryMaintenanceStepModel>();
        }

        #region Utilities
        public void SetAdditionalProperties(EquipmentItemModel model, List<EquipmentAdditionalProperty> additionalProperties)
        {
            foreach (var propName in EquipmentPropertiesConstants.GetEquipmentAdditionalPropertyNames())
            {
                System.Reflection.PropertyInfo propertyInfo = model.GetType().GetProperty(propName);
                var propValue = propertyInfo.GetValue(model)?.ToString();
                additionalProperties.Add(new EquipmentAdditionalProperty
                {
                    PropertyName = propName,
                    PropertyValue = propValue
                });
            }
        }
        #endregion

        public async Task UpdatePointsPerMonth()
        {
            try
            {
                var equipmentIds = await _equipmentItemRepository.GetEquipmentItemsByPointsUpdateRequired(DateTime.Now.Date.AddMonths(-1));

                while (equipmentIds.Count > 0)
                {
                    var idsToUpdate = equipmentIds.Take(50).ToList();
                    var equipmentItemsToUpdate = await _equipmentItemRepository.GetEquipmentItems(idsToUpdate);

                    foreach (var equipmentItem in equipmentItemsToUpdate)
                    {
                        await _equipmentItemRepository.UpdateEquipmentPointsPerMonth(equipmentItem);
                    }

                    await _context.SaveChangesAsync();

                    equipmentIds.RemoveAll(x => idsToUpdate.Contains(x));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating equipment items points per month");
                throw;
            }
        }


        public async Task<byte[]> GenerateEquipmentItemArchiveAsync(int id)
        {
            var model = await RetrieveDataCollectModelAsync(id);
            var logs = await GetEquipmentItemLogsByEquipmentItemId(model.EquipmentItem.EquipmentItemId.Value);

            using var archiveStream = new MemoryStream();
            using (var zip = new ZipArchive(archiveStream, ZipArchiveMode.Create, true))
            {
                await AddDocumentsToZipAsync(model,  zip);

                var equipmentItemLogs = await GetEquipmentItemLogsByEquipmentItemId(model.EquipmentItem.EquipmentItemId.Value);
                var excelBytes = GenerateEquipmentExcel(model.EquipmentItem, equipmentItemLogs);
                var excelFileName = $"{model.EquipmentItem.EquipmentNumber} - {DateTime.Now:yyyy-MM-dd HH-mm}.xlsx";

                var excelEntry = zip.CreateEntry(excelFileName, CompressionLevel.Fastest);
                using var excelStream = excelEntry.Open();
                await excelStream.WriteAsync(excelBytes, 0, excelBytes.Length);
            }

            return archiveStream.ToArray();
        }
        private async Task<CollectItemsDataModel> RetrieveDataCollectModelAsync(int equipmentItemId)
        {
            var equipmentItem = await _equipmentItemRepository.GetEquipmentItemById(equipmentItemId);
            var certificates = await _context.EquipmentItemCertificates
                .Where(c => c.EquipmentItemId == equipmentItemId).ToListAsync();
            var itemAttachments = await _context.EquipmentItemDocuments
                .Where(c => c.EquipmentItemId == equipmentItemId)
                .Include(c => c.Document)
                .Select(c => c.Document).ToListAsync();
            var mrAttachments = await _maintenanceService.GetDocumentsByEquipmentItemId(equipmentItemId);

            return new CollectItemsDataModel
            {
                EquipmentItem = _mapper.Map<EquipmentItemModel>(equipmentItem),
                Certificates = certificates,
                ItemAttachments = itemAttachments,
                MrAttachments = mrAttachments.ToList(),
            };
        }
        private async Task AddDocumentsToZipAsync(CollectItemsDataModel model, ZipArchive archive)
        {
            await AddDocumentsToFolderAsync(model.Certificates, archive, "Certificates");

            var mrDocuments = await _context.Documents
                .Where(d => model.MrAttachments.Select(a => a.DocumentId).Contains(d.DocumentId))
                .ToListAsync();

            foreach (var doc in model.MrAttachments)
            {
                var file = mrDocuments.FirstOrDefault(x => x.DocumentId == doc.DocumentId);
                if (file != null)
                    await AddFileToZipAsync(file.FilePath, archive, $"MR Documents/{doc.MaintenanceRecordNumber}_{file.FileName}");
            }

            foreach (var doc in model.ItemAttachments)
            {
                await AddFileToZipAsync(doc.FilePath, archive, $"MR Documents/{doc.FileName}");
            }
        }

        private async Task AddDocumentsToFolderAsync(IEnumerable<EquipmentItemCertificate> docs, ZipArchive archive, string folderName)
        {
            foreach (var doc in docs)
            {
                await AddFileToZipAsync(doc.FilePath, archive, $"{folderName}/{doc.FileName}");
            }
        }
        private async Task AddFileToZipAsync(string filePath, ZipArchive archive, string entryName)
        {
            var blob = await _storage.GetBlobClient(filePath);
            if (!await blob.ExistsAsync()) return;

            var entry = archive.CreateEntry(entryName, CompressionLevel.Fastest);
            using var entryStream = entry.Open();
            using var blobStream = await blob.OpenReadAsync();
            await blobStream.CopyToAsync(entryStream);
        }

        private void AddExcelToZip(ZipArchive archive, byte[] excelBytes, string fileName)
        {
            var entry = archive.CreateEntry(fileName, CompressionLevel.Fastest);
            using var entryStream = entry.Open();
            entryStream.Write(excelBytes, 0, excelBytes.Length);
        }

        private byte[] GenerateEquipmentExcel(EquipmentItemModel item, IEnumerable<EquipmentItemLogModel> logs)
        {
            using var excelEngine = new ExcelEngine();
            var app = excelEngine.Excel;
            app.DefaultVersion = ExcelVersion.Excel2016;
            var wb = app.Workbooks.Create(1);
            var sheet = wb.Worksheets[0];
            sheet.Name = "Asset Export";

            AddEquipmentDetailsSection(sheet, item);
            AddEquipmentHistorySection(sheet, logs);

            sheet.UsedRange.AutofitColumns();

            using var ms = new MemoryStream();
            wb.SaveAs(ms);
            return ms.ToArray();
        }

        private void AddEquipmentDetailsSection(IWorksheet sheet, EquipmentItemModel item)
        {
            var headers = new[]
            {
        "Equipment Number", "Selected To", "Assigned To", "Country Of Origin", "USHTS Commodity Code",
        "Import Commodity Code", "Export Commodity Code", "Waiver Code", "Waiver Requirement",
        "Received Date", "Purchased Date", "Commodity Code", "Currency", "Price", "Net Book Value",
        "Acceptance Note", "Division", "Internal Inv.Nbr", "External Inv.Nbr", "Tracked Non-Asset Item",
        "Current Location", "Manufacturer", "Maintenance Schedule Date(s)", "Active MRs", "Info",
        "Status", "Length", "Length Unit", "Width", "Width Unit", "Depth", "Depth Unit",
        "OD", "OD Unit", "Weight", "Weight Unit"
    };

            var values = new object[]
            {
        item.EquipmentItemName, item.SelectedTo, item.AssignedTo, item.CountryOfOrigin,
        item.USHTSCommodityCode, item.ImportCommodityCode, item.ExportCommodityCode,
        item.WaiverCode, item.WaiverRequirement, item.ReceivedDateOnly, item.PurchasedDateOnly,
        item.CommodityCode, item.CurrencyName, item.Price, item.DepreciatedPrice, item.CommentsandUser,
        item.DivisionName, item.InternalInvoiceNumber, item.ExternalInvoiceNumber,
        item.TrackedNonAssetItem == true ? "Yes" : "No", item.CurrentClientLocationName,
        item.ManufacturerCompanyName, item.MaintenanceScheduleDetail, item.MRCount,
        item.EquipmentInfo, item.StatusNames, item.Height, item.HeightUnitDescription,
        item.Width, item.WidthUnitDescription, item.Depth, item.DepthUnitDescription,
        item.OuterDiameter, item.OuterDiameterUnitDescription, item.Weight, item.WeightUnitDescription
            };

            for (int c = 0; c < headers.Length; c++)
            {
                var headerCell = sheet.Range[1, c + 1];
                headerCell.Text = headers[c];
                headerCell.CellStyle.Font.Bold = true;
                headerCell.CellStyle.Color = Color.Gray;
                headerCell.CellStyle.HorizontalAlignment = ExcelHAlign.HAlignCenter;
                headerCell.CellStyle.Borders.LineStyle = ExcelLineStyle.Thin;

                var valueCell = sheet.Range[2, c + 1];
                if (values[c] is DateTime dateValue)
                    valueCell.DateTime = dateValue;
                else if (values[c] is double || values[c] is decimal || values[c] is int)
                    valueCell.Number = Convert.ToDouble(values[c]);
                else
                    valueCell.Text = values[c]?.ToString();

                valueCell.CellStyle.Borders.LineStyle = ExcelLineStyle.Thin;

                if (c == 9 || c == 10) valueCell.NumberFormat = "mm/dd/yyyy";
                if (c == 13 || c == 14) valueCell.NumberFormat = "$#,##0.00";
            }
        }

        private void AddEquipmentHistorySection(IWorksheet sheet, IEnumerable<EquipmentItemLogModel> logs)
        {
            int titleRow = 4;
            var logHeaders = new[] { "Type", "Points", "Reference", "Log", "Performed By", "Date" };

            sheet.Range[titleRow, 1, titleRow, logHeaders.Length].Merge();
            var titleCell = sheet.Range[titleRow, 1];
            titleCell.Text = "Equipment History";
            titleCell.CellStyle.Font.Bold = true;
            titleCell.CellStyle.Font.Size = 12;
            titleCell.CellStyle.Color = Color.Yellow;
            titleCell.CellStyle.HorizontalAlignment = ExcelHAlign.HAlignCenter;
            titleCell.CellStyle.Borders.LineStyle = ExcelLineStyle.Thin;

            for (int c = 0; c < logHeaders.Length; c++)
            {
                var cell = sheet.Range[titleRow + 1, c + 1];
                cell.Text = logHeaders[c];
                cell.CellStyle.Font.Bold = true;
                cell.CellStyle.Color = Color.Gray;
                cell.CellStyle.HorizontalAlignment = ExcelHAlign.HAlignCenter;
                cell.CellStyle.Borders.LineStyle = ExcelLineStyle.Thin;
            }

            int row = titleRow + 2;
            foreach (var log in logs)
            {
                sheet.Range[row, 1].Text = log.Type;
                sheet.Range[row, 2].Number = log.Points;
                sheet.Range[row, 3].Text = log.Reference ?? string.Empty;
                sheet.Range[row, 4].Text = log.Log;
                sheet.Range[row, 5].Text = log.UserName ?? string.Empty;
                sheet.Range[row, 6].DateTime = log.Date;
                sheet.Range[row, 6].NumberFormat = "mm/dd/yyyy hh:mm";

                for (int c = 1; c <= logHeaders.Length; c++)
                {
                    sheet.Range[row, c].CellStyle.Borders.LineStyle = ExcelLineStyle.Thin;
                }

                row++;
            }
        }
    }
}
