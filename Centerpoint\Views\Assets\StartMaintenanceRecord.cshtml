﻿@model EquipmentItemModel
<div class="row">
    <h5 class="col-md-12">Please select the maintenance blueprint to start this MR</h5>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>Maintenance Blueprint</label>
            @(Html.Kendo().DropDownList()
             .Name("maintenanceBlueprint")
             .Filter("contains")
             .OptionLabel("Select Maintenance Blueprint")
             .DataValueField("MaintenanceBlueprintId")
             .DataTextField("Name")
             .AutoBind(false)
             .HtmlAttributes(new { @data_bind = "value:maintenanceBlueprintId" })
                .DataSource(dataSource => dataSource.Read(read => read.Action("GetAllMaintenanceBlueprintsByCategoryId", "Lookup").Data("equipmentCategoryMaintenanceStepData"))))
        </div>
    </div>
</div>
<button id="startMaintenanceRecordConfirm" data-bind="enabled:maintenanceBlueprintId" class="btn btn-primary btn-sm mt-2">Confirm</button>
