<div class="d-flex flex-column">
     <div class="w-50">
        <div class="form-group">
            <label>Certificate Category</label>
            <br />
            @(Html.Kendo().DropDownList()
                            .Name("certificateCategory")
                            .Events(e => e.Change("changeDropdownValue"))
                            .OptionLabel("Select Category")
                            .DataTextField("Name")
                            .DataValueField("CertificateCategoryId")
                            .DataSource(dataSource => dataSource.Read("GetCertificateCategories", "Lookup"))
                            .HtmlAttributes(new { id="certificateCategory", @style = "width:100%", @data_value_primitive = "true" })
             )
        </div>
    </div>
    <div class="w-50">
            <label>Upload Document</label>
            <br />
            <div>
                        @(Html.Kendo().Upload()
                            .Name("equipmentItemAttachmentDocuments")
                            .Messages(m => m.Select("Attach Equipment Item Documents"))
                            .Multiple(true)
                            .Enable(false)
                            .Events(e => e.Success("onEquipmentItemDocumentAttached").Complete("onEquipmentItemDocumentComplete").Upload("onEquipmentItemDocumentUpload"))
                            .HtmlAttributes(new { @style = "width:300px" })
                            .Async(async => async.Save("AttachEquipmentItemDocuments", "Assets", new { @equipmentItemId = Model.EquipmentItemId}).Batch(true)))  
            </div>


    </div>
</div>    
