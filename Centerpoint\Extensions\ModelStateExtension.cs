﻿using Centerpoint.Common;
using Centerpoint.Model;
using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace Centerpoint.Extensions
{
    public static class ModelStateExtension
    {
        public static void AddErrors(this ModelStateDictionary modelState, Result result)
        {
            if (result.Succeeded) return;

            foreach (var error in result.Errors)
            {
                modelState.TryAddModelError("", error);
            }
        }

        public static void AddErrors(this ModelStateDictionary modelState, GenericResponse result)
        {
            if (!result.HasErrors())
            {
                return;
            }

            foreach (var error in result.Errors)
            {
                modelState.TryAddModelError("", error);
            }
        }
    }
}
