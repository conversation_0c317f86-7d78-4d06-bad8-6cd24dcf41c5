﻿using Centerpoint.Extensions;
using Centerpoint.Model.Configuration;
using Centerpoint.Model.Entities;
using Centerpoint.Model.ViewModels;
using Centerpoint.Service.Interfaces;
using Centerpoint.Services;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Net;

namespace Centerpoint.Controllers
{
    [AllowAnonymous]
    public class AccountController : Controller
    {
        private readonly IIdentityService _identityService;
        private readonly ICurrentUserService _currentUser;
        private readonly ILogger<AccountController> _logger;
        private readonly SignInManager<User> _signInManager;
        private readonly AzureAdSettings _azureAdSettings;
        public AccountController(IIdentityService identityService,
            ICurrentUserService currentUser,
            SignInManager<User> signInManager,
            IOptions<AzureAdSettings> azureAdSettings,
            ILogger<AccountController> logger)
        {
            _identityService = identityService;
            _currentUser = currentUser;
            _signInManager = signInManager;
            _azureAdSettings = azureAdSettings.Value;
            _logger = logger;
        }

        public IActionResult Login()
        {
            if (_azureAdSettings.Enabled)
            {
                var authProperties = _signInManager.ConfigureExternalAuthenticationProperties("AzureAD",
                   Url.Action("Index", "Home", null, Request.Scheme));

                return Challenge(authProperties, "AzureAD");
            }
            else
            {
                _logger.LogInformation("Opening Login page");
                this.SetTitle("Login");

                return View();
            }
        }

        [HttpPost]
        public async Task<IActionResult> Login(LoginRequest loginRequest)
        {
            var result = await _identityService.SigninAsync(loginRequest.Email, loginRequest.Password, loginRequest.RememberMe);
            if (result)
            {
                return RedirectToAction("Index", "Home");
            }
            ModelState.AddModelError("", "Email or password is wrong!");

            return View(loginRequest);
        }

        [Authorize]
        public IActionResult ChangePassword()
        {
            this.SetTitle("Change Password");

            return View();
        }

        [Authorize, HttpPost]
        public async Task<IActionResult> ChangePassword(ChangePasswordRequest changePasswordRequest)
        {
            var result = await _identityService.ChangePasswordAsync(_currentUser.UserId,
                                                                    changePasswordRequest.CurrentPassword,
                                                                    changePasswordRequest.NewPassword);
            ViewBag.Succeeded = result.Succeeded;
            ModelState.AddErrors(result);

            this.SetTitle("Change Password");
            return View(changePasswordRequest);
        }

        public IActionResult ForgotPassword()
        {
            this.SetTitle("Forgot Password");

            return View();
        }

        [HttpPost]
        public async Task<IActionResult> ForgotPassword(ForgotPasswordRequest forgotPassword)
        {
            var result = await _identityService.ForgotPassword(forgotPassword.Email);

            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(result.Response);
        }

        public IActionResult ResetPassword()
        {
            this.SetTitle("Reset Password");

            return View(new ResetPasswordRequest
            {
                UserId = Convert.ToInt32(Request.Query["user"].ToString()),
                Token = Request.Query["token"].ToString()
            });
        }

        [HttpPost]
        public async Task<IActionResult> ResetPassword(ResetPasswordRequest resetPasswordRequest)
        {
            if (!ModelState.IsValid)
            {
                return View(resetPasswordRequest);
            }

            var result = await _identityService.ResetPasswordAsync(resetPasswordRequest.UserId,
                                                                   resetPasswordRequest.Token,
                                                                   resetPasswordRequest.NewPassword);
            ViewBag.Succeeded = result.Succeeded;
            ModelState.AddErrors(result);

            if (result.Succeeded)
            {
                return RedirectToAction("ResetPasswordConfirmation");
            }

            return RedirectToAction("ForgotPassword");

        }

        public IActionResult ResetPasswordConfirmation()
        {
            this.SetTitle("Reset Password Confirmation");

            return View();
        }

        [Authorize]
        public new async Task<IActionResult> SignOut()
        {
            await this.HttpContext.SignOutAsync();
            await _identityService.SignOutAsync();
            var postLogoutRedirectUri = Url.Action("Login", "Account", values: null, protocol: HttpContext.Request.Scheme);

            if (_azureAdSettings.Enabled && postLogoutRedirectUri != null)
            {
                var signOutUrl = $"https://login.microsoftonline.com/common/oauth2/v2.0/logout?post_logout_redirect_uri={Uri.EscapeDataString(postLogoutRedirectUri)}";
                return Redirect(signOutUrl);
            }
            else
            {
                return RedirectToAction("Login");
            }
        }

        public async Task<ActionResult> Unauthorised()
        {
            this.SetTitle("Unauthorised");

            await this.HttpContext.SignOutAsync();
            await _identityService.SignOutAsync();

            var postLogoutRedirectUri = Url.Action("Login", "Account", values: null, protocol: HttpContext.Request.Scheme);
            
            var signOutUrl = $"https://login.microsoftonline.com/common/oauth2/v2.0/logout?post_logout_redirect_uri={Uri.EscapeDataString(postLogoutRedirectUri)}";

            return View(signOutUrl);
        }

        [Authorize]
        public async Task<ActionResult> UpdateMobileNotification(bool mobileNotification, bool showAll)
        {
            await _identityService.UpdateMobileNotification(_currentUser.UserId, mobileNotification, showAll);

            return Json(new { success = "true" });
        }

        [Authorize]
        public async Task<ActionResult> UpdateEmailNotification(bool emailNotification, bool showAll)
        {
            await _identityService.UpdateEmailNotification(_currentUser.UserId, emailNotification, showAll);

            return Json(new { success = "true" });
        }
    }
}
