﻿<div class="row">
     <div class="col-md-6">
        <div class="form-group">
            <label>Current Client</label>
            <br />
            @(Html.Kendo().DropDownList()
             .Name("currentClient")
             .Filter("contains")
             .OptionLabel("Select Client")
             .DataTextField("Text")
             .DataValueField("Value")
             .Events(x=>x.Change("cascadeDropdownFilterHelper"))
             .DataSource(d => d.Read("GetCompanies", "Lookup"))
             .AutoBind(false)
             .HtmlAttributes(new { @data_bind = "value:currentCompanyId", @data_cascade_to="currentLocation" }))
        </div>
    </div>
     <div class="col-md-6">
        <div class="form-group">
            <label>Current Location</label>
            <br />
            @(Html.Kendo().DropDownList()
              .Name("currentLocation")
              .Filter("contains")
              .OptionLabel("Select Client Location")
              .DataTextField("Text")
              .DataValueField("Value")
              .Events(x=>x.Change("cascadeDropdownFilterHelper"))
              .DataSource(source => {
                  source.Read(read => {
                      read.Action("GetLocationsByCompanyId", "Lookup").Data("filterCompanyLocations");
                  });
              })
              .AutoBind(false)
              .HtmlAttributes(new { @data_bind = "value:currentCompanyLocationId", @data_cascade_to="project" }))
        </div>
    </div>
    </div>
<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>Project</label>
            <br />
            @(Html.Kendo().DropDownList()
             .Name("project")
             .Filter("contains")
             .OptionLabel("Select Project")
             .DataValueField("ProjectId")
             .DataTextField("ProjectNameandObjectives")
             .DataSource(source => {
                 source.Read(read => {
                     read.Action("GetAllProjectsByCompanyLocationId", "Lookup").Data("filterByCurrentCompanyLocation");
                 });
             })
             .AutoBind(false)
             .HtmlAttributes(new { @data_bind = "value:currentProjectId" }))
        </div>
    </div>
</div>
<span><a id="changeCurrentLocationConfirm" data-bind="enabled:currentCompanyLocationId" class="btn btn-primary btn-sm">Confirm</a></span>
