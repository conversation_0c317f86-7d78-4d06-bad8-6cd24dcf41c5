{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost\\SQLEXPRESS;Initial Catalog=wellsense_04_05_25;Integrated Security=True;MultipleActiveResultSets=true;Encrypt=True;TrustServerCertificate=True;"
    //"DefaultConnection": "Server=***********;Initial Catalog=regiis_live_04_Apr_24;Integrated Security=True;MultipleActiveResultSets=true;Encrypt=True;TrustServerCertificate=True;"
    //"DefaultConnection": "Data Source=insiso-dev;Initial Catalog=REGIISV2_DEV;User ID=sa;password=*********;MultipleActiveResultSets=true"
    //"DefaultConnection": "Data Source=***********;Initial Catalog = regiis061124;User ID=sa;password=*********;MultipleActiveResultSets=true;TrustServerCertificate=True;"
    //"DefaultConnection": "Server=tcp:centerpointapp.database.windows.net,1433;Initial Catalog=centerpoint-regiis;Persist Security Info=False;User ID=centerpoint;Password=**********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"

  },
  "BlobStorage": {
    "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=centerpointstorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    "Container": "development"
  },
  "EmailSettings": {
    "Username": "<EMAIL>",
    "Password": "**********",
    "SmtpHost": "smtp.office365.com",
    "SmtpPort": "587",
    "From": "<EMAIL>",
    "UseSSL": false

  },
  "Settings": {
    "webpages:Version": "*******",
    "webpages:Enabled": false,
    "ClientValidationEnabled": true,
    "UnobtrusiveJavaScriptEnabled": true,
    "EventTime": 30,
    "LoginAttempts": 5,
    "DownloadsFolderName": "Downloads",
    "DepreciationTotalYears": 8,
    "EmailGetter": "<EMAIL>",
    //"Client": "Regiis"
    "Client": "Well-Sense Technology Limited"

  },
  "TwilioSMS": {
    "TWILIO_ACCOUNT_SID": "**********************************",
    "TWILIO_AUTH_TOKEN": "398a000c97d4b7829326b085bb333828",
    "TWILIO_FROM_PHONE": "+************"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },

  "AzureAdSettings": {
    "Enabled": false,
    "ClientId": "4e196ea8-2422-46cf-bf1e-a3169e51a3b9",
    "TenantId": "22acb312-3d52-4fe4-8dd0-9252e7507bb4",
    "Authority": "https://login.microsoftonline.com/common/"
  },
  "AppUrl": "https://localhost:5229"
}
