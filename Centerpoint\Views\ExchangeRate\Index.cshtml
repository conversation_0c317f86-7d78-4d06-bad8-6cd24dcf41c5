﻿@model CurrencyModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fas fa-money-bill"></i>
        Exchange Rates
        (<span data-bind="text:totalCurrencies"></span>)
    </h4>
</div>
<hr />

<div class="grid-container">
    @(Html.Kendo().Grid<CurrencyModel>()
            .Name("currencyGrid")
            .Columns(c => {
                c.<PERSON>und(p => p.Name);
                c.<PERSON>und(p => p.Multiple).Format("{0:n4}");
                c.<PERSON>und(p => p.Format);
            })
            .Editable(editable => editable.Mode(GridEditMode.InLine))
            .ToolBar(t => {
                t.Excel().Text("Export");
            }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
            .Sortable()
            .Filterable()
            .Scrollable(s => s.Height(500))
            .Resizable(c => c.<PERSON>um<PERSON>(true))
            .ColumnMenu(c => c.Columns(true))
            .Events(e => e.DataBound("updateCurrencyTotal"))
            .Excel(excel => excel
                    .FileName(string.Format("Centerpoint_Currencies_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                    .Filterable(true)
                    .ProxyURL(Url.Action("Export", "Admin"))
            )
            .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Model(m => m.Id(p => p.CurrencyId))
                .Read(read => read.Action("GetCurrencies", "ExchangeRate")))
            )      
</div>

<environment include="Development">
    <script src="~/js/views/exchangeRate/exchangeRate.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/exchangeRate/exchangeRate.min.js" asp-append-version="true"></script>
</environment>