﻿using Centerpoint.Model.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Centerpoint.Model.ViewModels
{
    public class CollectItemsDataModel
    {
        public EquipmentItemModel EquipmentItem { get; set; } = new EquipmentItemModel();
        public List<Document> ItemAttachments { get; set; }
        public List<MaintenanceRecordAttachmentModel> MrAttachments { get; set; }
        public List<EquipmentItemCertificate> Certificates { get; set; }
    }
}
