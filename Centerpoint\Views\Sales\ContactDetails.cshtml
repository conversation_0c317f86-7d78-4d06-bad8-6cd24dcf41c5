﻿<div>
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label>Location</label><br />
                @(Html.Kendo().DropDownList()
                        .Name("location")
                        .Filter(FilterType.Contains)
                        .OptionLabel("Select Location")
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .DataSource(d => d.<PERSON>("GetLocationsByCompanyId", "Lookup", new { @companyId = ViewBag.CompanyId}))
                        .HtmlAttributes(new { @data_value_primitive = "true", @style = "font-size: 14px;" }))
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label>Not Active</label>
                <br />
                @(Html.Kendo().CheckBox().Name("active"))
            </div>
        </div>
    </div>
    <hr />
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label>First Name</label>
                <br />
                @(Html.Kendo().TextBox().Name("firstName")
                         .HtmlAttributes(new { @class = "form-control", @style = "width:100%; font-size: 14px;", tabindex = 1 }))
            </div>
            <div class="form-group">
                <label>Position</label>
                <br />
                @(Html.Kendo().TextBox().Name("position")
                       .HtmlAttributes(new { @class = "form-control", @style = "width:100%; font-size: 14px;", tabindex = 3 }))
            </div>
            <div class="form-group">
                <label>Mobile</label>
                <br />
                @(Html.Kendo().TextBox().Name("mobile")
                       .HtmlAttributes(new { @class = "form-control", @style = "width:100%; font-size: 14px;", tabindex = 5 }))
            </div>
        </div>
        <div class="col-md-5">
            <div class="form-group">
                <label>Last Name</label>
                <br />
                @(Html.Kendo().TextBox().Name("lastName")
                       .HtmlAttributes(new { @class = "form-control", @style = "width:100%; font-size: 14px;", tabindex = 2 }))
            </div>
            <div class="form-group">
                <label>Email Address</label>
                <br />
                @(Html.Kendo().TextBox().Name("emailAddress")
                       .HtmlAttributes(new { @class = "form-control", @style = "width:100%; font-size: 14px;", tabindex = 4 }))
            </div>
            <div class="form-group">
                <label>Work Telephone</label>
                <br />
                @(Html.Kendo().TextBox().Name("workPhone")
                       .HtmlAttributes(new { @class = "form-control", @style = "width:100%; font-size: 14px;", tabindex = 6 }))
            </div>
        </div>
    </div>
    <hr />
    <div>
        <div class="row">
            <div class="col-md-12"></div>
            <div class="form-group">
                <label>Comment</label>
                @(Html.TextArea("Comment", null, new { @class = "form-control", @style = "width:100%", @rows = "5", tabindex = 7 }))
            </div>
        </div>
    </div>
</div>
<hr />
<div class="d-flex justify-content-end">
  <button id="addContact" class="btn btn-success btn-sm">Add Contact</button>
</div>

