
        $(document).ready(function () {

            var editor = $('#Description').data('kendoEditor');
            editor.body.contentEditable = false;

            var joinedTask = editMaintenanceRecordStepModel.modelTasks;

            if (joinedTask) {
                viewModel.set("tasks", joinedTask.split(","));
            }
        });

        function deleteTaskClick() {
            var tasks = viewModel.get("tasks");
            tasks.splice(tasks.indexOf(), 1);
            viewModel.set("tasks", tasks);
        }

        var viewModel = new kendo.observable({
            stepType: editMaintenanceRecordStepModel.modelType,
            task: "",
            tasks: [],

            addtaskClick: function () {
                var task = this.get("task");
                var tasks = this.get("tasks");

                tasks.push(task);
                this.set("tasks", tasks);
                this.set("task", "");

            },

            stepTasks: function () {
                var tasks = this.get("tasks");
                return tasks.join(",");
            },

            canAddTask: function () {
                var task = this.get("task")
                return task;
            },

            checkBoxVisible: function () {
                var stepType = this.get("stepType");

                if (stepType == editMaintenanceRecordStepModel.centerpointCommonConstantsMaintenanceBlueprintStepTypeConstantBoolean) {
                    return true;
                } else {
                    return false;
                }
            },

            freeTextVisible: function () {
                var stepType = this.get("stepType");

                if (stepType == editMaintenanceRecordStepModel.centerpointCommonConstantsMaintenanceBlueprintStepTypeConstantText ||
                    stepType == editMaintenanceRecordStepModel.centerpointCommonConstantsMaintenanceBlueprintStepTypeConstantBoolean ||
                    stepType == editMaintenanceRecordStepModel.centerpointCommonConstantsMaintenanceBlueprintStepTypeConstantDocument) {
                    return true;
                } else {
                    return false;
                }
            },
            resultVisible: function () {
                var stepType = this.get("stepType");

                if (stepType == editMaintenanceRecordStepModel.centerpointCommonConstantsMaintenanceBlueprintStepTypeConstantText ||
                     stepType == editMaintenanceRecordStepModel.centerpointCommonConstantsMaintenanceBlueprintStepTypeConstantDocument) {
                    return true;
                } else {
                    return false;
                }
            },
            documentVisible: function () {
                var stepType = this.get("stepType");

                if (stepType == editMaintenanceRecordStepModel.centerpointCommonConstantsMaintenanceBlueprintStepTypeConstantDocument) {
                    return true;
                } else {
                    return false;
                }
            },

            deleteAttachment:function(){
                var confirmDelete = confirm("Are you sure you wish to delete this attachment");

                if(confirmDelete){
                    window.location.href= `/Maintenance/DeleteMaintenanceRecordStepDocument?id=${editMaintenanceRecordStepModel.modelMaintenanceRecordStepId}`;
                }
            },
        });

        kendo.bind(document.body.children, viewModel);