﻿@model ServiceImprovementModel
<p>In order to the change the Corrective Action Target Date, please add a new date and fill in the reason for date change</p>
<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>New Corrective Action Target Date</label>
            <br />
            @(Html.Kendo().DatePicker().Name("CorrectiveActionNewTargetDate").Min(Model.CorrectiveActionTargetDate.HasValue ? Model.CorrectiveActionTargetDate.Value.AddDays(1) : DateTime.Now.AddDays(1)).HtmlAttributes(new { @data_bind = "value:newCorrectiveActionTargetDate", @style = "width:200px; font-size: 14px;" }))
        </div>
        <div class="form-group">
            <label>Comment</label>
            @(Html.TextArea("NewCorrectiveActionTargetDateCommentWindow", null, new { @class = "form-control", @style = "100%", @rows = "5",@data_bind="value:newCorrectiveActionComment", @data_value_update = "keyup" }))
        </div>
    </div>
</div>
<button id="correctiveActionDateChangeSelectedConfirm" data-bind="enabled:newCorrectiveActionComment" class="btn btn-primary btn-sm">Confirm</button>
@Html.HiddenFor(m => m.CorrectiveActionTargetDate)