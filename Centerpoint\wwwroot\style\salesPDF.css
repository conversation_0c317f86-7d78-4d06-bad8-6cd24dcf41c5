﻿.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0,0,0,.125);
    border-radius: 0.25rem;
}
.card-header {
    padding: 0.4rem 0.5rem;
    margin-bottom: 0;
    color: #fff;
    background-color: #6eb6b4;
    border-bottom: 1px solid rgba(0,0,0,.125);
}
    .card-header:first-child {
        border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
    }

.card-body {
    flex: 1 1 auto;
    min-height: 1px;
    padding: 1.25rem;
}

h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    margin-bottom: 0;
}
h6 {
    font-size: 13px;
}
.card-list-item-count {
    color: #fff;
}

.card-list-item-count-exacrt-width {
    width: 150px;
}

.justify-content-between {
    justify-content: space-between !important;
}

.d-flex {
    display: flex !important;
}

.card-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}
.w-50 {
    width: 50% !important;
}
.panel-heading {
    background: #6eb6b4;
    color: #fff;
}

.panel-heading h1,
.panel-heading h2 {
    padding: 12px 18px;
    border-bottom: 1px solid transparent;
    margin-bottom: 5px;
}

.table > thead > tr > th {
    border-bottom: 1px solid #DDDDDD;
    vertical-align: bottom;
}

.table > thead > tr > th,
.table > tbody > tr > th,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > tbody > tr > td,
.table > tfoot > tr > td {
    border-top: 1px solid #e7eaec;
    line-height: 1.42857;
    padding: 8px;
    vertical-align: top;
}

table > thead > tr > th {
    font-weight: bold;
}

.table-bordered {
    border: 1px solid #EBEBEB;
}

    .table-bordered > thead > tr > th,
    .table-bordered > thead > tr > td {
        background-color: #F5F5F6;
        border-bottom-width: 1px;
    }

    .table-bordered > thead > tr > th,
    .table-bordered > tbody > tr > th,
    .table-bordered > tfoot > tr > th,
    .table-bordered > thead > tr > td,
    .table-bordered > tbody > tr > td,
    .table-bordered > tfoot > tr > td {
        border: 1px solid #e7e7e7;
    }

body {
    color: #676a6c !important;
    font-size: 14px !important;
    margin: 0;
    margin-right: 1px !important;
}

h1, h2, h3, h4, h5, h6, .card, .panel {
    font-family: 'Raleway', sans-serif !important;
}

.table {
     table-layout: fixed; 
     color: #676a6c !important; 
     width: 100%; 
}
table {
    border-collapse: collapse;
    overflow-wrap: break-word;
}

.panel-title {
    font-size: 18px;
}

.panel-body {
    padding: 0px;
    border-bottom: none;
    width:100%;
}

.panel-default {
    border: none;
}

.breakhere {
    page-break-after: always;
}

table, th, td {
    border: 1px solid #000000;
    text-align: center;
}