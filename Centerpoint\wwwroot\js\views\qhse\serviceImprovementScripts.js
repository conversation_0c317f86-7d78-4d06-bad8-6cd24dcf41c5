$(document).ready(function () {
        onTextAreaChange();
    $('#editServiceImprovement').submit(function (e) {
        e.preventDefault();
        var formData = $(this).serialize();

        $.ajax({
            type: 'POST',
            url: '/Qhse/EditServiceImprovement',
            data: formData,
            success: function (result) {
                updateMessage("SIF");
                window.location.href = '/Qhse/EditServiceImprovement?id=' + result.ServiceImprovementId + '&tabIndex=' + viewModel.get("tabIndex");
            },
            error: function (xhr, textStatus, errorThrown) {
                jqXHRErrors(e)
            }
        });
    });
        onTextAreaChange();
});

function validateEditServiceImprovementForm(e) {
    e.preventDefault();

    var formData = $('#editServiceImprovement').serialize();

    $.ajax({
        type: 'POST',
        url: '/Qhse/EditServiceImprovement',
        data: formData,
        success: function (result) {
            updateMessage("SIF");
        },
        error: function (xhr, textStatus, errorThrown) {
            jqXHRErrors(e)
        }
    });

}

function companyChange(e) {
    if ($("#CompanyContactId").data("kendoDropDownList").value()) {
        let companyLocationDropDown = $("#CompanyLocationId").data("kendoDropDownList");
        companyLocationDropDown.dataSource.read().then(() => companyLocationDropDown.trigger("change"))
    }
    else {
        cascadeDropdownFilterHelper(e)
    }
}

$("#rejectConfirm").click(function () {
    $.ajax({
        type: 'POST',
        url: '/Qhse/RejectReason',
        data: {
            serviceImprovementId: serviceImprovementScriptModel.modelServiceImprovementId,
            comment: $("#RejectReasonComment").val()
        },
        success: function () {
            $("#editServiceImprovement").submit();
        }
    });
});

$("#reRequestInvestigationConfirm").click(function () {
    $.ajax({
        type: 'POST',
        url: '/Qhse/ReRequestInvestigationReason',
        data: {
            serviceImprovementId: serviceImprovementScriptModel.modelServiceImprovementId,
            comment: $("#ReRequestInvestigationComment").val()
        },
        success: function () {
            window.location.reload();
        },
    });
});

$("#reEvaluatePreventiveConfirm").click(function () {
    $.ajax({
        type: 'POST',
        url: '/Qhse/ReEvaluatePreventiveReason',
        data: {
            serviceImprovementId: serviceImprovementScriptModel.modelServiceImprovementId,
            comment: $("#ReEvaluatePreventiveComment").val()
        },
        success: function () {
            window.location.reload();
        },
    });
});

$("#reEvaluateCorrectiveConfirm").click(function () {
    $.ajax({
        type: 'POST',
        url: '/Qhse/ReEvaluateCorrectiveReason',
        data: {
            serviceImprovementId: serviceImprovementScriptModel.modelServiceImprovementId,
            comment: $("#ReEvaluateCorrectiveComment").val()
        },
        success: function () {
            window.location.reload();
        },
    });
});
$("#dateChangeSelectedConfirm").click(function () {
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: '/Qhse/RequestNewInvestigationDate',
        data: {
            serviceImprovementId: serviceImprovementScriptModel.modelServiceImprovementId,
            dateRequested: kendo.toString($("#NewTargetDate").data("kendoDatePicker").value(), "dd/MM/yyyy"),
            comment: $("#NewInvestigationTargetDateComment").val()
        },
        success: function () {
            window.location.reload();
        },
    });
});

$("#preventiveActionDateChangeSelectedConfirm").click(function () {
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: '/Qhse/RequestNewPreventiveActionDate',
        data: {
            serviceImprovementId: serviceImprovementScriptModel.modelServiceImprovementId,
            dateRequested: kendo.toString($("#PreventiveActionNewTargetDate").data("kendoDatePicker").value(), "dd/MM/yyyy"),
            comment: $("#NewPreventiveActionTargetDateCommentWindow").val()
        },
        success: function () {
            window.location.reload();
        },
    });
});

$("#correctiveActionDateChangeSelectedConfirm").click(function () {
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: '/Qhse/RequestNewCorrectiveActionDate',
        data: {
            serviceImprovementId: serviceImprovementScriptModel.modelServiceImprovementId,
            dateRequested: kendo.toString($("#CorrectiveActionNewTargetDate").data("kendoDatePicker").value(), "dd/MM/yyyy"),
            comment: $("#NewCorrectiveActionTargetDateCommentWindow").val()
        },
        success: function () {
            window.location.reload();
        },
    });
});
$("#rejectInvestigationDateConfirm").click(function () {
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: '/Qhse/RejectInvestigationDateReason',
        data: {
            serviceImprovementId: serviceImprovementScriptModel.modelServiceImprovementId,
            comment: $("#InvestigationDateRejectReasonComment").val()
        },
        success: function () {
            window.location.reload();
        },
    });
});

$("#rejectPreventiveActionDateConfirm").click(function () {
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: '/Qhse/RejectPreventiveActionDateReason',
        data: {
            serviceImprovementId: serviceImprovementScriptModel.modelServiceImprovementId,
            comment: $("#PreventiveActionDateRejectReasonComment").val()
        },
        success: function () {
            window.location.reload();
        },
    });
});

$("#rejectCorrectiveActionDateConfirm").click(function () {
    $.ajax({
        type: 'POST',
        dataType: 'json',
        traditional: true,
        url: '/Qhse/RejectCorrectiveActionDateReason',
        data: {
            serviceImprovementId: serviceImprovementScriptModel.modelServiceImprovementId,
            comment: $("#CorrectiveActionDateRejectReasonComment").val()
        },
        success: function () {
            window.location.reload();
        },
    });
});

function sifPdf(fileName) {
    var element = $("#page");
    kendo.ui.progress(element, true);
    setTimeout(function () { kendo.ui.progress(element, false); }, 8000);
    $("<div id='dialog'></div>").kendoDialog({
        closable: false,
        title: fileName,
        width: 500,
        buttonLayout: "normal",
        content: `The \'${fileName}\' document is been generated, please wait for it.`,
        actions: [{ text: "OK", cssClass: 'btn-primary' }]
    }).data("kendoDialog").open().center()
}

function updateServiceImprovementDocumentGrid() {
    var serviceImprovementDocumentsGrid = $("#serviceImprovementDocumentsGrid").data("kendoGrid");
    var totalServiceImprovementDocuments = serviceImprovementDocumentsGrid.dataSource.total();
    viewModel.set("totalServiceImprovementDocuments", totalServiceImprovementDocuments);
    refreshSifLogGrid();
}

function onServiceImprovementDocumentAttached() {
    var serviceImprovementDocumentsGrid = $("#serviceImprovementDocumentsGrid").data("kendoGrid");
    serviceImprovementDocumentsGrid.dataSource.read();
}

function refreshSifLogGrid() {
    var sifLogGrid = $("#sifLogGrid").data("kendoGrid");
    sifLogGrid.dataSource.read();
}

function onServiceImprovementDocumentUpload(e) {
    uploadValidation(e);
    $(".k-upload-files.k-reset").show();
}

function onServiceImprovementDocumentComplete(e) {
    $(".k-upload-files.k-reset").find("li").remove();
    $(".k-upload-files.k-reset").slideUp();
}

function filterCompanyLocations() {
    return {
        companyId: $("#CompanyId").data("kendoDropDownList").value()
    };
}

function filterCompanyContacts() {
    return {
        companyLocationId: $("#CompanyLocationId").data("kendoDropDownList").value()
    };
}

function serviceImprovementData() {
    return {
        sId: serviceImprovementScriptModel.modelSid
    };
}
function updateSifLogs() {
    var sifLogGrid = $("#sifLogGrid").data("kendoGrid");
    var totalSifLogs = sifLogGrid.dataSource.total();
    viewModel.set("totalSifLogs", totalSifLogs);

    var grid = this;

    grid.table.find("tr[role='row']").each(function () {
        var model = grid.dataItem(this);

        if (!model.Details) {
            $(this).find(".k-hierarchy-cell .k-icon").hide();
        }
    });
}

function onTextAreaChange() {
    var textArea = $("#SignOffComment").data("kendoTextArea");
    var button = $("#signOffButton");
    if (textArea && button) {
        if (textArea.value().trim().length > 0) {
            button.removeClass("disabled");
            button.removeAttr("disabled");
        } else {
            button.addClass("disabled");
            button.attr("disabled", true);
        }
    }
}


$("#sifTabs li").click(function () {
    viewModel.set("tabIndex", $(this).index());
})

function validateRequiredFields(ids) {
    var hasErorrs = false;
    ids.forEach(id => {
        if (!document.getElementById(id).value) {
            showError(`"Value not found or invalid for ${id}"`)
            hasErorrs = true;
        }
    });
    return hasErorrs;
}

var viewModel = kendo.observable({
    tabIndex: serviceImprovementScriptModel.tabIndexModel,
    isAdmin: serviceImprovementScriptModel.isAdminModel,
    totalSifLogs: 0,
    sifAdminUserId: serviceImprovementScriptModel.sifAdminUserIdModel,
    totalServiceImprovementDocuments: 0,
    isJobRelated: serviceImprovementScriptModel.isJobRelatedModel,
    createdDate: serviceImprovementScriptModel.createdDateModel,
    correctiveActionUserId: serviceImprovementScriptModel.correctiveActionUserIdModel,
    preventiveActionUserId: serviceImprovementScriptModel.preventiveActionUserIdModel,
    investigationUserId: serviceImprovementScriptModel.investigationUserIdModel,
    investigationTargetDate: serviceImprovementScriptModel.investigationTargetDateModel,
    preventiveActionTargetDate: serviceImprovementScriptModel.preventiveActionTargetDateModel,
    correctiveActionTargetDate: serviceImprovementScriptModel.correctiveActionTargetDateModel,
    newInvestigationTargetDate: serviceImprovementScriptModel.newInvestigationTargetDateModel,
    newPreventiveActionTargetDate: serviceImprovementScriptModel.newPreventiveActionTargetDateModel,
    newCorrectiveActionTargetDate: serviceImprovementScriptModel.newCorrectiveActionTargetDateModel,
    correctiveActionAcceptedDate: serviceImprovementScriptModel.correctiveActionAcceptedDateModel,
    preventiveActionAcceptedDate: serviceImprovementScriptModel.preventiveActionAcceptedDateModel,
    investigationAcceptedDate: serviceImprovementScriptModel.investigationAcceptedDateModel,
    isInvestigationComplete: serviceImprovementScriptModel.isInvestigationCompleteModel,
    isPreventiveActionComplete: serviceImprovementScriptModel.isPreventiveActionCompleteModel,
    isCorrectivelActionComplete: serviceImprovementScriptModel.isCorrectivelActionCompleteModel,
    newInvestigationActionComment: serviceImprovementScriptModel.newInvestigationActionCommentModel,
    newCorrectiveActionComment: serviceImprovementScriptModel.newCorrectiveActionCommentModel,
    newPreventiveActionComment: serviceImprovementScriptModel.newPreventiveActionCommentModel,
    requestCorrectiveAction: serviceImprovementScriptModel.requestCorrectiveActionModel,
    correctiveActionParty: serviceImprovementScriptModel.correctiveActionPartyModel,
    requestPreventiveAction: serviceImprovementScriptModel.requestPreventiveActionModel,
    preventiveActionParty: serviceImprovementScriptModel.preventiveActionPartyModel,
    requestInvestigationAction: serviceImprovementScriptModel.requestInvestigationActionModel,
    investigationActionParty: serviceImprovementScriptModel.investigationActionPartyModel,
    attachmentStripText: function () {
        return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Attachment (<span data-bind="text:totalServiceImprovementDocuments"></span>)</span>`;
    },
    sifUpdatesStripText: function () {
        return `<span class="k-link"><i class="fa fa-clock mr-1"></i> SIF Updates (<span data-bind="text:totalSifLogs"></span>)</span>`;
    },

    deleteSif: function (event) {
        event.preventDefault();
        var confirmation = confirm("Are you sure you want to delete this SIF?");

        if (confirmation) {
            window.location.href = `/Qhse/DeleteServiceImprovement?id=${serviceImprovementScriptModel.modelServiceImprovementId}`;
        }
    },

    enableCorrectionActionButton: function () {
        var requestCorrectiveAction = this.get("requestCorrectiveAction");
        var correctiveActionParty = this.get("correctiveActionParty");
        var correctiveActionTargetDate = this.get("correctiveActionTargetDate");

        return this.correctiveActionParty && correctiveActionTargetDate && requestCorrectiveAction;
    },

    enablePreventiveActionButton: function () {
        var requestPreventiveAction = this.get("requestPreventiveAction");
        var preventiveActionParty = this.get("preventiveActionParty");
        var preventiveActionTargetDate = this.get("preventiveActionTargetDate");

        return this.preventiveActionParty && preventiveActionTargetDate && requestPreventiveAction;
    },

    enableInvestigationActionButton: function () {
        var requestInvestigationAction = this.get("requestInvestigationAction");
        var investigationActionParty = this.get("investigationActionParty");
        var investigationTargetDate = this.get("investigationTargetDate");

        return this.investigationActionParty && investigationTargetDate && requestInvestigationAction;
    },

    canEditInvestigation: function () {
        return !this.get("isInvestigationComplete") || this.get("isAdmin");
    },

    canEditPreventiveAction: function () {
        return !this.get("isPreventiveActionComplete") || this.get("isAdmin");
    },

    canEditCorrectiveAction: function () {
        return !this.get("isCorrectivelActionComplete") || this.get("isAdmin");
    },

    canEditCorrectiveActionReEvaluatedReason: function () {
        return this.get("isAdmin");
    },

    canEditReRequestedReason: function () {
        return this.get("isAdmin");
    },
    canEditPreventiveActionReEvaluatedReason: function () {
        return this.get("isAdmin");
    },
    canEditTargetExtensionRejectReason: function () {
        return this.get("isAdmin");
    },
    canEditCorrectiveActionTargetDateRejectReason: function () {
        return this.get("isAdmin");
    },
    canEditPreventiveActionTargetDateRejectReason: function () {
        return this.get("isAdmin");
    },

    isInvestigationTargetDateValid: function () {
        var correctiveActionTargetDate = this.get("correctiveActionTargetDate");
        var investigationTargetDate = this.get("investigationTargetDate");

        if (correctiveActionTargetDate && investigationTargetDate) {
            correctiveActionTargetDate = kendo.parseDate(correctiveActionTargetDate) ? kendo.parseDate(correctiveActionTargetDate) : correctiveActionTargetDate;
            investigationTargetDate = kendo.parseDate(investigationTargetDate) ? kendo.parseDate(investigationTargetDate) : investigationTargetDate;

            return investigationTargetDate < correctiveActionTargetDate;

        }

        return true;
    },

    isPreventiveActionTargetDateValid: function () {
        var investigationTargetDate = this.get("investigationTargetDate");
        var preventiveActionTargetDate = this.get("preventiveActionTargetDate");

        if (preventiveActionTargetDate && investigationTargetDate) {
            investigationTargetDate = kendo.parseDate(investigationTargetDate) ? kendo.parseDate(investigationTargetDate) : investigationTargetDate;
            preventiveActionTargetDate = kendo.parseDate(preventiveActionTargetDate) ? kendo.parseDate(preventiveActionTargetDate) : preventiveActionTargetDate;

            return preventiveActionTargetDate < investigationTargetDate;
        }
        return true;
    },

    isNewInvestigationTargetDateValid: function () {
        var investigationTargetDate = this.get("investigationTargetDate");
        var newInvestigationTargetDate = this.get("newInvestigationTargetDate");

        if (investigationTargetDate && newInvestigationTargetDate) {
            investigationTargetDate = kendo.parseDate(investigationTargetDate) ? kendo.parseDate(investigationTargetDate) : investigationTargetDate;
            newInvestigationTargetDate = kendo.parseDate(newInvestigationTargetDate) ? kendo.parseDate(newInvestigationTargetDate) : newInvestigationTargetDate;

            return investigationTargetDate < newInvestigationTargetDate;
        }

        return true;
    },

    isNewPreventiveActionTargetDateValid: function () {
        var preventiveActionTargetDate = this.get("preventiveActionTargetDate");
        var newPreventiveActionTargetDate = this.get("newPreventiveActionTargetDate");

        if (preventiveActionTargetDate && newPreventiveActionTargetDate) {
            preventiveActionTargetDate = kendo.parseDate(preventiveActionTargetDate) ? kendo.parseDate(preventiveActionTargetDate) : preventiveActionTargetDate;
            newPreventiveActionTargetDate = kendo.parseDate(newPreventiveActionTargetDate) ? kendo.parseDate(newPreventiveActionTargetDate) : newPreventiveActionTargetDate;

            return newPreventiveActionTargetDate < preventiveActionTargetDate;
        }

        return true;
    },

    isNewCorrectiveActionTargetDateValid: function () {
        var correctiveActionTargetDate = this.get("correctiveActionTargetDate");
        var newCorrectiveActionTargetDate = this.get("newCorrectiveActionTargetDate");

        if (correctiveActionTargetDate && newCorrectiveActionTargetDate) {
            correctiveActionTargetDate = kendo.parseDate(correctiveActionTargetDate) ? kendo.parseDate(correctiveActionTargetDate) : correctiveActionTargetDate;
            newCorrectiveActionTargetDate = kendo.parseDate(newCorrectiveActionTargetDate) ? kendo.parseDate(newCorrectiveActionTargetDate) : newCorrectiveActionTargetDate;

            return correctiveActionTargetDate < newCorrectiveActionTargetDate;
        }

        return true;
    },

    isNewCorrectiveActionCommentValid: function () {
        var newCorrectiveActionTargetDate = this.get("newCorrectiveActionTargetDate");
        var newCorrectiveActionComment = this.get("newCorrectiveActionComment");

        return this.isNewCorrectiveActionTargetDateValid() && newCorrectiveActionComment && newCorrectiveActionTargetDate;
    },

    isNewPreventiveActionCommentValid: function () {
        var newPreventiveActionTargetDate = this.get("newPreventiveActionTargetDate");
        var newPreventiveActionComment = this.get("newPreventiveActionComment");

        return this.isNewPreventiveActionTargetDateValid() && newPreventiveActionComment && newPreventiveActionTargetDate;
    },

    isNewInvestigationActionCommentValid: function () {
        var newInvestigationTargetDate = this.get("newInvestigationTargetDate");
        var newInvestigationActionComment = this.get("newInvestigationActionComment");

        return this.isNewInvestigationTargetDateValid() && newInvestigationActionComment && newInvestigationTargetDate;
    },

    isPreventiveActionDateValid: function () {
        return this.isPreventiveActionTargetDateValid();
    },
    isInvestigationDateValid: function () {
        return this.isInvestigationTargetDateValid();
    },
    isNewInvestigationDateValid: function () {
        return this.isNewInvestigationTargetDateValid();
    },
    isNewPreventiveActionDateValid: function () {
        return this.isNewPreventiveActionTargetDateValid();
    },
    isNewCorectiveActionDateValid: function () {
        return this.isNewCorrectiveActionTargetDateValid();
    },
    acceptNewInvestigationDateClick: function () {
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: `/Qhse/NewInvestigationDate`,
            data: {
                id: serviceImprovementScriptModel.modelServiceImprovementId,
                newInvestigationTargetDate: $("#NewInvestigationTargetDate").val()
            },
            success: function () {
                window.location.reload();
                $("#NewTargetDateComment").val("")
            },
            dataType: "json"
        });
    },
    acceptNewPreventiveActionDateClick: function () {
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: `/Qhse/NewPreventiveActionDate`,
            data: {
                id: serviceImprovementScriptModel.modelServiceImprovementId,
                newPreventiveActionTargetDate: $("#NewPreventiveActionTargetDate").val()
            },
            success: function () {
                window.location.reload();
                $("#NewPreventiveActionTargetDateComment").val("")
            },
            dataType: "json"
        });
    },
    acceptNewCorrectiveActionDateClick: function () {
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: `/Qhse/NewCorrectiveActionDate`,
            data: {
                id: serviceImprovementScriptModel.modelServiceImprovementId,
                newCorrectiveActionTargetDate: $("#NewCorrectiveActionTargetDate").val(),
            },
            success: function () {
                window.location.reload();
                $("#NewCorrectiveActionTargetDateComment").val("")
            },
            dataType: "json"
        });
    },
    requestInvestigationClick: function () {
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: `/Qhse/UpdateStatus?id=${serviceImprovementScriptModel.modelServiceImprovementId}&status=${serviceImprovementScriptModel.serviceImprovementStatusConstantInvestigationRequested}&investigationTarget=${serviceImprovementScriptModel.modelInvestigationTargetDate}`,
            data: {
                id: serviceImprovementScriptModel.modelServiceImprovementId,
                investigatorUser: $("#InvestigatorUserId").data("kendoDropDownList").value(),
                investigationTargetDate: $("#InvestigationTargetDate").val()
            },
            success: function () {
                $("#editServiceImprovement").submit();
            },
            dataType: "json"
        });
    },
    submitSifClick: function () {
        var confirmation = confirm("Are you sure you wish to submit this SIF");

        if (confirmation) {
            $.ajax({
                type: 'POST',
                url: `/Qhse/UpdateStatus?id=${serviceImprovementScriptModel.modelServiceImprovementId}&status=${serviceImprovementScriptModel.serviceImprovementStatusConstantPendingApproval}`,
                data: {
                    id: serviceImprovementScriptModel.modelServiceImprovementId,
                },
                success: function () {
                    $("#editServiceImprovement").submit();
                }
            });
        }
    },
    submitAcceptClick: function () {
        $.ajax({
            type: 'POST',
            url: `/Qhse/UpdateStatus?id=${serviceImprovementScriptModel.modelServiceImprovementId}&status=${serviceImprovementScriptModel.serviceImprovementStatusConstantAccepted}`,
            data: {
                id: serviceImprovementScriptModel.modelServiceImprovementId,
            },
            success: function () {
                $("#editServiceImprovement").submit();
            }
        });
    },
    submitAbandonClick: function () {
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: `/Qhse/UpdateStatus?id=${serviceImprovementScriptModel.modelServiceImprovementId}&status=${serviceImprovementScriptModel.serviceImprovementStatusConstantAbandoned}`,
            data: {
                id: serviceImprovementScriptModel.modelServiceImprovementId,
            },
            success: function () {
                $("#editServiceImprovement").submit();
            },
            dataType: "json"
        });
    },
    submitInvestigationClick: function () {
        var confirmation = confirm("Are you sure you wish to submit this Investigation");

        if (confirmation) {
            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url: `/Qhse/UpdateStatus?id=${serviceImprovementScriptModel.modelServiceImprovementId}&status=${serviceImprovementScriptModel.serviceImprovementStatusConstantInvestigationSubmitted}`,
                data: {
                    id: serviceImprovementScriptModel.modelServiceImprovementId,
                },
                success: function () {
                    $("#editServiceImprovement").submit();
                },
                dataType: "json"
            });
        }
    },
    acceptInvestigationClick: function () {
        var confirmation = confirm("Are you sure you wish to accept this Investigation");

        if (confirmation) {
            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url: `/Qhse/UpdateStatus?id=${serviceImprovementScriptModel.modelServiceImprovementId}&status=${serviceImprovementScriptModel.serviceImprovementStatusConstantInvestigationAccepted}`,
                data: {
                    id: serviceImprovementScriptModel.modelServiceImprovementId,
                },
                success: function () {
                    $("#editServiceImprovement").submit();
                },
                dataType: "json"
            });
        }
    },
    requestPreventiveActionClick: function () {
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: `/Qhse/UpdateStatus?id=${serviceImprovementScriptModel.modelServiceImprovementId}&status=${serviceImprovementScriptModel.serviceImprovementStatusConstantPreventiveActionRequested}&preventiveActionTargetDate=${serviceImprovementScriptModel.modelPreventiveActionTargetDate}`,
            data: {
                id: serviceImprovementScriptModel.modelServiceImprovementId,
                preventiveActionUser: $("#PreventiveActionUserId").data("kendoDropDownList").value(),
                preventiveActionTargetDate: $("#PreventiveActionTargetDate").val()
            },
            success: function () {
                $("#editServiceImprovement").submit();
            },
            dataType: "json"
        });
    },
    submitPreventiveActionClick: function () {
        var confirmation = confirm("Are you sure you wish to submit this Preventive Action");

        if (confirmation) {
            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url: `/Qhse/UpdateStatus?id=${serviceImprovementScriptModel.modelServiceImprovementId}&status=${serviceImprovementScriptModel.serviceImprovementStatusConstantPreventiveActionSubmitted}`,
                data: {
                    id: serviceImprovementScriptModel.modelServiceImprovementId,
                },
                success: function () {
                    $("#editServiceImprovement").submit();
                },
                dataType: "json"
            });
        }
    },
    preventiveAcceptClick: function () {
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: `/Qhse/UpdateStatus?id=${serviceImprovementScriptModel.modelServiceImprovementId}&status=${serviceImprovementScriptModel.serviceImprovementStatusConstantPreventiveActionAccepted}`,
            data: {
                id: serviceImprovementScriptModel.modelServiceImprovementId,
            },
            success: function () {
                $("#editServiceImprovement").submit();
            },
            dataType: "json"
        });
    },
    requestCorrectiveActionClick: function () {
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: `/Qhse/UpdateStatus?id=${serviceImprovementScriptModel.modelServiceImprovementId}&status=${serviceImprovementScriptModel.serviceImprovementStatusConstantCorrectiveActionRequested}`,
            data: {
                id: serviceImprovementScriptModel.modelServiceImprovementId,
                correctiveActionUser: $("#CorrectiveActionUserId").data("kendoDropDownList").value(),
                correctiveActionTargetDate: $("#CorrectiveActionTargetDate").val()
            },
            success: function () {

                $("#editServiceImprovement").submit();
            },
            dataType: "json"
        });
    },
    submitCorrectiveActionClick: function () {
        var confirmation = confirm("Are you sure you wish to submit this Corrective Action");
        if (confirmation) {
            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url: `/Qhse/UpdateStatus?id=${serviceImprovementScriptModel.modelServiceImprovementId}&status=${serviceImprovementScriptModel.serviceImprovementStatusConstantCorrectiveActionSubmitted}`,
                data: {
                    id: serviceImprovementScriptModel.modelServiceImprovementId,
                },
                success: function () {
                    $("#editServiceImprovement").submit();
                },
                dataType: "json"
            });
        }
    },
    correctiveAcceptClick: function () {
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: `/Qhse/UpdateStatus?id=${serviceImprovementScriptModel.modelServiceImprovementId}&status=${serviceImprovementScriptModel.serviceImprovementStatusConstantCorrectiveActionAccepted}`,
            data: {
                id: serviceImprovementScriptModel.modelServiceImprovementId,
            },
            success: function () {
                $("#editServiceImprovement").submit();
            },
            dataType: "json"
        });
    },
   
    signOffClick: function () {
        var confirmation = confirm("Are you sure you wish to sign-off this SIF Investigation & Actions");

        if (confirmation) {
            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url: `/Qhse/UpdateStatus?id=${serviceImprovementScriptModel.modelServiceImprovementId}&status=${serviceImprovementScriptModel.serviceImprovementStatusConstantClosed}`,
                data: {
                    id: serviceImprovementScriptModel.modelServiceImprovementId,
                },
                success: function () {
                    $("#editServiceImprovement").submit();
                },
                dataType: "json"
            });
        }
    },
    reopenSifClick: function () {
        var confirmation = confirm("Are you sure you wish to reopen this SIF ");

        if (confirmation) {
            $.ajax({
                type: 'POST',
                dataType: 'json',
                traditional: true,
                url: `/Qhse/ReopenSIF?id=${serviceImprovementScriptModel.modelServiceImprovementId}`,
                data: {
                    id: serviceImprovementScriptModel.modelServiceImprovementId,
                },
                success: function () {
                    $("#editServiceImprovement").submit();
                },
                dataType: "json"
            });
        }
    },
    showRejectWindow: function () {
        $("#rejectWindow").data("kendoWindow").center().open();
    },

    showReInvestigationWindow: function () {
        $("#reRequestInvestigationWindow").data("kendoWindow").center().open();
    },

    showReEvaluatePreventiveWindow: function () {
        $("#reEvaluatePreventiveWindow").data("kendoWindow").center().open();
    },

    showReEvaluateCorrectiveWindow: function () {
        $("#reEvaluateCorrectiveWindow").data("kendoWindow").center().open();
    },
    showRequestExtensionWindow: function () {
        $("#requestExtensionWindow").data("kendoWindow").center().open();
    },
    showRejectNewDateWindow: function () {
        $("#rejectInvestigationDateWindow").data("kendoWindow").center().open();
    },
    showPreventiveActionRequestExtensionWindow: function () {
        $("#requestPreventiveActionExtensionWindow").data("kendoWindow").center().open();
    },
    showRejectNewPreventiveActionDateWindow: function () {
        $("#rejectPreventiveActionDateWindow").data("kendoWindow").center().open();
    },

    showCorrectiveActionRequestExtensionWindow: function () {
        $("#requestCorrectiveActionExtensionWindow").data("kendoWindow").center().open();
    },
    showRejectNewCorrectiveActionDateWindow: function () {
        $("#rejectCorrectiveActionDateWindow").data("kendoWindow").center().open();
    },
    tabStripHeaderDetails: function () {
        return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Details </span>`;
    },
    tabStripHeaderCorrectiveAction: function () {
        return `<span class="k-link"><i class="fa fa-list mr-1"></i> Corrective Action </span>`;
    },
    tabStripHeaderPreventiveAction: function () {
        return `<span class="k-link"><i class="fa fa-list mr-1"></i> Preventive Action </span>`;
    },
    tabStripHeaderSignOff: function () {
        return `<span class="k-link"><i class="fa fa-pencil-square mr-1"></i> Sign-off </span>`;
    },
    tabStripHeaderInvestigation: function () {
        return `<span class="k-link"><i class="fa fa-search mr-1"></i> Investigation </span>`;
    }
});

kendo.bind(document.body.children, viewModel);