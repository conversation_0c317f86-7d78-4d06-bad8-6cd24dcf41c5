﻿using Centerpoint.Extensions;
using Centerpoint.Model.ViewModels;
using Centerpoint.Service.Interfaces;
using Centerpoint.Services;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Centerpoint.Controllers
{
    [Authorize]
    public class PersonnelController : Controller
    {
        private readonly IPersonnelService _personnelService;
        private readonly ICurrentUserService _currentUserService;
        private readonly IConfiguration _configuration;


        public PersonnelController(IPersonnelService personnelService,ICurrentUserService currentUserService, IConfiguration configuration)
        {
            _personnelService = personnelService;
            _currentUserService = currentUserService;
            _configuration = configuration;
        }

        public async Task<ActionResult> Employees()
        {
            var model = await _personnelService.Employees();
            this.SetTitle("Employees");

            return View(model);
        }

        public ActionResult UserLocation()
        {
            this.SetTitle("User Location");
            ViewBag.PhoneNumber = _configuration["TwilioSMS:TWILIO_FROM_PHONE"];

            return View();
        }

        public async Task<ActionResult> Location()
        {
            var data = await _personnelService.Location();
            return Json(data);
        }


        public async Task<ActionResult> GetUsersLocation([DataSourceRequest] DataSourceRequest request)
        {
            var result = await _personnelService.GetUsersLocation();

            return Json(result.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateUserLocation([DataSourceRequest] DataSourceRequest request, UserLocationModel model)
        {
            var result = await _personnelService.UpdateUserLocation(model, _currentUserService.UserId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteUserLocation([DataSourceRequest] DataSourceRequest request, UserLocationModel model)
        {
            var result = await _personnelService.DeleteUserLocation(model);
            return Json(result.ToDataSourceResult(request));
        }
    }
}
