$(document).ready(function() {
    var currencyGrid = $('#currencyGrid').data("kendoGrid");
    currencyGrid.bind('dataBound', function(e) {
        this.element.find('.k-add').remove();
        this.element.find('.k-i-excel').remove();
    });
});

function updateCurrencyTotal() {
    var currencyGrid = $("#currencyGrid").data("kendoGrid");
    var totalCurrencies = currencyGrid.dataSource.total();
    viewModel.set("totalCurrencies", totalCurrencies);
}

var viewModel = new kendo.observable({
    totalCurrencies: 0
});

kendo.bind(document.body.children, viewModel);
