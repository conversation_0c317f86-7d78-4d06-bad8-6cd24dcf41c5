﻿@(Html.<PERSON>().TabStrip()
    .Name("runsStrips")
    .SelectedIndex(0)
    .Animation(false)
    .Items( tabstrip => {

     tabstrip.Add().Text("")
         .HtmlAttributes(new { @data_bind = "click:refreshJobServiceChart, html:tabStripHeaderRuns" })
         .Selected(true)
         .Content(@<text>
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Runs (per <span data-bind="text:runChartShowBy"></span>)</h6>
                </div>

                <div class="card-body">
                    <div class="d-flex">
                        <div class="form-group w-50 mr-5">
                            <label>Show By</label>
                            <input 
                                data-role="dropdownlist" 
                                data-spinners="false" 
                                data-bind="value:runChartShowBy, source:showByOptions, events:{change:refreshRunChart}" 
                                style="margin-left:5px" 
                                data-format="0 months" 
                                data-max="12" 
                                data-min="3" 
                                />
                        </div>
                        <div class="form-group w-50">
                            <label>Period</label>
                            <input 
                                data-role="numerictextbox" 
                                data-spinners="false" 
                                data-bind="value:runChartMonths, events:{change:refreshRunChart}" 
                                data-format="Last 0 months" 
                                data-min="3" 
                                />
                        </div>
                    </div>
                    @(Html.Kendo().Chart<StatisticsBaseModel>()
                    .Name("runChart")
                    .Series(c => {
                        c.Column(p => p.Count);
                    })
                    .AutoBind(false)
                    .ValueAxis(v => v.Numeric().Title("Count").MajorGridLines(l => l.Visible(false)).MinorGridLines(l => l.Visible(false)))
                    .Legend(l => l.Visible(false))
                    .CategoryAxis(c => c.Categories(p => p.Category))
                    .Tooltip(t => t.Visible(true).Template("#=category# - #=value# runs"))
                    .DataSource(d => d.Read(read => read.Action("GetRunChartData", "Statistics").Data("runChartData"))))
                </div>
           </div>
           </text>
         );




         tabstrip.Add().Text("")
         .HtmlAttributes(new { @data_bind="click:refreshRunServiceChart, html:tabStripHeaderRunsByService"})
         .Content(@<text>
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Runs by Service (per <span data-bind="text:runServiceChartShowBy"></span>)</h6>       
                </div>     
                <div class="card-body">
                    <div class="d-flex">
                        <div class="form-group w-50 mr-5">
                            <label>Show By</label>
                            <input 
                            data-role="dropdownlist" 
                            data-spinners="false" 
                            data-bind="value:runServiceChartShowBy, source:showByOptions, events:{change:refreshRunServiceChart}"
                            data-format="0 months" 
                            data-max="12" 
                            data-min="3" 
                            /> 
                        </div>
                        <div class="form-group w-50">
                            <label>Period</label>
                            <input 
                            data-role="numerictextbox" 
                            data-spinners="false" 
                            data-bind="value:runServiceChartMonths, events:{change:refreshRunServiceChart}" 
                            data-format="Last 0 months" 
                            data-min="3" 
                            />
                        </div >
                    </div>
                    @(Html.Kendo().Chart<StatisticsServiceModel>()
                    .Name("runsByServiceChart")
                    .Series(c => {
                        c.Bar(p => p.Count).Stack("service").Name("#=group.value#");
                    })
                    .AutoBind(false)
                    .ValueAxis(v => v.Numeric().Title("Count").MajorGridLines(l => l.Visible(false)).MinorGridLines(l => l.Visible(false)))
                    .Legend(l => l.Visible(true).Position(ChartLegendPosition.Bottom))
                    .CategoryAxis(c => c.Categories(p => p.Category))
                    .Tooltip(t => t.Visible(true).Template("#=category# - #=series.name# (#=value# runs)"))
                    .DataSource(d => d.Group(g => g.Add(p => p.Service)).Sort(s => { s.Add(p => p.Year); s.Add(p => p.Quarter); s.Add(p => p.Month); }).Read(read => read.Action("GetRunServiceChartData", "Statistics").Data("runServiceChartData"))))
                </div>
            </div>
           </text>
         );
        }
      )
    )
