﻿@model UserModel
<div class="container-fluid pl-0 pr-0">
    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">My Lessons</h6>
                </div>
                <div class="card-body">
                    <div class="card-list-item">
                        <span class="card-list-item-name">Total</span>
                        <a  href="#myLessonGrid" class="card-list-item-count" style="background: #68217A" data-bind="click:totalLessonClick,text:totalLessons">0</a>
                    </div>
                    <div class="card-list-item">
                        <span class="card-list-item-name">Pending Approval</span>
                        <a  href="#myLessonGrid" class="card-list-item-count" style="background: #DB8C8C" data-bind="click:pendingApprovalClick,text:totalPendingApproval">0</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <hr />
    @(Html.Kendo().Grid<LessonModel>()
        .Name("myLessonGrid")
        .Columns(columns => {
            columns.Bound(c => c.Number).ClientTemplate("<a href='" + @Url.Action("EditLesson", "Qhse") + "/#=LessonId#'>#=Number#</a>").Title("Lesson ID");
            columns.Bound(c => c.Name).Title("Title");
            columns.Bound(c => c.LessonCategoryName).Title("Lesson Category");
            columns.Bound(c => c.EquipmentCategories).Title("Equipment Category").ClientTemplate("#=EquipmentCategories ? EquipmentCategories : 'N/A'#");
            columns.Bound(c => c.CompanyName).Title("Client").ClientTemplate("#=CompanyName ? CompanyName : 'N/A'#");
            columns.Bound(c => c.Services).Title("Service").ClientTemplate("#=Services ? Services : 'N/A'#"); ;
            columns.Bound(c => c.StatusDescription).Title("Status");
        })
        .Sortable()
        .ToolBar(t => {
            t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetMyLessonGrid", @class="bg-danger text-white"});
            t.Excel().Text("Export");
        }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
        .Filterable()
        .Events(e => e.DataBound("updateMyLessonGrid").ColumnReorder("saveLessonGrid").ColumnResize("saveLessonGrid").ColumnShow("saveLessonGrid").ColumnHide("saveLessonGrid"))
        .Excel(excel => excel
            .FileName(string.Format("Centerpoint_My_Lessons_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Qhse"))
        )
        .Groupable()
        .Scrollable(s => s.Height("auto"))
        .Resizable(c => c.Columns(true))
        .Reorderable(c => c.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Read(read => read.Action("GetMyLessons", "Home").Data("lessonData"))
        )
    )
</div>
