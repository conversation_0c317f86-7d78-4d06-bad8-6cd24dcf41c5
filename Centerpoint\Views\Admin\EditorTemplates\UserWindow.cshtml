﻿@model UserModel
<div style="padding:0 20px">
    <div class="row">
        <div class="col-md-5">
            <div class="form-group input-group">
                @Html.LabelFor(m => m.Name)<br />
                @Html.TextBoxFor(m => m.Name, new { @class = "form-control" })
            </div>
        </div>
        <div class="col-md-5">
            <div class="form-group input-group">
                @Html.LabelFor(m => m.EmailAddress)<br />
                @Html.TextBoxFor(m => m.EmailAddress, new { @class = "form-control" })
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-5">
            <div class="form-group input-group">
                @Html.LabelFor(m => m.Password)<br />
                @Html.PasswordFor(m => m.Password, new { @class = "form-control" })
            </div>
        </div>
        <div class="col-md-5">
            <div class="form-group input-group">
                @Html.LabelFor(m => m.Roles)<br />
                @(Html.Kendo().DropDownListFor(m => m.Roles)
                    .HtmlAttributes(new { @style = "width:200px" })
                    .Filter("contains")
                    .OptionLabel("Select Role")
                    .DataValueField("Key")
                    .DataTextField("Value")
                    .BindTo(UserRoleConstant.ValuesAndDescriptions.ToList()))
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-2" >
            @Html.LabelFor(m => m.IsEnabled) 
        </div>
        <div class="col-md-8" style="padding: 0px; margin-left: -20px; margin-top: -5px;" >
            @Html.CheckBoxFor(m => m.IsEnabled, new { @class = "checkbox" })
        </div>
    </div>
</div>