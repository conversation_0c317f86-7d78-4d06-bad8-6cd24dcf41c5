@model ProjectModel

<div>
    @Html.ValidationSummary(false)

    <div class="row mb-3 mt-3">
        <div class="col-md-4">
            <div class="form-group">
                <label>Project Creation Date</label>
                @(Html.Kendo().DateTimePickerFor(m => m.ProjectCreationDate)
                    .HtmlAttributes(new { @tabindex = "1", @class = "utcTimePicker" })
                )
            </div>
            <div class="form-group">
                <label>Customer
                    @if (GlobalSettings.IsAisus)
                    {
                        @if (Html.IsOperationAdmin() || Html.IsGlobalAdmin() || Html.IsSeniorUSEngineer())
                        {
                            <a href="#" data-bind="click: showAddCompanyWindow">
                                <i class="fa fa-plus" data-container="body" data-toggle="tooltip"></i>
                            </a>
                        }
                    }
                </label>

                @if (Model.OppsProjectId) {
                    @(Html.Kendo().TextBoxFor(m => m.OppsCustomerCompanyName).HtmlAttributes(new { @readonly = "readonly" }))
                        @Html.HiddenFor(m => m.CompanyId)
                }
                else {
                    @(Html.Kendo().DropDownListFor(x=>x.CompanyId)
                    .Filter(FilterType.Contains)
                    .OptionLabel("Select Customer")
                    .DataTextField("Text")
                    .DataValueField("Value")
                    .Filter("contains")
                    .Events(m => m.Change("companyChange"))
                    .Enable(false)
                    .DataSource(source => {
                        source.Read(read => {
                            read.Action("GetCompanyByOperatorOrPartnerId", "Lookup").Data("projectCompanyData");
                        });
                    }).AutoBind(false)
                            .HtmlAttributes(new { @tabindex = "4", @data_bind = "invisible:hasJobs", @data_value_primitive = "true", @data_cascade_to = "CompanyLocationId,CompanyContactId" })
)
                        @Html.TextBox("CompanyDisabled", Model.CompanyName, new {  @disabled = "disabled", @data_bind = "visible:hasJobs", @class= "form-control"})
                }
            </div>
            
                <div class="form-group">
                    <label>Services</label>
                    @(
                        Html.Kendo().MultiSelect()
                            .Name("ObjectiveIds")
                            .Filter(FilterType.Contains)
                            .DataTextField("Text")
                            .DataValueField("Value")
                            .Placeholder("Select Services")
                            .HtmlAttributes(new { @tabindex = "8" })
                            .Events(e=> e.Open("multiselectOpen"))
                            .DataSource(source =>
                            {
                                source.Read(read =>
                                {
                                    read.Action("GetObjectivesByCompanyId", "Lookup").Data("companyData");
                                })
                                .ServerFiltering(true);
                        }).AutoBind(false)
                        )
                </div>
            
        </div>
        <div class="col-md-4">
                @if (GlobalSettings.IsWellsense)
                {
                    <div class="form-group">
                    @Html.LabelFor(m => m.ProjectTitle)
                        @(Html.Kendo().TextBoxFor(m => m.ProjectTitle)
                        )
                    </div>
                }
                else
                {
                @Html.HiddenFor(m => m.ProjectTitle)
                    <div class="form-group">
                        @Html.LabelFor(m => m.BaseCompanyLocationId)
                        @(Html.Kendo().DropDownListFor(x => x.BaseCompanyLocationId)
                            .OptionLabel("Select Base")
                            .DataTextField("Text")
                            .DataValueField("Value")
                            .Filter("contains")
                            .DataSource(d => d.Read("GetBaseCompanyLocations", "Lookup"))
                            .HtmlAttributes(new { @tabindex = "9" }))
                    </div>
                }

               <div class="form-group">
                <label>
                    <span>Customer Location</span>
                    @if (Html.IsOperationAdmin() || Html.IsGlobalAdmin() || Html.IsSeniorUSEngineer())
                    {
                        <a href="#" data-bind="click: showAddCompanyLocationWindow, visible:selectedCustomerId">
                            <i class="fa fa-plus" data-container="body" data-toggle="tooltip"></i>
                        </a>
                    }
                </label>
                @if (Model.OppsProjectId)
                {
                    @(Html.Kendo().DropDownListFor(x => x.CompanyLocationId)
                        .OptionLabel("Select Customer Location")
                        .DataTextField("Text")
                        .DataValueField("Value")
                        @* .CascadeFrom("CompanyId") *@
                        .Filter("contains")
                        .DataSource(source =>
                        {
                            source.Read(read =>
                            {
                                read.Action("GetLocationsByCompanyId", "Lookup").Data("filterCustomerCompanyLocations");
                            });
                        })
                        )
                    @* @Html.HiddenFor(m => m.CompanyLocationId) *@
                }
                else
                {
                    @(Html.Kendo().DropDownListFor(x => x.CompanyLocationId)
                        .OptionLabel("Select Customer Location")
                        .DataTextField("Text")
                        .DataValueField("Value")
                        @* .CascadeFrom("CompanyId") *@
                        .Filter("contains")
                        .DataSource(source =>
                        {
                            source.Read(read =>
                            {
                                read.Action("GetLocationsByCompanyId", "Lookup").Data("filterCompanyLocations");
                            });
                        })
                        .HtmlAttributes(new { @tabindex = "5", @data_bind = "invisible:hasJobs" }))
                    @Html.TextBox("CompanyLocationDisabled", Model.CompanyLocationName, new { @disabled = "disabled", @data_bind = "visible:hasJobs", @class= "form-control" })
                }
               </div>
               
                @if (GlobalSettings.IsWellsense)
                {
                    <div class="form-group">
                        @Html.LabelFor(m => m.BaseCompanyLocationId)
                        @(Html.Kendo().DropDownListFor(x => x.BaseCompanyLocationId)
                            .OptionLabel("Select Base")
                            .DataTextField("Text")
                            .DataValueField("Value")
                            .Filter("contains")
                            .DataSource(d => d.Read("GetBaseCompanyLocations", "Lookup"))
                            .HtmlAttributes(new { @tabindex = "9" }))
                    </div>
                }
        </div>
        <div class="col-md-4">
            <div class="form-group">
                    @if (Html.IsGlobalAdmin()) {
                    <label> Created By</label>
                    @(Html.Kendo().DropDownListFor(x=>x.UserId)
                    .OptionLabel("Select User")
                    .DataTextField("Text")
                    .DataValueField("Value")
                    .Filter("contains")
                    .DataSource(d => d.Read("GetUsers", "Lookup"))
                    .HtmlAttributes(new { @tabindex = "2" }))
                } else {
                    <label> Created By</label>

                    @(Html.Kendo().DropDownListFor(m => m.UserId)
                    .OptionLabel("Select User")
                    .DataTextField("Text")
                    .DataValueField("Value")
                    .Filter("contains")
                        .DataSource(d => d.Read("GetUsers", "Lookup"))
                    .HtmlAttributes(new { @tabindex = "2", @readonly = "readonly" }))
                }
            </div>     
            <div class="form-group">
                <span>Customer Contact</span>
                @if (Model.OppsProjectId) {
                    @(Html.Kendo().TextBoxFor(m => m.FirstNameLastName).HtmlAttributes(new { @readonly = "readonly" }))
                    @Html.HiddenFor(m => m.CompanyContactId)
                }
                else {
                    <label>
                        @if (Html.IsOperationAdmin() || Html.IsGlobalAdmin() || Html.IsSeniorUSEngineer()) {
                            <a href="#" data-bind="click: showAddCompanyContactWindow, visible:selectedCustomerId">
                                <i class="fa fa-plus" data-container="body" data-toggle="tooltip"></i>
                            </a>
                        }
                    </label>

                    @(Html.Kendo().DropDownListFor(x=>x.CompanyContactId)
                    .OptionLabel("Select Customer Contact")
                    .DataTextField("Text")
                    .DataValueField("Value")
                    .Filter("contains")
                        @* .CascadeFrom("CompanyLocationId") *@
                    .DataSource(source => {
                        source.Read(read => {
                            read.Action("GetContactsByCompanyId", "Lookup").Data("filterCompanyContacts");
                        });
                        })
                    .HtmlAttributes(new {@tabindex = "6", @data_bind = "invisible:hasJobs" }))
                    @Html.TextBox("CompanyContactDisabled", Model.FirstNameLastName, new { @disabled = "disabled", @data_bind = "visible:hasJobs", @class= "form-control" })
                }
            </div>
           
                <div class="form-group" style="display:none">
                    <label>Division</label>
                    @(
                        Html.Kendo().DropDownListFor(x=>x.DivisionId)
                        .OptionLabel("Select Division ")
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .Filter("contains")
                        .DataSource(d => d.Read("GetDivisions", "Lookup")).AutoBind(false)
                        .Events(e => e.Change("changeDivision").DataBound("dataBoundDivision"))
                        .HtmlAttributes(new { @tabindex = "3" })
                        )
                </div>
            
        </div>
    </div>
    @if (!Model.OppsProjectId) {
        <div class="card">
            <div class="card-default">
                <div class="card-header">
                    <h6 class="mb-0">Service Delivery Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Operator</label>
                                @(Html.Kendo().DropDownListFor(x=>x.CustomerCompanyId)
                                .Filter(FilterType.Contains)
                                .OptionLabel("Select Operator")
                                .DataTextField("Text")
                                .DataValueField("Value")
                                .Filter("contains")
                                .Events(m => m.Change("projectOperatorChange").DataBound("projectOperatorDataBound"))
                                    .DataSource(d => d.Read("GetOperatorCompanies", "Lookup"))
                                    .HtmlAttributes(new { @tabindex = "1", @data_bind = "value:operatorId" }))
                            </div>
                            <div class="form-group">
                                <label>
                                    <span>Fields</span>
                                    @if ((Html.IsOperationAdmin() || Html.IsGlobalAdmin() || Html.IsSeniorUSEngineer()) && GlobalSettings.IsAisus)
                                    {
                                        <a href="#" data-bind="click: showFieldWindow">
                                            <i class="fa fa-plus" data-container="body" data-toggle="tooltip"></i>
                                        </a>
                                    }
                                </label>
                                @(Html.Kendo().MultiSelect()
                                .Name("CompanyFieldIds")
                                .Placeholder("Select Field(s)")
                                .DataTextField("Text")
                                .DataValueField("Value")
                                .Events(m => {
                                    m.Change("projectCompanyFieldChange");
                                    m.Open("multiselectOpen");
                                })
                                .DataSource(source => {
                                    source.Read(read => {
                                        read.Action("GetFieldsByCompanyOrPartnerId", "Lookup").Data("projectCompanyData");
                                        })
                                    .ServerFiltering(true);
                                    }).AutoBind(false)
                             .HtmlAttributes(new {@data_bind = "value:projectCompanyFieldIds", @data_value_primitive = "true" }))
                            </div>
                            
                            @if (GlobalSettings.IsWellsense)
                            {
                                <div class="form-group">
                                    <label>Deployment type</label>
                                    @(
                                        Html.Kendo().MultiSelectFor(m => m.Conveyances)
                                        .DataTextField("Value")
                                        .Filter(FilterType.Contains)
                                        .DataValueField("Key")
                                        .Placeholder("Select Deployment Type")
                                        .BindTo(Centerpoint.Common.Constants.DeploymentTypeConstant.ValuesAndDescriptions.ToList())
                                        )
                                </div>
                            }
                            else
                            {
                                <div class="form-group" >
                                    <label>Conveyance</label>
                                    @(
                                        Html.Kendo().MultiSelectFor(m => m.Conveyances)
                                        .DataTextField("Value")
                                        .Filter(FilterType.Contains)
                                        .DataValueField("Key")
                                        .Placeholder("Select Conveyance Method")
                                        .BindTo(Centerpoint.Common.Constants.ConveyanceConstant.ValuesAndDescriptions.ToList())
                                        )
                                </div>

                            }
                            <div class="form-group">
                                <label>@(GlobalSettings.IsAisus ? "Personnel Mobilisation Date" : "Mobilisation Date")</label>
                                @Html.Kendo().DatePickerFor(m => m.MobilisationDate)
                            </div>
                            
                                <div class="form-group">
                                    <label>Main Equipment Required</label>
                                @(
                            Html.Kendo().MultiSelect()
                                    .Name("EquipmentCategoryIds")
                                .DataTextField("Text")
                                .DataValueField("Value")
                                .Placeholder("Select Equipment")
                                .HtmlAttributes(new { @data_value_primitive = "true" })
                                .Events(e => e.Open("multiselectOpen"))
                                .DataSource(source =>
                                {
                                    source.Read(read =>
                                    {
                                        read.Action("GetEquipmentCategoriesByDivisionId", "Lookup").Data("divisionData");
                                    })
                                    .ServerFiltering(true);
                                        }).AutoBind(false)
                                    )
                                </div>
                            
                            

                            <div class="form-group">
                                <label>New Personnel Visa Required ?</label>
                                @(Html.Kendo().DropDownListFor(m => m.NewPersonnelVisaRequired)
                                .Filter("contains")
                                .OptionLabel("Select Personnel Visa Required")
                                .DataValueField("Key")
                                .DataTextField("Value")
                                .BindTo(Html.BooleanListValues().ToList())
                                )
                            </div>

                            @if (GlobalSettings.IsWellsense)
                            {
                                <div class="form-group">
                                    <label>ID Restriction </label>
                                    @(
                                        Html.Kendo().TextBoxFor(m => m.IdRestriction)
                                        .HtmlAttributes(new { @class = "form-control" })
                                        )
                                </div>
                                <div class="form-group" >
                                    <label>Max Deviation</label>
                                    @(
                                    Html.Kendo().TextBoxFor(m => m.MaxDeviation)
                                    .HtmlAttributes(new { @class = "form-control" })

                                    )
                                </div>
                                <div class="form-group" >
                                    <label>Wellhead Connection Type </label>
                                    @(
                                    Html.Kendo().TextBoxFor(m => m.WellheadConnectionType)
                                    .HtmlAttributes(new { @class = "form-control" })

                                    )
                                </div>
                                <div class="form-group" >
                                    <label>Wellhead Minimum ID </label>
                                    @(
                                    Html.Kendo().TextBoxFor(m => m.WellheadMinimumId)
                                    .HtmlAttributes(new { @class = "form-control" })

                                    )
                                </div>
                            }


                        </div>
                        <div class="col-md-4">
                            @if (!GlobalSettings.IsAisus)
                            {
                                <div class="form-group">
                                <label>Partner </label>
                                    @(Html.Kendo().DropDownListFor(x => x.PartnerCompanyId)
                                .Filter(FilterType.Contains)
                                .OptionLabel("Select Partner")
                                .DataTextField("Text")
                                .DataValueField("Value")
                                .Events(m => m.Change("partnerCompanyChange").DataBound("projectPartnerDataBound"))
                                        .DataSource(d => d.Read("GetPartnerCompanies", "Lookup"))
                                .HtmlAttributes(new { @tabindex = "3" }))
                                </div>
                            }
                            else
                            {
                                <div class="form-group" style="display:none">
                                    <label>Partner </label>
                                    @(Html.Kendo().DropDownListFor(x => x.PartnerCompanyId)
                                        .Filter(FilterType.Contains)
                                        .OptionLabel("Select Partner")
                                        .DataTextField("Text")
                                        .DataValueField("Value")
                                        .Events(m => m.Change("partnerCompanyChange"))
                                        .DataSource(d => d.Read("GetPartnerCompanies", "Lookup")).AutoBind(false)
                                        .HtmlAttributes(new { @tabindex = "3" }))
                                </div>
                            }
                            <div class="form-group">
                                <label>
                                    <span>Wells</span>
                                    @if (Html.IsOperationAdmin() || Html.IsGlobalAdmin() || Html.IsSeniorUSEngineer())
                                    {
                                        <a href="#" data-bind="click: showWellWindow">
                                            <i class="fa fa-plus" data-container="body" data-toggle="tooltip"></i>
                                        </a>
                                    }
                                </label>

                                @(Html.Kendo().MultiSelect()
                                    .Name("CompanyWellIds")
                                    .Placeholder("Select Well(s)")
                                    .DataTextField("Text")
                                    .DataValueField("Value")
                                    .Filter(FilterType.Contains)
                                    .Events(e => e.Open("multiselectOpen"))
                                    .DataSource(source =>
                                    {
                                        source.Read(read =>
                                        {
                                            read.Action("GetCompanyWellsByCompanyFieldIds", "Lookup").Data("compFields");
                                        })
                                        .ServerFiltering(true);
                                    })
                                    .AutoBind(false)
                                    .HtmlAttributes(new { @data_bind = "value:projectCompanyWellIds", @data_value_primitive = "true" })
                                 )
                            </div>
                            <div class="form-group">
                                    <label>FTA Required ?</label>
                                    @(Html.Kendo().DropDownListFor(m => m.FtaRequired)
                                        .OptionLabel(@GlobalSettings.IsAisus ? "Select Third party Inspection" : "Select FTA Required")
                                        .DataValueField("Key")
                                        .DataTextField("Value")
                                        .Filter(FilterType.Contains)
                                        .BindTo(Html.BooleanListValues().ToList()))
                            </div>

                            @if (!GlobalSettings.IsAisus)
                            {
                                @if (GlobalSettings.IsWellsense)
                                {
                                    <div class="form-group">
                                        <label>Expected Deployment Date </label>
                                        @Html.Kendo().DatePickerFor(m => m.ExpectedDeploymentDate)
                                    </div>
                                }
                                <div class="form-group">
                                    <label>Rig Type</label>
                                  @(Html.Kendo().DropDownListFor(m => m.RigType)
                                .DataTextField("Value")
                                .DataValueField("Key")
                                .Filter(FilterType.Contains)
                                .OptionLabel("Select Rig Type")
                                .BindTo(Centerpoint.Common.Constants.RigTypeConstant.ValuesAndDescriptions.ToList())
                                )
                                </div>

                                @if (!GlobalSettings.IsWellsense)
                                {
                                    <div class="form-group">
                                        <label>System Integration Required</label>

                                        @(
                                       Html.Kendo().DropDownListFor(m => m.SystemIntegrationTestingRequired)
                                      .DataValueField("Key")
                                      .DataTextField("Value")
                                      .Filter(FilterType.Contains)
                                      .BindTo(Html.BooleanListValues().ToList())
                                      )
                                    </div>
                                }
                            }else{
                                <div class="form-group" style="display:none">
                                    <label>Rig Type</label>

                                    @(
                                        Html.Kendo().DropDownListFor(m => m.RigType)
                                        .DataTextField("Value")
                                        .DataValueField("Key")
                                        .Filter(FilterType.Contains)
                                        .OptionLabel("Select Rig Type")
                                        .BindTo(Centerpoint.Common.Constants.RigTypeConstant.ValuesAndDescriptions.ToList())
                                        )
                                </div>

                                <div class="form-group" style="display:none">
                                    <label>System Integration Required</label>

                                    @(
                                        Html.Kendo().DropDownListFor(m => m.SystemIntegrationTestingRequired)
                                        .DataValueField("Key")
                                        .DataTextField("Value")
                                        .Filter(FilterType.Contains)
                                        .BindTo(Html.BooleanListValues().ToList())
                                        )
                                </div>
                            }
                           

                            @if (GlobalSettings.IsWellsense)
                            {
                                <div class="form-group">
                                    <label>Pressure Control Required </label>
                                    @(
                                    Html.Kendo().DropDownListFor(m => m.PressureControlRequired)
                                    .DataValueField("Key")
                                    .DataTextField("Value")
                                    .Filter(FilterType.Contains)
                                    .BindTo(Html.BooleanListValues().ToList())
                                    )
                                </div>

                                
                                <div class="form-group">
                                    <label for="wellType">Well Type</label>
                                    <br />
                                    @(
                                    Html.Kendo().DropDownListFor(x => x.WellType)
                                    .DataValueField("Key")
                                    .DataTextField("Value")
                                    .Filter(FilterType.Contains)
                                    .OptionLabel("Select Well Type")
                                    .HtmlAttributes(new { @tabindex = "7" })
                                    .BindTo(Centerpoint.Common.Constants.WellTypeConstant.ValuesAndDescriptions.ToList())
                                    )
                                </div>
                                <div class="form-group">
                                    <label>Well Depth/Hold-up Depth </label>
                                    @(
                                    Html.Kendo().TextBoxFor(m => m.Depth)
                                    .HtmlAttributes(new { @class = "form-control" })
                                    )
                                </div>
                            }


                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Objectives</label>
                                @(Html.TextAreaFor(p => p.ObjectiveComments, new { @class = "form-control w-100", @rows = "5" }))
                            </div>
                            <div class="form-group">
                                <label>Special Requirements</label>
                                @(Html.TextAreaFor(p => p.SpecialRequirements, new { @class = "form-control w-100", @rows = "5" }))
                            </div>
                            <div class="form-group">
                                <label>Client Induction Requirement / Training Requirements</label>
                                @(Html.TextAreaFor(p => p.ClientInductionTrainingRequirements, new { maxlength = 4000, @class = "form-control w-100", @rows = "5" }))
                            </div>
                            <div class="form-group">
                                <label>QHSE consideration</label>
                                @(Html.TextAreaFor(p => p.QhseConsideration, new { @class = "form-control w-100", @rows = "5" }))
                            </div>
                            <div class="form-group">
                                <label>Comments</label>
                                @(Html.TextAreaFor(p => p.Comments, new { @class = "form-control w-100", @rows = "5" }))
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
    else {
        @Html.HiddenFor(m => m.OpportunityId)
        <div class="card">
            <div class="card-default">
                <div class="card-header">
                    <h6 class="mb-0">Service Delivery Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Operator</label>
        
                                @(Html.Kendo().TextBoxFor(m => m.OperatorCompanyName).HtmlAttributes(new {@class="form-control", @tabindex = "3", @readonly = "readonly" }))
                            </div>
                            <div class="form-group">
                                <label>Fields</label>
                                @(
                                Html.Kendo().MultiSelect()
                                    .Name("OppsFieldIds")
                                    .Placeholder("Select Field(s)")
                                    .DataTextField("Text")
                                    .DataValueField("Value")
                                    .Events(m => {
                                            m.Change("companyFieldChange");
                                            m.Open("multiselectOpen");
                                    })
                                .DataSource(source => {
                                    source.Read(read => {
                                        read.Action("GetOppsFieldsByCompanyId", "Lookup").Data("customerCompanyData");
                                    })
                                    .ServerFiltering(true);
                                    }).AutoBind(false)
                                .HtmlAttributes(new { @data_bind = "value:companyFieldIds", @data_value_primitive = "true" }))

                            </div>
                            
                            <div class="form-group">
                                <label>Mobilisation Date</label>
                                @Html.Kendo().DatePickerFor(m => m.OppsMobilisationDate)
                            </div>
                            <div class="form-group">
                                <label>Main Equipment Required</label>
                                @(
                                 Html.Kendo().MultiSelect()
                                    .Name("OppsEquipmentCategoryIds")
                                    .DataTextField("Text")
                                    .DataValueField("Value")
                                    .Placeholder("Select Equipment")
                                    .HtmlAttributes(new { @data_value_primitive = "true" })
                                    .Filter(FilterType.Contains)
                                    .Events(m =>
                                    {
                                        m.Change("companyFieldChange");
                                        m.Open("multiselectOpen");
                                    })
                                    .DataSource(source =>
                                    {
                                        source.Read(read =>
                                        {
                                            read.Action("GetEquipmentCategoriesByDivisionId", "Lookup").Data("divisionData");
                                        })
                                        .ServerFiltering(true);
                                    }).AutoBind(false)
                                 )
                            </div>
                           
                            <div class="form-group">
                                <label>New Personnel Visa Required ?</label>
                                @(Html.Kendo().DropDownListFor(m => m.OppsNewPersonnelVisaRequired)
                                .OptionLabel("Select Personnel Visa Required")
                                .Filter(FilterType.Contains)
                                .DataValueField("Key")
                                .DataTextField("Value")
                                .BindTo(Html.BooleanListValues().ToList())
                                )
                            </div>


                            @if (GlobalSettings.IsWellsense)
                            {
                                
                                <div class="form-group">
                                    <label>ID Restriction </label>
                                    @(
                                        Html.Kendo().TextBoxFor(m => m.IdRestriction)
                                        .HtmlAttributes(new { @class = "form-control" })
                                        )
                                </div>
                                <div class="form-group">
                                    <label>Max Deviation</label>
                                    @(
                                        Html.Kendo().TextBoxFor(m => m.MaxDeviation)
                                        .HtmlAttributes(new { @class = "form-control" })

                                        )
                                </div>
                                <div class="form-group">
                                    <label>Wellhead Connection Type </label>
                                    @(
                                        Html.Kendo().TextBoxFor(m => m.WellheadConnectionType)
                                        .HtmlAttributes(new { @class = "form-control" })

                                        )
                                </div>
                                <div class="form-group">
                                    <label>Wellhead Minimum ID </label>
                                    @(
                                        Html.Kendo().TextBoxFor(m => m.WellheadMinimumId)
                                        .HtmlAttributes(new { @class = "form-control" })

                                        )
                                </div>
                            }



                        </div>
                        <div class="col-md-4">
                            @if (!GlobalSettings.IsAisus)
                            {
                                <div class="form-group">
                                    <label>Partner</label>
                                    @(
                                    Html.Kendo().TextBoxFor(m => m.OppsPartnerCompanyName)
                                .HtmlAttributes(new { @class = "form-control", @readonly = "readonly" })
                                    )
                                </div>
                            }else{
                                <div class="form-group" style="display:none">
                                    <label>Partner</label>
                                    @(
                                        Html.Kendo().TextBoxFor(m => m.OppsPartnerCompanyName)
                                        .HtmlAttributes(new { @class = "form-control", @readonly = "readonly" })
                                        )
                                </div>
                            }

                            
                           
                            <div class="form-group">
                                <label>Wells</label>
                                @(Html.Kendo().MultiSelect()
                                        .Name("OppsWellIds")
                                .Placeholder("Select Well(s)")
                                .DataTextField("Text")
                                .DataValueField("Value")
                                .Filter(FilterType.Contains)
                                .Events(e => e.Open("multiselectOpen"))
                                .DataSource(source => {
                                    source.Read(read => {
                                            read.Action("GetCompanyWellsByCompanyFieldIds", "Lookup").Data("oppsFields");
                                    })
                                    .ServerFiltering(true);
                                })
                                .AutoBind(false)
                                .HtmlAttributes(new { @data_bind = "value:companyWellIds", @data_value_primitive = "true" }))
                            </div>
                            <div class="form-group">
                                <label>FTA Required ?</label>
                                @(Html.Kendo().DropDownListFor(m => m.OppsFtaRequired)
                                    .Filter(FilterType.Contains)
                                    .OptionLabel("Select FTA Required")
                                    .DataValueField("Key")
                                    .DataTextField("Value")
                                    .BindTo(Html.BooleanListValues().ToList())
                                    )
                            </div>
                          
                            
                            <div class="form-group">
                                <label>Expected Deployment Date </label>
                                @Html.Kendo().DatePickerFor(m => m.ExpectedDeploymentDate)
                            </div>
                            
                           
                            <div class="form-group">
                                <label>Rig Type</label>
                                @(
                                Html.Kendo().DropDownListFor(m => m.OppsRigType)
                            .DataTextField("Value")
                            .DataValueField("Key")
                            .Filter(FilterType.Contains)
                            .OptionLabel("Select Rig Type")
                            .BindTo(Centerpoint.Common.Constants.RigTypeConstant.ValuesAndDescriptions.ToList())
                            )
                            </div>
                            @if (GlobalSettings.IsWellsense)
                            {
                                <div class="form-group">
                                    <label>Pressure Control Required </label>
                                    @(
                                        Html.Kendo().DropDownListFor(m => m.PressureControlRequired)
                                        .DataValueField("Key")
                                        .DataTextField("Value")
                                        .Filter(FilterType.Contains)
                                        .BindTo(Html.BooleanListValues().ToList())
                                        )
                                </div>

                                <div class="form-group">
                                    <label for="wellType">Well Type</label>
                                    <br />
                                    @(
                                        Html.Kendo().DropDownListFor(x => x.WellType)
                                        .DataValueField("Key")
                                        .DataTextField("Value")
                                        .Filter(FilterType.Contains)
                                        .OptionLabel("Select Well Type")
                                        .HtmlAttributes(new { @tabindex = "7" })
                                        .BindTo(Centerpoint.Common.Constants.WellTypeConstant.ValuesAndDescriptions.ToList())
                                        )
                                </div>
                                <div class="form-group">
                                    <label>Well Depth/Hold-up Depth </label>
                                    @(
                                        Html.Kendo().TextBoxFor(m => m.Depth)
                                        .HtmlAttributes(new { @class = "form-control" })
                                        )
                                </div>
                            }
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Objectives</label>
                                @(Html.TextAreaFor(p => p.OppsObjectives, new { @class = "form-control w-100", @rows = "5" }))
                            </div>
                            <div class="form-group">
                                <label>Special Requirements</label>
                                @(Html.TextAreaFor(p => p.OppsSpecialRequirements, new { @class = "form-control w-100", @rows = "5" }))
                            </div>
                            <div class="form-group">
                                <label>Client Induction Requirement / Training Requirements</label>
                                @(Html.TextAreaFor(p => p.ClientInductionTrainingRequirements, new { maxlength = 4000, @class = "form-control w-100", @rows = "5" }))
                            </div>
                            <div class="form-group">
                                <label>QHSE consideration</label>
                                @(Html.TextAreaFor(p => p.OppsQhseConsideration, new { @class = "form-control w-100", @rows = "5" }))
                            </div>
                            <div class="form-group">
                                <label>Comments</label>
                                @(Html.TextAreaFor(p => p.OppsComments, new { @class = "form-control w-100", @rows = "5" }))
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
    <div class="d-flex actionsContainer mt-5">
        @Html.HiddenFor(m => m.ProjectId)

        @if (!Model.ProjectId.HasValue && (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsLogisticsAdmin() || Html.IsSeniorFieldEngineer() || Html.IsJuniorFieldEngineer() || Html.IsFieldEngineer() || Html.IsSeniorUSEngineer())) {
                <button id="createProject" type="button" class="btn btn-sm btn-primary">Save Project Details</button>
        } 
        else if (Model.ProjectId.HasValue && (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsSeniorUSEngineer())) {
                <button id="projectSaveDetails" type="button" class="btn btn-sm btn-primary">Save Project Details</button>
        }
    </div>
    <hr />
    @if (Model.HasJobs) {
        <h4 class="text-primary mb-3">Jobs (<span data-bind="text:totalJobs"></span>)</h4>

        <ul id="jobPanelBar"></ul>
    }
</div>



<script>
    let k;
    $(document).ready(function () {
        if (document.getElementById('ProjectId').value) {
            $.ajax({
                url: `/Operation/GetProjectMultiData`,
                data: { id: editProjectModel.projectId },
                dataType: "json",
                method: 'GET',
                success: (e) => {

                    k = e;
                    e.map(d => {
                        let values = [];
                        d.Items.map(i => {
                            $(`#${d.Name}`).data("kendoMultiSelect")?.dataSource.add(i);
                            values.push(i.Value)
                        })

                        $(`#${d.Name}`).data("kendoMultiSelect")?.value(values);
                    })
                },
                error: (e) => {
                    kendo.alert(e)
                }
            })
        }

    });

    
 

</script>