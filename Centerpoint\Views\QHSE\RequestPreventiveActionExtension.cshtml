﻿@model ServiceImprovementModel
<p>In order to the change the Preventive Action Target Date, please add a new date and fill in the reason for date change</p>
<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>New Preventive Action Target Date</label>
            <br />
            @(Html.Kendo().DatePicker().Name("PreventiveActionNewTargetDate").Min(Model.PreventiveActionTargetDate.HasValue ? Model.PreventiveActionTargetDate.Value.AddDays(1) : DateTime.Now.AddDays(1)).HtmlAttributes(new { @data_bind = "value:newPreventiveActionTargetDate", @style = "width:200px; font-size: 14px;"}))
        </div>
        <div class="form-group">
            <label>Comment</label>
            @(Html.TextArea("NewPreventiveActionTargetDateCommentWindow", null, new { @class = "form-control", @style = "100%", @rows = "5", @data_bind = "value:newPreventiveActionComment", @data_value_update = "keyup" }))
        </div>
    </div>
</div>
<button id="preventiveActionDateChangeSelectedConfirm" data-bind="enabled:newPreventiveActionComment" class="btn btn-primary btn-sm">Confirm</button>
@Html.HiddenFor(m => m.PreventiveActionTargetDate)