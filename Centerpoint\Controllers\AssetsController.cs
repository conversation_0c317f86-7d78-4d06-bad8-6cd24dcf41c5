using Centerpoint.Common;
using Centerpoint.Common.Constants;
using Centerpoint.Extensions;
using Centerpoint.Model.Configuration;
using Centerpoint.Model.Entities;
using Centerpoint.Model.ViewModels;
using Centerpoint.Service;
using Centerpoint.Service.Interfaces;
using Centerpoint.Service.Services;
using Centerpoint.Services;
using Centerpoint.Storage.Interfaces;
using Hangfire;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Connections;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Net;

namespace Centerpoint.Controllers
{
    [Authorize]
    public class AssetsController : Controller
    {
        private readonly IAssetService _assetService;
        private readonly ICurrentUserService _currentUserService;
        private readonly IIdentityService _identityService;
        private readonly IMaintenanceService _maintenanceService;
        private readonly IExportBackgroundService _exportBackgroundService;
        private readonly IStorage _storage;

        public AssetsController(IAssetService assetService, ICurrentUserService currentUserService, IIdentityService identityService, IMaintenanceService maintenanceService, IExportBackgroundService exportBackgroundService, IStorage storage)
        {
            _assetService = assetService;
            _currentUserService = currentUserService;
            _identityService = identityService;
            _maintenanceService = maintenanceService;
            _exportBackgroundService = exportBackgroundService;
            _storage = storage;
        }

        public async Task<ActionResult> Index(int? equipmentCategoryId, string equipmentCategory)
        {
            this.SetTitle("Equipment Item");

            if (equipmentCategoryId.HasValue)
            {
                ViewBag.EquipmentCategoryId = equipmentCategoryId;
            }

            ViewBag.EquipmentCategory = equipmentCategory;
            return View();
        }
        public async Task<ActionResult> GetEquipmentItemDropdownData(int id)
        {
            var model = await _assetService.GetEquipmentItemDropdownData(id);

            return Json(model);
        }
        public async Task<ActionResult> EditEquipmentItem(int id,
                                                          string returnUrl,
                                                          int? projectId,
                                                          int? packingListId,
                                                          int? shipmentId,
                                                          int? runId,
                                                          int? maintenanceRecordId)
        {
            var model = await _assetService.GetEquipmentItem(id);

            ViewBag.ReturnUrl = string.IsNullOrWhiteSpace(returnUrl) ? Request.GetTypedHeaders().Referer?.ToString() : returnUrl;

            if (projectId.HasValue)
            {
                ViewBag.ProjectId = projectId.Value;
            }

            if (packingListId.HasValue)
            {
                ViewBag.PackingListId = packingListId.Value;
            }

            if (shipmentId.HasValue)
            {
                ViewBag.ShipmentId = shipmentId.Value;
            }

            if (runId.HasValue)
            {
                ViewBag.RunId = runId.Value;
            }

            if (maintenanceRecordId.HasValue)
            {
                ViewBag.MaintenanceRecordId = maintenanceRecordId.Value;
            }

            this.SetTitle("Equipment Item");
            return View(model);
        }

        [HttpPost]
        public async Task<ActionResult> EditEquipmentItem(EquipmentItemModel model)
        {
            if (ModelState.IsValid)
            {
                var result = await _assetService.EditEquipmentItemAsync(model, _currentUserService.UserId, model.EquipmentCategoryId.Value);

                if (result.HasErrors())
                {
                    Response.StatusCode = (int)HttpStatusCode.BadRequest;
                    return Json(result.Errors);
                }

                return Json(result.Response);
            }
            Response.StatusCode = (int)HttpStatusCode.BadRequest;
            return Json(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)));
        }

        public async Task<ActionResult> DeleteEquipmentItem(int id, string returnUrl)
        {
            var equipmentItem = await _assetService.DeleteEquipmentItemAsync(id);
            this.SetMessage(MessageType.Success, string.Format(ResourceMessages.DeleteEquipmentItemSuccess, equipmentItem.EquipmentNumber));
            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<ActionResult> AcceptEquipmentItem(int equipmentItemId,
                                                            int currentCompanyLocationId,
                                                            string acceptanceNote,
                                                            int customStatusId,
                                                            string acceptCustomStatusComment,
                                                            DateTime expiryDate)
        {
            await _assetService.AcceptEquipmentItemAsync(equipmentItemId,
                                                         currentCompanyLocationId,
                                                         acceptanceNote,
                                                         customStatusId,
                                                         acceptCustomStatusComment,
                                                         expiryDate,
                                                         _currentUserService.UserId);

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<ActionResult> ChangeCurrentLocation(int currentCompanyLocationId, int equipmentItemId, int? currentProjectId)
        {
            await _assetService.ChangeCurrentLocationAsync(currentCompanyLocationId, equipmentItemId, _currentUserService.UserId, currentProjectId);
            return Json(new { success = "true" });
        }

        public async Task<ActionResult> GetEquipmentItems([DataSourceRequest] DataSourceRequest request, int? equipmentCategoryId)
        {
            var result = await _assetService.GetEquipmentItemsAsync(equipmentCategoryId, _currentUserService.UserId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetFilteredEquipmentItemsCount([DataSourceRequest] DataSourceRequest request, int? equipmentCategoryId)
        {
            var result = await _assetService.GetEquipmentItemsAsync(equipmentCategoryId, _currentUserService.UserId);

            // Apply default filtering to exclude Lost, Quarantined, and Inactive assets
            // This matches the behavior expected for the main assets grid
            var filteredResult = result.Where(e =>
                e.Status != EquipmentConstant.Lost &&
                e.Status != EquipmentConstant.Quarantined &&
                e.Status != EquipmentConstant.Inactive);

            var dataSourceResult = filteredResult.ToDataSourceResult(request);
            return Json(new { total = dataSourceResult.Total });
        }

        public async Task<ActionResult> DuplicateItems()
        {
            var model = await _assetService.DuplicateItemsAsync(_currentUserService.UserId);
            return View(model);
        }

        [HttpPost]
        public async Task<ActionResult> UpdateEquipmentItemInfo(List<int> equipmentItemIds, string info)
        {
            await _assetService.UpdateEquipmentItemInfoAsync(equipmentItemIds, info);
            return Json(new { success = "true" });
        }

        public async Task<ActionResult> GetEquipmentItemsStatistics([DataSourceRequest] DataSourceRequest request, int? equipmentCategoryId, DateTime startDate, DateTime endDate)
        {
            var result = await _assetService.GetEquipmentItemsStatisticsAsync(equipmentCategoryId, startDate, endDate, _currentUserService.UserId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetEquipmentItemsNotInEquipmentItemBundleId([DataSourceRequest] DataSourceRequest request, int? equipmentCategoryId, int equipmentItemId, int? companyLocationId)
        {
            var result = await _assetService.GetEquipmentItemsNotInEquipmentItemBundleIdAsync(equipmentItemId, _currentUserService.UserId, companyLocationId);
            return Json(result.ToDataSourceResult(request));

        }

        public async Task<ActionResult> GetBundledEquipmentItems([DataSourceRequest] DataSourceRequest request, int parentEquipmentItemId)
        {
            var result = await _assetService.GetBundledEquipmentItemsAsync(parentEquipmentItemId);
            return Json(result.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateBundledEquipmentItems(int[] equipmentItemIds, int parentEquipmentItemId)
        {
            await _assetService.UpdateBundledEquipmentItemsAsync(equipmentItemIds, parentEquipmentItemId, _currentUserService.UserId);
            return Json(new { success = "true" });
        }

        public async Task<ActionResult> DeleteBundleEquipmentItem(int equipmentItemBundleId)
        {
            await _assetService.DeleteBundleEquipmentItemAsync(equipmentItemBundleId);
            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<ActionResult> Export(string contentType, string base64, string fileName)
        {
            var fileContents = Convert.FromBase64String(base64);
            return File(fileContents, contentType, fileName);
        }

        #region Equipment Item Maintenance Schedule

        public async Task<ActionResult> GetEquipmentItemMaintenanceSchedules([DataSourceRequest] DataSourceRequest request, int eId)
        {
            var result = await _assetService.GetEquipmentItemMaintenanceSchedules(eId);
            return Json(result.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateEquipmentItemMaintenanceSchedule([DataSourceRequest] DataSourceRequest request, EquipmentItemMaintenanceScheduleModel model, int eId)
        {
            var result = await _assetService.UpdateEquipmentItemMaintenanceSchedule(model, eId);
            return Json(result.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> DeleteEquipmentItemMaintenanceSchedule([DataSourceRequest] DataSourceRequest request, EquipmentItemMaintenanceScheduleModel model)
        {
            var result = await _assetService.DeleteEquipmentItemMaintenanceSchedule(model);
            return Json(result.ToDataSourceResult(request));
        }

        #endregion

        #region Equipment Item Note

        public async Task<ActionResult> GetEquipmentItemNotes([DataSourceRequest] DataSourceRequest request, int equipmentItemId)
        {
            var result = await _assetService.GetEquipmentItemNotes(equipmentItemId);
            return Json(result.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateEquipmentItemNote([DataSourceRequest] DataSourceRequest request, EquipmentItemNoteModel model, int eId)
        {
            var result = await _assetService.UpdateEquipmentItemNote(model, eId, _currentUserService.UserId);
            return Json(new[] { result }.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteEquipmentItemNote([DataSourceRequest] DataSourceRequest request, EquipmentItemNoteModel model)
        {
            var result = await _assetService.DeleteEquipmentItemNote(model);
            return Json(result.ToDataSourceResult(request));
        }

        #region Equipment Item Status

        [HttpPost]
        public async Task<ActionResult> UpdateStatus(int id, string status)
        {
            await _assetService.UpdateStatus(id, status, _currentUserService.UserId);

            return Json(new { success = "true" });
        }

        #endregion

        #endregion

        #region Equipment Item Documents

        public async Task<ActionResult> GetEquipmentItemDocuments([DataSourceRequest] DataSourceRequest request, int equipmentItemId)
        {
            var result = await _assetService.GetEquipmentItemDocuments(equipmentItemId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> AttachEquipmentItemDocuments(IEnumerable<IFormFile> equipmentItemAttachmentDocuments, int equipmentItemId)
        {
            await _assetService.AttachEquipmentItemDocuments(equipmentItemAttachmentDocuments, equipmentItemId, _currentUserService.UserId);
            return Json(true);
        }

        public async Task<ActionResult> AttachEquipmentItemCertificates(IEnumerable<IFormFile> equipmentItemCertificates, int equipmentItemId)
        {
            await _assetService.AttachEquipmentItemCertificates(equipmentItemCertificates, equipmentItemId, _currentUserService.UserId);
            return Json(true);
        }

        public async Task<ActionResult> DeleteEquipmentItemDocument([DataSourceRequest] DataSourceRequest request, DocumentModel model, int equipmentItemId)
        {
            await _assetService.DeleteEquipmentItemDocument(model, equipmentItemId, _currentUserService.UserId);
            return Json(ModelState.ToDataSourceResult());
        }
        public async Task<ActionResult> GetEquipmentItemCertificates([DataSourceRequest] DataSourceRequest request, int equipmentItemId)
        {
            var result = await _assetService.GetEquipmentItemCertificates(equipmentItemId);
            return Json(result.ToDataSourceResult(request));
        }
        public async Task<ActionResult> GetEquipmentItemCertificate(int Id)
        {
            var filePath = await _assetService.GetEquipmentItemCertificate(Id);



            return Redirect(filePath);
        }
        public async Task<ActionResult> DeleteEquipmentItemCertificate([DataSourceRequest] DataSourceRequest request, EquipmentItemCertificateModel model, int equipmentItemId)
        {
            await _assetService.DeleteEquipmentItemCertificate(model, equipmentItemId, _currentUserService.UserId);
            return Json(ModelState.ToDataSourceResult(request));
        }
        public async Task<ActionResult> EditEquipmentItemCertificate([DataSourceRequest] DataSourceRequest request, EquipmentItemCertificateModel model, int equipmentItemId)
        {
            var result = await _assetService.EditEquipmentItemCertificate(model, equipmentItemId, _currentUserService.UserId);
            return Json(new[] { result }.ToDataSourceResult(request));
        }

        #endregion

        #region Equipment Item Custom Status Codes

        public async Task<ActionResult> GetEquipmentItemCustomStatusCodes([DataSourceRequest] DataSourceRequest request, int equipmentItemId)
        {
            var result = await _assetService.GetEquipmentItemCustomStatusCodes(equipmentItemId);
            return Json(result.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateEquipmentItemCustomStatusCode([DataSourceRequest] DataSourceRequest request, EquipmentItemCustomStatusCodeModel model, int eId, int customStatusCodeId)
        {
            var result = await _assetService.UpdateEquipmentItemCustomStatusCode(model, eId, customStatusCodeId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> DeleteEquipmentItemCustomStatusCode([DataSourceRequest] DataSourceRequest request, EquipmentItemCustomStatusCodeModel model)
        {
            var result = await _assetService.DeleteEquipmentItemCustomStatusCode(model);
            return Json(result.ToDataSourceResult(request));
        }

        #endregion

        #region Equipment Item Log

        public async Task<ActionResult> GetEquipmentItemLogsByEquipmentItemId([DataSourceRequest] DataSourceRequest request, int eId)
        {
            var result = await _assetService.GetEquipmentItemLogsByEquipmentItemId(eId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> EquipmentItemLogReference(int id, string type)
        {
            if (type == EquipmentLogTypeConstant.Job)
            {
                return RedirectToAction("EditJob", "Operation", new { @id = id });
            }
            else if (type == EquipmentLogTypeConstant.Maintenance)
            {
                return RedirectToAction("EditMaintenanceRecord", "Maintenance", new { @id = id });
            }
            else if (type == EquipmentLogTypeConstant.Shipment)
            {
                return RedirectToAction("EditEquipmentShipment", "Logistics", new { @id = id });
            }
            else if (type == EquipmentLogTypeConstant.PackingList)
            {
                return RedirectToAction("EditEquipmentPackingList", "Logistics", new { @id = id });
            }
            else if (type == EquipmentLogTypeConstant.Project)
            {
                return RedirectToAction("EditProject", "Operation", new { @id = id });
            }
            else if (type == EquipmentLogTypeConstant.Run)
            {
                return RedirectToAction("EditRun", "Operation", new { @id = id });
            }

            return Redirect(Request.GetTypedHeaders().Referer.AbsoluteUri.ToString());
        }

        #endregion


        [HttpPost]
        public async Task<ActionResult> MoveAssetItem(int[] equipmentItemIds, int equipmentCategoryId)
        {
            await _assetService.MoveAssetItem(equipmentItemIds, equipmentCategoryId);
            return Json(new { success = "true" });
        }

        public async Task<ActionResult> GetEquipmentCategoryMaintenanceSteps([DataSourceRequest] DataSourceRequest request, int? categoryId)
        {
            var result = await _assetService.GetEquipmentCategoryMaintenanceSteps(categoryId);
            return Json(result.ToDataSourceResult(request));
        }

        #region Schedule Maintenance

        public async Task StartScheduledMaintenance()
        {
            if (GlobalSettings.IsWellsense)
            {
                var equipmentMaintenanceSchedules = await _assetService.GetAllEquipmentItemMaintenanceSchedules();
                var user = await _identityService.GetUserByEmailAsync("<EMAIL>");

                if (equipmentMaintenanceSchedules.Any() && user != null)
                {
                    foreach (var schedule in equipmentMaintenanceSchedules)
                    {
                        if (schedule.StartDate.Date == DateTime.UtcNow.Date && !schedule.CompanyLocationId.HasValue)
                        {
                            await _maintenanceService.StartMaintenanceRecord(schedule.EquipmentItemId, schedule.MaintenanceBlueprintId, user.Id);
                        }
                    }
                }
            }
        }
        #endregion

        public async Task UpdatePointsPerMonth()
        {
            await _assetService.UpdatePointsPerMonth();
        }

        public async Task<IActionResult> CollectEquipmentItemsData(int id)
        {
            var archiveBytes = await _assetService.GenerateEquipmentItemArchiveAsync(id);

            if (archiveBytes == null || archiveBytes.Length == 0)
                return NotFound("Archive could not be generated.");

            var fileName = $"Equipment_Archive_{DateTime.UtcNow:yyyyMMdd_HHmmss}.zip";
            return File(archiveBytes, "application/zip", fileName);
        }

        #region Export All Assets

        [HttpGet("ExportAllAssets")]
        public async Task<IActionResult> ExportAllAssets(string jobId = null)
        {
            // If jobId is provided, this is a status check - redirect to dedicated status method
            if (!string.IsNullOrEmpty(jobId))
            {
                return await GetExportJobStatus(jobId);
            }

            // Start new export job for all assets (GET fallback for direct URL access)
            var newJobId = Guid.NewGuid().ToString();

            // Queue the background job
            BackgroundJob.Enqueue<IExportBackgroundService>(service =>
                service.ProcessAllEquipmentExportAsync(newJobId, _currentUserService.UserId));

            return Json(new { jobId = newJobId, status = "started" });
        }

        [HttpGet("GetExportJobStatus")]
        public async Task<IActionResult> GetExportJobStatus(string jobId)
        {
            var status = await _exportBackgroundService.GetExportJobStatusAsync(jobId);
            if (status == null)
            {
                return Json(new { error = "Job not found" });
            }

            return Json(new
            {
                status = status.Status.ToLower(),
                totalItems = status.TotalItems,
                processedItems = status.ProcessedItems,
                progressPercentage = status.ProgressPercentage,
                downloadUrl = status.DownloadUrl,
                errorMessage = status.ErrorMessage
            });
        }

        [HttpPost("ExportAllAssets")]
        public async Task<IActionResult> ExportAllAssets([FromBody] FilteredExportRequest request)
        {
            // Start new export job
            var newJobId = Guid.NewGuid().ToString();

            if (request?.IsFiltered == true && request.EquipmentItemIds?.Any() == true)
            {
                // Queue the background job for filtered export
                BackgroundJob.Enqueue<IExportBackgroundService>(service =>
                    service.ProcessFilteredEquipmentExportAsync(newJobId, _currentUserService.UserId, request.EquipmentItemIds));
            }
            else
            {
                // Queue the background job for all assets
                BackgroundJob.Enqueue<IExportBackgroundService>(service =>
                    service.ProcessAllEquipmentExportAsync(newJobId, _currentUserService.UserId));
            }

            return Json(new { jobId = newJobId, status = "started" });
        }

        [HttpGet]
        public async Task<IActionResult> DownloadExport(string jobId)
        {
            var status = await _exportBackgroundService.GetExportJobStatusAsync(jobId);
            if (status == null || status.Status != "Completed")
            {
                return NotFound("Export not found or not completed");
            }

            // Use the stored blob filename from the job status
            if (string.IsNullOrEmpty(status.BlobFileName))
            {
                return NotFound("Export file path not found");
            }

            var blobClient = await _storage.GetBlobClient(status.BlobFileName);

            if (!await blobClient.ExistsAsync())
            {
                return NotFound("Export file not found");
            }

            var stream = await blobClient.OpenReadAsync();
            var fileName = $"All_Equipment_Items_{DateTime.UtcNow:yyyyMMdd_HHmmss}.zip";

            return File(stream, "application/zip", fileName);
        }

        #endregion
    }
}

