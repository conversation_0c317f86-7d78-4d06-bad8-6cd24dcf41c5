$(document).ready(function () {
    getMonthlySummary();
    loadSifGrid();
});

$(window).resize(function(){
    $("#sifStatusChart").data("kendoChart").redraw();
});

$(function () {
    var grid = $("#serviceImprovementGrid").data("kendoGrid");

    $("#reset").click(function (e) {
        e.preventDefault();
        localStorage["serviceImprovementGrid"] = "";
        window.location.reload();
    });
});

function saveSifGrid(e) {
    setTimeout(function () {
        var grid = $("#serviceImprovementGrid").data("kendoGrid");
        localStorage["serviceImprovementGrid"] = kendo.stringify(grid.getOptions());
    }, 10);
}

function loadSifGrid() {
    var grid = $("#serviceImprovementGrid").data("kendoGrid");
    var toolBar = $("#serviceImprovementGrid .k-grid-toolbar").html();
    var options = localStorage["serviceImprovementGrid"];
    viewModel.set("initialServiceImprovementGridOptions", kendo.stringify(grid.getOptions()));
    if (options) {
        grid.setOptions(JSON.parse(options));
        $("#serviceImprovementGrid .k-grid-toolbar").html(toolBar);
        $("#serviceImprovementGrid .k-grid-toolbar").addClass("k-grid-top");
    }
}

function onError(e, status) {
    if (e.status == "customerror") {
        alert(e.errors);

        var serviceImprovementGrid = $("#serviceImprovementGrid").data("kendoGrid");
        serviceImprovementGrid.dataSource.cancelChanges();
    }
}

function refreshSifStatusChart(){
    var sifStatusChart = $("#sifStatusChart").data("kendoChart");
    sifStatusChart.dataSource.read();

    refreshServiceImprovementGrid();
} 

function sifData(){
    return{
        type:viewModel.get("type"),
        month:viewModel.get("month"),
        year:viewModel.get("year")
    }
}

function sifStatusChartData(){
    return{
        from:kendo.toString(viewModel.get("fromDate"), 'dd/MM/yyyy'),
        to:kendo.toString(viewModel.get("toDate"), 'dd/MM/yyyy')
    }
}

function monthlySummaryData(){
    return{
        month:viewModel.get("month"),
        year:viewModel.get("year")
    }
}

function updateServiceImprovementGrid() {
    $("#resetServiceImprovementGrid").click(function (e) {
        e.preventDefault();
        resetGridView('serviceImprovementGrid', 'initialServiceImprovementGridOptions')
    });

    var serviceImprovementGrid = $("#serviceImprovementGrid").data("kendoGrid");
    var totalServiceImprovements = serviceImprovementGrid.dataSource.total();
    viewModel.set("totalServiceImprovements", totalServiceImprovements);
}

function monthChanged(e){
    getMonthlySummary();
}

function yearChanged(e){
    getMonthlySummary();
}

function getMonthlySummary(){
    $.ajax({
        url: "/Qhse/GetMonthlySummary",
        data: {
            month:viewModel.get("month"),
            year:viewModel.get("year")
        },
        success: function(result){
            if(result){
                viewModel.set("totalCreatedMonth",result.TotalCreated);
                viewModel.set("totalClosedMonth",result.TotalClosed);
            }
        },
        dataType: "json"
    });
    refreshServiceImprovementGrid();
}

function refreshServiceImprovementGrid(){
    var serviceImprovementGrid = $("#serviceImprovementGrid").data("kendoGrid");
    serviceImprovementGrid.dataSource.read();
}

var viewModel = kendo.observable({
    totalServiceImprovements: 0,
    chartTotals: [],
    fromDate: qhseModel.modelFromDate,
    toDate: qhseModel.modelToDate,
    totalCreatedMonth:0,
    totalClosedMonth:0,
    month: qhseModel.modelMonth,
    year: qhseModel.modelYear,
    type:"",
    totalClick:function(){
        this.set("type", "total");
        refreshServiceImprovementGrid();
    },
    qualityClick:function(){
        this.set("type", "quality");
        refreshServiceImprovementGrid();
    },
    healthClick:function(){
        this.set("type", "health");
        refreshServiceImprovementGrid();
    },
    safetyClick:function(){
        this.set("type", "safety");
        refreshServiceImprovementGrid();
    },
    environmentClick:function(){
        this.set("type", "environment");
        refreshServiceImprovementGrid();
    },
    securityClick:function(){
        this.set("type", "security");
        refreshServiceImprovementGrid();
    },
    improvementClick:function(){
        this.set("type", "improvement");
        refreshServiceImprovementGrid();
    },
    openClick:function(){
        this.set("type", "open");
        refreshServiceImprovementGrid();
    },
    onTargetClick:function(){
        this.set("type", "onTarget");
        refreshServiceImprovementGrid();
    },
    overdueClick:function(){
        this.set("type", "overdue");
        refreshServiceImprovementGrid();
    },
    closedClick:function(){
        this.set("type", "closed");
        refreshServiceImprovementGrid();
    },
    userClick:function(){
        this.set("type", "user");
        refreshServiceImprovementGrid();
    },
    createdMonthClick:function(){
        this.set("type", "createdMonth");
        refreshServiceImprovementGrid();
    },
    closedMonthClick:function(){
        this.set("type", "closedMonth");
        refreshServiceImprovementGrid();
    }
});

kendo.bind(document.body.children, viewModel);