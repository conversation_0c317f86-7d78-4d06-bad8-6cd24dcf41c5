﻿namespace Centerpoint.Common.Constants
{
    public static class MaintenanceConstant
    {

        public const string UnAssigned = "UNA";
        public const string Assigned = "ASS";
        public const string InProgress = "PRO";
        public const string OnHold = "HOL";
        public const string AwaitingApproval = "AAP";
        public const string OnGoing = "ONG";
        public const string Closed = "CLS";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {UnAssigned,"Unassigned"},
                    {Assigned,"Assigned"},
                    {InProgress,"In-Progress"},
                    {OnHold,"On Hold"},
                    {OnGoing,"On Going"},
                    {AwaitingApproval,"Awaiting Approval"},
                    {Closed,"Closed"}
                };
            }
        }
    }
}
