﻿@model CompanyWellModel

<div class="header-container-between">
    <h4>
        <i class="fa fa-tags"></i>
        @Model.Name
    </h4>
    <div>
       <a class="btn btn-info btn-sm" href="@Url.Action("ViewCompany","Company",new { @id = Model.CompanyId} )">
            <i class="fa fa-refresh"></i>Go to Company
        </a>
    </div>
</div>
<hr />

@(Html.Kendo().TabStrip()
.Name("viewCompanyStrips")
.SelectedIndex(0)
.Animation(false)
.Items( tabstrip => {

tabstrip.Add().Text("")
    .HtmlAttributes(new { @data_bind = "html:tabStripHeaderDetails" })
    .Selected(true)
    .Content(@<text>

        <div id="wellDetails">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Field</label>
                            <br />
                            @(Html.Kendo().DropDownListFor(m => m.CompanyFieldId)
                            .DataTextField("Name")
                            .DataValueField("CompanyFieldId")
                            .Filter("contains")
                            .OptionLabel("Select Fields")
                            .HtmlAttributes(new { @data_value_primitive = "true" })
                            .DataSource(d => d.Read("GetFieldsByCompanyId", "Lookup", new { @companyId = Model.CompanyId })))
                        </div>
                        <div class="form-group">
                            @Html.LabelFor(m => m.MinimumId)
                            <br />
                            @(Html.Kendo().NumericTextBoxFor(p => p.MinimumId)
                            .Spinners(false)
                            .Format("n3")
                            .Decimals(3))
                        </div>
                        <div class="form-group">
                            @Html.LabelFor(m => m.MaximumPressure)
                            <br />
                            @(Html.Kendo().NumericTextBoxFor(p => p.MaximumPressure)
                            .Spinners(false)
                            .Format("n2")
                            .Decimals(2))
                        </div>
                        <div class="form-group">
                            <label>Units</label>
                            <br />
                            @(Html.Kendo().DropDownListFor(m => m.MaximumPressureUnits)
                            .DataTextField("Value")
                            .DataValueField("Key")
                            .Filter("contains") 
                            .BindTo(Centerpoint.Common.Constants.MaximumPressureUnitsConstant.ValuesAndDescriptions.ToList())
                            .HtmlAttributes(new { @data_value_primitive = "true" }))
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Well</label>
                            <br />
                            @(Html.Kendo().TextBoxFor(m => m.Name)
                             .HtmlAttributes(new { @style = "width:100%" })
                            )
                        </div>
                        <div class="form-group">
                            @Html.LabelFor(m => m.MaximumDeviation)
                            <br />
                            @(Html.Kendo().NumericTextBoxFor(p => p.MaximumDeviation)
                            .HtmlAttributes(new { @style = "width:100%" })
                            .Spinners(false)
                            .Format("n0"))
                        </div>
                        <div class="form-group">
                            @Html.LabelFor(m => m.MaximumTemperature)
                            <br />
                            @(Html.Kendo().NumericTextBoxFor(p => p.MaximumTemperature)
                            .HtmlAttributes(new { @style = "width:100%;" })
                            .Spinners(false)
                            .Format("n0"))
                        </div>
                        <div class="form-group">
                            <label>Degrees</label>
                            <br />
                            @(Html.Kendo().DropDownListFor(m => m.MaximumTemperatureDegrees)
                            .DataTextField("Value")
                            .DataValueField("Key")
                            .Filter("contains")
                            .BindTo(Centerpoint.Common.Constants.MaximumTemperatureDegreesConstant.ValuesAndDescriptions.ToList())
                        .HtmlAttributes(new { @data_value_primitive = "true" }))
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            @Html.LabelFor(m => m.WellType)
                            <br />
                            @(Html.Kendo().DropDownListFor(p => p.WellType)
                            .DataValueField("Key")
                            .DataTextField("Value")
                            .Filter("contains")
                            .OptionLabel("Select Well Type")
                        .HtmlAttributes(new { @tabindex = "7" })
                            .BindTo(Centerpoint.Common.Constants.WellTypeConstant.ValuesAndDescriptions.ToList()))
                        </div>
                        <div class="form-group">
                            <label>H<sub>2</sub>S (ppm)</label>
                            <br />
                            @(Html.Kendo().NumericTextBoxFor(m => m.H2S)
                            .HtmlAttributes(new { @style = "width:100%" })
                            .Spinners(false)
                            .Format("n0"))
                        </div>
                        <div class="form-group">
                            <label>CO<sub>2</sub> (%)</label>
                            <br />
                            @(Html.Kendo().NumericTextBoxFor(m => m.CO2)
                            .HtmlAttributes(new { @style = "width:100%" })
                            .Spinners(false)
                            .Format("n0"))
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Fluids</label>
                            @(Html.Kendo().MultiSelectFor(m => m.FluidTypeIds)
                            .Placeholder("Select Fluid(s)")
                            .DataTextField("Name")
                            .DataValueField("FluidTypeId")
                            .DataSource(source => {
                                source.Read(read => {
                                    read.Action("GetCompanyWellFluids", "Lookup");
                                })
                                .ServerFiltering(true);
                            })
                            )
                        </div>
                    </div>
                </div>
        </div>

    </text>);

tabstrip.Add().Text("")
    .HtmlAttributes(new { @data_bind="html:attachmentsStripText"})
    .Content(@<text>

        <div id="documents">
            @if (Model.DevitationSurveyType != null || Model.SchematicType != null || Model.TallyType != null) {
                <div class="mb-2">
                    <div class="card-header">
                        <h6 class="mb-0">Latest Revisions</h6>
                    </div>
                    <div>
                        <table class="table table-striped table-bordered attachments-container" cellspacing="0" id="table">
                            <thead>
                                <tr>
                                    <th style="width:40%">Document</th>
                                    <th style="width:20%">Type</th>
                                    <th style="width:20%">Created</th>
                                    <th style="width:20%">Created By</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.DevitationSurveyType != null) {
                                    <tr>
                                        <td style="width:40%"><a href="@Url.Action("Index", "Document", new { @id = Model.DevitationSurveyType.DocumentId })">@Model.DevitationSurveyType.FileName</a></td>
                                        <td style="width:20%">@Model.DevitationSurveyType.CompanyWellDocumentTypeDescription</td>
                                        <td style="width:20%">@Model.DevitationSurveyType.CreatedDate</td>
                                        <td style="width:20%">@Model.DevitationSurveyType.Username</td>
                                    </tr>
                                }
                                @if (Model.SchematicType != null) {
                                    <tr>
                                        <td style="width:40%"><a href="@Url.Action("Index", "Document", new { @id = Model.SchematicType.DocumentId })">@Model.SchematicType.FileName</a></td>
                                        <td style="width:20%">@Model.SchematicType.CompanyWellDocumentTypeDescription</td>
                                        <td style="width:20%">@Model.SchematicType.CreatedDate</td>
                                        <td style="width:20%">@Model.SchematicType.Username</td>
                                    </tr>
                                }
                                @if (Model.TallyType != null) {
                                    <tr>
                                        <td style="width:35%"><a href="@Url.Action("Index", "Document", new { @id = Model.TallyType.DocumentId })">@Model.TallyType.FileName</a></td>
                                        <td style="width:20%">@Model.TallyType.CompanyWellDocumentTypeDescription</td>
                                        <td style="width:20%">@Model.TallyType.CreatedDate</td>
                                        <td style="width:20%">@Model.TallyType.Username</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
                <hr />
            }
            <div class="form-group">
                <label>Document Type</label>
                <br />
                @(Html.Kendo().DropDownList()
                .Name("documentType")
                .DataValueField("Key")
                .DataTextField("Value")
                .Filter("contains")
                .OptionLabel("Select Type")
                .Events(e => {
                    e.Change("documentTypeChanged");
                })
                .BindTo(Centerpoint.Common.Constants.CompanyWellDocumentTypeConstant.ValuesAndDescriptions.ToList()))
            </div>
            <br />
            <div>
                <div class="card-header">
                    <h6 class="mb-0">Past Revisions</h6>
                </div>
                <div>
                    @(Html.Kendo().Grid<CompanyWellDocumentModel>()
                        .Name("wellDocumentsGrid")
                        .Columns(c => {
                        c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                        c.Bound(p => p.CompanyWellDocumentTypeDescription).Title("Type").ClientTemplate("#=CompanyWellDocumentTypeDescription ? CompanyWellDocumentTypeDescription : 'N/A'#");
                        c.Bound(p => p.Created).Format(DateConstants.DateFormat).Title("Created");
                        c.Bound(p => p.Username).Title("Created By");
                        })
                        .Events(e => e.DataBound("updateWellDocumentsGrid"))
                        .Sortable()
                        .Resizable(r => r.Columns(true))
                        .ColumnMenu(c => c.Columns(true))
                        .Filterable()
                        .Groupable()
                        .Editable(e => e.Mode(GridEditMode.InLine))
                        .Scrollable(s => s.Height(300))
                        .DataSource(dataSource => dataSource
                        .Ajax()
                        .Events(e => e.RequestEnd("onRequestEnd"))
                        .ServerOperation(false)
                        .Model(model => model.Id(p => p.CompanyWellDocumentId))
                        .Read(read => read.Action("GetCompanyWellDocuments", "Admin", new { @companyWellId = Model.CompanyWellId }))))
                </div>
            </div>
        </div>
        
    </text>);    
}
))

<script>
   const viewCompanyWellModel = {
       modelCompanyWellId: "@Model.CompanyWellId",
   }
</script>


<environment include="Development">
    <script src="~/js/views/companies/viewCompanyWell.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/companies/viewCompanyWell.min.js" asp-append-version="true"></script>
</environment>