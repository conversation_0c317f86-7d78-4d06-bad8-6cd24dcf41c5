﻿@(Html.<PERSON><PERSON>().TabStrip()
    .Name("shipmentsStrips")
    .SelectedIndex(0)
    .Animation(false)
    .Items( tabstrip => {

     tabstrip.Add().Text("")
         .HtmlAttributes(new { @data_bind = "click:refreshShipmentChart, html:tabStripHeaderShipmentsMonthly" })
         .Selected(true)
         .Content(@<text>
            <div class="card">
                <div class="card-header">
                   <h6 class="mb-0">Shipments per Month</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label>Period</label>
                        <input 
                           data-role="numerictextbox" 
                           data-spinners="false"
                           data-bind="value:shipmentChartMonths, events:{change:refreshShipmentChart}"
                           data-format="Last 0 months" 
                           data-min="1" 
                           />
                    </div>
                    @(Html.Kendo().Chart<StatisticsShipmentModel>()
                    .Name("shipmentMonthChart")
                    .SeriesDefaults(series => series.Column())
                    .Series(series => {
                        series.Column(p => p.Sent).Name("Items Sent").Stack("shipment").Color("#7cbb00");
                        series.Column(p => p.Missing).Name("Items Missing").Stack("shipment").Color("#cd1533");
                    })
                    .AutoBind(false)
                    .ValueAxis(v => v.Numeric().Title("Count").MajorGridLines(l => l.Visible(false)).MinorGridLines(l => l.Visible(false)))
                    .Legend(l => l.Position(ChartLegendPosition.Bottom))
                    .CategoryAxis(c => c.Categories(p => p.Category).Title("Shipments"))
                    .Tooltip(t => t.Visible(true).Template("#=value# #=category#"))
                    .DataSource(d => d.Read(read => read.Action("GetShipmentMonthChartData", "Statistics").Data("shipmentMonthChartData"))))
                </div>
            </div>
             
          </text>); 


      
     tabstrip.Add().Text("")
         .HtmlAttributes(new { @data_bind="html:tabStripHeaderConstructorOfShipmentsWithNumber"})
         .Content(@<text>
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Shipments (with active MRs)</h6>
                </div>
                <div class="card-body">
                    @(Html.Kendo().Grid<StatisticsShipmentMRModel>()
                    .Name("shipmentMRGrid")
                    .Columns(c => {
                        c.Bound(p => p.ShipmentNumberandStatus).Title("Shipment").ClientTemplate("<a href='" + @Url.Action("EditEquipmentShipment", "Logistics", new { @id = "" }) + "/#=EquipmentShipmentId#'>#=ShipmentNumberandStatus#</a>");
                        c.Bound(p => p.EquipmentItemName).Title("Equipment Item").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#'>#=EquipmentItemName#</a>");
                        c.Bound(p => p.EquipmentShipmentProjectName).Title("Project").ClientTemplate("<a href='" + @Url.Action("EditProject", "Operation", new { @id = "" }) + "/#=EquipmentShipmentProjectId#'>#=EquipmentShipmentProjectName#</a>");
                        c.Bound(p => p.EquipmentShipmentSentDateOnly).Title("Shipped Date");
                        c.Bound(p => p.EquipmentShipmentReceivedDateOnly).Title("Received Date");
                        c.Bound(p => p.ActiveMRs).Title("Active MR");
                    })
                        .ToolBar(t => {
                            t.Excel().Text("Export").HtmlAttributes(new { @class = "bnt btn-success" });
                        }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
                        .Sortable()
                        .Filterable()
                        .Scrollable(s => s.Height(500))
                        .Resizable(c => c.Columns(true))
                        .ColumnMenu(c => c.Columns(true))
                        .Excel(excel => excel
                            .FileName(string.Format("Centerpoint_Shipments_With_Active_MR_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                            .Filterable(true)
                            .ProxyURL(Url.Action("Export", "Logistics")))
                        .Events(e => e.DataBound("updateShipmentMRGrid"))
                        .DataSource(dataSource => dataSource
                        .Ajax()
                        .ServerOperation(false)
                        .Model(model => {
                            model.Id(m => m.EquipmentShipmentItemId);
                        })
                        .Read(read => read.Action("GetEquipmentShipmentItemsWithActiveMR", "Statistics"))))
                </div>
            </div>

            </text>);
    }
  )
)