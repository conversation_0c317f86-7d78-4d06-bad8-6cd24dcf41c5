﻿@model LessonModel

<div class="header-container-between">
    <h4>
        <i class="fa fa-bars"></i>
        Lessons Learned
    </h4>
    <div>
       <a class="btn btn-primary btn-sm" href="@Url.Action("AddLesson", "Qhse")"><i class="fa fa-plus"></i> New Lesson Learned</a>
    </div>
</div>
<hr />

<div class="row">
    <div class="col-md-3">
        <div class="card default-height">
            <div class="card-header">
                <h6 class="mb-0">Lessons Learned</h6>
            </div>
            <div class="card-body">
                <div class="card-list-item">
                    <span class="card-list-item-name">Total</span>
                    <a class="card-list-item-count" href="#lessonGrid" style="background: #999" data-bind="click:totalClick,text:totalCreated"></a>
                </div>  
                <div class="card-list-item">
                    <span class="card-list-item-name">By You</span>
                    <a class="card-list-item-count" href="#lessonGrid" style="background: #68217A" data-bind="click:byYouClick,text:totalByYou"></a>
                </div>         
                <div class="card-list-item">
                    <span class="card-list-item-name">Pending Approval</span>
                    <a class="card-list-item-count" href="#lessonGrid" style="background: #BA141A" data-bind="click:pendingApprovalClick,text:totalPendingByYou"></a>
                </div>
            </div>
        </div>       
    </div>
    <div class="col-md-9">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Most Recent</h6>
            </div>
            <div class="card-body">
                @(Html.Kendo().ListView<LessonModel>()
                .Name("recentLessons")
                .TagName("div")
                .ClientTemplateId("lessonTemplate")
                .HtmlAttributes(new { @style = "border:none" })
                .DataSource(dataSource => dataSource
                  .Ajax()
                  .Read("GetRecentLessons", "Qhse")))
            </div>
        </div>
    </div>
</div>
<hr />
<div class="row">
    <div class="col-md-12">
        <div class="row mb-3">
                <div class="col-md-8">
                    <span>Search</span>
                    @Html.Kendo().TextBox().Name("searchBox").HtmlAttributes(new { @class = "form-control", @style = "width: 385px", @data_bind = "value:query" })
                </div>
                <div class="col-md-4 d-flex">
                    <div class="d-flex align-items-center">
                        @Html.RadioButton("TitleText", "true", new { name = "lessonFilter", data_type = "boolean", @data_bind = "checked:titleOnly" })
                        <span class="ml-2">Title and Text</span>
                    </div>
                    <div class="d-flex ml-4 align-items-center">
                        @Html.RadioButton("TitleOnly", "false", new { name = "lessonFilter", data_type="boolean", @data_bind = "checked:titleOnly" })
                        <span class="ml-2">Title Only</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label>Category</label>
                    <br />
                    @(Html.Kendo().DropDownListFor(m => m.LessonCategoryId)
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .Filter(FilterType.Contains)
                        .OptionLabel("Select Category")
                        .HtmlAttributes(new { @data_bind = "value:lessonCategoryId" })
                        .DataSource(source => {
                                                source.Read(read => {
                                                     read.Action("GetLessonCategories", "Lookup");
                                                });
                                            }))
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label>Client</label>
                    <br />
                    @(Html.Kendo().DropDownListFor(m => m.CompanyId)
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .Filter(FilterType.Contains)
                        .OptionLabel("Select Client")
                        .HtmlAttributes(new { @data_bind = "value:clientId" })
                        .DataSource(d => d.Read("GetCompanies", "Lookup")))
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label>Related Equipment</label>
                    <br />
                    @(Html.Kendo().MultiSelectFor(m => m.EquipmentCategoryIds)
                    .DataTextField("Name")
                    .DataValueField("EquipmentCategoryId")
                    .Placeholder("Select Equipment")
                    .HtmlAttributes(new { @class = "form-control", @data_bind = "value:equipmentCategoryIds", @data_value_primitive = "true" })
                    .DataSource(source => {
                                                source.Read(read => {
                                                     read.Action("GetEquipmentCategoriesByEquipmentItem", "Lookup");
                                                }).ServerFiltering(true);
                                            }))
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label>Related Services</label>
                    <br />
                    @(Html.Kendo().MultiSelectFor(m => m.ObjectiveIds)
                     .DataTextField("Text")
                     .DataValueField("Value")
                     .Placeholder("Select Services ...")
                     .HtmlAttributes(new { @class = "form-control", @data_bind = "value:objectiveIds", @data_value_primitive="true" })
                     .DataSource(source => {
                                                source.Read(read => {
                                                     read.Action("GetObjectives", "Lookup");
                                                }).ServerFiltering(true);
                                            }))
                </div>
            </div>
        </div>
    <div class="d-flex actionsContainer justify-content-end">
            <button class="btn btn-primary btn-sm" data-bind="click:searchButtonClick"><i class="fa fa-search"></i>Search</button>
            <button class="btn btn-success btn-sm" data-bind="click:resetButtonClick"><i class="fa fa-refresh"></i>Reset Search</button>
    </div>
<hr />
<div>
@(Html.Kendo().Grid<LessonModel>()
    .Name("lessonGrid")
    .Columns(columns => {
           columns.Bound(c => c.Number).ClientTemplate("<a href='" + @Url.Action("EditLesson", "Qhse") + "/#=LessonId#'>#=Number#</a>").Title("Lesson ID").Width(100);
            columns.Bound(c => c.Name).Title("Title").Width(120);
            columns.Bound(c => c.Description).Title("Text").Hidden(true).Width(500);
            columns.Bound(c => c.LessonCategoryName).Title("Lesson Category").Width(100);
            columns.Bound(c => c.DivisionName).Title("Division").Hidden(true).Width(100);
            columns.Bound(c => c.EquipmentCategories).Title("Equipment Category").ClientTemplate("#=EquipmentCategories ? EquipmentCategories : 'N/A'#").Width(175);
            columns.Bound(c => c.CompanyName).Title("Client").ClientTemplate("#=CompanyName ? CompanyName : 'N/A'#").Width(100);
            columns.Bound(c => c.Objectives).Title("Service").ClientTemplate("#=Objectives ? Objectives : 'N/A'#").Width(100);
            columns.Bound(c => c.StatusDescription).Title("Status").Width(100);
    })
    .Sortable()
    .ToolBar(t => {
        t.Custom().Text("Reset Grid View").HtmlAttributes( new { @id="resetLessonGrid", @class="bg-danger text-white"});
        t.Excel().Text("Export");
    }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
    .Filterable()
    .Events(e => e.DataBound("updateLessonGrid").ColumnReorder("saveLessonGrid").ColumnResize("saveLessonGrid").ColumnShow("saveLessonGrid").ColumnHide("saveLessonGrid"))
    .Excel(excel => excel
           .FileName(string.Format("Centerpoint_Lessons_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
           .Filterable(true)
           .ProxyURL(Url.Action("Export", "Qhse")))
    .Groupable()
    .Scrollable(s => s.Height("auto"))
    .Resizable(c => c.Columns(true))
     .Reorderable(r => r.Columns(true))
    .ColumnMenu(c => c.Columns(true))
    .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Read(read => read.Action("GetLessons", "Qhse").Data("lessonData"))))
</div>

    <script type="text/x-kendo-tmpl" id="lessonTemplate">
    <a style="line-height: 3;"  href="@Url.Action("EditLesson", "Qhse")/#=LessonId#">  <span style="color:\\#6eb6b4"> <i class="fa fa-file-text">  </i>  #=Number# - #=Name# (#=kendo.toString(kendo.parseDate(CreatedDate, 'yyyy-MM-dd'),'dd-MMM-yyyy HH:mm')#)</span></a>
        <br />
    </script>

    <environment include="Development">
        <script src="~/js/views/qhse/lesson.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script src="~/js/views/qhse/lesson.min.js" asp-append-version="true"></script>
    </environment>