﻿@model DivisionModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-building"></i>
        Divisions
        (<span data-bind="text:totalDivisions"></span>)
    </h4>
</div>
<hr />

<div class="grid-container">
    @(Html.Kendo().Grid<DivisionModel>()
        .Name("divisionGrid")
        .Columns(c => {
            c.Bound(p => p.Name);
            c.Bound(p => p.SimplifiedMobilisation).EditorTemplateName("SimplifiedMobilisationLookup").Title("Simplified Mobilisation").ClientTemplate("#if(SimplifiedMobilisation){#Yes#}else{#No#}#");
            c.Command(command => { 
                command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
            }).Width(200);
        })
        .Editable(editable => editable.Mode(GridEditMode.InLine))
        .ToolBar(t => {
            t.Create().Text("Add Division");
            t.Excel().Text("Export");
        }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
        .Sortable()
        .Mobile(MobileMode.Auto)
        .Filterable()
        .Height("100%")
        .Scrollable()
        .Resizable(c => c.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .Events(e => e.DataBound("updateDivisionTotal"))
        .Excel(excel => excel
            .FileName(string.Format("Centerpoint_Divisions_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Admin"))
        )
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(m => m.Id(p => p.DivisionId))
            .Events(e => e.Error("onError"))
            .Read(read => read.Action("GetDivisions", "Admin"))
            .Create(create => create.Action("UpdateDivision", "Admin"))
            .Update(update => update.Action("UpdateDivision", "Admin"))
            .Destroy(destroy => destroy.Action("DeleteDivision", "Admin"))
        )
    )
</div>


    <script>
        function updateDivisionTotal() {
            var divisionGrid = $("#divisionGrid").data("kendoGrid");
            var totalDivisions = divisionGrid.dataSource.total();
            viewModel.set("totalDivisions", totalDivisions);
        }

        function onError(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var divisionGrid = $("#divisionGrid").data("kendoGrid");
                divisionGrid.dataSource.cancelChanges();
            }
        }

        var viewModel = new kendo.observable({
            totalDivisions: 0
        });

        kendo.bind(document.body.children, viewModel);
    </script>
