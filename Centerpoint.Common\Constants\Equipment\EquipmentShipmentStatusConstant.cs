﻿namespace Centerpoint.Common.Constants
{
    public static class EquipmentShipmentStatusConstant
    {

        public const string InTransit = "INT";
        public const string Pending = "PEN";
        public const string Received = "REC";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {InTransit,"In Transit"},
                    {Pending,"Pending"},
                    {Received,"Received"},
                };
            }
        }
    }
}
