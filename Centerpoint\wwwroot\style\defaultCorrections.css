ul{
    list-style: none;
}

a, a:hover{
    text-decoration: none;
}

.k-tabstrip-top 
.k-tabstrip-items-wrapper 
.k-item,
.k-tabstrip-top 
.k-tabstrip-items-wrapper 
.k-item.k-active,
.k-tabstrip-top 
.k-tabstrip-items-wrapper 
.k-item:hover,
.k-tabstrip-items-wrapper,
.k-tabstrip-content,
.k-tabstrip
.k-content{
    border: none;
}

.k-panelbar > .k-item > .k-link:hover,
.k-panelbar > .k-item > .k-link.k-state-hover,
.k-panelbar > .k-item > .k-link.k-hover,
.k-panelbar > .k-panelbar-header > .k-link:hover,
.k-panelbar > .k-panelbar-header > .k-link.k-state-hover,
.k-panelbar > .k-panelbar-header > .k-link.k-hover {
    background-color: var(--normalLightGrey);
}

.k-tabstrip-top
.k-tabstrip-items-wrapper
.k-item.k-state-active{
    background-color: var(--primary);
    color: #ffffff;
}
.k-tabstrip-top .k-tabstrip-items-wrapper .k-item:active, .k-tabstrip-top > .k-tabstrip-items-wrapper .k-item.k-active{
    background-color: var(--primary);
    color: #ffffff;
}

.k-tabstrip-top 
.k-tabstrip-items-wrapper
.k-item{
    margin-left: 7px!important;
    background-color: #E7E7E7;
    color: #A9B1C0;
}

.k-tabstrip-top 
.k-tabstrip-items-wrapper
ul > :first-child {
    margin-left: 0px!important;
  }

.k-tabstrip-content,
.k-tabstrip > .k-content {
    padding: 1rem 0;
}

.btn{
    display: inline-flex;
    align-items: center;
}

.btn i{
    margin-right: 0.3rem;
}

tbody .btn i{
    margin-right: 0!important;
}

.card{
    height: 100%;
}

.card-header {
    padding: 0.4rem 0.5rem;
}

.k-content {
    outline: none!important;
}

.k-slider .k-slider-selection{
    background-color: var(--primary);
}

.k-tabstrip-wrapper,
.k-grid{
    height: 100%;
}

.k-grid-excel{
    background-color: var(--success);
    color: #ffffff
}

.k-grid-excel:hover{
    background-color: #218838;
}

#projectLessonGrid {
    height: calc(100% - 50px );
}

.validation-summary-errors {
    background-color: #f2dede;
    border-color: #ebccd1;
    border-radius: 4px;
}

.validation-summary-errors li{
    color: #a94442;
    font-weight: 700;
}

.validation-summary-errors ul{
    list-style-type: disc;
    padding-top: 15px;
    padding-bottom: 15px;
}

.k-popup
.k-list
.k-list-optionlabel {
    gap: unset;
}

.k-treeview-leaf.k-selected{
    background-color: var(--accent);
}

.k-popup-edit-form.k-window-content .k-edit-form-container{
    width: auto;
}

td .badge {
    padding: 0.4em 0.4em;
    border-radius: 0.5rem;
    font-size: 11px;
}

.k-popup-edit-form > .k-actions, .k-popup-edit-form > .k-edit-buttons,
.k-popup-edit-form > .k-action-buttons,
.k-popup-edit-form > .k-columnmenu-actions, .k-popup-edit-form > .k-form-buttons,
.k-edit-form-container .k-actions,
.k-edit-form-container .k-edit-buttons,
.k-edit-form-container .k-action-buttons,
.k-edit-form-container .k-columnmenu-actions,
.k-edit-form-container .k-form-buttons {
    margin: 1rem -0.5rem -0.5rem;
}

.k-window-content,
.k-prompt-container {
    padding: 1.2rem 1.2rem;
    border-width: 0;
    border-color: inherit;
    color: inherit;
    background: none;
    outline: 0;
    overflow: auto;
    position: relative;
    -ms-flex: 1 1 auto;
        flex: 1 1 auto;
}

.k-tabstrip > .k-content.k-state-active {
    overflow-x: hidden;
}

.k-toolbar.k-grid-toolbar > .k-grid-add {
    color: #ffffff!important;
    background-color: #6eb6b4;
    border-color: #6eb6b4;
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

#return-to-top {
    margin-bottom: 50px;
    color: #fff;
    position: fixed;
    z-index: 2000;
    bottom: 20px;
    cursor: pointer;
    right: 3px;
    background: #6eb6b4;
    width: 50px;
    height: 50px;
    display: block;
    text-decoration: none;
    -webkit-border-radius: 35px;
    -moz-border-radius: 35px;
    border-radius: 35px;
    display: none;
    -webkit-transition: all 0.3s linear;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}
#return-to-top i {
    margin: 0;
    position: relative;
    left: 16px;
    top: 13px;
    font-size: 19px;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}
#return-to-top:hover {
    background: #6eb6b4;
}

#return-to-top:hover i {
    color: #fff;
    top: 5px;
}

.k-calendar-view div {
    display: flex!important;
}

#projectPanelBar .k-item {
    position: relative;
}

#projectPanelBar .k-loading-mask .k-loading-image::before{
    font-size: 30px;
}

#jobPanelBar .k-item {
    position: relative;
}

#jobPanelBar .k-loading-mask .k-loading-image::before{
    font-size: 30px;
}



/* Maintenance checkbox type ui */
.task-template-item {
    display: flex;
}

.task-template-item.hint:after {
    content: "";
    display: block;
    width: 0;
    height: 0;
    border-top: 8px solid transparent; 
    border-bottom: 8px solid transparent;
    border-right: 8px solid #6ca8a7;
    position: absolute;
    left: -20px;
    top: 30%;
}

.task-template-item.hint:last-child {
    border-radius: 4px;
}

.task-template-item.hint span {
    color: #fff;
}

.task-template-item.placeholder {
    color: #6ca8a7;
    text-align: right;
}
/* Maintenance checkbox type ui end*/

/* custom tab list */
#opportunityAttachmentWindowTab .nav-item .active {
  background-color: var(--primary);
  color: white;
}
#opportunityAttachmentWindowTab .nav-item {
    margin-right: 7px;
}
#opportunityAttachmentWindowTab .nav-item .nav-link:focus {
   outline: none!important;
}

#opportunityAttachmentWindowTab .nav-item .nav-link:not(.active) {
   color: #A9B1C0; 
}
.default-height {
    height : auto;
}
/* custom tab list end*/

#mainTabStrip .k-tabstrip-content,
.k-tabstrip > .k-content {
    overflow: unset;
}

.k-toolbar {
    gap: 0.5rem;
}

.k-window-actions {
    justify-content: flex-end !important;
    margin: -0.5rem -0.5rem 1rem;
}

.k-window {
    border-radius: 0.25rem;
}

.k-window-titlebar {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
}

.k-toolbar {
    flex-flow: unset;
}

.k-actions .k-button-solid-primary,
.k-actions .k-button-solid-base,
.k-command-cell .k-button-solid-primary{
    background-color: #6eb6b4;
    border-color: #6eb6b4;
    color: #ffffff !important;
}

.k-actions .k-button-solid-primary:hover,
.k-actions .k-button-solid-base:hover,
.k-command-cell .k-button-solid-primary:hover {
    color: #fff;
    background-color: #55a9a6;
    border-color: #51a09e;
}

.k-multiselect{
    height: auto;
}

.toolbar-inline-padding {
    padding-right: inherit;
}