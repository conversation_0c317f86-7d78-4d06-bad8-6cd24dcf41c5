@model EquipmentShipmentModel;

<!-- Access the settings -->
<form id="shipmentForm">
    <div id="shipmentDetails">
        @if (GlobalSettings.IsWellsense)
        {
            @Html.ValidationSummary(false)
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label> Description</label>
                        @(Html.Kendo().TextBoxFor(m => m.Description).HtmlAttributes(new { @required="required"}))
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label> Project From</label>
                        @(Html.Kendo().TextBoxFor(m => m.ProjectFrom).HtmlAttributes(new { @readonly="readonly" }))
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>@(!GlobalSettings.IsWellsense ? "Custom Status" : "Incoterm")</label>
                        @(Html.Kendo().TextBoxFor(m => m.CustomStatus))
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Creation Date</label>
                        @Html.Kendo().DatePickerFor(m => m.CreatedDate).HtmlAttributes(new { @data_bind = "value:createdDate" })
                    </div>
                    <div class="form-group">
                        <label>Shipment Created By</label>
                        @(Html.Kendo().DropDownList()
                        .Name("CreatedByUserId")
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .Filter("contains")
                        .Events(e=> e.Open("kendoDropdownOpen"))
                        .DataSource(d => d.Read("GetUsers", "Lookup").ServerFiltering(false))
                        .AutoBind(false)
                        )
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label> Shipped Date</label>
                            @Html.Kendo().DatePickerFor(m => m.SentDate).Enable(Html.IsGlobalAdmin() || Html.IsLogisticsAdmin())
                    </div>
                    <div class="form-group">
                        <label>Shipment Method</label>
                        <br />
                        @(Html.Kendo().DropDownList()
                            .Name("ShipmentMethodId")
                            .OptionLabel("Select Shipment Method")
                            .DataTextField("Text")
                            .DataValueField("Value")
                            .Filter("contains")
                            .Events(e=> e.Open("kendoDropdownOpen"))
                            .DataSource(d => d.Read("GetShipmentMethods", "Lookup").ServerFiltering(false))
                            .AutoBind(false)
                        )
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label> Received Date</label>
                        @(Html.Kendo().DatePickerFor(m => m.ReceivedDate).HtmlAttributes(new { @id = "receivedDate" })
                        .Enable(Html.IsGlobalAdmin() || Html.IsLogisticsAdmin()))
                    </div>
                </div>
            </div>
        }
            else
            {
                @Html.HiddenFor(m => m.Description)
                @Html.ValidationSummary(false)
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>@(!GlobalSettings.IsWellsense ? "Custom Status" : "Incoterm")</label>
                            @(Html.Kendo().TextBoxFor(m => m.CustomStatus))
                        </div>
                        <div class="form-group">
                            <label>Shipment Created By</label>
                            @(Html.Kendo().DropDownList()
                                .Name("CreatedByUserId")
                                .DataTextField("Text")
                                .DataValueField("Value")
                                .Filter("contains")
                                .Events(e => e.Open("kendoDropdownOpen"))
                                .DataSource(d => d.Read("GetUsers", "Lookup").ServerFiltering(false))
                                .AutoBind(false)
                                )
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Creation Date</label>
                            @Html.Kendo().DatePickerFor(m => m.CreatedDate).HtmlAttributes(new { @data_bind = "value:createdDate" })
                        </div>
                         <div class="form-group">
                            <label>Shipment Method</label>
                            <br />
                            @(Html.Kendo().DropDownList()
                                .Name("ShipmentMethodId")
                                .OptionLabel("Select Shipment Method")
                                .DataTextField("Text")
                                .DataValueField("Value")
                                .Filter("contains")
                                .Events(e => e.Open("kendoDropdownOpen"))
                                .DataSource(d => d.Read("GetShipmentMethods", "Lookup").ServerFiltering(false))
                                .AutoBind(false)
                                )
                        </div>
                    </div>
                    <div class="col-md-4">
                        @if(Model.FromProjectId!=null)
                        {
                        <div class="form-group">
                            <label> Received Date</label>
                                @(Html.Kendo().DatePickerFor(m => m.ReceivedDate).HtmlAttributes(new { @id = "receivedDate" }).Enable(
                                            Model.EquipmentShipmentStatus == EquipmentShipmentStatusConstant.Pending || Model.EquipmentShipmentStatus == EquipmentShipmentStatusConstant.InTransit || !Model.EquipmentShipmentId.HasValue))
                        </div>
                            <div class="form-group d-none" >
                                @Html.Kendo().DatePickerFor(m => m.SentDate).Value(DateTime.Today.ToString())
                            </div>
                        }else{
                        <div class="form-group">
                            <label> Shipped Date</label>
                            @Html.Kendo().DatePickerFor(m => m.SentDate).Enable(Html.IsGlobalAdmin() || Html.IsLogisticsAdmin())
                        </div>
                        }
                    </div>

                </div>
           
            }
        <div class="row">
            <div class="col-md-8">
                <div class="form-group">
                    <input type="checkbox" onchange="onCheckBoxChange(event)" data-bind="checked:isProjectRelated"/>
                    <input id='IsProjectRelated' type="text" name="IsProjectRelated" hidden data-bind="value: isProjectRelated"/>
                    <label>Make shipment items available to Project?</label>
                </div>
                <div class="form-group" id="projectIdContainer">
                    <label>Shipment Available to Project</label>
                    @(Html.Kendo().DropDownList()
                        .Name("ProjectId")
                        .DataValueField("Value")
                        .DataTextField("Text")
                        .OptionLabel("Select Project")
                        .HtmlAttributes(new { @data_bind = "enabled:isProjectRelated" })
                        .Filter("contains")
                        .Events(e =>
                        {
                            e.Change("onChange");
                            e.Open("kendoDropdownOpen");
                        })
                        .DataSource(d => d.Read("GetActiveOppurtunityProjects", "Lookup").ServerFiltering(false)).AutoBind(false)
                        )
                </div>
            </div>
        </div>
        <hr />
        <h4><b>From</b></h4>
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label>Company</label>
                    @(Html.Kendo().DropDownList()
                        .Name("FromCompanyId")
                        .OptionLabel("Select Company")
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .Events(x=>x.Change("cascadeDropdownFilterHelper"))
                        .Filter("contains")
                        .Events(e => e.Open("kendoDropdownOpen"))
                        .DataSource(d => d.Read("GetCompanies", "Lookup").ServerFiltering(false))
                        .HtmlAttributes(new { @data_cascade_to = "FromCompanyFieldId,FromCompanyLocationId" }).AutoBind(false)
                    )
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label>Company Location</label>
                    @(Html.Kendo().DropDownList()
                        .Name("FromCompanyLocationId")
                        .OptionLabel("Select Company Location")
                        .DataTextField("Name")
                        .DataValueField("CompanyLocationId")
                        .Filter("contains")
                        .Events(e => e.Change("fromCompanyLocationChanged").Open("kendoDropdownOpen"))
                        .DataSource(source => {
                            source.Read(read => {
                                read.Action("GetLocationsByFromCompanyId", "Lookup").Data("filterFromCompanyLocations");
                                }).ServerFiltering(false);
                        }).AutoBind(false)
                    )
                </div>
            </div>

            <div class="col-md-4">
                <div class="form-group">
                    <label>Company Asset</label>
                    @(Html.Kendo().DropDownList()
                        .Name("FromCompanyFieldId")
                        .OptionLabel("Select Company Asset")
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .Filter("contains")
                        .Events(e => e.Open("kendoDropdownOpen"))
                        .DataSource(source => {
                            source.Read(read => {
                                read.Action("GetAssetsByFromCompanyId", "Lookup").Data("filterFromCompanyAssets");
                                }).ServerFiltering(false);
                        }).AutoBind(false)
                    )
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-10">
                <div class="form-group">
                    <label>From</label>
                    @Html.TextAreaFor(p => p.FromAddress, new { @class = "form-control", @rows = "5"})
                </div>
            </div>
        </div>
        <h4> <b>To</b> </h4>
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label>Company</label>
                    @(Html.Kendo().DropDownList()
                        .Name("ToCompanyId")
                        .OptionLabel("Select Company")
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .Events(x => x.Change("cascadeDropdownFilterHelper").Open("kendoDropdownOpen"))
                            .DataSource(d => d.Read("GetCompanies", "Lookup").ServerFiltering(false))
                        .Filter("contains")
                            .HtmlAttributes(new { @data_bind = "value:toCompanyId", @data_cascade_to = "ToCompanyLocationId,ToCompanyFieldId" })
                    )
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label>Company Location</label>
                    @(Html.Kendo().DropDownList()
                        .Name("ToCompanyLocationId")
                        .OptionLabel("Select Company Location")
                        .DataTextField("Name")
                        .DataValueField("CompanyLocationId")
                        .Filter("contains")
                        .Events(e => e.Change("toCompanyLocationChanged").Open("kendoDropdownOpen"))
                        .DataSource(source => {
                            source.Read(read => {
                                read.Action("GetLocationsByToCompanyId", "Lookup").Data("filterToCompanyLocations");
                                }).ServerFiltering(false);
                        })
                            .HtmlAttributes(new{@data_bind = "value:toCompanyLocationId" })
                    )
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label>Company Asset</label>
                    @(Html.Kendo().DropDownList()
                        .Name("ToCompanyFieldId")
                        .OptionLabel("Select Company Asset")
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .Filter("contains")
                        .Events(e => e.Open("kendoDropdownOpen"))
                        .DataSource(source => {
                            source.Read(read => {
                                read.Action("GetAssetsByToCompanyId", "Lookup").Data("filterToCompanyAssets");
                                }).ServerFiltering(false);
                        }).AutoBind(false)
                    )
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-10">
                <div class="form-group">
                    <label>To</label>
                    @Html.TextAreaFor(p => p.ToAddress, new { @class = "form-control w-100", @style = "width:100%", @rows = "5", })
                </div>
            </div>    
        </div>
        @if (GlobalSettings.IsWellsense)
        {
            <div class="row">
                <div class="col-md-10">
                    <div class="form-group">
                        <label>Delivery Address</label>
                        @Html.TextAreaFor(p => p.DeliveryAddress, new { @class = "form-control w-100", @style = "width:100%", @rows = "5", })
                    </div>
                </div>
            </div>
        }
        <hr />
        <div class="row">
            <div class="col-md-10">
                <div class="form-group">
                    <label>Shipment Notes</label>
                    @Html.TextAreaFor(p => p.ShipmentNotes, new { @class = "form-control", @style = "width:100%", @rows = "5", })
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-10">
                <div class="form-group">
                    <label>Packing Notes</label>
                    <br />
                    @Html.TextAreaFor(p => p.PackingNotes, new { @class = "form-control", @style = "width:100%", @rows = "5", })
                </div>
            </div>
        </div>
        @if(GlobalSettings.IsRegiis){
        <div class="row">
            <div class="col-md-10">
                <div class="form-group">
                    <label>Notes</label>
                    <br />
                    @Html.TextAreaFor(p => p.Notes, new { @class = "form-control", @style = "width:100%", @rows = "5", })
                </div>
            </div>
        </div>
            
        }
        <br />
        <div>
            <div class="d-flex actionsContainer">
                @if (Html.IsLogisticsAdmin() || Html.IsGlobalAdmin()) {
                    <button type="button" class="btn btn-sm btn-primary" onclick="submitShipmentForm(event)">Save Shipment Details</button>
                }

                @if (Model.EquipmentShipmentId.HasValue && Model.EquipmentShipmentStatus == EquipmentShipmentStatusConstant.Pending && (Html.IsLogisticsAdmin() || Html.IsGlobalAdmin())) {
                    <a class="btn btn-danger btn-sm" data-bind="click:deleteShipment"><i class="fa fa-thumbs-down"></i>Delete Shipment</a>
                }
            </div>
            @Html.HiddenFor(m => m.EquipmentShipmentId)
            @Html.HiddenFor(m => m.FromProjectId)
        </div>
    </div>
</form>

<script>
    function kendoDropdownOpen(e) {
        if (!e.sender.requestFullfield) {
            e.sender.dataSource.read();
            e.sender.requestFullfield = true
        }
    }

    function shipmentFormDisabled() {
        if (editEquipmentShipmentModel.modelEquipmentShipmentStatus == editEquipmentShipmentModel.equipmentShipmentStatusConstantReceived && "@Html.IsGlobalAdmin()" === "False" && "@Html.IsLogisticsAdmin()" === "False") {
            $('#shipmentForm').find('input,button').attr('disabled', 'disabled');
        }
    }

    $(document).ready(function () {
        if (document.getElementById('EquipmentShipmentId').value) {
            $.ajax({
                url: `/Logistics/GetShipmentDropdownData`,
                data: { id: editEquipmentShipmentModel.modelEquipmentShipmentId },
                dataType: "json",
                method: 'GET',
                success: (e) => {
                    e.map(d => {
                        if (d.Item) {
                            if (d.Name == 'FromCompanyLocationId' || d.Name == 'ToCompanyLocationId') {
                                $(`#${d.Name} `).data("kendoDropDownList")?.dataSource.add({
                                    CompanyLocationId: d.Item.Value,
                                    Name: d.Item.Text
                                });
                            }
                            else {
                                $(`#${d.Name} `).data("kendoDropDownList")?.dataSource.add(d.Item);
                            }

                            $(`#${d.Name}`).data("kendoDropDownList")?.value(d.Item.Value);
                        }
                    })
                },
                error: (e) => {
                    kendo.alert(e)
                }
            });
        }
        else {
            $.ajax({
                url: `/Logistics/GetAddData`,
                dataType: "json",
                method: 'GET',
                success: (e) => {
                    e.map(d => {
                        if (d.Item) { 
                            $(`#${d.Name} `).data("kendoDropDownList")?.dataSource.add(d.Item);
                        } 
                    })
                },
            })
        }
        shipmentFormDisabled();
    });
</script>
