﻿@model QhseDashboardModel

<div class="header-container-between">
    <h4>
        <i class="fa fa-bars"></i>
        Service Improvement Form
    </h4>
    <div>
       <a class="btn btn-primary btn-sm" href="@Url.Action("AddServiceImprovement", "QHSE")"><i class="fa fa-plus"></i>Create New SIF</a>
    </div>
</div>
<hr />

<div class="row">
    <div class="col-md-5">
        <div class="row mb-2">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">SIF's</h6>
                    </div>
                    <div class="card-body">
                        <div class="card-list-item">
                            <span class="card-list-item-name">Total</span>
                            <a class="card-list-item-count" href="#serviceimprovementgrid" style="background: black" data-bind="click:totalClick" title="Click here to show All SIF's">@Model.TotalSif</a>
                        </div>
                        <div class="card-list-item">
                            <span class="card-list-item-name">Open</span>
                            <a class="card-list-item-count" href="#serviceimprovementgrid" style="background: #55ADD3" data-bind="click:openClick" title="Click here to show All SIF's Open">@Model.TotalOpen</a>
                        </div>
                        <div class="card-list-item">
                            <span class="card-list-item-name">On Target</span>
                            <a class="card-list-item-count" href="#serviceimprovementgrid" style="background: #999" data-bind="click:onTargetClick" title="Click here to show Total on target">@Model.TotalOnTarget</a>
                        </div>
                        <div class="card-list-item">
                            <span class="card-list-item-name">Overdue</span>
                            <a class="card-list-item-count" href="#serviceimprovementgrid" style="background: #FF0080" data-bind="click:overdueClick" title="Click here to show Total overdue">@Model.TotalOverdue</a>
                        </div>
                        <div class="card-list-item">
                            <span class="card-list-item-name">Closed</span>
                            <a class="card-list-item-count" href="#serviceimprovementgrid" style="background: #BA141A" data-bind="click:closedClick" title="Click here to show Total closed">@Model.TotalClosed</a>
                        </div>                        
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Category</h6>
                    </div>
                    <div class="card-body">
                        <div class="card-list-item">
                            <span class="card-list-item-name">Quality</span>
                            <a class="card-list-item-count" href="#serviceimprovementgrid" style="background: #7CBB00" data-bind="click:qualityClick" title="Click here to show Total Quality">@Model.TotalQuality</a>
                        </div>
                        <div class="card-list-item">
                            <span class="card-list-item-name">Health</span>
                            <a class="card-list-item-count" href="#serviceimprovementgrid" style="background: #7CBB00" data-bind="click:healthClick" title="Click here to show Total Health">@Model.TotalHealth</a>
                        </div>
                        <div class="card-list-item">
                            <span class="card-list-item-name">Safety</span>
                            <a class="card-list-item-count" href="#serviceimprovementgrid" style="background: #7CBB00" data-bind="click:safetyClick" title="Click here to show Total Safety">@Model.TotalSafety</a>
                        </div>    
                        <div class="card-list-item">
                            <span class="card-list-item-name">Environment</span>
                            <a class="card-list-item-count" href="#serviceimprovementgrid" style="background: #7CBB00" data-bind="click:environmentClick" title="Click here to show Total Environment">@Model.TotalEnvironment</a>
                        </div>  
                        <div class="card-list-item">
                            <span class="card-list-item-name">Security</span>
                            <a class="card-list-item-count" href="#serviceimprovementgrid" style="background: #7CBB00" data-bind="click:securityClick" title="Click here to show Total Security">@Model.TotalSecurity</a>
                        </div>                     
                        <div class="card-list-item">
                            <span class="card-list-item-name">Improvement</span>
                            <a class="card-list-item-count" href="#serviceimprovementgrid" style="background: #7CBB00" data-bind="click:improvementClick" title="Click here to show Total Improvement">@Model.TotalImprovement</a>
                        </div>   
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
           <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Action Pending</h6>
                    </div>
                    <div class="card-body">
                        <div class="card-list-item">
                            <span class="card-list-item-name">By You</span>
                            <a class="card-list-item-count" href="#serviceimprovementgrid" style="background: #68217A" data-bind="click:userClick" title="Click here to show Total by you">@Model.TotalByUser</a>
                        </div>
                    </div>
                </div>
           </div>
           <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Monthly Summary</h6>
                    </div>
                    <div class="card-body">
                        <div class="card-list-item">
                            <div>
                                @(Html.Kendo().DropDownListFor(m => m.Year)
                                    .DataTextField("Value")
                                    .DataValueField("Key")
                                    .Filter(FilterType.Contains)
                                    .DataSource(d => d.Read("GetYears", "Lookup", new { @years = 10 }))
                                    .Events(e => e.Change("yearChanged"))
                                    .HtmlAttributes(new { @data_bind = "value:year", @style = "width:100px; font-size: 14px;" }))
                            </div>
                            <div>
                                @(Html.Kendo().DropDownListFor(m => m.Month)
                                    .DataValueField("Key")
                                    .DataTextField("Value")
                                    .Filter(FilterType.Contains)
                                    .DataSource(d => d.Read("GetMonths", "Lookup"))
                                    .Events(e => e.Change("monthChanged"))
                                    .HtmlAttributes(new { @data_bind = "value:month", @style = "width:100px; font-size: 14px;" }))
                            </div>
                        </div>
                        <div class="card-list-item">
                            <span class="card-list-item-name">Created</span>
                            <a class="card-list-item-count" href="#serviceimprovementgrid" style="background: #16185F" data-bind="text:totalCreatedMonth, click:createdMonthClick" title="Click here to show Total Created">@Model.TotalByUser</a>
                        </div>
                        <div class="card-list-item">
                            <span class="card-list-item-name">Closed</span>
                            <a class="card-list-item-count" href="#serviceimprovementgrid" style="background: #C78A5D" data-bind="text:totalClosedMonth, click:closedMonthClick" title="Click here to show Total Closed">@Model.TotalByUser</a>
                        </div>
                    </div>
                </div>
           </div>
        </div>
    </div>
    <div class="col-md-7">
        <div class="card">
            <div class="card-body">
                @(Html.Kendo().Chart<ChartModel>()
                    .Name("sifStatusChart")
                    .Series(c =>{ c.Bar(p => p.Count).ColorField("Colour");})
                    .ValueAxis(v => v.Numeric().Title("SIFs").MajorUnit(1).MajorGridLines(l => l.Visible(false)).MinorGridLines(l => l.Visible(false)))
                    .Legend(l => l.Visible(false))
                    .AutoBind(true)
                    .CategoryAxis(c => c.Categories(p => p.Category))
                    .Tooltip(t => t.Visible(true).Template("#=value#"))
                    .HtmlAttributes(new { @style = "height:300px; border:none" })
                    .DataSource(dataSource => dataSource.Read(read => read.Action("GetSifStatusChartData", "Qhse").Data("sifStatusChartData"))))
                    <div class="d-flex w-100 justify-content-between toolbar-inline-padding">
                        <div class="form-group">
                            <label>SIF's Created From</label>
                            <br />
                            @(Html.Kendo().DatePickerFor(m => m.FromDate).Events(e => e.Change("refreshSifStatusChart")).HtmlAttributes(new { @data_bind = "value:fromDate", @style = "width:120px; font-size: 14px" }))
                        </div>
                        <div class="form-group">
                            <label>SIF's Created To</label>
                            <br />
                            @(Html.Kendo().DatePickerFor(m => m.ToDate).Events(e => e.Change("refreshSifStatusChart")).HtmlAttributes(new { @data_bind = "value:toDate", @style = "width:120px; font-size: 14px" }))
                        </div>
                    </div>
            </div>   
        </div>
    </div>
</div>
<hr />
<div>
    @(Html.Kendo().Grid<ServiceImprovementModel>()
    .Name("serviceImprovementGrid")
    .Columns(columns => {
        columns.Bound(c => c.Name).Title("SIF ID").ClientTemplate("<a href='" + @Url.Action("EditServiceImprovement", "Qhse") + "/#=ServiceImprovementId#'>#=Name#</a>");
        columns.Bound(c => c.CreatedDate).Title("Created Date").Format(DateConstants.DateFormat);
        columns.Bound(c => c.CreatedByUserName).Title("Raised By").ClientTemplate("#=CreatedByUserName#").Hidden(true);
        columns.Bound(c => c.BaseCompanyLocationName).Title("Base").Hidden(true);
        columns.Bound(c => c.SifTitle).Title("Title").Hidden(true);
        columns.Bound(c => c.CorrectiveActionTargetDate).Title("Corrective Action Due Date").Format(DateConstants.DateFormat);
        columns.Bound(c => c.CorrectiveActionUserId).Title("Corrective Action Party").ClientTemplate("#=CorrectiveActionUserName == null ? '': CorrectiveActionUserName #").Hidden(true); ;
        columns.Bound(c => c.InvestigatorUserId).Title("Investigation Action Party").ClientTemplate("#=InvestigatorUserName == null ? '': InvestigatorUserName #").Hidden(true);
        columns.Bound(c => c.InvestigationTargetDate).Title("Investigation Action Due Date").Format(DateConstants.DateFormat);
        columns.Bound(c => c.PreventiveActionUserId).Title("Preventive Action Party").ClientTemplate("#=PreventiveActionUserName == null ? '' :PreventiveActionUserName #").Hidden(true);
        columns.Bound(c => c.PreventiveActionTargetDate).Title("Preventive Action Due Date").Format(DateConstants.DateFormat);
        columns.Bound(c => c.SignOffBy).Title("Sign-off By").Hidden(true);
        columns.Bound(c => c.SignOffDate).Title("Sign-off Date").Format(DateConstants.DateTimeFormat);
        columns.Bound(c => c.ServiceImprovementStatusDescription).Title("Status");
        columns.Bound(c => c.SifCategoryDescription).Title("Category");
        columns.Bound(c => c.SubCategories).Title("Sub-Category(s)").Hidden(true);

    })
    .Sortable()
    .Filterable()
    .ToolBar(t => {
        t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetServiceImprovementGrid", @class="bg-danger text-white"});
        t.Excel().Text("Export");
    }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
    .Groupable()
    .Scrollable(s => s.Height("auto"))
    .Mobile(MobileMode.Auto)
    .ColumnMenu(c => c.Columns(true))
    .Resizable(r => r.Columns(true))
    .Reorderable(r => r.Columns(true))
    .Events(e => e.DataBound("updateServiceImprovementGrid").ColumnReorder("saveSifGrid").ColumnResize("saveSifGrid").ColumnShow("saveSifGrid").ColumnHide("saveSifGrid"))
    .Excel(excel => excel
        .FileName(string.Format("Centerpoint_Service_Improvement_Form_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
        .Filterable(true)
        .ProxyURL(Url.Action("Export", "Qhse")))
    .DataSource(dataSource => dataSource
    .Ajax()
    .ServerOperation(false)
    .Model(model => {
        model.Id(m => m.ServiceImprovementId);
    })
    .Read(read => read.Action("GetServiceImprovements", "Qhse").Data("sifData"))))
</div>

<script>
    const qhseModel = {
      modelFromDate:"@Model.FromDate",
      modelToDate:"@Model.ToDate",
      modelMonth: @Model.Month,
      modelYear: @Model.Year,
    }
</script>

<environment include="Development">
    <script src="~/js/views/qhse/qhse.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/qhse/qhse.min.js" asp-append-version="true"></script>
</environment>