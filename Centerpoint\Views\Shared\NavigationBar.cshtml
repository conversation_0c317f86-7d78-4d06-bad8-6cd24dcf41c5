<header>
    <nav>
        <div class="logo">
            <a href="/home/<USER>">
                    <img src="/img/logo-nav.svg" alt="centerpoint logo">
            </a>
        </div>
        <ul class="main-list">
            <li class="nav-item nav-item-without-hover mb-4">
                <a href="/home/<USER>/?tab=personnel">
                    <i class="fa fa-user"></i>
                    <span class="ml-1">@Html.AccountUserFullName()</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="/home/<USER>/">
                    <svg width="14" height="16" viewBox="0 0 14 16" xmlns="http://www.w3.org/2000/svg"
                        class="nav-item-icon">
                        <path
                            d="M12.7501 4.51795L8.25007 0.572949C7.83755 0.203983 7.30352 0 6.75007 0C6.19662 0 5.66259 0.203983 5.25007 0.572949L0.750071 4.51795C0.511847 4.731 0.321746 4.99237 0.192425 5.28463C0.0631039 5.5769 -0.******** 5.89336 7.10956e-05 6.21295V12.7679C7.10956e-05 13.3647 0.237124 13.937 0.659081 14.3589C1.08104 14.7809 1.65333 15.0179 2.25007 15.0179H11.2501C11.8468 15.0179 12.4191 14.7809 12.8411 14.3589C13.263 13.937 13.5001 13.3647 13.5001 12.7679V6.20545C13.5015 5.88712 13.4355 5.5721 13.3062 5.28121C13.1769 4.99032 12.9873 4.73017 12.7501 4.51795ZM8.25007 13.5179H5.25007V9.76795C5.25007 9.56904 5.32909 9.37827 5.46974 9.23762C5.61039 9.09697 5.80116 9.01795 6.00007 9.01795H7.50007C7.69898 9.01795 7.88975 9.09697 8.0304 9.23762C8.17105 9.37827 8.25007 9.56904 8.25007 9.76795V13.5179ZM12.0001 12.7679C12.0001 12.9669 11.9211 13.1576 11.7804 13.2983C11.6397 13.4389 11.449 13.5179 11.2501 13.5179H9.75007V9.76795C9.75007 9.17121 9.51302 8.59892 9.09106 8.17696C8.6691 7.755 8.09681 7.51795 7.50007 7.51795H6.00007C5.40333 7.51795 4.83104 7.755 4.40908 8.17696C3.98712 8.59892 3.75007 9.17121 3.75007 9.76795V13.5179H2.25007C2.05116 13.5179 1.86039 13.4389 1.71974 13.2983C1.57909 13.1576 1.50007 12.9669 1.50007 12.7679V6.20545C1.50021 6.09896 1.52302 5.99372 1.56698 5.89673C1.61095 5.79975 1.67507 5.71323 1.75507 5.64295L6.25507 1.70545C6.39194 1.58521 6.56789 1.5189 6.75007 1.5189C6.93225 1.5189 7.1082 1.58521 7.24507 1.70545L11.7451 5.64295C11.8251 5.71323 11.8892 5.79975 11.9332 5.89673C11.9771 5.99372 11.9999 6.09896 12.0001 6.20545V12.7679Z"
                            fill="white" />
                    </svg>
                    <span>Home</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="collapse-action" data-bs-toggle="collapse"  href="#businessDevelopmentCollapse" role="button" aria-expanded="false" aria-controls="businessDevelopmentCollapse">
                    <svg width="10" height="15" viewBox="0 0 10 15" fill="none" xmlns="http://www.w3.org/2000/svg"
                        class="nav-item-icon">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M6.28058 13.076V13.675C6.28058 14.0351 6.12965 14.3529 5.89398 14.6124C5.65302 14.8719 5.29819 14.9831 4.95661 14.999C4.62561 15.0149 4.24961 14.8428 4.01923 14.6124C3.78092 14.3714 3.63263 14.0139 3.63263 13.675V13.0792H2.73494C2.37482 13.0792 2.05706 12.9309 1.79756 12.6926C1.53807 12.4517 1.42685 12.0968 1.41096 11.7552C1.39508 11.4242 1.56719 11.0482 1.79756 10.8179C2.03853 10.5796 2.396 10.4313 2.73494 10.4313H4.91166C4.92666 10.4302 4.94165 10.4293 4.95661 10.4286C5.0138 10.4259 5.07234 10.4288 5.13124 10.4366H5.61327C5.66225 10.4366 5.71155 10.437 5.76103 10.4374L5.76127 10.4374C5.89871 10.4386 6.03762 10.4398 6.1753 10.432C6.2411 10.4174 6.30614 10.3998 6.37042 10.3791C6.43553 10.3452 6.49857 10.3083 6.56002 10.2683C6.60975 10.2231 6.6575 10.1758 6.70303 10.1261C6.74317 10.0644 6.78015 10.001 6.81414 9.93608C6.83572 9.86924 6.85431 9.80159 6.86975 9.73312C6.87323 9.66123 6.87324 9.58934 6.86978 9.51745C6.85438 9.45004 6.83575 9.38339 6.81419 9.31752C6.78145 9.2547 6.74591 9.19357 6.70725 9.13412C6.66092 9.08327 6.61228 9.03486 6.56145 8.9885C6.5035 8.95111 6.44394 8.91623 6.38279 8.88402C6.31377 8.86177 6.24389 8.84277 6.17318 8.82685C6.03942 8.8197 5.90497 8.82122 5.77134 8.82272C5.71582 8.82334 5.66045 8.82397 5.60533 8.82397H4.95658C4.91114 8.82397 4.8663 8.82153 4.8221 8.8168C4.73395 8.8178 4.64535 8.81961 4.55655 8.82143C4.25427 8.82762 3.9495 8.83386 3.65113 8.80808C3.07652 8.75777 2.52045 8.61743 2.03058 8.30497C1.5566 8.00575 1.15941 7.60856 0.868136 7.12663C0.237925 6.0754 0.240572 4.73289 0.833712 3.66841C1.40053 2.65214 2.48301 2.00084 3.63263 1.92322V1.32499C3.63263 0.964869 3.78092 0.647116 4.01923 0.387617C4.2602 0.128118 4.61502 0.0169047 4.95661 0.00101698C5.2876 -0.0148707 5.66361 0.157246 5.89398 0.387617C6.1323 0.62858 6.28058 0.986053 6.28058 1.32499V1.92342H7.17821C7.53833 1.92342 7.85608 2.0717 8.11558 2.31002C8.37508 2.55098 8.48629 2.90581 8.50218 3.24739C8.51807 3.57838 8.34595 3.95439 8.11558 4.18476C7.87462 4.42308 7.51715 4.57136 7.17821 4.57136H4.95677C4.95672 4.57137 4.95666 4.57137 4.95661 4.57137C4.89937 4.57412 4.84079 4.57124 4.78185 4.56342H4.30252C4.24741 4.56342 4.19206 4.5628 4.13656 4.56217H4.13652C4.00063 4.56065 3.86389 4.55911 3.7279 4.56667C3.6595 4.5822 3.59188 4.60105 3.52506 4.62293C3.46671 4.65334 3.40981 4.68616 3.35436 4.72167C3.30017 4.77132 3.24851 4.82333 3.19908 4.87771C3.16154 4.93586 3.12653 4.99561 3.09421 5.05697C3.0723 5.12494 3.05354 5.19374 3.03777 5.26335C3.03457 5.33411 3.03473 5.40486 3.03825 5.47561C3.053 5.54209 3.0707 5.60781 3.09164 5.67275C3.12553 5.73786 3.16241 5.80089 3.20246 5.86235C3.2476 5.91207 3.29496 5.95983 3.34466 6.00535C3.40635 6.0455 3.46968 6.08248 3.53464 6.11646C3.60307 6.13856 3.67237 6.15752 3.74252 6.17318C3.87188 6.18027 4.0021 6.17877 4.13208 6.17727H4.13209C4.18632 6.17664 4.24051 6.17602 4.29458 6.17602H4.95656C5.00132 6.17602 5.04549 6.17839 5.08902 6.18297C5.17615 6.1818 5.26367 6.17976 5.35141 6.1777C5.66245 6.17043 5.97606 6.1631 6.2832 6.19456C6.60625 6.22633 6.92401 6.2687 7.22587 6.3905C7.5754 6.53084 7.85873 6.67648 8.1553 6.9042C8.45452 7.13722 8.67165 7.37289 8.89937 7.68005C9.08473 7.92896 9.20389 8.20699 9.31775 8.49562C9.55077 9.08876 9.54547 9.73486 9.43691 10.3518C9.34952 10.8576 9.10062 11.3263 8.79081 11.7288C8.18147 12.5182 7.27336 13.0065 6.28058 13.076Z"
                            fill="white" />
                    </svg>
                    <span>Business Development</span>
                    <i class="fa fa-caret-left"></i>
                    <i class="fa fa-caret-down"></i>
                </a>
                <div id="businessDevelopmentCollapse" class="collapse">
                    <ul>
                        <li class="nav-item">
                            <a href="/Sales">
                                <i class="fa fa-bar-chart"></i> 
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href='/Sales/SalesForecast?year=@DateTime.Now.Year'>
                                <i class="fa fa-area-chart"></i>
                                Forecast
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/Sales/Matrix">
                            <i class="fa fa-bar-chart"></i>
                                Matrix
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/Sales/SalesHistory">
                                <i class="fa fa-clock"></i>
                                History
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <li class="nav-item">
                <a class="collapse-action" data-bs-toggle="collapse" data-bs-toggle="collapse" href="#operationsCollapse" role="button" aria-expanded="false" aria-controls="operationsCollapse">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"
                        class="nav-item-icon">
                        <path
                            d="M14.0625 3.15131C13.3255 2.21523 12.3858 1.45856 11.314 0.938291C10.2422 0.418019 9.06639 0.147705 7.87501 0.147705C6.68364 0.147705 5.50779 0.418019 4.43602 0.938291C3.36424 1.45856 2.42449 2.21523 1.68751 3.15131C0.591739 4.5296 -0.00327415 6.23927 1.35517e-05 8.00006C1.35517e-05 8.22131 1.35517e-05 8.44256 1.35517e-05 8.66381C0.135028 10.1953 0.722424 11.652 1.68751 12.8488C2.42407 13.7911 3.36558 14.5532 4.4406 15.0774C5.51561 15.6015 6.6959 15.8739 7.89189 15.8739C9.08788 15.8739 10.2682 15.6015 11.3432 15.0774C12.4182 14.5532 13.3597 13.7911 14.0963 12.8488C15.1799 11.4661 15.7629 9.75682 15.75 8.00006C15.7504 7.56099 15.7147 7.12264 15.6431 6.68944C15.4246 5.39519 14.8807 4.17771 14.0625 3.15131ZM1.12501 7.43756C1.12501 7.37569 1.12501 7.31944 1.12501 7.25756C1.12501 7.19569 1.12501 7.01569 1.17001 6.89194C1.21501 6.76819 1.17001 6.75131 1.20939 6.68381C1.24876 6.61631 1.24876 6.47006 1.27689 6.36319C1.30501 6.25631 1.31626 6.22256 1.33876 6.14944C1.36126 6.07631 1.39501 5.94694 1.42876 5.84569C1.46251 5.74444 1.47939 5.70506 1.50751 5.63194C1.53564 5.55881 1.58064 5.43506 1.62001 5.33944L1.71564 5.13131C1.76064 5.03569 1.80564 4.94569 1.85064 4.85006L1.96314 4.64756L2.12626 4.37756L2.25564 4.18069L2.40189 4.06256H5.06251C5.2117 4.06256 5.35477 4.12183 5.46026 4.22731C5.56575 4.3328 5.62501 4.47588 5.62501 4.62506C5.62397 4.69988 5.60801 4.77375 5.57807 4.84232C5.54813 4.9109 5.50481 4.97281 5.45064 5.02444C5.34726 5.127 5.20813 5.18548 5.06251 5.18756C4.61496 5.18756 4.18574 5.36535 3.86927 5.68182C3.5528 5.99829 3.37501 6.42751 3.37501 6.87506C3.37501 7.32262 3.5528 7.75184 3.86927 8.06831C4.18574 8.38477 4.61496 8.56256 5.06251 8.56256C5.20813 8.56465 5.34726 8.62312 5.45064 8.72569C5.50481 8.77731 5.54813 8.83923 5.57807 8.9078C5.60801 8.97638 5.62397 9.05024 5.62501 9.12506C5.62397 9.19988 5.60801 9.27375 5.57807 9.34232C5.54813 9.4109 5.50481 9.47281 5.45064 9.52444C5.34726 9.627 5.20813 9.68548 5.06251 9.68756C4.61496 9.68756 4.18574 9.86535 3.86927 10.1818C3.5528 10.4983 3.37501 10.9275 3.37501 11.3751C3.37501 11.5242 3.31575 11.6673 3.21026 11.7728C3.10477 11.8783 2.9617 11.9376 2.81251 11.9376H2.40189L2.30626 11.8082L2.16564 11.6001L2.01376 11.3469L1.89564 11.1332C1.85064 11.0432 1.80564 10.9588 1.76626 10.8688L1.68751 10.6438C1.64814 10.5538 1.61439 10.4582 1.57501 10.3626C1.53564 10.2669 1.51876 10.2163 1.49064 10.1376C1.46251 10.0588 1.42876 9.94069 1.40064 9.83944C1.37251 9.73819 1.35564 9.69319 1.33876 9.62006C1.32189 9.54694 1.29376 9.40631 1.27126 9.29944C1.24876 9.19256 1.24314 9.15881 1.23189 9.08569C1.22064 9.01256 1.19814 8.84944 1.18689 8.72569C1.17564 8.60194 1.18689 8.60194 1.18689 8.54006C1.18689 8.36006 1.18689 8.17444 1.18689 7.97756C1.18689 7.78069 1.12501 7.62881 1.12501 7.43756ZM3.30751 12.9613C3.64836 12.8568 3.94735 12.647 4.16158 12.362C4.37582 12.077 4.49429 11.7315 4.50001 11.3751C4.50105 11.3002 4.51701 11.2264 4.54695 11.1578C4.5769 11.0892 4.62022 11.0273 4.67439 10.9757C4.77777 10.8731 4.9169 10.8146 5.06251 10.8126C5.51007 10.8126 5.93929 10.6348 6.25576 10.3183C6.57222 10.0018 6.75001 9.57262 6.75001 9.12506C6.75001 8.67751 6.57222 8.24829 6.25576 7.93182C5.93929 7.61535 5.51007 7.43756 5.06251 7.43756C4.98769 7.43652 4.91383 7.42056 4.84525 7.39062C4.77668 7.36068 4.71476 7.31736 4.66314 7.26319C4.56057 7.15981 4.5021 7.02068 4.50001 6.87506C4.50105 6.80024 4.51701 6.72638 4.54695 6.6578C4.5769 6.58923 4.62022 6.52731 4.67439 6.47569C4.77777 6.37312 4.9169 6.31465 5.06251 6.31256C5.51007 6.31256 5.93929 6.13477 6.25576 5.81831C6.57222 5.50184 6.75001 5.07262 6.75001 4.62506C6.75001 4.17751 6.57222 3.74829 6.25576 3.43182C5.93929 3.11535 5.51007 2.93756 5.06251 2.93756H3.42564C4.67031 1.83477 6.28011 1.23339 7.94296 1.25002C9.60582 1.26665 11.2033 1.9001 12.4256 3.02756C12.0861 3.13563 11.7895 3.34839 11.5783 3.63536C11.3672 3.92233 11.2522 4.26877 11.25 4.62506C11.249 4.69988 11.233 4.77375 11.2031 4.84232C11.1731 4.9109 11.1298 4.97281 11.0756 5.02444C10.9723 5.127 10.8331 5.18548 10.6875 5.18756C10.24 5.18756 9.81074 5.36535 9.49427 5.68182C9.1778 5.99829 9.00001 6.42751 9.00001 6.87506C9.00001 7.32262 9.1778 7.75184 9.49427 8.06831C9.81074 8.38477 10.24 8.56256 10.6875 8.56256C10.7623 8.5636 10.8362 8.57956 10.9048 8.6095C10.9734 8.63945 11.0353 8.68277 11.0869 8.73694C11.1895 8.84032 11.2479 8.97945 11.25 9.12506C11.249 9.19988 11.233 9.27375 11.2031 9.34232C11.1731 9.4109 11.1298 9.47281 11.0756 9.52444C10.9723 9.627 10.8331 9.68548 10.6875 9.68756C10.24 9.68756 9.81074 9.86535 9.49427 10.1818C9.1778 10.4983 9.00001 10.9275 9.00001 11.3751C9.00001 11.8226 9.1778 12.2518 9.49427 12.5683C9.81074 12.8848 10.24 13.0626 10.6875 13.0626H12.3244C11.0797 14.1654 9.46992 14.7667 7.80706 14.7501C6.14421 14.7335 4.54676 14.1 3.32439 12.9726L3.30751 12.9613ZM14.625 8.56256C14.625 8.62444 14.625 8.68069 14.625 8.74256C14.625 8.80444 14.625 8.98444 14.58 9.10256C14.535 9.22069 14.58 9.24319 14.5406 9.31069C14.5013 9.37819 14.5013 9.52444 14.4731 9.63131C14.445 9.73819 14.4338 9.77756 14.4113 9.84506C14.3888 9.91256 14.355 10.0476 14.3213 10.1488C14.2875 10.2501 14.2706 10.2894 14.2425 10.3626C14.2144 10.4357 14.1694 10.5594 14.13 10.6494L14.0344 10.8576C13.9894 10.9532 13.9444 11.0432 13.8994 11.1388L13.7869 11.3413L13.6238 11.6113L13.4944 11.8082L13.3931 11.9432H10.6875C10.6127 11.9421 10.5388 11.9262 10.4703 11.8962C10.4017 11.8663 10.3398 11.823 10.2881 11.7688C10.1842 11.664 10.1256 11.5226 10.125 11.3751C10.1261 11.3002 10.142 11.2264 10.172 11.1578C10.2019 11.0892 10.2452 11.0273 10.2994 10.9757C10.4028 10.8731 10.5419 10.8146 10.6875 10.8126C11.1351 10.8126 11.5643 10.6348 11.8808 10.3183C12.1972 10.0018 12.375 9.57262 12.375 9.12506C12.375 8.67751 12.1972 8.24829 11.8808 7.93182C11.5643 7.61535 11.1351 7.43756 10.6875 7.43756C10.6127 7.43652 10.5388 7.42056 10.4703 7.39062C10.4017 7.36068 10.3398 7.31736 10.2881 7.26319C10.1856 7.15981 10.1271 7.02068 10.125 6.87506C10.1261 6.80024 10.142 6.72638 10.172 6.6578C10.2019 6.58923 10.2452 6.52731 10.2994 6.47569C10.4028 6.37312 10.5419 6.31465 10.6875 6.31256C11.1351 6.31256 11.5643 6.13477 11.8808 5.81831C12.1972 5.50184 12.375 5.07262 12.375 4.62506C12.375 4.47588 12.4343 4.3328 12.5398 4.22731C12.6453 4.12183 12.7883 4.06256 12.9375 4.06256H13.3481L13.4438 4.19194L13.5844 4.40006L13.7363 4.65319L13.8544 4.86694C13.8994 4.95694 13.9444 5.04694 13.9838 5.13694L14.085 5.35631C14.1244 5.44631 14.1581 5.54194 14.1975 5.63756C14.2369 5.73319 14.2538 5.78381 14.2819 5.86256C14.31 5.94131 14.3438 6.05944 14.3719 6.16069C14.4 6.26194 14.4169 6.30694 14.4338 6.38006C14.4506 6.45319 14.4788 6.59381 14.5013 6.70069C14.5238 6.80756 14.5294 6.84131 14.5406 6.91444C14.5519 6.98756 14.5744 7.15631 14.5856 7.27444C14.5969 7.39256 14.5856 7.39256 14.5856 7.45444C14.5856 7.63444 14.5856 7.82006 14.5856 8.01694C14.5856 8.21381 14.625 8.37131 14.625 8.56256Z"
                            fill="white" />
                    </svg>
                    <span>Operations</span>
                    <i class="fa fa-caret-left"></i>
                    <i class="fa fa-caret-down"></i>
                </a>
                <div id="operationsCollapse" class="collapse">
                    <ul>
                        <li class="nav-item">
                            <a href="/Operation">
                                <i class="fa fa-bar-chart"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/Operation/EmployeeOnSite">
                                <i class="fa fa-users"></i>
                                Employees On-Site (<span id="employeeOnSiteCountWrapper"></span>)
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <li class="nav-item">
                <a class="collapse-action" data-bs-toggle="collapse" data-bs-toggle="collapse" href="#logisticsCollapse" role="button" aria-expanded="false" aria-controls="logisticsCollapse">
                    <svg width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg"
                        class="nav-item-icon">
                        <path
                            d="M5.18528 15.4701C5.06192 15.4703 4.93975 15.446 4.82582 15.3987C4.71189 15.3514 4.60847 15.282 4.52152 15.1945L4.45402 15.1157L2.85652 12.7813C2.81069 12.7171 2.7535 12.6618 2.68777 12.6182L0.274649 10.9307C0.0987802 10.7546 0 10.5159 0 10.267C0 10.0181 0.0987802 9.77934 0.274649 9.60321L1.02277 8.85508C1.14764 8.73026 1.30535 8.64339 1.47759 8.60456C1.64983 8.56573 1.82955 8.57653 1.9959 8.63571L3.31777 8.93383L5.56777 6.68384L2.48527 3.61259C2.31757 3.44492 2.18455 3.24585 2.09379 3.02676C2.00303 2.80768 1.95631 2.57286 1.95631 2.33571C1.95631 2.09857 2.00303 1.86375 2.09379 1.64466C2.18455 1.42557 2.31757 1.22651 2.48527 1.05884C2.77766 0.769966 3.15935 0.588766 3.56802 0.544835C3.97669 0.500903 4.38817 0.596836 4.73527 0.816964L8.8134 3.45509L10.6809 1.59321C10.8334 1.44357 11.0312 1.34863 11.2434 1.32321L13.3809 1.02509C13.5259 1.00773 13.673 1.02363 13.8109 1.07157C13.9489 1.11951 14.0741 1.19824 14.1771 1.30179C14.2801 1.40533 14.3582 1.53097 14.4054 1.66918C14.4526 1.80739 14.4678 1.95454 14.4497 2.09946L14.1515 4.24259C14.1265 4.45488 14.0315 4.6528 13.8815 4.80509L12.014 6.67259L14.6522 10.7507C14.8723 11.0978 14.9682 11.5093 14.9243 11.918C14.8803 12.3266 14.6992 12.7083 14.4103 13.0007C14.0661 13.3288 13.6089 13.5118 13.1334 13.5118C12.6579 13.5118 12.2007 13.3288 11.8565 13.0007L8.7684 9.91258L6.5184 12.1626L6.81652 13.4845C6.87501 13.6513 6.88491 13.8314 6.84507 14.0036C6.80524 14.1759 6.71732 14.3333 6.59153 14.4576L5.8434 15.2057C5.66733 15.377 5.43088 15.4719 5.18528 15.4701ZM1.23652 10.2501L3.3234 11.6788C3.50411 11.8007 3.66053 11.9553 3.78465 12.1345L5.22465 14.2382L5.7309 13.732L5.29777 11.797L8.11028 8.98446C8.20151 8.89212 8.31011 8.81875 8.42981 8.76855C8.54952 8.71835 8.67797 8.69232 8.80778 8.69196C8.93763 8.69201 9.06617 8.71789 9.18592 8.76811C9.30567 8.81833 9.41423 8.89187 9.50528 8.98446L12.689 12.1682C12.8187 12.2917 12.9909 12.3605 13.17 12.3605C13.349 12.3605 13.5212 12.2917 13.6509 12.1682C13.7611 12.0573 13.83 11.912 13.8464 11.7565C13.8627 11.6011 13.8255 11.4447 13.7409 11.3132L11.0409 7.14509C10.9187 6.95479 10.865 6.72856 10.8885 6.50364C10.912 6.27872 11.0114 6.0685 11.1703 5.90759L13.049 4.06259L13.3078 2.18946L11.4122 2.42571L9.53903 4.33259C9.37811 4.49149 9.1679 4.59088 8.94297 4.61439C8.71805 4.63791 8.49182 4.58415 8.30153 4.46196L4.1334 1.76196C4.00195 1.67733 3.84554 1.64012 3.69006 1.65648C3.53458 1.67285 3.38935 1.74181 3.2784 1.85196C3.15096 1.97958 3.07939 2.15255 3.07939 2.3329C3.07939 2.51325 3.15096 2.68622 3.2784 2.81384L6.46215 5.99759C6.64626 6.18304 6.74959 6.43376 6.74959 6.69509C6.74959 6.95641 6.64626 7.20713 6.46215 7.39259L3.64965 10.2051L1.71465 9.77196L1.23652 10.2501Z"
                            fill="white" />
                    </svg>
                    <span>Logistics</span>
                    <i class="fa fa-caret-left"></i>
                    <i class="fa fa-caret-down"></i>
                </a>
                <div id="logisticsCollapse" class="collapse">
                    <ul>
                        <li class="nav-item">
                            <a href="/Logistics">
                                <i class="fa fa-bar-chart"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/Logistics/PackingList">
                                <i class="fa fa-file-text"></i>
                                Packing List
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <li class="nav-item">
                <a href="/Assets">
                    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"
                        class="nav-item-icon">
                        <path
                            d="M0.506793 10.5709C0.826715 10.8908 1.14488 11.209 1.4648 11.5289C2.16265 12.2267 2.8605 12.9246 3.55836 13.6224C4.25797 14.3221 4.95933 15.0234 5.65894 15.723C5.98414 16.0482 6.30582 16.3769 6.63453 16.6986C7.08277 17.1346 7.77184 17.3349 8.37652 17.1346C8.66305 17.0396 8.90914 16.892 9.12359 16.6775C9.23785 16.565 9.35035 16.4508 9.46461 16.3365C10.0043 15.7969 10.5439 15.2572 11.0836 14.7176C11.8254 13.9758 12.5671 13.234 13.3089 12.4922C13.9963 11.8066 14.6836 11.1193 15.3691 10.432C15.7488 10.0523 16.1285 9.67263 16.5082 9.29294C16.9511 8.84997 17.2254 8.25935 17.2043 7.62478C17.1972 7.42263 17.1867 7.22224 17.1779 7.02009C17.1357 6.04274 17.0953 5.0654 17.0531 4.08981C17.0232 3.40075 16.9968 2.71169 16.9652 2.02262C16.9423 1.54977 16.7701 1.09625 16.4273 0.762269C16.088 0.430043 15.6504 0.261292 15.181 0.240199C14.9525 0.229652 14.724 0.220863 14.4937 0.210316C13.5023 0.168128 12.5127 0.125941 11.5212 0.0837531C10.8744 0.0556281 10.2275 0.0239874 9.58063 0.00113586C8.94605 -0.0199579 8.35543 0.254261 7.91246 0.69723C7.77711 0.832582 7.64176 0.967934 7.50641 1.10329C6.93687 1.67282 6.3691 2.24059 5.79957 2.81012C5.05426 3.55544 4.30894 4.30075 3.56187 5.04782C2.89215 5.71755 2.22066 6.38903 1.55093 7.05876C1.20992 7.39978 0.868902 7.74079 0.527887 8.08181C0.32398 8.28747 0.190386 8.51423 0.0884332 8.78318C-0.0258247 9.08552 -0.0152778 9.41423 0.0409722 9.72712C0.0989801 10.0418 0.287066 10.3441 0.506793 10.5709C0.663238 10.7308 0.905816 10.8275 1.12906 10.8275C1.34527 10.8275 1.60191 10.7326 1.75132 10.5709C1.90425 10.4039 2.01851 10.1842 2.00797 9.94861C1.99742 9.71306 1.91656 9.49861 1.75132 9.32634C1.72672 9.30173 1.70386 9.27536 1.68277 9.249C1.72847 9.30876 1.77418 9.36677 1.81988 9.42654C1.77418 9.36677 1.7355 9.30349 1.70562 9.23493C1.7355 9.30525 1.76539 9.37556 1.79351 9.44411C1.76187 9.36853 1.74078 9.29119 1.73023 9.21033C1.74078 9.28767 1.75132 9.36677 1.76187 9.44411C1.75308 9.37204 1.75308 9.29997 1.76187 9.2279C1.75132 9.30525 1.74078 9.38435 1.73023 9.46169C1.74078 9.38083 1.76187 9.30349 1.79351 9.2279C1.76363 9.29822 1.73375 9.36853 1.70562 9.43708C1.73726 9.36853 1.77418 9.30525 1.81988 9.24548C1.77418 9.30525 1.72847 9.36326 1.68277 9.42302C1.76011 9.3281 1.85152 9.24548 1.93765 9.15935C2.11695 8.98005 2.298 8.799 2.4773 8.6197C3.07672 8.02028 3.67613 7.42087 4.27554 6.82146C5.00152 6.09548 5.72926 5.36774 6.45523 4.64177C7.07574 4.02126 7.69801 3.39899 8.31851 2.77848C8.61207 2.48493 8.90562 2.18961 9.19918 1.89782C9.24137 1.85563 9.28707 1.8152 9.33453 1.77829C9.27477 1.82399 9.21676 1.86969 9.15699 1.9154C9.24664 1.84684 9.34332 1.79059 9.44703 1.74665C9.37672 1.77653 9.30641 1.80641 9.23785 1.83454C9.35035 1.78708 9.46637 1.75543 9.58766 1.73961C9.51031 1.75016 9.43121 1.76071 9.35387 1.77125C9.59117 1.74313 9.83902 1.77301 10.0763 1.7818C10.5246 1.80114 10.9728 1.82047 11.4228 1.83981C12.4546 1.88376 13.4865 1.9277 14.5183 1.97165C14.7732 1.98219 15.0386 1.97516 15.2918 2.01032C15.2144 1.99977 15.1353 1.98922 15.058 1.97868C15.1371 1.99098 15.2109 2.01208 15.2847 2.04196C15.2144 2.01208 15.1441 1.98219 15.0755 1.95407C15.1476 1.98571 15.2127 2.02438 15.2759 2.07008C15.2162 2.02438 15.1582 1.97868 15.0984 1.93297C15.1652 1.98571 15.225 2.04372 15.2759 2.11051C15.2302 2.05075 15.1845 1.99274 15.1388 1.93297C15.1845 1.99626 15.2232 2.06129 15.2548 2.13336C15.225 2.06305 15.1951 1.99274 15.167 1.92418C15.1968 1.99801 15.2179 2.0736 15.2302 2.15094C15.2197 2.0736 15.2091 1.9945 15.1986 1.91715C15.2267 2.12633 15.2214 2.3443 15.2302 2.55348C15.2496 3.01051 15.2689 3.46755 15.2882 3.92282C15.3322 4.94763 15.3761 5.97419 15.4183 6.99899C15.4306 7.27849 15.4675 7.57556 15.4359 7.85505C15.4464 7.77771 15.457 7.6986 15.4675 7.62126C15.45 7.74255 15.4183 7.85857 15.3726 7.97107C15.4025 7.90075 15.4324 7.83044 15.4605 7.76189C15.4148 7.8656 15.3586 7.96052 15.2918 8.05193C15.3375 7.99216 15.3832 7.93415 15.4289 7.87439C15.3427 7.98513 15.2373 8.08181 15.1388 8.18025C14.9613 8.35779 14.7838 8.53532 14.6062 8.71286C14.0103 9.30876 13.4127 9.90642 12.8168 10.5023C12.0908 11.2283 11.363 11.956 10.6371 12.682C10.013 13.306 9.38902 13.9301 8.76676 14.5523C8.46793 14.8512 8.17086 15.1517 7.87027 15.4488C7.84215 15.4752 7.81402 15.5015 7.78414 15.5262C7.84391 15.4804 7.90191 15.4347 7.96168 15.389C7.90191 15.4347 7.83863 15.4734 7.77008 15.5033C7.84039 15.4734 7.9107 15.4435 7.97926 15.4154C7.90367 15.4471 7.82633 15.4681 7.74547 15.4787C7.82281 15.4681 7.90191 15.4576 7.97926 15.4471C7.90719 15.4558 7.83512 15.4558 7.76305 15.4471C7.84039 15.4576 7.91949 15.4681 7.99684 15.4787C7.91598 15.4681 7.83863 15.4471 7.76305 15.4154C7.83336 15.4453 7.90367 15.4752 7.97223 15.5033C7.90367 15.4717 7.84039 15.4347 7.78062 15.389C7.84039 15.4347 7.8984 15.4804 7.95816 15.5262C7.8773 15.4594 7.80523 15.382 7.73141 15.3082C7.58551 15.1623 7.43961 15.0164 7.29547 14.8722C6.81031 14.3871 6.3234 13.9002 5.83824 13.415C5.24058 12.8174 4.64644 12.2232 4.0523 11.6291C3.53902 11.1158 3.02574 10.6025 2.51246 10.0892C2.26285 9.83962 2.015 9.58474 1.76187 9.33865C1.75836 9.33513 1.75484 9.33161 1.75132 9.3281C1.59136 9.16814 1.35582 9.07146 1.12906 9.07146C0.912848 9.07146 0.656207 9.16638 0.506793 9.3281C0.353863 9.49509 0.239605 9.71482 0.250152 9.95037C0.260699 10.1824 0.3398 10.4021 0.506793 10.5709Z"
                            fill="white" />
                        <path
                            d="M12.625 3.01404C12.4229 3.23201 12.3016 3.49744 12.2998 3.79802C12.2981 4.08982 12.4194 4.37634 12.625 4.58201C12.7305 4.68923 12.8483 4.75779 12.9836 4.81404C13.0399 4.83865 13.0838 4.85974 13.1401 4.87205C13.2227 4.88962 13.3229 4.90544 13.409 4.90544C13.4951 4.90544 13.5953 4.88962 13.6779 4.87205C13.7342 4.85974 13.7781 4.83689 13.8344 4.81404C13.9697 4.75779 14.0875 4.68748 14.193 4.58201C14.6184 4.1531 14.6201 3.44119 14.193 3.01404C13.9873 2.80837 13.7008 2.68884 13.409 2.68884C13.1084 2.68884 12.843 2.81189 12.625 3.01404C12.4633 3.16345 12.3684 3.42009 12.3684 3.6363C12.3684 3.85251 12.4633 4.10916 12.625 4.25857C12.792 4.4115 13.0117 4.52576 13.2473 4.51521C13.4863 4.50466 13.692 4.42205 13.8695 4.25857C13.8783 4.24978 13.8871 4.24099 13.8977 4.23396C13.8379 4.27966 13.7799 4.32537 13.7201 4.37107C13.736 4.36052 13.75 4.35173 13.7676 4.3447C13.6973 4.37459 13.627 4.40447 13.5584 4.43259C13.576 4.42556 13.5918 4.42205 13.6094 4.41853C13.532 4.42908 13.4529 4.43962 13.3756 4.45017C13.4002 4.44666 13.4213 4.44666 13.4459 4.45017C13.3686 4.43962 13.2895 4.42908 13.2121 4.41853C13.2297 4.42205 13.2455 4.42732 13.2631 4.43259C13.1928 4.40271 13.1225 4.37283 13.0539 4.3447C13.0715 4.35173 13.0856 4.36052 13.1014 4.37107C13.0416 4.32537 12.9836 4.27966 12.9238 4.23396C12.9449 4.24978 12.9608 4.26736 12.9766 4.28669C12.9309 4.22693 12.8852 4.16892 12.8395 4.10916C12.85 4.12498 12.8588 4.13904 12.8658 4.15662C12.836 4.0863 12.8061 4.01599 12.7779 3.94744C12.785 3.96501 12.7885 3.98083 12.792 3.99841C12.7815 3.92107 12.7709 3.84197 12.7604 3.76462C12.7639 3.78923 12.7639 3.81033 12.7604 3.83494C12.7709 3.75759 12.7815 3.67849 12.792 3.60115C12.7885 3.61873 12.7832 3.63455 12.7779 3.65212C12.8078 3.58181 12.8377 3.5115 12.8658 3.44294C12.8588 3.46052 12.85 3.47459 12.8395 3.49041C12.8852 3.43064 12.9309 3.37263 12.9766 3.31287C12.9608 3.33396 12.9432 3.34978 12.9238 3.3656C12.9836 3.3199 13.0416 3.27419 13.1014 3.22849C13.0856 3.23904 13.0715 3.24783 13.0539 3.25486C13.1242 3.22498 13.1945 3.19509 13.2631 3.16697C13.2455 3.174 13.2297 3.17751 13.2121 3.18103C13.2895 3.17048 13.3686 3.15994 13.4459 3.14939C13.4213 3.15291 13.4002 3.15291 13.3756 3.14939C13.4529 3.15994 13.532 3.17048 13.6094 3.18103C13.5918 3.17751 13.576 3.17224 13.5584 3.16697C13.6287 3.19685 13.699 3.22673 13.7676 3.25486C13.75 3.24783 13.736 3.23904 13.7201 3.22849C13.7799 3.27419 13.8379 3.3199 13.8977 3.3656C13.8766 3.34978 13.8608 3.3322 13.8449 3.31287C13.8906 3.37263 13.9363 3.43064 13.982 3.49041C13.9715 3.47459 13.9627 3.46052 13.9557 3.44294C13.9856 3.51326 14.0154 3.58357 14.0436 3.65212C14.0365 3.63455 14.033 3.61873 14.0295 3.60115C14.0401 3.67849 14.0506 3.75759 14.0611 3.83494C14.0576 3.81033 14.0576 3.78923 14.0611 3.76462C14.0506 3.84197 14.0401 3.92107 14.0295 3.99841C14.033 3.98083 14.0383 3.96501 14.0436 3.94744C14.0137 4.01775 13.9838 4.08806 13.9557 4.15662C13.9627 4.13904 13.9715 4.12498 13.982 4.10916C13.9363 4.16892 13.8906 4.22693 13.8449 4.28669C13.852 4.27615 13.8608 4.26736 13.8695 4.25857C14.026 4.08806 14.1262 3.87185 14.1262 3.6363C14.1262 3.42009 14.0313 3.16345 13.8695 3.01404C13.7026 2.86111 13.4828 2.74685 13.2473 2.7574C13.0188 2.76619 12.7832 2.84177 12.625 3.01404Z"
                            fill="white" />
                    </svg>

                    <span>Assets</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#maintenanceCollapse" aria-controls="maintenanceCollapse" class="collapse-action" data-bs-toggle="collapse" data-bs-toggle="collapse" role="button" aria-expanded="false">
                    <i class="fa fa-wrench nav-item-icon"></i>
                    <span>Maintenance</span>
                    <i class="fa fa-caret-left"></i>
                    <i class="fa fa-caret-down"></i>
                </a>
                <div id="maintenanceCollapse" class="collapse">
                    <ul>
                        <li class="nav-item">
                            <a href="/Maintenance">
                                <i class="fa fa-bar-chart"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/Maintenance/UpcomingMaintenance">
                                <i class="fa fa-arrow-circle-up"></i>
                                Upcoming
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/Maintenance/MaintenanceHistory">
                                <i class="fa fa-clock"></i>
                                History
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <li class="nav-item">
                <a href="#QHSECollapse" aria-controls="maintenanceCollapse" class="collapse-action" data-bs-toggle="collapse" data-bs-toggle="collapse" role="button" aria-expanded="false">
                    <i class="fa fa-medkit nav-item-icon"></i>
                    <span>QHSE</span>
                    <i class="fa fa-caret-left"></i>
                    <i class="fa fa-caret-down"></i>
                </a>
                <div id="QHSECollapse" class="collapse">
                    <ul>
                        <li class="nav-item">
                            @if (GlobalSettings.IsWellsense)
                            {
                                <a href="/Qhse/RiskIdentificationSafetyControl"><i class="fa fa-bars"></i>SOC</a>
                            }
                            else
                            {
                                <a href="/Qhse/RiskIdentificationSafetyControl"><i class="fa fa-bars"></i>RISC</a>
                            }
                        </li>
                        <li class="nav-item">
                            <a href="/Qhse">
                                <i class="fa fa-bars"></i>
                                SIF
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/Qhse/Lesson">
                                <i class="fa fa-bars"></i>
                                Lessons
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <li class="nav-item">
                <a href="/company/companies">
                    <i class="fa fa-building nav-item-icon"></i>
                    <span>Companies</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="/Statistics/index">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"
                        class="nav-item-icon">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M1.24918 14.1874H15.3117C15.6222 14.1874 15.8741 14.4395 15.8741 14.75C15.8741 15.0605 15.6222 15.3126 15.3117 15.3126H0.686722C0.375941 15.3126 0.124268 15.0605 0.124268 14.75V1.24999C0.124268 0.93949 0.376246 0.687378 0.686722 0.687378C0.997197 0.687378 1.24918 0.93949 1.24918 1.24999V14.1874Z"
                            fill="white" />
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M5.75 12.5C5.75 12.1893 5.49828 11.9375 5.1875 11.9375H2.9375C2.62672 11.9375 2.375 12.1893 2.375 12.5V14.75C2.375 15.0607 2.62672 15.3125 2.9375 15.3125H5.1875C5.49828 15.3125 5.75 15.0607 5.75 14.75V12.5ZM3.5 13.0625V14.1875H4.625V13.0625H3.5Z"
                            fill="white" />
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M10.25 10.2501C10.25 9.93962 9.99828 9.68762 9.6875 9.68762H7.4375C7.12672 9.68762 6.875 9.93962 6.875 10.2501V14.7501C6.875 15.0606 7.12672 15.3126 7.4375 15.3126H9.6875C9.99828 15.3126 10.25 15.0606 10.25 14.7501V10.2501ZM8 10.8126V14.1876H9.125V10.8126H8Z"
                            fill="white" />
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M14.75 5.74988C14.75 5.43938 14.4983 5.18738 14.1875 5.18738H11.9375C11.6267 5.18738 11.375 5.43938 11.375 5.74988V14.7499C11.375 15.0604 11.6267 15.3124 11.9375 15.3124H14.1875C14.4983 15.3124 14.75 15.0604 14.75 14.7499V5.74988ZM12.5 6.31238V14.1874H13.625V6.31238H12.5Z"
                            fill="white" />
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M4.46012 10.6476L13.4601 1.64762C13.6798 1.42824 13.6798 1.07162 13.4601 0.852242C13.2407 0.632586 12.8841 0.632586 12.6647 0.852242L3.66474 9.85224C3.44509 10.0716 3.44509 10.4282 3.66474 10.6476C3.88412 10.8673 4.24074 10.8673 4.46012 10.6476Z"
                            fill="white" />
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M12.5 1.8125H10.8125C10.502 1.8125 10.25 1.5605 10.25 1.25C10.25 0.9395 10.502 0.6875 10.8125 0.6875H13.0625C13.3733 0.6875 13.625 0.939219 13.625 1.25V3.5C13.625 3.8105 13.373 4.0625 13.0625 4.0625C12.752 4.0625 12.5 3.8105 12.5 3.5V1.8125Z"
                            fill="white" />
                    </svg>

                    <span>Statistics</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="/exchangeRate/index">
                    <i class="fas fa-money-bill nav-item-icon"></i>
                    <span>Exchange Rates</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#personnelCollapse" aria-controls="personnelCollapse" class="collapse-action" data-bs-toggle="collapse" data-bs-toggle="collapse" role="button" aria-expanded="false">
                    <i class="fa fa-male nav-item-icon"></i>
                    <span>Personnel</span>
                    <i class="fa fa-caret-left"></i>
                    <i class="fa fa-caret-down"></i>
                </a>
                <div id="personnelCollapse" class="collapse">
                    <ul>
                        <li class="nav-item">
                            <a href="/Personnel/Employees">
                                <i class="fa fa-users"></i>
                                Personnel Details
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/Personnel/UserLocation">
                                <i class="fa fa-globe"></i>
                                Personnel Location
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            @if (Html.IsAdmin()) {
                <li class="nav-item">
                    <a href="#adminCollapse" aria-controls="adminCollapse" class="collapse-action" data-bs-toggle="collapse" data-bs-toggle="collapse" role="button" aria-expanded="false">
                        <svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg"
                            class="nav-item-icon">
                            <path
                                d="M1.5 6.21234H9.885C10.0427 6.6467 10.3302 7.02199 10.7086 7.28721C11.087 7.55242 11.5379 7.69469 12 7.69469C12.4621 7.69469 12.913 7.55242 13.2914 7.28721C13.6698 7.02199 13.9573 6.6467 14.115 6.21234H16.5C16.6989 6.21234 16.8897 6.13332 17.0303 5.99267C17.171 5.85202 17.25 5.66125 17.25 5.46234C17.25 5.26343 17.171 5.07266 17.0303 4.93201C16.8897 4.79136 16.6989 4.71234 16.5 4.71234H14.115C13.9573 4.27797 13.6698 3.90268 13.2914 3.63747C12.913 3.37226 12.4621 3.22998 12 3.22998C11.5379 3.22998 11.087 3.37226 10.7086 3.63747C10.3302 3.90268 10.0427 4.27797 9.885 4.71234H1.5C1.30109 4.71234 1.11032 4.79136 0.96967 4.93201C0.829018 5.07266 0.75 5.26343 0.75 5.46234C0.75 5.66125 0.829018 5.85202 0.96967 5.99267C1.11032 6.13332 1.30109 6.21234 1.5 6.21234ZM12 4.71234C12.1483 4.71234 12.2933 4.75632 12.4167 4.83874C12.54 4.92115 12.6361 5.03828 12.6929 5.17533C12.7497 5.31237 12.7645 5.46317 12.7356 5.60866C12.7066 5.75414 12.6352 5.88778 12.5303 5.99267C12.4254 6.09756 12.2918 6.16899 12.1463 6.19793C12.0008 6.22687 11.85 6.21201 11.713 6.15525C11.5759 6.09848 11.4588 6.00235 11.3764 5.87902C11.294 5.75568 11.25 5.61067 11.25 5.46234C11.25 5.26343 11.329 5.07266 11.4697 4.93201C11.6103 4.79136 11.8011 4.71234 12 4.71234ZM16.5 12.2123H8.115C7.95735 11.778 7.66978 11.4027 7.29138 11.1375C6.91297 10.8723 6.46209 10.73 6 10.73C5.53791 10.73 5.08703 10.8723 4.70862 11.1375C4.33022 11.4027 4.04265 11.778 3.885 12.2123H1.5C1.30109 12.2123 1.11032 12.2914 0.96967 12.432C0.829018 12.5727 0.75 12.7634 0.75 12.9623C0.75 13.1612 0.829018 13.352 0.96967 13.4927C1.11032 13.6333 1.30109 13.7123 1.5 13.7123H3.885C4.04265 14.1467 4.33022 14.522 4.70862 14.7872C5.08703 15.0524 5.53791 15.1947 6 15.1947C6.46209 15.1947 6.91297 15.0524 7.29138 14.7872C7.66978 14.522 7.95735 14.1467 8.115 13.7123H16.5C16.6989 13.7123 16.8897 13.6333 17.0303 13.4927C17.171 13.352 17.25 13.1612 17.25 12.9623C17.25 12.7634 17.171 12.5727 17.0303 12.432C16.8897 12.2914 16.6989 12.2123 16.5 12.2123ZM6 13.7123C5.85166 13.7123 5.70666 13.6684 5.58332 13.5859C5.45999 13.5035 5.36386 13.3864 5.30709 13.2493C5.25032 13.1123 5.23547 12.9615 5.26441 12.816C5.29335 12.6705 5.36478 12.5369 5.46967 12.432C5.57456 12.3271 5.7082 12.2557 5.85368 12.2267C5.99917 12.1978 6.14997 12.2127 6.28701 12.2694C6.42406 12.3262 6.54119 12.4223 6.6236 12.5457C6.70601 12.669 6.75 12.814 6.75 12.9623C6.75 13.1612 6.67098 13.352 6.53033 13.4927C6.38968 13.6333 6.19891 13.7123 6 13.7123Z"
                                fill="white" />
                        </svg>
                        <span>Admin</span>
                        <i class="fa fa-caret-left"></i>
                        <i class="fa fa-caret-down"></i>
                    </a>

                    <div id="adminCollapse" class="collapse">
                        <ul class="navigation-admin-list">
                            @if (Html.IsGlobalAdmin())
                            {
                                <li class="nav-item">
                                    <a href="/Admin/Divisions">
                                        <i class="fa fa-building"></i>
                                        Divisions
                                    </a>
                                </li>
                            }
                            @if (Html.IsOperationAdmin() || Html.IsGlobalAdmin())
                            {
                                <li class="nav-item">
                                    <a href="#adminOperationsCollapse" aria-controls="adminOperationsCollapse" class="collapse-action" data-bs-toggle="collapse" data-bs-toggle="collapse" role="button" aria-expanded="false">
                                        <i class="fa fa-wrench"></i> 
                                        <span>Operations</span>
                                        <i class="fa fa-caret-left"></i>
                                        <i class="fa fa-caret-down"></i>
                                    </a>
                                    <div id="adminOperationsCollapse" class="collapse">
                                        <ul class="second-level-collapse">
                                            <li class="nav-item">
                                                <a href="/Admin/Services">
                                                    <i class="fa fa-list-alt"></i>
                                                    Services
                                                </a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="/Admin/FluidTypes">
                                                    <i class="fa fa-tint"></i>
                                                    Fluids
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </li>
                            }
                            @if (Html.IsLogisticsAdmin() || Html.IsGlobalAdmin())
                            {

                                <li class="nav-item">
                                    <a href="#adminLogisiticsCollapse" aria-controls="adminLogisiticsCollapse" class="collapse-action" data-bs-toggle="collapse" data-bs-toggle="collapse" role="button" aria-expanded="false">
                                        <i class="fa fa-plane"></i>
                                        <span>Logistics</span>
                                        <i class="fa fa-caret-left"></i>
                                        <i class="fa fa-caret-down"></i>
                                    </a>
                                    <div id="adminLogisiticsCollapse" class="collapse">
                                        <ul class="second-level-collapse">
                                            <li class="nav-item">
                                                <a href="/Admin/ShipmentMethods">
                                                    <i class="fa fa-road"></i>
                                                    Shipment Method
                                                </a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="/Admin/CustomStatusCodes">
                                                    <i class="fa fa-flag"></i>
                                                    Custom Status
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </li>
                            }
                            @if (Html.IsAssetAdmin() || Html.IsGlobalAdmin())
                            {
                                <li class="nav-item">
                                    <a href="#adminReadAssetsCollapse" aria-controls="adminReadAssetsCollapse" class="collapse-action" data-bs-toggle="collapse" data-bs-toggle="collapse" role="button" aria-expanded="false">
                                        <i class="fa fa-tags"></i>
                                        <span>Assets</span>
                                        <i class="fa fa-caret-left"></i>
                                        <i class="fa fa-caret-down"></i>
                                    </a>
                                    <div id="adminReadAssetsCollapse" class="collapse">
                                        <ul class="second-level-collapse">
                                            <li class="nav-item">
                                                <a href="/Admin/Equipment">
                                                    <i class="fa fa-tags"></i>
                                                    Equipment
                                                </a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="/Admin/ArchivedItems">
                                                    <i class="fa fa-tags"></i>
                                                    Archived Items
                                                </a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="/Admin/EquipmentImport">
                                                    <i class="fa fa-upload"></i>
                                                    Import
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </li>
                                <li class="nav-item">
                                    <a href="/Admin/CustomerAssets">
                                        <i class="fa fa-tags"></i>
                                        Customer Assets
                                    </a>
                                </li>
                            }
                            @if (Html.IsMaintenanceAdmin() || Html.IsGlobalAdmin())
                            {
                                <li class="nav-item">
                                    <a href="#adminMaintenanceCollapse" aria-controls="adminMaintenanceCollapse" class="collapse-action" data-bs-toggle="collapse" data-bs-toggle="collapse" role="button" aria-expanded="false">
                                        <i class="fa fa-wrench"></i>
                                        <span>Maintenance</span>
                                        <i class="fa fa-caret-left"></i>
                                        <i class="fa fa-caret-down"></i>
                                    </a>
                                    <div id="adminMaintenanceCollapse" class="collapse">
                                        <ul class="second-level-collapse">
                                            <li class="nav-item">
                                                <a href="/Admin/MaintenanceBlueprints">
                                                    <i class="fa fa-file-image"></i>
                                                    Blueprints
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </li>
                            }
                            @if (Html.IsQhseAdmin() || Html.IsGlobalAdmin() || Html.IsSifAdmin() || Html.IsLessonsLearnedAdmin())
                            {

                                <li class="nav-item">
                                    <a href="#adminQHSECollapse" aria-controls="adminMaintenanceCollapse" class="collapse-action" data-bs-toggle="collapse" data-bs-toggle="collapse" role="button" aria-expanded="false">
                                        <i class="fa fa-medkit"></i>
                                        <span>QHSE</span>
                                        <i class="fa fa-caret-left"></i>
                                        <i class="fa fa-caret-down"></i>
                                    </a>
                                    <div id="adminQHSECollapse" class="collapse">
                                        <ul class="second-level-collapse">
                                            @if (Html.IsQhseAdmin() || Html.IsGlobalAdmin() || Html.IsSifAdmin())
                                            {
                                                <li class="nav-item">
                                                    <a href="/Admin/ServiceImprovementForms">
                                                        <i class="fa fa-bars"></i>
                                                        SIF
                                                    </a>
                                                </li>
                                            }
                                            @if (Html.IsQhseAdmin() || Html.IsGlobalAdmin() || Html.IsLessonsLearnedAdmin())
                                            {
                                                <li class="nav-item">
                                                    <a href="/Admin/LessonCategories">
                                                        <i class="fa fa-bars"></i>
                                                        Lesson Learned
                                                    </a>
                                                </li>
                                            }
                                        </ul>
                                    </div>
                                </li>
                            }
                            @if (Html.IsSalesAdmin() || Html.IsGlobalAdmin())
                            {
                                <li class="nav-item">
                                    <a href="#adminSalesCollapse" aria-controls="adminSalesCollapse" class="collapse-action" data-bs-toggle="collapse" data-bs-toggle="collapse" role="button" aria-expanded="false">
                                        <i class="fa fa-area-chart"></i>
                                        <span>Sales</span>
                                        <i class="fa fa-caret-left"></i>
                                        <i class="fa fa-caret-down"></i>
                                    </a>
                                    <div id="adminSalesCollapse" class="collapse">
                                        <ul class="second-level-collapse">
                                            <li class="nav-item">
                                                <a href="/Admin/ActionTypes">
                                                    <i class="fa fa-cogs"></i>
                                                    Action Type
                                                </a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="/Admin/EventTypes">
                                                    <i class="fa fa-calendar"></i>
                                                    Event Type
                                                </a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="/Admin/OpportunityClosedReasons">
                                                    <i class="fa fa-file-text"></i>
                                                    Closure Reason
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </li>
                            }
                            @if (Html.IsGlobalAdmin() || Html.IsPersonnelAdministrator())
                            {
                                <li class="nav-item">
                                    <a href="#adminPersonnelCollapse" aria-controls="adminPersonnelCollapse" class="collapse-action" data-bs-toggle="collapse" data-bs-toggle="collapse" role="button" aria-expanded="false">
                                        <i class="fa fa-male"></i>
                                        <span>Personnel</span>
                                        <i class="fa fa-caret-left"></i>
                                        <i class="fa fa-caret-down"></i>
                                    </a>
                                    <div id="adminPersonnelCollapse" class="collapse">
                                        <ul class="second-level-collapse">
                                            <li class="nav-item">
                                                <a href="/Admin/Users">
                                                    <i class="fa fa-users"></i>
                                                    Employees
                                                </a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="/Admin/Certificates">
                                                    <i class="fa fa-file-text"></i>
                                                    View Certificates
                                                </a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="/Admin/CertificateCategories">
                                                    <i class="fa fa-file-text"></i>
                                                    Certificate Category
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </li>
                            }

                            <li class="nav-item">
                                <a href="#adminCompaniesCollapse" aria-controls="adminCompaniesCollapse" class="collapse-action" data-bs-toggle="collapse" data-bs-toggle="collapse" role="button" aria-expanded="false">
                                    <i class="fa fa-building"></i>
                                    <span>Companies</span>
                                    <i class="fa fa-caret-left"></i>
                                    <i class="fa fa-caret-down"></i>
                                </a>
                                <div id="adminCompaniesCollapse" class="collapse">
                                    <ul class="second-level-collapse">
                                       
                                        <li class="nav-item">
                                            <a href="/Admin/Companies">
                                                <i class="fa fa-building"></i>
                                                Entities
                                            </a>
                                        </li>
                                        @if (Html.IsGlobalAdmin())
                                        {
                                            <li class="nav-item">
                                                <a href="/Admin/Categories">
                                                    <i class="fa fa-list-alt"></i>
                                                    Categories
                                                </a>
                                            </li>
                                        }
                                    </ul>
                                </div>
                            </li>
                            @if (Html.IsLogisticsAdmin() || Html.IsGlobalAdmin() || Html.IsAssetAdmin() || Html.IsMaintenanceAdmin() || Html.IsPersonnelAdministrator())
                            {
                                <li class="nav-item">
                                    <a href="/Admin/Currencies">
                                        <i class="fa fa-money-bill"></i>
                                        Currencies
                                    </a>
                                </li>
                            }

                            @if (Html.IsGlobalAdmin())
                            {
                                <li class="nav-item">
                                    <a href="/Admin/SystemSettings">
                                        <i class="fa fa-cogs"></i>
                                        System Settings
                                    </a>
                                </li>
                            }
                           
                        </ul>
                    </div>
                </li>

            }
            <li class="nav-item">
                <a href="/Document/Info">
                    <i class="bi bi-info-circle nav-item-icon"></i>
                    <span> Info</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="/Account/SignOut">
                    <i class="fa fa-unlock nav-item-icon"></i>
                    <span>Logout</span>
                </a>
            </li>
        </ul>
    </nav>
    <span class="version-wrapper">
        Version 1.0.0
    </span>    
</header>