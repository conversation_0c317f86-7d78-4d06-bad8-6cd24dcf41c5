﻿@model UserModel

<div class="container-fluid pl-0 pr-0">
    <div class="row mb-3">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">My Projects</h6>
                </div>
                <div class="card-body">
                    @(Html.Kendo().ListView<ProjectModel>()
                        .Name("myProjects")
                        .TagName("div")
                        .Events(e => e.DataBound("updateTotalMyProjects"))
                        .ClientTemplateId("myProjectTemplate")
                        .HtmlAttributes(new { @style = "border:none", @data_bind = "visible:totalMyProjects" })
                        .DataSource(dataSource => dataSource
                            .Ajax()
                            .Read("GetProjectsByUser", "Home")
                        )
                    )
                    <div data-bind="invisible:totalMyProjects">
                       <span>No projects found</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row mb-3">
        <div class="col-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">My Certificates</h6>
                </div>
                <div class="card-body">
                    <div class="card-list-item">
                        <span class="card-list-item-name">Expiring this month</span>
                        <span class="card-list-item-count" style="background: #DB8C8C" data-bind="text:totalExpiringThisMonth">0</span>
                    </div>
                    <div class="card-list-item">
                        <span class="card-list-item-name">Expiring within one month</span>
                        <span class="card-list-item-count" style="background: #DB8C8C" data-bind="text:totalExpiringOneMonth">0</span>
                    </div>
                    <div class="card-list-item">
                        <span class="card-list-item-name">Expiring within two months</span>
                        <span class="card-list-item-count" style="background: #DB8C8C" data-bind="text:totalExpiringTwoMonth">0</span>
                    </div>
                    <div class="card-list-item">
                        <span class="card-list-item-name">Expiring within three months</span>
                        <span class="card-list-item-count" style="background: #DB8C8C" data-bind="text:totalExpiringThreeMonth">0</span>
                    </div>
                    <div class="card-list-item d-flex justify-content-between">
                        <span class="card-list-item-name">Expiring this year</span>
                        <span class="card-list-item-count" style="background: #DB8C8C" data-bind="text:totalExpiringThisYear">0</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">My SIFs</h6>
                </div>
                <div class="card-body">
                    <div class="card-list-item">
                        <span class="card-list-item-name">Open</span>
                        <span class="card-list-item-count" style="background: #666666" data-bind="text:totalSifOpen">0</span>
                    </div>
                    <div class="card-list-item">
                        <span class="card-list-item-name">On Target</span>
                        <span class="card-list-item-count" style="background: #A9B1C0" data-bind="text:totalSifOnTarget">0</span>
                    </div>
                    <div class="card-list-item">
                        <span class="card-list-item-name">Overdue</span>
                        <span class="card-list-item-count" style="background: #DB8C8C" data-bind="text:totalSifOverdue">0</span>
                    </div>
                    <div class="card-list-item d-flex justify-content-between">
                        <span class="card-list-item-name">Closed</span>
                        <span class="card-list-item-count" style="background: #DB8C8C" data-bind="text:totalSifClosed">0</span>
                    </div>
                </div>
            </div>
        </div> 
        <div class="col-4">
            <div class="card">
                <div class="card-header">
                    @if (GlobalSettings.IsWellsense)
                    {
                        <h6 class="mb-0">My SOC Cards</h6>
                    }else{
                        <h6 class="mb-0">My RISC Cards</h6>
                    }
                </div> 
               
                <div class="card-body">
                    <div class="card-list-item">
                        @(Html.Kendo().DropDownList()
                            .HtmlAttributes(new { @style = "width:100px;", @data_bind = "value:summaryMonth" })
                            .Name("summaryDropdown")
                            .DataTextField("Text")
                            .Events(e => e.Change("summaryMonthChanged"))
                            .DataValueField("Value")
                            .Filter("contains")
                            .BindTo(DateConstants.GetAllMonths())
                        )
                    </div>
                    <div class="card-list-item">
                        <span class="card-list-item-name">Total</span>
                        <span class="card-list-item-count" style="background: #DB8C8C" data-bind="text:totalCreatedMonth">0</span>
                    </div>
                    <div class="card-list-item">
                        <span class="card-list-item-name">Positive</span>
                        <span class="card-list-item-count" style="background: #97DB8C" data-bind="text:totalPositiveMonth">0</span>
                    </div>
                    <div class="card-list-item">
                        <span class="card-list-item-name">Corrective</span>
                        <span class="card-list-item-count" style="background: #666666" data-bind="text:totalNegativeMonth">0</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">My Lessons</h6>
                </div>
                <div class="card-body">
                    <div class="card-list-item">
                        <span class="card-list-item-name">Total</span>
                        <span class="card-list-item-count" style="background: #C18CDB" data-bind="text:totalLessons">0</span>
                    </div>
                    <div class="card-list-item d-flex justify-content-between">
                        <span class="card-list-item-name">Pending Approval</span>
                        <span class="card-list-item-count" style="background: #DB8C8C" data-bind="text:totalPendingApproval">0</span>
                    </div>
                </div>
            </div>
        </div> 
        <div class="col-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Days On-Site</h6>
                </div>
                <div class="card-body">
                    <div class="card-list-item">
                        @(Html.Kendo().DropDownList()
                            .HtmlAttributes(new { @style = "width:100px", @data_bind = "value:daysOnSiteMonth" })
                            .Name("daysOnsiteMonthDropdown")
                            .DataTextField("Text")
                            .Events(e => e.Change("daysOnsiteMonthChanged"))
                            .DataValueField("Value")
                            .Filter("contains")
                            .BindTo(DateConstants.GetAllMonths())
                        )
                    </div>
                    <div class="card-list-item d-flex justify-content-between">
                        <span class="card-list-item-name">Total Days</span>
                        <span class="card-list-item-count" style="background: #97DB8C" data-bind="text:totalInLast30Days">0</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Project Messages</h6>
                </div>
                <div class="card-body">
                    <div class="card-list-item">
                        <span class="card-list-item-name">Send new message warnings to my mobile?</span>
                        <div class="custom-control custom-switch small-switch">
                            @(Html.Kendo().Switch()
                                .Name("isMobileNotification")
                                .Events(ev => ev.Change("checkMobileNotification"))
                                .Checked(Model.MobileNotification)
                                )
                        </div>
                    </div>
                    <div class="card-list-item d-flex justify-content-between">
                        <span class="card-list-item-name">Send new message warnings to my email?</span>
                        <div class="custom-control custom-switch small-switch">
                            @(Html.Kendo().Switch()
                                .Name("isEmailNotification")
                                .Events(ev => ev.Change("checkEmailNotification"))
                                .Checked(Model.EmailNotification)
                                )
                        </div>
                    </div>
                </div>
            </div>
        </div> 
    </div>
</div>

<script type="text/x-kendo-tmpl" id="myProjectTemplate">
    <a href="@Url.Action("EditProject", "Operation")/#=ProjectId#">
        <h4>
            <i class="fa fa-file-text"></i>
            #=ProjectNameandObjectives#
        </h4>
    </a>
</script>

