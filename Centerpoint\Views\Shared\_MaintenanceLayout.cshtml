﻿<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="content-type" content="text/plain; charset=UTF-8" />
    <link href='https://fonts.googleapis.com/css?family=Raleway' rel='stylesheet' type='text/css'>
    <title>@ViewBag.Title</title>
@*    @Styles.Render("~/Content/css")
    @Styles.Render("~/Content/kendo")

    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/jqueryval")
    @Scripts.Render("~/bundles/kendo")
    @Scripts.Render("~/bundles/custom")
    @Scripts.Render("~/bundles/bootstrap")
    @Scripts.Render("~/bundles/modernizr")*@

    <script src="~/Content/js/bootstrap.min.js"></script>

    <link rel="stylesheet" href="@Url.Content("~/Content/css/dataTables.bootstrap.min.css")" type="text/css" />
    <link rel="stylesheet" href="@Url.Content("~/Content/css/scroller.bootstrap.min.css")" type="text/css" />
    <link rel="stylesheet" href="@Url.Content("~/Content/css/jquery.dataTables.min.css")" type="text/css" />

    @RenderSection("Head", false)
</head>
<body>
    <div class="logo">
        <div class="container pull-left">
            @if (Html.IsTestInstance()) {
                <a href="@Url.Action("Index", "Home")"><img src="~/Content/img/logo.png" alt="Centerpoint"><span style="font-size:20px;color:#fff;margin-left:120px;font-weight:600"> You are currently using the TEST System</span></a>
            } else {
                <a href="@Url.Action("Index", "Home")"><img src="~/Content/img/logo.png" alt="Centerpoint"></a>
            }
        </div>
    </div>
    <div id="wrapper">
        @Html.Partial("_Navigation")
        <div id="page-wrapper" class="gray-bg dashbard-1">
            <div class="row border-bottom">
                <nav class="navbar navbar-static-top" role="navigation" style="margin-bottom: 0">
                    <div class="navbar-header">
                        <a class="navbar-minimalize minimalize-styl-2 btn btn-primary " href="#"><i class="fa fa-bars" style="margin-right:0"></i></a>
                    </div>
                    <ul class="nav navbar-top-links navbar-right">
                        <li>
                            <h2 style="font-family:'Open Sans'; margin-top:10px"><i class="fa fa-calendar"></i><span id="currentDate" style="margin-right:10px"></span><i class="fa fa-clock"></i><span id="currentTime"></span></h2>
                        </li>
                    </ul>
                </nav>
            </div>
            <div class="row  border-bottom white-bg dashboard-header">
                @RenderBody()
            </div>
            @Html.Partial("_Windows")
            <div class="footer">
                <p>Version @Html.Version() | &copy; @DateTime.Now.Year -  Centerpoint. All Rights Reserved.</p>
            </div>
        </div>
    </div>
    <a href="javascript:" id="return-to-top"><i class="fa fa-chevron-up"></i></a>

   
    
    <script src="~/Content/js/jquery.datatables.min.js"></script>
    <script src="~/Content/js/dataTables.scroller.min.js"></script>
    <script src="~/Content/js/buttons.print.min.js"></script>
    <script src="~/Content/js/dataTables.bootstrap.min.js"></script>
    <script src="~/Content/js/jquery.btechco.excelexport.js"></script>
    <script src="~/Content/js/jquery.base64.js"></script>
    <script src="~/Content/js/tableExport.js"></script>
    <script src="~/Content/js/jquery.metisMenu.js"></script>
    <script src="~/Content/js/jquery.slimscroll.min.js"></script>
    <script src="~/Content/js/pace.min.js"></script>

    <script type="text/javascript">
        $(document).ready(function () {
            DisplayCurrentTime();
        });

        function DisplayCurrentTime() {
            var now = new Date();
            $("#currentDate").html(kendo.toString(now, "dd-MMM-yyyy"));
            $("#currentTime").html(kendo.toString(now, "HH:mm:ss"));

            window.setTimeout(DisplayCurrentTime, 1000);
        }

    </script>
    @RenderSection("scripts", required: false)
</body>
</html>
