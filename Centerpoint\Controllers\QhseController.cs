﻿using Centerpoint.Common;
using Centerpoint.Common.Constants;
using Centerpoint.Common.Enums;
using Centerpoint.Controllers.Extensions.cs;
using Centerpoint.Extensions;
using Centerpoint.Model;
using Centerpoint.Model.Entities;
using Centerpoint.Model.ViewModels;
using Centerpoint.Service.Interfaces;
using Centerpoint.Services;
using Centerpoint.Utils;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis;
using System.Net;

namespace Centerpoint.Controllers
{
    [Authorize]
    public class QhseController : Controller
    {
        private readonly IQhseService _qhseService;
        private readonly IIdentityService _identityService;
        private readonly ICurrentUserService _currentUser;
        private readonly ILessonService _lessonService;
        private readonly IProjectService _projectService;
        private readonly ICommentService _commentService;
        private readonly ISifService _sifService;
        private readonly IRiskService _riskService;
        private readonly ICertificateService _certificateService;
        private readonly ICrewService _crewService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IReportService _reportService;



        public QhseController(IQhseService qhseService,
                               IIdentityService identityService,
                               ICurrentUserService currentUser,
                               ILessonService lessonService,
                               IProjectService projectService,
                               ICommentService commentService,
                               ISifService sifService,
                               IRiskService riskService,
                               ICertificateService certificateService,
                               ICrewService crewService,
                               IHttpContextAccessor httpContextAccessor,
                               IReportService reportService)
        {
            _identityService = identityService;
            _currentUser = currentUser;
            _lessonService = lessonService;
            _projectService = projectService;
            _commentService = commentService;
            _sifService = sifService;
            _riskService = riskService;
            _certificateService = certificateService;
            _crewService = crewService;
            _qhseService = qhseService;
            _httpContextAccessor = httpContextAccessor;
            _reportService = reportService;
        }
        public async Task<IActionResult> Index(string tab)
        {
            var model = await _qhseService.GetQhseDashboardModel(tab, _currentUser.UserId);
            this.SetTitle("Service Improvement Form");

            return View(model);
        }

        public async Task<IActionResult> GetMonthlySummary(int month, int year)
        {
            var model = await _qhseService.GetMonthlySummary(month, year);

            return Json(model);
        }


        #region RISC

        public async Task<IActionResult> GetRiskIdentificationSafetyControls([DataSourceRequest] DataSourceRequest request, string type, int? month, int? year)
        {
            var result = await _qhseService.GetRiskIdentificationSafetyControls(type, month, year);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> RiskIdentificationSafetyControl(int? month, int? year)
        {
            this.SetTitle("QHSE");

            ViewBag.Month = month.HasValue ? month.Value : DateTime.UtcNow.Month;
            ViewBag.Year = year.HasValue ? year.Value : DateTime.UtcNow.Year;

            return View();
        }

        public async Task<IActionResult> AddRiskIdentificationSafetyControl(int? id)
        {
            var model = await _qhseService.AddRiskIdentificationSafetyControl(id, _currentUser.UserId);

            this.SetTitle("Add new RISC Card");
            return View("EditRiskIdentificationSafetyControl", model);
        }

        public async Task<IActionResult> EditRiskIdentificationSafetyControl(int id)
        {
            var model = await _qhseService.EditRiskIdentificationSafetyControl(id);
            this.SetTitle("Edit RISC Card");
            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> EditRiskIdentificationSafetyControl(RiskIdentificationSafetyControlModel model)
        {
            if (!model.ItemId.HasValue && !model.NotRelated)
                ModelState.AddModelError("ItemId", GlobalSettings.IsAisus ? "Either Associated Project or Not Project related is required" : "Either Associated Job or Not job related is required");

            if (ModelState.IsValid)
            {
                var result = await _qhseService.EditRiskIdentificationSafetyControl(model, _currentUser.UserId);

                if (result.HasErrors())
                {
                    Response.StatusCode = (int)HttpStatusCode.BadRequest;
                    return Json(result.Errors);
                }

                return Json(result.Response);
            }
            Response.StatusCode = (int)HttpStatusCode.BadRequest;
            return Json(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)));
        }

        [HttpPost]
        public async Task<IActionResult> SignoffRiskIdentificationSafetyControl(int id, string comment)
        {
            await _qhseService.SignoffRiskIdentificationSafetyControl(id, comment, _currentUser.UserId);

            return Json(new { success = "true" });
        }

        public async Task<IActionResult> DeleteRiskIdentificationSafetyControl(int id)
        {
            await _qhseService.DeleteRiskIdentificationSafetyControl(id);

            return RedirectToAction("RiskIdentificationSafetyControl");
        }

        public async Task<IActionResult> GetRiscMonthlySummary(int month, int year)
        {
            var model = await _qhseService.GetRiscMonthlySummary(month, year);

            return Json(model);
        }

        public async Task<IActionResult> GetRiscContributorSummary(int month, int year)
        {
            var model = await _qhseService.GetRiscContributorSummary(month, year);

            return Json(model);
        }

        public async Task<IActionResult> GetRiscChartData()
        {
            var model = await _qhseService.GetRiscChartData();

            return Json(model);
        }

        #endregion

        #region Service Improvement Form

        public async Task<IActionResult> GetServiceImprovements([DataSourceRequest] DataSourceRequest request, string type, int? month, int? year)
        {
            var result = await _qhseService.GetServiceImprovements(type, month, year, _currentUser.UserId);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> GetSifStatusChartData(string from, string to)
        {
            var result = await _qhseService.GetSifStatusChartData(from, to);

            return Json(result);
        }

        public async Task<IActionResult> AddServiceImprovement()
        {
            var model = await _qhseService.AddServiceImprovement(_currentUser.UserId);

            this.SetTitle("Add New SIF");

            ViewBag.TabIndex = 0;

            return View("EditServiceImprovement", model);
        }

        public async Task<IActionResult> EditServiceImprovement(int id, int tabIndex = 0)
        {
            var model = await _qhseService.EditServiceImprovement(id, _currentUser.UserId, tabIndex);
            this.SetTitle("Edit SIF");

            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> EditServiceImprovement(ServiceImprovementModel model)
        {
            if (!model.JobId.HasValue && !model.IsRelated)
                ModelState.AddModelError("JobId", GlobalSettings.IsAisus ? "Either Associated Project or Not Project related is required" : "Either Associated Job or Not job related is required");

            if (ModelState.IsValid)
            {
                var result = await _qhseService.EditServiceImprovement(model, _currentUser.UserId);

                if (result.HasErrors())
                {
                    Response.StatusCode = (int)HttpStatusCode.BadRequest;
                    return Json(result.Errors);
                }

                return Json(result.Response);
            }
            Response.StatusCode = (int)HttpStatusCode.BadRequest;
            return Json(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)));
        }

        [HttpPost]
        public async Task<IActionResult> ReopenSIF(int id)
        {
            var result = await _qhseService.ReopenSIF(id, _currentUser.UserId);
            if (result.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(result.Errors);
            }

            return Json(new { success = "true" });
        }
        public async Task<IActionResult> DeleteServiceImprovement(int id)
        {
            await _qhseService.DeleteServiceImprovement(id);

            return RedirectToAction("Index");
        }

        [HttpPost]
        public async Task<IActionResult> RejectReason(int serviceImprovementId, string comment, string details, string correctiveActionUser, string preventiveActionUser, string investigatorUser, DateTime? correctiveActionTargetDate, DateTime? preventiveActionTargetDate, DateTime? investigationTargetDate)
        {
            await _qhseService.RejectReason(serviceImprovementId, comment, details, correctiveActionUser, preventiveActionUser, investigatorUser, correctiveActionTargetDate, preventiveActionTargetDate, investigationTargetDate, _currentUser.UserId);

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<IActionResult> ReRequestInvestigationReason(int serviceImprovementId, string comment, string details, string correctiveActionUser, string preventiveActionUser, string investigatorUser, DateTime? correctiveActionTargetDate, DateTime? preventiveActionTargetDate, DateTime? investigationTargetDate)
        {
            var serviceImprovementName = await _qhseService.ReRequestInvestigationReason(serviceImprovementId, comment, details, correctiveActionUser, preventiveActionUser, investigatorUser, correctiveActionTargetDate, preventiveActionTargetDate, investigationTargetDate, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("Investigation '{0}' has been successfully Re-Requested", serviceImprovementName));

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<IActionResult> ReEvaluatePreventiveReason(int serviceImprovementId, string comment, string details, string correctiveActionUser, string preventiveActionUser, string investigatorUser, DateTime? correctiveActionTargetDate, DateTime? preventiveActionTargetDate, DateTime? investigationTargetDate)
        {
            var serviceImprovementName = await _qhseService.ReEvaluatePreventiveReason(serviceImprovementId, comment, details, correctiveActionUser, preventiveActionUser, investigatorUser, correctiveActionTargetDate, preventiveActionTargetDate, investigationTargetDate, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("Preventive Action '{0}' has been successfully Re-Requested", serviceImprovementName));

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<IActionResult> ReEvaluateCorrectiveReason(int serviceImprovementId, string comment, string details, string correctiveActionUser, string preventiveActionUser, string investigatorUser, DateTime? correctiveActionTargetDate, DateTime? preventiveActionTargetDate, DateTime? investigationTargetDate)
        {
            var serviceImprovementName = await _qhseService.ReEvaluateCorrectiveReason(serviceImprovementId, comment, details, correctiveActionUser, preventiveActionUser, investigatorUser, correctiveActionTargetDate, preventiveActionTargetDate, investigationTargetDate, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("Corrective Action '{0}' has been successfully Re-Requested", serviceImprovementName));

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<IActionResult> RequestNewInvestigationDate(ServiceImprovement model, int serviceImprovementId, string comment, DateTime? dateRequested)
        {
            var serviceImprovementName = await _qhseService.RequestNewInvestigationDate(model, serviceImprovementId, comment, dateRequested, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("New Investigation Date for '{0}' has been successfully requested", serviceImprovementName));

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<IActionResult> RequestNewPreventiveActionDate(ServiceImprovement model, int serviceImprovementId, string comment, DateTime? dateRequested)
        {
            var serviceImprovementName = await _qhseService.RequestNewPreventiveActionDate(model, serviceImprovementId, comment, dateRequested, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("New Preventive Action Target date for '{0}' has been successfully requested", serviceImprovementName));

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<IActionResult> RequestNewCorrectiveActionDate(ServiceImprovement model, int serviceImprovementId, string comment, DateTime? dateRequested)
        {
            var serviceImprovementName = await _qhseService.RequestNewCorrectiveActionDate(model, serviceImprovementId, comment, dateRequested, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("New Corrective Action Target date for '{0}' has been successfully requested", serviceImprovementName));

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<IActionResult> RejectInvestigationDateReason(int serviceImprovementId, string comment)
        {
            var serviceImprovementName = await _qhseService.RejectInvestigationDateReason(serviceImprovementId, comment, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("New Target date for '{0}' has been successfully rejected", serviceImprovementName));

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<IActionResult> RejectPreventiveActionDateReason(int serviceImprovementId, string comment)
        {
            var serviceImprovementName = await _qhseService.RejectPreventiveActionDateReason(serviceImprovementId, comment, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("New Preventive Action Target date for '{0}' has been successfully rejected", serviceImprovementName));

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<IActionResult> RejectCorrectiveActionDateReason(int serviceImprovementId, string comment)
        {
            var serviceImprovementName = await _qhseService.RejectCorrectiveActionDateReason(serviceImprovementId, comment, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("New Corrective Action Target date for '{0}' has been successfully rejected", serviceImprovementName));

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<IActionResult> NewInvestigationDate(int id, DateTime newInvestigationTargetDate)
        {
            var serviceImprovementName = await _qhseService.NewInvestigationDate(id, newInvestigationTargetDate, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("New Target date for '{0}' has been successfully updated", serviceImprovementName));

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<IActionResult> NewPreventiveActionDate(int id, DateTime newPreventiveActionTargetDate)
        {
            var serviceImprovementName = await _qhseService.NewPreventiveActionDate(id, newPreventiveActionTargetDate, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("New Target date for '{0}' has been successfully updated", serviceImprovementName));

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<IActionResult> NewCorrectiveActionDate(int id, DateTime newCorrectiveActionTargetDate)
        {
            var serviceImprovementName = await _qhseService.NewCorrectiveActionDate(id, newCorrectiveActionTargetDate, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("New Target date for '{0}' has been successfully updated", serviceImprovementName));

            return Json(new { success = "true" });
        }

        #region Service Improvement Attachment Documents

        public async Task<IActionResult> GetServiceImprovementDocuments([DataSourceRequest] DataSourceRequest request, int sId)
        {
            var result = await _qhseService.GetServiceImprovementDocuments(sId);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> AttachServiceImprovementDocuments(IEnumerable<IFormFile> serviceImprovementAttachmentDocuments, int serviceImprovementId, DocumentModel model)
        {
            await _qhseService.AttachServiceImprovementDocuments(serviceImprovementAttachmentDocuments, serviceImprovementId, model, _currentUser.UserId);

            return Json(true);
        }

        public async Task<IActionResult> DeleteServiceImprovementDocument([DataSourceRequest] DataSourceRequest request, int serviceImprovementId, DocumentModel model)
        {
            await _qhseService.DeleteServiceImprovementDocument(serviceImprovementId, model, _currentUser.UserId);

            return Json(ModelState.ToDataSourceResult(request));
        }

        #endregion

        #region Service Improvement Status

        [HttpPost]
        public async Task<IActionResult> UpdateStatus(int id, string status, string details, string correctiveActionUser, string preventiveActionUser, string investigatorUser, DateTime? correctiveActionTargetDate, DateTime? preventiveActionTargetDate, DateTime? investigationTargetDate)
        {
            var serviceImprovementName = await _qhseService.UpdateStatus(id, status, details, correctiveActionUser, preventiveActionUser, investigatorUser, correctiveActionTargetDate, preventiveActionTargetDate, investigationTargetDate, _currentUser.UserId);

            if (!String.IsNullOrEmpty(serviceImprovementName))
            {
                this.SetMessage(MessageType.Success, string.Format("Service Improvement Form '{0}' status has been successfully updated to '{1}'", serviceImprovementName, ServiceImprovementStatusConstant.GetDescription(status)));
                return Json(new { success = "true" });
            } else
                return Json(new { success = false, responseText = string.Format("This Service Improvement Form status has't been successfully updated to '{0}'", ServiceImprovementStatusConstant.GetDescription(status)) });

        }

        #endregion

        public async Task<IActionResult> GetServiceImprovementLogByServiceImprovementId([DataSourceRequest] DataSourceRequest request, int sId)
        {
            var result = await _qhseService.GetServiceImprovementLogByServiceImprovementId(sId);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        #endregion

        #region Lessons

        public async Task<IActionResult> GetLessons([DataSourceRequest] DataSourceRequest request, int? lessonCategoryId, int?[] equipmentCategoryIds, int? clientId, int?[] objectiveIds, string query, bool titleOnly, string type)
        {
            var result = await _qhseService.GetLessons(lessonCategoryId, equipmentCategoryIds, clientId, objectiveIds, query, titleOnly, type, _currentUser.UserId);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> GetRecentLessons([DataSourceRequest] DataSourceRequest request)
        {
            var result = await _qhseService.GetRecentLessons(_currentUser.UserId);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> GetLessonsLearnedSummary()
        {
            var model = await _qhseService.GetLessonsLearnedSummary(_currentUser.UserId);

            return Json(model);
        }

        public async Task<IActionResult> Lesson()
        {
            this.SetTitle("Lessons");
            return View();
        }

        public async Task<IActionResult> AddLesson()
        {
            var model = new LessonModel();

            this.SetTitle("New Lesson");
            return View("EditLesson", model);
        }

        public async Task<IActionResult> EditLesson(int id)
        {
            var model = await _qhseService.EditLesson(id, _currentUser.UserId);

            this.SetTitle("Edit Lesson");

            return View(model);
        }


        [HttpPost]
        public async Task<IActionResult> RejectLessonReason(int lessonId, string comment)
        {
            var lessonName = await _qhseService.RejectLessonReason(lessonId, comment, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("Lesson '{0}' has been successfully rejected", lessonName));

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<IActionResult> ReSubmitLesson(int lessonId, string description)
        {
            var lessonName = await _qhseService.ReSubmitLesson(lessonId, description, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("Lesson '{0}' has been successfully re-submitted", lessonName));

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<IActionResult> EditLesson(LessonModel model)
        {
            if (!model.OnJob && !model.CompanyId.HasValue)
            {
                ModelState.AddModelError("CompanyId", "The Client Company field is required");
            }

            if (string.IsNullOrEmpty(model.Name))
            {
                ModelState.AddModelError("Name", "The Name field is required");
            }

            if (string.IsNullOrEmpty(model.Description))
            {
                ModelState.AddModelError("Description", "The Text field is required");
            }

            if (!model.LessonCategoryId.HasValue)
            {
                ModelState.AddModelError("LessonCategoryId", "The Category field is required");
            }

            if (!ModelState.IsValid)
            {
                return View(model);
            }

            var lessonModel = await _qhseService.EditLesson(model, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("{0} Lesson details have been successfully updated", model.Name));

            return RedirectToAction("EditLesson", new { @id = lessonModel.Response.LessonId });
        }
        [HttpPost]
        public IActionResult DeleteLesson(int id)
        {
            var lessonName = _qhseService.DeleteLesson(id);
            this.SetMessage(MessageType.Success, string.Format("'{0}' Lesson has been successfully deleted", lessonName));

            return Json(new { success = "true" });
        }
        public async Task<ActionResult> GetLessonDropdownData(int id)
        {
            var model = await _lessonService.GetLessonDropdownData(id);
            return Json(model);
        }

        #region Lesson Status

        [HttpPost]
        public async Task<IActionResult> UpdateLessonStatus(int id, string status)
        {
            var lessonName = await _qhseService.UpdateLessonStatus(id, status, _currentUser.UserId);

            this.SetMessage(MessageType.Success, string.Format("Lesson '{0}' status has been successfully updated to '{1}'", lessonName, LessonStatusConstant.GetDescription(status)));

            return Json(new { success = "true" });
        }

        #endregion

        #region Lesson Documents

        public async Task<IActionResult> GetLessonDocuments([DataSourceRequest] DataSourceRequest request, int lessonId)
        {
            var result = await _qhseService.GetLessonDocuments(lessonId);

            return Json(await result.ToDataSourceResultAsync(request));
        }

        public async Task<IActionResult> AttachLessonDocuments(IEnumerable<IFormFile> lessonAttachmentDocuments, int lessonId, DocumentModel model)
        {
            await _qhseService.AttachLessonDocuments(lessonAttachmentDocuments, lessonId, model, _currentUser.UserId);

            return Json(true);
        }

        public async Task<IActionResult> DeleteLessonDocument([DataSourceRequest] DataSourceRequest request, DocumentModel model, int lessonId)
        {
            await _qhseService.DeleteLessonDocument(model, lessonId, _currentUser.UserId);

            return Json(ModelState.ToDataSourceResult());
        }

        #endregion

        #endregion

        #region Export

        [HttpPost]
        public async Task<IActionResult> Export(string contentType, string base64, string fileName)
        {
            var fileContents = Convert.FromBase64String(base64);

            return this.File(fileContents, contentType, fileName);
        }
        #endregion

        #region SIF PDF Report

        [Authorize]
        public async Task<IActionResult> ServiceImprovementPdf(int id)
        {
            var resultModel = await _qhseService.ServiceImprovementPdf(id);
            var viewPath = $"PDF_Reports/SifPdfReport";
            resultModel.SifHeader = string.Join("-", resultModel.Name, resultModel.SifTitle, resultModel.ServiceImprovementStatusDescription);

            var viewHtml = await this.RenderViewAsync(viewPath, resultModel, true);

            var cookies = WebContext.GetAuthCookies(Request);
            var resultConverting = _reportService.ExportSIFAsPdf(cookies,
                string.Join("-", resultModel.Name, resultModel.SifTitle, resultModel.ServiceImprovementStatusDescription), PDFType.SifReport, id);

            if (resultConverting.HasErrors())
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(resultConverting.Errors);
            }

            string fileName = string.Format("{0}.pdf", resultModel.Name);

            return File(resultConverting.Response, "application/pdf", fileName);
        }

        [Authorize]
        public async Task<ActionResult> ViewServiceImprovementHTML(int id)
        {
            var resultModel = await _qhseService.ServiceImprovementPdf(id);
            return View("PDF_Reports/SifPdfReport", resultModel);
        }

        #endregion
    }
}

