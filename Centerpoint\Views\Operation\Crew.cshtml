<div id="crew" class="grid-container-standard">
    @(Html.<PERSON>().Grid<CrewModel>()
        .Name("crewGrid")
        .Columns(c => {
            c.Bound(p => p.UserId).EditorTemplateName("CrewList").Title("Crew").ClientTemplateId("userNameTemplate");
            c.Bound(p => p.OnJob).Title("On Job").ClientTemplate("#if(OnJob){#Yes#}else{#No#}#").Width(100);
            c.<PERSON>und(p => p.DateIn).EditorTemplateName("Date").Format(DateConstants.DateTimeFormat);
            c.Bound(p => p.DateOut).EditorTemplateName("Date").Title("Out").Format(DateConstants.DateTimeFormat);
            if (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsSeniorUSEngineer()) {
                 c.<PERSON>(command => { 
                    command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                    command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}).Visible("canDeleteCrewMember"); 
                });
            }
        })
        .Editable(editable => editable.Mode(GridEditMode.InLine))
        .ToolBar(t => t.ClientTemplateId("crewGridAdd"))
        .Sortable()
        .Filterable()
        .Scrollable()
        .Resizable(c => c.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .Events(e => e.DataBound("updateCrewGrid"))
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(model => {
                model.Id(m => m.CrewId);
                model.Field(m => m.DateIn);
                model.Field(m => m.DateOut);
                model.Field(m => m.OnJob).Editable(false);
            })
        .Events(e => e.Error("onError").RequestEnd("onRequestEndRefreshCrewGrid"))
            .Read(read => read.Action("GetCrewsbyProjectId", "Operation", new { @projectId = Model.ProjectId }))
            .Create(create => create.Action("UpdateCrew", "Operation").Data("crewData"))
            .Update(update => update.Action("UpdateCrew", "Operation").Data("crewData"))
            .Destroy(destroy => destroy.Action("DeleteCrew", "Operation").Data("crewData")))
    )
</div>
<script>
    function canDeleteCrewMember(data) {
      return data.OnJob || (!data.OnJob && data.DateOut!=null) ? false : true
    }
</script>

<script type="text/x-kendo-template" id="crewGridAdd">
    <button type='button' class='btn btn-primary btn-sm' onclick='createRow(event)'>
        <i class='fa fa-plus'></i> 
        Add Crew
    </button>
</script>

<script type="text/x-kendo-template" id="userNameTemplate">
    #=data.UserName#    
</script>

