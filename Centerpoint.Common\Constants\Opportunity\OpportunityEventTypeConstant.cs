﻿namespace Centerpoint.Common.Constants
{
    public static class OpportunityEventTypeConstant
    {

        public const string Call = "CAL";
        public const string Email = "EML";
        public const string Meeting = "MET";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Call,"Call"},
                    {Email,"Email"},
                    {Meeting,"Meeting"}
                };
            }
        }
    }
}
