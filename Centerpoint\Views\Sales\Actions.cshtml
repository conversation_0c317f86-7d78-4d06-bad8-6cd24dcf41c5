﻿@model OpportunityModel
@(Html.<PERSON>().Grid<OpportunityActionModel>()
    .Name("opportunityActionsGrid")
    .Columns(c => {
        c.Bound(p => p.NewName).Title("Action ID").ClientTemplate("<a href='" + @Url.Action("EditAction", "Sales", new { @id = "" }) + "/#=OpportunityActionId#?revision=" + ViewBag.Revision + "'>#=NewName#</a>");
        c.<PERSON>und(p => p.AssignedUserName).Title("Assignee");
        c.Bound(p => p.TargetDate).Title("Target Date").Format(DateConstants.DateFormat);
        c.<PERSON>und(p => p.CompletedDate).Title("Completed Date").Format(DateConstants.DateFormat);
        c.<PERSON>und(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
        c.<PERSON>(p => p.CreatedBy).Title("Created By");
        c.<PERSON>und(p => p.Status).Title("Status").ClientTemplate("<span class='badge' style='background:#=StatusColor#;color:#=StatusTextColor#'>#=Status#</span>");
        if(Model.Stage != OpportunityStageConstant.Closed) {
            c.Command(command => { 
                        command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
            }).Width(150);
        }
    })
    .ToolBar(t => t.ClientTemplate("#=opportunityActionsGridToolbarTemplate(data)#"))
    .Events(e => e
        .DataBound("updateOpportunityActionsGrid")
        .ColumnReorder("saveOpportunityActionGrid")
        .ColumnResize("saveOpportunityActionGrid")
        .ColumnShow("saveOpportunityActionGrid")
        .ColumnHide("saveOpportunityActionGrid")
    )
    .ColumnMenu(c => c.Columns(true))
    .Sortable()
    .Resizable(r => r.Columns(true))
    .Filterable()
    .Groupable()
    .Editable(e => e.Mode(GridEditMode.InLine))
    .Scrollable(s => s.Height(300))
    .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Model(model => model.Id(p => p.OpportunityActionId))
        .Read(read => read
            .Action("GetOpportunityActionsByOpportunityId", "Sales", new { @opportunityId = Model.LinkOpportunityId })
        )
        .Destroy(destroy => destroy.Action("DeleteOpportunityAction", "Sales"))
    )
)

<script type="text/x-kendo-template" id="opportunityActionsGridToolbar">
    <div class="d-flex justify-content-between w-100 toolbar-inline-padding">
        #if("@Model.Stage" != "@OpportunityStageConstant.Closed"){#
            <a class="btn btn-success mr-2" href="@Url.Action("AddAction", "Sales", new { @opportunityId = Model.ParentOpportunityId.HasValue ? Model.ParentOpportunityId : Model.OpportunityId, @revision = ViewBag.Revision })">
                <i class="fa fa-plus mr-2"></i>Add New Action
            </a>
        #} else {#
            <div></div>
        #}# 
        <button class="btn btn-danger" id="resetOpportunityActionsGrid">Reset Grid View</button>
    </div>
</script>

<script>
        function opportunityActionsGridToolbarTemplate(data) {
           let template = kendo.template($("#opportunityActionsGridToolbar").html())
           let result = template(data)
           return result
        }
</script>