﻿@model LogisticsDashboardModel

<div class="header-container-between">
    <h4>
        <i class="fa fa-file-text"></i>
        Packing List
        (<span data-bind="text: totalPackingListItems"></span>)
    </h4>
    <div>
        @if (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsLogisticsAdmin() || Html.IsAssetAdmin() || Html.IsMaintenanceAdmin() || Html.IsSeniorFieldEngineer() || Html.IsJuniorFieldEngineer() || Html.IsFieldEngineer()) {
        <a class="btn btn-primary btn-sm" href="@Url.Action("AddEquipmentPackingList", "Logistics")"><i class="fa fa-file-text"></i>Create Packing List</a>
        }
    </div>
</div>
<hr />

@(Html.Kendo().TabStrip()
    .Name("assetsStrips")
    .SelectedIndex(0)
    .Animation(false)
    .Items( tabstrip => {

    tabstrip.Add().Text("")
        .HtmlAttributes(new { @data_bind="html:notAssignedStripText"})
        .Selected(true)
        .Content(@<text>

            <div id="Pending">
                @(Html.Kendo().Grid<EquipmentPackingListModel>()
                .Name("PendingEquipmentPackingListGrid")
                .Columns(columns => {
                    columns.Bound(c => c.Number).Title("Packing List ID").ClientTemplate("<a href='" + @Url.Action("EditEquipmentPackingList", "Logistics") + "/#=EquipmentPackingListId#'>#=Number#</a>");
                    //columns.Bound(c => c.EquipmentShipmentNumber).Title("Shipment ID").ClientTemplate("<a href='" + @Url.Action("EditEquipmentShipment", "Logistics") + "/#=EquipmentShipmentId#'>#if(EquipmentShipmentId == null){##} else{# #=EquipmentShipmentNumber# #}#</a>");
                    columns.Bound(c => c.PackingListTitle);
                    columns.Bound(c => c.ProjectNameandObjectives).Title("Project").ClientTemplate("#if(ProjectId == null){#'N/A'#} else{# #=ProjectNameandObjectives# #}#");
                    columns.Bound(c => c.CreatedByUserName).Title("Created By").Hidden(true);
                    columns.Bound(c => c.Created).Title("Date Created").Format(DateConstants.DateFormat);
                    columns.Bound(c => c.DateRequired).Title("Date Required").ClientTemplate("#=DateRequiredOnly#");
                    columns.Bound(c => c.SignedOffBy).Title("Signed Off By");
                    columns.Bound(c => c.SignedOffDate).Title("Signed Off Date").Format(DateConstants.DateFormat).Hidden(true);
                    columns.Bound(c => c.EquipmentItemsCount).Title("No.of Equipment PackingList Items");
                })
                .ToolBar(t => {
                    t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetPendingEquipmentPackingListGrid", @class="bg-danger text-white"});
                    t.Excel().Text("Export");
                }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
                .Groupable()
                .Filterable()
                .Sortable()
                .ColumnMenu(c => c.Columns(true))
                .Resizable(r => r.Columns(true))
                .Reorderable(r => r.Columns(true))
                .Events(e => e.DataBound("updatePendingEquipmentPackingListGrid").ColumnReorder("savePendingEquipmentPackingListGrid").ColumnResize("savePendingEquipmentPackingListGrid").ColumnShow("savePendingEquipmentPackingListGrid").ColumnHide("savePendingEquipmentPackingListGrid"))
                .Excel(excel => excel
                        .FileName(string.Format("Centerpoint_PackingLists_Pending_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                        .Filterable(true)
                        .ProxyURL(Url.Action("Export", "Logistics")))
                .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Model(model => {
                    model.Id(m => m.EquipmentPackingListId);
                })
                .Events(e => e.Error("onError"))
                .Read(read => read.Action("GetEquipmentPackingListsPending", "Logistics"))))
            </div> 

        </text>);

    tabstrip.Add().Text("")
        .HtmlAttributes(new { @data_bind="html:assignedStripText"})
        .Content(@<text>

            <div id="shipped">
                @(Html.Kendo().Grid<EquipmentPackingListModel>()
                .Name("shippedEquipmentPackingListGrid")
                .Columns(columns => {
                columns.Bound(c => c.Number).Title("Packing List ID").ClientTemplate("<a href='" + @Url.Action("EditEquipmentPackingList", "Logistics") + "/#=EquipmentPackingListId#'>#=Number#</a>");
                columns.Bound(c => c.EquipmentShipmentNumber).Title("Shipment ID").ClientTemplate("<a href='" + @Url.Action("EditEquipmentShipment", "Logistics") + "/#=EquipmentShipmentId#'>#=EquipmentShipmentNumber#</a>");
                columns.Bound(c => c.PackingListTitle);
                columns.Bound(c => c.ProjectNameandObjectives).Title("Project").ClientTemplate("#if(ProjectId == null){#'N/A'#} else{# #=ProjectNameandObjectives# #}#");
                columns.Bound(c => c.CreatedByUserName).Title("Created By").Hidden(true);
                columns.Bound(c => c.Created).Title("Date Created").Format(DateConstants.DateFormat);
                columns.Bound(c => c.DateRequired).Title("Date Required").Format(DateConstants.DateFormat);
                columns.Bound(c => c.EquipmentItemsCount).Title("No.of Equipment PackingList Items");
                if (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsLogisticsAdmin() || Html.IsAssetAdmin() || Html.IsSeniorFieldEngineer() || Html.IsJuniorFieldEngineer() || Html.IsFieldEngineer()) {
                    columns.Template("#if(IsReceived){# <a class='btn btn-sm btn-primary' href='" + Url.Action("CloneEquipmentPackingList", "Logistics") + "/#=EquipmentPackingListId#'><i class='fa fa-copy'></i><span class='ml-1'>Duplicate</span></a> #}#").Width(100);
                    }
                })
                .ToolBar(t => {
                    t.Custom().Text("Reset Grid View").HtmlAttributes(new{@id="resetShippedEquipmentPackingListGrid", @class="bg-danger text-white"});
                    t.Excel().Text("Export");
                }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
                .Groupable()
                .Filterable()
                .Sortable()
                .Events(e => e.DataBound("updateShippedEquipmentPackingListGrid").ColumnReorder("saveShippedEquipmentPackingListGrid").ColumnResize("saveShippedEquipmentPackingListGrid").ColumnShow("saveShippedEquipmentPackingListGrid").ColumnHide("saveShippedEquipmentPackingListGrid"))
                .ColumnMenu(c => c.Columns(true))
                .Resizable(r => r.Columns(true))
                .Reorderable(r => r.Columns(true))
                .Excel(excel => excel
                        .FileName(string.Format("Centerpoint_PackingLists_Shipped_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                        .Filterable(true)
                        .ProxyURL(Url.Action("Export", "Logistics")))
                .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Model(model => {
                    model.Id(m => m.EquipmentPackingListId);
                })
                .Events(e => e.Error("onError"))
                .Read(read => read.Action("GetEquipmentPackingListsShipped", "Logistics"))))
            </div>       

        </text>);
    }
    ))


    <script>
        $(document).ready(function () {
            loadPendingEquipmentPackingListGrid();
            loadShippedEquipmentPackingListGrid();
            var PendingEquipmentPackingListGrid = $('#PendingEquipmentPackingListGrid').data("kendoGrid");
            var shippedEquipmentPackingListGrid = $('#shippedEquipmentPackingListGrid').data("kendoGrid");
            PendingEquipmentPackingListGrid.bind('dataBound', function (e) {
                this.element.find('.k-i-excel').remove();
            });
            shippedEquipmentPackingListGrid.bind('dataBound', function (e) {
                this.element.find('.k-i-excel').remove();
            });
        });

        function savePendingEquipmentPackingListGrid(e) {
            setTimeout(function () {
                var pendingEquipmentPackingListGrid = $("#PendingEquipmentPackingListGrid").data("kendoGrid");
                localStorage["PendingEquipmentPackingListGrid"] = kendo.stringify(pendingEquipmentPackingListGrid.getOptions());
            }, 10);
        }

        function loadPendingEquipmentPackingListGrid() {
            var pendingEquipmentPackingListGrid = $("#PendingEquipmentPackingListGrid").data("kendoGrid");
            var toolBar = $("#PendingEquipmentPackingListGrid .k-grid-toolbar").html();
            var options = localStorage["PendingEquipmentPackingListGrid"];
            viewModel.set("initialPendingEquipmentPackingListGridOptions", kendo.stringify(pendingEquipmentPackingListGrid.getOptions()));
            if (options) {
                pendingEquipmentPackingListGrid.setOptions(JSON.parse(options));
                $("#PendingEquipmentPackingListGrid .k-grid-toolbar").html(toolBar);
                $("#PendingEquipmentPackingListGrid .k-grid-toolbar").addClass("k-grid-top");
            }
        }

        function saveShippedEquipmentPackingListGrid(e) {
            setTimeout(function () {
                var shippedEquipmentPackingListGrid = $("#shippedEquipmentPackingListGrid").data("kendoGrid");
                localStorage["shippedEquipmentPackingListGrid"] = kendo.stringify(shippedEquipmentPackingListGrid.getOptions());
            }, 10);
        }

        function loadShippedEquipmentPackingListGrid() {
            var shippedEquipmentPackingListGrid = $("#shippedEquipmentPackingListGrid").data("kendoGrid");
            var toolBar = $("#shippedEquipmentPackingListGrid .k-grid-toolbar").html();
            var options = localStorage["shippedEquipmentPackingListGrid"];
            viewModel.set("initialShippedEquipmentPackingListGridOptions", kendo.stringify(shippedEquipmentPackingListGrid.getOptions()));
            if (options) {
                shippedEquipmentPackingListGrid.setOptions(JSON.parse(options));
                $("#shippedEquipmentPackingListGrid .k-grid-toolbar").html(toolBar);
                $("#shippedEquipmentPackingListGrid .k-grid-toolbar").addClass("k-grid-top");
            }
        }

        function onError(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var equipmentPackingListGrid = $("#equipmentPackingListGrid").data("kendoGrid");
                equipmentPackingListGrid.dataSource.cancelChanges();
            }

        }
        
        function updatePendingEquipmentPackingListGrid() {
            $("#resetPendingEquipmentPackingListGrid").click(function (e) {
                e.preventDefault();
                resetGridView('PendingEquipmentPackingListGrid', 'initialPendingEquipmentPackingListGridOptions')
            });

            var PendingEquipmentPackingListGrid = $("#PendingEquipmentPackingListGrid").data("kendoGrid");
            var totalPending = PendingEquipmentPackingListGrid.dataSource.total();
            viewModel.set("totalPending", totalPending);
        }

        function updateShippedEquipmentPackingListGrid() {
            $("#resetShippedEquipmentPackingListGrid").click(function (e) {
                e.preventDefault();
                resetGridView('shippedEquipmentPackingListGrid', 'initialShippedEquipmentPackingListGridOptions')
            });

            var shippedEquipmentPackingListGrid = $("#shippedEquipmentPackingListGrid").data("kendoGrid");
            var totalShipped = shippedEquipmentPackingListGrid.dataSource.total();
            viewModel.set("totalShipped", totalShipped);
        }

        var viewModel = kendo.observable({
            totalPending: 0,
            totalShipped:0,
            totalPackingListItems: function() {
              return this.get("totalPending") + this.get("totalShipped")
            },
            notAssignedStripText: function(){
              return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Not Assigned (<span data-bind="text:totalPending"></span>)</span>`;
            },
            assignedStripText: function(){
              return `<span class="k-link"><i class="fa fa-check mr-1"></i> Assigned (<span data-bind="text:totalShipped"></span>)</span>`;
            },
        });

        kendo.bind(document.body.children, viewModel);
    </script>   