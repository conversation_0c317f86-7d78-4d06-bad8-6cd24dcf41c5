﻿@model SalesDashboardModel
<div id="page" style="position: relative;">
<div class="header-container-between">
    <h4>
        <i class="fa fa-bar-chart"></i>
        Sales Dashboard
    </h4>
    <div>
        <a class="btn btn-primary btn-sm" href="@Url.Action("WeeklySalesReport", "Sales")" onclick='downloadFile()'><i class="fa fa-file-pdf"></i> Sales Report</a>
    </div>
</div>
<hr />

<div>
    <div class="row mb-2">
        <div class="d-flex w-100 flex-wrap">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Opportunity Value</h6>
                    </div>
                    <div class="card-body" >
                        <div class="card-list-item">
                                <span class="card-list-item-name">Enquiry</span>
                                <div class="d-flex justify-content-between card-list-item-count-exacrt-width">
                                    <span class="card-list-item-count" style="background: #FF748C; color: #fff">@Model.TotalEnquiry</span>
                                    <span class="card-list-item-count" style="background: #000; color: #fff">@Model.TotalEnquiries</span>                               
                                </div>
                        </div>
                        <div class="card-list-item">
                                <span class="card-list-item-name">Proposal</span>
                                <div class="d-flex justify-content-between card-list-item-count-exacrt-width">
                                    <span class="card-list-item-count" style="background: #B03060; color: #fff">@Model.TotalProposal</span>
                                    <span class="card-list-item-count" style="background: #000; color: #fff">@Model.TotalProposals</span>
                                </div>
                        </div>
                        <div class="card-list-item">
                                <span class="card-list-item-name">Review</span>
                                <div class="d-flex justify-content-between card-list-item-count-exacrt-width">
                                    <span class="card-list-item-count" style="background: #cccc00; color: #fff">@Model.TotalReview</span>
                                    <span class="card-list-item-count" style="background: #000; color: #fff">@Model.TotalReviews</span>
                                </div>
                        </div>
                        <div class="card-list-item">
                                <span class="card-list-item-name ">Award</span>
                                <div class="d-flex justify-content-between card-list-item-count-exacrt-width">
                                    <span class="card-list-item-count" style="background: #008000; color: #fff">@Model.TotalAward</span>
                                    <span class="card-list-item-count" style="background: #000; color: #fff">@Model.TotalAwards</span>
                                </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Total Value</h6>
                    </div>
                    <div class="card-body">
                        @(Html.Kendo().Chart()
                        .Name("chart")
                        .Legend(legend => legend.Position(ChartLegendPosition.Bottom))
                        .HtmlAttributes(new { @style = "height:400px" })
                        .Series(series => {
                            series.Pie(new dynamic[] {
                                new {category = "Leads",value = Model.TotalLeadsValue},
                                new {category = "Opportunities",value = Model.TotalOpportunitiesValue},
                            })
                        .Labels(labels => labels
                            .Visible(true)
                            .Template("#if(value>0){##=category# - £ #= kendo.format('{0:n2}', value)##}#")
                            );
                        })
                        .Tooltip(tooltip => tooltip
                            .Visible(true)
                            .Template("#=category# - £ #= kendo.format('{0:n2}', value)#")))
                    </div>
                </div>         
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Actions by User</h6>
                </div>
                <div class="card-body">
                    @foreach (var userAction in Model.UserActions) {
                        <div class="card-list-item">
                                <span class="card-list-item-name">@userAction.User</span>
                                <div class="d-flex w-50 justify-content-between">
                                    <span class="card-list-item-count" style="background: #FF0000; color: #fff" title="Overdue">@userAction.Overdue</span>
                                    <span class="card-list-item-count" style="background: #F7A54A; color: #fff" title="Upcoming">@userAction.Upcoming</span>
                                    <span class="card-list-item-count" style="background: #7CBB00; color: #fff" title="Registered">@userAction.Registered</span>
                                </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>


    <div>
        <div class="w-100">
            <a href="#" data-bind="click: showCustomerWindow" class="btn btn-primary btn-sm mt-4 mb-3"><i class="fa fa-plus"></i>Add Focus Entity</a>
            <div>
                @(Html.Kendo().ListView<FocusedCustomerModel>()
                    .Name("customerListView")
                    .TagName("div")
                    .ClientTemplateId("customerListViewTemplate")
                    .HtmlAttributes(new { @class="without-border" })
                    .DataSource(dataSource => {
                        dataSource.Ajax().Read(read => read.Action("GetFocusedCustomers", "Sales"));
                    })
                )
            </div>
        </div>
    </div>
</div>
</div>

@(Html.Kendo().Window().Name("customerWindow")
 .Title("Focus Entities")
 .Content(@<text>@Html.Partial("OpportunityCustomer")</text>)
 .Width(500)
 .Modal(true)
 .Draggable()
 .Visible(false))

@(Html.Kendo().Window().Name("closedReasonWindowOpen")
 .Title("Reason for closure")
 .Content(@<text>@Html.Partial("ClosureReason")</text>)
 .Width(600)
 .Modal(true)
 .Visible(false))

    <script type="text/x-kendo-tmpl" id="customerListViewTemplate">
        <div class="card mt-2 pr-2 w-25">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex w-100 justify-content-between toolbar-inline-padding">
                        <span class="mb-0" title="#=Name#" style="overflow:hidden; white-space:nowrap; text-overflow: ellipsis;">#=Name#</span>
                        <span class="btn btn-danger ml-3" style="width:auto; height:20px;" onclick="deleteCustomer(#=CompanyId#)">
                            <i class="fa fa-close m-0"></i>
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="card-list-item">
                        <span class="card-list-item-name">Total Leads</span>
                        <span class="card-list-item-count" style="background-color: \\#008000; color: \\#fff">#=TotalLeads#</span>
                    </div>
                    <div class="card-list-item">
                        <span class="card-list-item-name">Total Opportunities</span>
                        <span class="card-list-item-count" style="background-color: \\#FF748C; color: \\#fff">#=TotalOpportunities#</span>
                    </div>
                    <div class="card-list-item">
                        <span class="card-list-item-name">Lead Value</span>
                        <span class="card-list-item-count" style="background-color: \\#000; color: \\#fff">#=TotalCustomerLeads#</span>
                    </div>                    
                    <div class="card-list-item">
                        <span class="card-list-item-name">Opportunity Value</span>
                        <span class="card-list-item-count" style="background-color: \\#000; color: \\#fff">#=TotalCustomerOpportunities#</span>
                    </div>  
                    <div class="card-list-item">
                        <span class="card-list-item-name">Events this month</span>
                        <span class="card-list-item-count" style="background-color: \\#cccc00; color: \\#fff">#=TotalEvents#</span>
                    </div>                      
                    <div class="card-list-item">
                        <span class="card-list-item-name">Last Contact</span>
                        #if(HasLastContact){#
                          <span class="card-list-item-count" style="color: \\#000">#=LastContactDateOnly#</span>
                          #} else{# 
                          <span class="card-list-item-count" style="color: \\#000">N/A</span>
                          #}# 
                    </div> 
                </div>
            </div>
            #if(UserActions.length > 0){#
            <div class="card" style="border-top:none">
                <div class="card-header">
                    <h6 class="mb-0">Actions by User</h6>
                </div>
                <div class="card-body">
                    #for(var i = 0; i < UserActions.length; ++i){#
                        <div class="card-list-item">
                            <span class="card-list-item-name">#=UserActions[i].User#</span>
                            <div class="d-flex w-50 justify-content-between">
                                <span class="card-list-item-count" style="background: \\#FF0000; color: \\#fff" title="Overdue">#=UserActions[i].Overdue#</span>
                                <span class="card-list-item-count" style="background: \\#F7A54A; color: \\#fff" title="Upcoming">#=UserActions[i].Upcoming#</span>
                                <span class="card-list-item-count" style="background: \\#7CBB00; color: \\#fff" title="Registered">#=UserActions[i].Registered#</span>
                            </div>
                        </div>
                    #}#
                </div>
            </div>
            #}#
        </div>
    </script> 
    
    <environment include="Development">
        <script src="~/js/views/sales/sales.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script src="~/js/views/sales/sales.min.js" asp-append-version="true"></script>
    </environment>