﻿@(Html.<PERSON>().TabStrip()
    .Name("assetsStrips")
    .SelectedIndex(0)
    .Animation(false)
    .Items( tabstrip => {

     tabstrip.Add().Text("")
         .HtmlAttributes(new { @data_bind = "click:refreshAssetStats, html:tabStripHeaderAssetsStats" })
         .Selected(true)
         .Content(@<text>
            <div class="card">
                <div class="card-header">  
                    <h6 class="mb-0">Asset Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex">
                         <div class="form-group w-50 mr-5">
                            <label>Start</label>
                            <input  
                                data-role="datepicker" 
                                data-bind="value:assetStartDate, events:{change:refreshAssetStats}" 
                                data-format="dd-MMM-yyyy" 
                                />
                         </div>
                         <div class="form-group w-50">
                            <label>End</label>
                            <input  
                            data-role="datepicker" 
                            data-bind="value:assetEndDate, events:{change:refreshAssetStats}" 
                            data-format="dd-MMM-yyyy" 
                            />
                         </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            @(Html.Kendo().TreeView()
                                .Name("equipmentCategoryTreeView")
                                .DataTextField("NewName")
                                .LoadOnDemand(false)
                                .Events(e => e
                                    .Change("equipmentCategorySelected")
                                    .Drop("equipmentCategoryDropped")
                                    .DataBound("equipmentCategoryLoaded")
                                    .Select("userEquipmentCategorySelected")
                                )
                                .DataSource(datasource => datasource
                                    .Events(e => e.RequestEnd("categoriesLoaded"))
                                    .Model(m => m.Id("EquipmentCategoryId").HasChildren("HasChildren").Children("Children"))
                                    .Read(r => r.Action("GetEquipmentCategories", "Admin"))
                                )
                            )
                        </div>
                        <div class="col-md-9" data-bind="visible:selectedEquipmentCategory">
                            <div class="form-group">
                                <label>Show by Status</label>
                                @(Html.Kendo().MultiSelect().Name("assetEquipmentStatusFilter")
                                    .DataTextField("Value")
                                    .DataValueField("Key")
                                    .Events(e => e.Change("assetEquipmentGridFilter"))
                                    .HtmlAttributes(new { @data_bind = "value:assetEquipmentFilterOptions" })
                                    .BindTo(Html.EquipmentFilterOptions())
                                )
                            </div>
                            @(Html.Kendo().Grid<EquipmentItemStatisticsModel>()
                                .Name("assetEquipmentItemStatsGrid")
                                .Columns(columns => {
                                    columns.Bound(c => c.EquipmentItemName).ClientTemplate("<a href='/Assets/EditEquipmentItem/#=EquipmentItemId#?returnUrl=#=window.location.href#'>#=EquipmentItemName#</a>").Title("Item Number").Width(150);
                                    columns.Bound(c => c.DivisionName).Title("Division").Width(150);
                                    columns.Bound(c => c.CurrentCompanyLocationName).Title("Current Location").Width(150);
                                    columns.Bound(c => c.Price).Format("{0:n2}").Width(150);
                                    columns.Bound(c => c.DepreciatedPrice).Title("Net Book Value").Format("{0:n2}").Width(150);
                                    columns.Bound(c => c.DaysUsed).Title("Utilisation (days)").ClientTemplate("#=DaysUsed# (#=kendo.toString(DaysUsedPercentage,'p0')#)").Width(130);
                                    columns.Bound(c => c.MeanTimeBetweenFailure).Title("MTBF (days)").Width(100);
                                    columns.Bound(c => c.MeanTimeToRepair).Title("MTTR (days)").Width(100);
                                    columns.Bound(c => c.Jobs).Title("Jobs").Width(100);
                                    columns.Bound(c => c.Runs).Title("Runs").Width(100);
                                    columns.Bound(c => c.AllStatusDescription).Title("Status").Encoded(false).Filterable(f => f.Operators(o => o.ForString(str => str.Clear().Contains("Contains").DoesNotContain("Does not contain")))).Width(150);
                            })
                                .AutoBind(false)
                                .ColumnMenu(c => c.Columns(true))
                                .Events(e => e.DataBound("updateEquipmentTotals"))
                                .Sortable()
                                .Filterable()
                                .Excel(excel => excel
                                    .FileName(string.Format("Centerpoint_Assets_Equipment_Items_Statistics_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                                    .AllPages(true)
                                    .Filterable(true)
                                    .ProxyURL(Url.Action("Export", "Asset"))
                                )
                                .ToolBar(t => {
                                    t.Excel().Text("Export");
                                })
                                .Scrollable(scrollable => scrollable.Endless(true))
                                .HtmlAttributes( new { @style= "height:88%", @class="justify-toolbar-content-to-end" })
                                .Resizable(resize => resize.Columns(true))
                                .Reorderable(reorder => reorder.Columns(true))
                                .DataSource(dataSource => dataSource
                                    .Ajax()
                                    .Model(model => {
                                        model.Id(m => m.EquipmentItemId);
                                    })
                                    .PageSize(100)
                                    .Read(read => read.Action("GetEquipmentItemsStatistics", "Assets").Data("equipmentItemData")))
                                )
                        </div>
                    </div>
                </div>
            </div>
        </text>);

        tabstrip.Add().Text("Asset Value")
         .HtmlAttributes(new { @data_bind="click:refreshAssetValueChart, html:tabStripHeaderAssetValue"})
         .Content(@<text>
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Asset Value</h6>
                </div>
                <div class="card-body">
                    @(Html.Kendo().TreeMap()
                        .Name("assetValueChart")
                        .ValueField("Value")
                        .TextField("Name")
            
                        .AutoBind(false)
                        .Type(TreeMapType.Squarified)
                        .DataSource(dataSource => dataSource
                            .Events(e => e.RequestEnd("assetValueDataRequestEnd"))
                            .Read(read => read.Action("GetAssetValueChartData", "Statistics"))
                            .Model(m => m.Children("Children"))
                        )
                    )
                </div>
            </div>
        </text>);
     }
   )
 )
