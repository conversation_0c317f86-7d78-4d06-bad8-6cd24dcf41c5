﻿@model UserModel

@(Html.<PERSON>().TabStrip()
        .Name("myProjectsTabStrip")
        .SelectedIndex(0)
        .Animation(animation =>
        {
            animation.Enable(false);
        })
        .Items(tabstrip =>
        {
            tabstrip.Add()
            .Text("")
            .HtmlAttributes(new { @data_bind="html:totalCommentsText"})
            .Selected(true)
            .Content(@<text>

                    @(Html.Kendo().Grid<CommentModel>()
                        .Name("allCommentGrid")
                        .HtmlAttributes( new { @data_bind="visible:showAll"})
                        .Columns(c => {
                            c.Bound(p => p.ProjectName).Title(" ").ClientTemplate("#if(AnsaProjectProjectName){#<a href='" + @Url.Action("EditProject", "Operation") + "/#=AnsaProjectProjectId#?tab=comments'><i class='fa fa-pencil-square' style='font-size:16px;color:\\#7CBB00'></i></a>#} else if(ProjectId){#<a href='" + @Url.Action("EditProject", "Operation") + "/#=ProjectId#?tab=comments'><i class='fa fa-pencil-square' style='font-size:16px;color:\\#7CBB00'></i></a>#}#").Width(60);
                            c.Bound(p => p.ProjectGridName).Title("Project").Width(200);
                            c.Bound(p => p.JobName).ClientTemplate("#if(JobId){# <a href='" + @Url.Action("EditJob", "Operation") + "/#=JobId#?tab=comments'>#=JobNewName#</a> #} else{#N/A#}#").Title("Job").Width(180);
                            c.Bound(p => p.Comment).Encoded(false).ClientTemplate("#= getCommentHtmlNewLinesString(Comment) #").Width(400);
                            c.Bound(p => p.UserName).Width(125);
                            c.Bound(p => p.Date).Format(DateConstants.DateTimeFormat).Width(125);
                        })
                            .ToolBar(t => {
                            t.ClientTemplateId("allCommentGridToolbarTemplate");
                        })
                        .Scrollable(scrollable => scrollable.Endless(true).Height(535))
                        .Sortable()
                        .Filterable()
                        .Resizable(c => c.Columns(true))
                        .Reorderable(c => c.Columns(true))
                        .Groupable()
                        .Events(e => e.DataBound("updateAllCommentTotals"))
                        .Excel(excel => excel
                            .FileName(string.Format("Centerpoint_Comments_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                            .Filterable(true)
                            .ProxyURL(Url.Action("Export", "Home")))
                        .Resizable(c => c.Columns(true))
                        .ColumnMenu(c => c.Columns(true))
                        .DataSource(dataSource => dataSource
                            .Ajax()
                            .ServerOperation(true)
                            .PageSize(300)
                            .Sort(sort => sort.Add(p => p.Date).Descending())
                            .Read(read => read.Action("GetAllComments", "Home")))
                    )      



                    @(Html.Kendo().Grid<CommentModel>()
                        .Name("myCommentGrid")
                        .HtmlAttributes( new { @data_bind="invisible:showAll"})
                        .Columns(c => {
                            c.Bound(p => p.ProjectName).Title(" ").ClientTemplate("#if(AnsaProjectProjectName){#<a href='" + @Url.Action("EditProject", "Operation") + "/#=AnsaProjectProjectId#?tab=comments'><i class='fa fa-pencil-square' style='font-size:16px;color:\\#7CBB00'></i></a>#} else if(ProjectId){#<a href='" + @Url.Action("EditProject", "Operation") + "/#=ProjectId#?tab=comments'><i class='fa fa-pencil-square' style='font-size:16px;color:\\#7CBB00'></i></a>#}#").Width(60);
                            c.Bound(p => p.ProjectGridName).Title("Project").Width(200);
                            c.Bound(p => p.JobName).ClientTemplate("#if(JobId){# <a href='" + @Url.Action("EditJob", "Operation") + "/#=JobId#?tab=comments'>#=JobNewName#</a> #}else{#N/A#}#").Title("Job").Width(180);
                            c.Bound(p => p.Comment).Encoded(false).ClientTemplate("#= getCommentHtmlNewLinesString(Comment) #").Width(400);
                            c.Bound(p => p.UserName).Width(125);
                            c.Bound(p => p.Date).Format(DateConstants.DateTimeFormat).Width(125);
                        })
                        .ToolBar(t => {
                            t.ClientTemplateId("myCommentGridToolbarTemplate");
                        })
                        .Sortable()
                        .Filterable()
                        .Groupable()
                        .Events(e => e.DataBound("updateMyCommentTotals"))
                        .Excel(excel => excel
                            .FileName(string.Format("Centerpoint_Comments_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                            .Filterable(true)
                            .ProxyURL(Url.Action("Export", "Home")))
                        .Scrollable(s => s.Height("auto"))
                        .Resizable(c => c.Columns(true))
                        .Reorderable(c => c.Columns(true))
                        .ColumnMenu(c => c.Columns(true))
                        .DataSource(dataSource => dataSource
                        .Ajax()
                        .ServerOperation(false)
                        .Sort(sort => sort.Add(p => p.Date).Descending())
                        .Read(read => read.Action("GetCommentsByUser", "Home")))
                    )
            </text>);

            tabstrip.Add()
            .Text("")
            .HtmlAttributes(new {@data_bind="html:totalProjectLessonsText"})
                .Content(@<text>
                    @(Html.Kendo().DropDownList()
                      .Name("myProjectsLessons")
                      .DataValueField("ProjectId")
                      .DataTextField("ProjectNameandObjectives")
                      .OptionLabel("Show All")
                      .HtmlAttributes(new { @class = "mb-2", @style = "width:20%", @data_bind = "value:projectId" })
                      .Filter("contains")
                      .Events(e => {
                          e.Change("projectChange");
                      })
                      .DataSource(d => d.Read(r => r.Action("GetProjectsByUserId", "Lookup", new { @userId = Model.UserId })))
                    )
                    @(Html.Kendo().Grid<LessonModel>()
                        .Name("projectLessonGrid")
                        .Columns(columns => {
                            columns.Bound(c => c.Number).ClientTemplate("<a href='" + @Url.Action("EditLesson", "Qhse") + "/#=LessonId#'>#=Number#</a>").Title("Lesson ID");
                            columns.Bound(c => c.Name).Title("Title");
                            columns.Bound(c => c.LessonCategoryName).Title("Lesson Category");
                            columns.Bound(c => c.EquipmentCategories).Title("Equipment Category").ClientTemplate("#=EquipmentCategories ? EquipmentCategories : 'N/A'#");
                            columns.Bound(c => c.CompanyName).Title("Client").ClientTemplate("#=CompanyName ? CompanyName : 'N/A'#");
                            columns.Bound(c => c.Services).Title("Service").ClientTemplate("#=Services ? Services : 'N/A'#"); ;
                            columns.Bound(c => c.StatusDescription).Title("Status");
                        })
                        .Sortable()
                        .ToolBar(t => {
                            t.Excel().Text("Export");
                        }).HtmlAttributes( new { @class="justify-toolbar-content-to-end"})
                        .Filterable()
                        .Events(e => e.DataBound("updateProjectLessonTotals"))
                        .Excel(excel => excel
                            .FileName(string.Format("Centerpoint_My_Lessons_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                            .Filterable(true)
                            .ProxyURL(Url.Action("Export", "Home")))
                        .Groupable()
                        .Scrollable()
                        .Resizable(c => c.Columns(true))
                        .Reorderable(c => c.Columns(true))
                        .ColumnMenu(c => c.Columns(true))
                        .DataSource(dataSource => dataSource
                            .Ajax()
                            .ServerOperation(false)
                            .Read(read => read.Action("GetLessonsByServiceEquipment", "Home").Data("projectData"))
                        )
                    )
                </text>);
        })
)

<script>
    function getCommentHtmlNewLinesString(text) {
        var regexp = new RegExp('\n', 'g');
        return text ? text.replace(regexp, '<br>') : text;
    }
</script>

<script type="text/x-kendo-tmpl" id="myCommentGridToolbarTemplate">
    <div class="toolbar d-flex w-100 justify-content-between toolbar-inline-padding">
        <button class="btn btn-primary" data-bind="click:showAllProject">Show All Project Comments</button>
        <button class="btn btn-success k-grid-excel" >
            <i class="fa fa-file-excel"></i>
            Export
        </button>
    </div>
</script>

<script type="text/x-kendo-tmpl" id="allCommentGridToolbarTemplate">
    <div class="toolbar d-flex w-100 justify-content-between toolbar-inline-padding">
        <button class="btn btn-primary" data-bind="click:showMyProject">Show My Project Comments</button>
        <button class="btn btn-success k-grid-excel" >
            <i class="fa fa-file-excel"></i>
            Export
        </button>
    </div>
</script>

