﻿<div class="header-container-single-item-with-hr">
    <div>
        <h4 data-bind="visible:daysVisible">
            <i class="fa fa-arrow-circle-up"></i> Upcoming Maintenance By Days (<span data-bind="text: totalEquipmentItemMaintenanceSchedulesDays"></span>)
        </h4>
        @if (!GlobalSettings.IsWellsense)
        {
            <h4 data-bind="visible:daysAndPointsVisible">
                <i class="fa fa-arrow-circle-up"></i> Upcoming Maintenance (<span data-bind="text: totalEquipmentItemMaintenanceSchedules"></span>)
            </h4>
            <h4 data-bind="visible:pointsVisible">
                <i class="fa fa-arrow-circle-up"></i> Upcoming Maintenance By Points (<span data-bind="text: totalEquipmentItemMaintenanceSchedulesPoints"></span>)
            </h4>
        }
    </div>
</div>
<hr />

@if (!GlobalSettings.IsWellsense)
{
    <div class="pull-left" style="width: fit-content; display: inline-flex; place-items: center;">
        <h5>Filter By</h5>
        <div style="margin-left:5px;">
            @(Html.Kendo().DropDownList()
                .Name("maintenanceFilter")
                .DataTextField("Text")
                .DataValueField("Value")
                .Events(e => e.Change("filterChanged").DataBound("filterChanged"))
                .BindTo(new List<SelectListItem>() {
        new SelectListItem() { Text = "Points & Days", Value = "1"},
        new SelectListItem() { Text = "Points", Value = "2"},
        new SelectListItem() { Text = "Days", Value = "3"}})
                .Value("1"))
        </div>
    </div>

    <div data-bind="visible:daysAndPointsVisible">
        <h5 class="pull-left">
            Showing records where an MR is due in less than @(Html.Kendo().DropDownList()
        .HtmlAttributes(new { @style = "width:100px", @data_bind = "value:days" })
        .Name("summaryByDays")
        .Events(e => e.Change("pointsChanged"))
        .DataSource(d => d.Read(read => read.Action("GetSummaryByDays", "Lookup")))) days and/or the number of points is equal or greater to @(Html.Kendo().DropDownList()
        .HtmlAttributes(new { @style = "width:100px", @data_bind = "value:points" })
        .Name("summaryByPoints")
        .Events(e => e.Change("daysChanged"))
        .DataSource(d => d.Read(read => read.Action("GetSummaryByPoints", "Lookup"))))
        </h5>

        <div style="clear:both"></div>

        @(Html.Kendo().Grid<UpcomingMaintenanceModel>()
            .Name("equipmentItemMaintenanceScheduleGrid")
            .Columns(columns =>
            {
                columns.Bound(c => c.EquipmentItemName).Title("Item Number").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#'>#=EquipmentItemName#</a>").Width(150);
                columns.Bound(c => c.ShipmentTransit).Title("Current Location").ClientTemplate("#=ShipmentTransit ? ShipmentTransit : 'Not Yet Accepted'#").Width(150);
                columns.Bound(c => c.ProjectName).Title("Assigned To").Width(150);
                columns.Bound(c => c.EquipmentItemPoints).Title("Current Points").ClientTemplate("#=EquipmentItemPoints#").Width(150);
                columns.Bound(c => c.MaintenanceBlueprintName).ClientTemplate("<a href='" + @Url.Action("EditMaintenanceBlueprint", "Admin", new { @id = "" }) + "/#=MaintenanceBlueprintId#'>#=MaintenanceBlueprintName ? MaintenanceBlueprintName : ''#</a>").Title("Maintenance Blueprint").Width(150);
                columns.Bound(c => c.StartDate).Title("Next").Format(DateConstants.DateFormat).Width(125);
                columns.Bound(c => c.RemainingDays).Title("Due In Days").ClientTemplate("#=RemainingDays#").Width(150);
            })
            .ColumnMenu(c => c.Columns(true))
            .Sortable()
            .Events(e => e.DataBound("updateEquipmentItemMaintenanceScheduleTotals"))
            .ToolBar(t =>
            {
                t.Excel().Text("Export");
            }).HtmlAttributes(new { @class = "justify-toolbar-content-to-end" })
            .Groupable()
            .Filterable()
            .Resizable(resize => resize.Columns(true))
            .Reorderable(reorder => reorder.Columns(true))
            .Excel(excel => excel
            .FileName(string.Format("Centerpoint_Upcoming_Maintenance_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Admin")))
            .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Read(read => read.Action("GetAllEquipmentItemMaintenanceSchedulesByDaysAndPoints", "Maintenance").Data("maintenanceScheduleData"))))
    </div>

    <div data-bind="visible:pointsVisible">
        <h5 class="pull-left">
            Showing records where number of points for MR is equal or greater to
            @(Html.Kendo().DropDownList()
                .HtmlAttributes(new { @style = "width:100px", @data_bind = "value:points" })
                .Name("maintenancePoints")
                .Events(e => e.Change("pointsChanged"))
                .DataSource(d => d.Read(read => read.Action("GetSummaryByPoints", "Lookup"))))
        </h5>
        <div style="clear:both"></div>

        @(Html.Kendo().Grid<UpcomingMaintenanceModel>()
            .Name("equipmentItemMaintenanceSchedulePointsGrid")
            .Columns(columns =>
            {
                columns.Bound(c => c.EquipmentItemName).Title("Item Number").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#'>#=EquipmentItemName#</a>").Width(150);
                columns.Bound(c => c.ProjectName).Title("Assigned To").Width(150);
                columns.Bound(c => c.ShipmentTransit).Title("Current Location").ClientTemplate("#=ShipmentTransit ? ShipmentTransit : 'Not Yet Accepted'#").Width(150);
                columns.Bound(c => c.EquipmentItemPoints).Title("Current Points").ClientTemplate("#=EquipmentItemPoints ? EquipmentItemPoints : 'N/A'#").Width(150);
            })
            .ColumnMenu(c => c.Columns(true))
            .Sortable()
            .Events(e => e.DataBound("updateEquipmentItemMaintenanceSchedulePointsTotals"))
            .ToolBar(t =>
            {
                t.Excel().Text("Export");
            }).HtmlAttributes(new { @class = "justify-toolbar-content-to-end" })
            .Groupable()
            .Filterable()
            .Resizable(resize => resize.Columns(true))
            .Reorderable(reorder => reorder.Columns(true))
            .Excel(excel => excel
            .FileName(string.Format("Centerpoint_Upcoming_Maintenance_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Admin")))
            .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Read(read => read.Action("GetAllEquipmentItemMaintenanceSchedulesByPoints", "Maintenance").Data("maintenanceScheduleData"))))
    </div>
}

<div data-bind="visible:daysVisible">
    <h5 class="pull-left">
        Showing records where an MR is due in less than
        @(Html.Kendo().DropDownList()
            .HtmlAttributes(new { @style = "width:100px", @data_bind = "value:days" })
            .Name("maintenanceDays")
            .Filter("contains")
            .Events(e => e.Change("daysChanged"))
            .DataSource(d => d.Read(read => read.Action("GetSummaryByDays", "Lookup"))))
        days
    </h5>

    <div style="clear:both"></div>

    @(Html.Kendo().Grid<UpcomingMaintenanceModel>()
        .Name("equipmentItemMaintenanceScheduleDaysGrid")
        .Columns(columns =>
        {
            columns.Bound(c => c.EquipmentItemName).Title("Item Number").ClientTemplate("<a href='" + @Url.Action("EditEquipmentItem", "Assets", new { @id = "" }) + "/#=EquipmentItemId#'>#=EquipmentItemName#</a>").Width(150);
            columns.Bound(c => c.ProjectName).Title("Assigned To").Width(150);
            columns.Bound(c => c.ShipmentTransit).Title("Current Location").ClientTemplate("#=ShipmentTransit ? ShipmentTransit : 'Not Yet Accepted'#").Width(150);
            columns.Bound(c => c.MaintenanceBlueprintName).ClientTemplate("<a href='" + @Url.Action("EditMaintenanceBlueprint", "Admin", new { @id = "" }) + "/#=MaintenanceBlueprintId#'>#=MaintenanceBlueprintName ? MaintenanceBlueprintName : ''#</a>").Title("Maintenance Blueprint").Width(150);
            columns.Bound(c => c.StartDate).Title("Next").ClientTemplate("#= (StartDate == null) ? 'N/A' : StartDateOnly #").Width(125);
            columns.Bound(c => c.RemainingDays).Title("Due In Days").ClientTemplate("#=RemainingDays#").Width(125);
        })
        .ColumnMenu(c => c.Columns(true))
        .Sortable()
        .Events(e => e.DataBound("updateEquipmentItemMaintenanceScheduleDaysTotals"))
        .ToolBar(t => {
            t.Excel().Text("Export");
        }).HtmlAttributes(new { @class = "justify-toolbar-content-to-end" })
        .Groupable()
        .Filterable()
        .Resizable(resize => resize.Columns(true))
        .Reorderable(reorder => reorder.Columns(true))
        .Excel(excel => excel
        .FileName(string.Format("Centerpoint_Upcoming_Maintenance_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
        .Filterable(true)
        .ProxyURL(Url.Action("Export", "Admin")))
        .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Read(read => read.Action("GetAllEquipmentItemMaintenanceSchedulesByDays", "Maintenance").Data("maintenanceScheduleData"))))
</div>


    <environment include="Development">
        <script src="~/js/views/maintenance/upcomingMaintenance.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script src="~/js/views/maintenance/upcomingMaintenance.min.js" asp-append-version="true"></script>
    </environment>