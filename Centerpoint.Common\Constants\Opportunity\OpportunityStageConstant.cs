﻿namespace Centerpoint.Common.Constants
{
    public static class OpportunityStageConstant
    {

        public const string Enquiry = "ENQ";
        public const string Proposal = "PRP";
        public const string Review = "REV";
        public const string Award = "AWD";
        public const string Closed = "CLS";
        public const string Realised = "REL";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Enquiry,"Enquiry"},
                    {Proposal,"Proposal"},
                    {Review,"Review"},
                    {Award,"Award"},
                    {Closed,"Closed"},
                    {Realised,"Realised"}
                };
            }
        }

        public static Dictionary<string, string> ValuesAndDescriptionsWithoutClosed
        {
            get
            {
                return new Dictionary<string, string> {
                    {Enquiry,"Enquiry"},
                    {Proposal,"Proposal"},
                    {Review,"Review"},
                    {Award,"Award"}
                };
            }
        }

        public static Dictionary<string, string> ValuesAndDescriptionsWithRealised
        {
            get
            {
                return new Dictionary<string, string> {
                    {Realised,"Realised"},
                };
            }
        }
    }
}
