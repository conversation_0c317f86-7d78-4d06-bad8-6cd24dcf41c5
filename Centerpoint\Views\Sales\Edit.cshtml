﻿@model OpportunityModel

<div class="header-container-between">
    <h4>
        <i class="fa fa-line-chart"></i>
        @(Model.OpportunityId.HasValue ? Model.EditName : string.Format("Add New {0}", OpportunityTypeConstant.GetDescription(Model.Type)))
    </h4>
    <div class="d-flex flex-row-reverse justify-content-start">
        @if (Model.OpportunityId.HasValue && (Html.IsSalesAdmin() || Html.IsGlobalAdmin())) {
            <a class="btn btn-primary btn-sm margin-l" href="/Sales/Clone?id=@Model.OpportunityId">
                <i class="fa fa-copy mr-2"></i>
                Duplicate
            </a>
            if (Model.Stage != OpportunityStageConstant.Closed) {
                if (Model.Type == OpportunityTypeConstant.Lead) {
                    <button class="btn btn-danger btn-sm margin-l" data-bind="click:deleteLead">
                        <i class="fa fa-ban mr-2"></i>
                        Delete
                    </button>
                } else if (!Model.ProjectId.HasValue) {
                    if (Model.Revision != 0) {
                        <button class="btn btn-danger btn-sm margin-l" data-bind="click:deleteCurrentOpportunity">
                            <i class="fa fa-ban mr-2"></i>
                            Delete Current Revision
                        </button>
                    }
                    if (Model.Revision == 0) {
                        <button class="btn btn-danger btn-sm margin-l" data-bind="click:deleteOpportunity">
                            <i class="fa fa-ban mr-2"></i>
                            Delete
                        </button>
                    }
                }
                if (Model.Stage != OpportunityStageConstant.Realised) {
                    <button class="btn btn-warning btn-sm margin-l" data-bind="click:showClosedReasonWindow">
                        <i class="fa fa-thumbs-down mr-2"></i>
                        Close
                    </button>
                }
                if (Model.Type == OpportunityTypeConstant.Lead) {
                    <button class="btn btn-success btn-sm margin-l" data-bind="click:convertToOpportunity">
                        <i class="fa fa-star mr-2"></i>
                        Convert to Opportunity
                    </button>
                } else if (Model.Type == OpportunityTypeConstant.Opportunity && Model.Stage != OpportunityStageConstant.Realised && Model.CanConvertToProject && !Model.HasProject && (Html.IsOperationAdmin() || Html.IsGlobalAdmin() || Html.IsSeniorUSEngineer() || Html.IsAnsaAdministrator() || Html.IsSalesAdmin())) {
                    <button class="btn btn-success btn-sm margin-l" data-bind="click:convertToProject">
                        <i class="fa fa-thumbs-up mr-2"></i>
                        Convert to Project
                    </button>
                } else {
                    <span id="convertToProjectButton" data-bind="visible:hasProject">
                        <button disabled="disabled" class="btn btn-success k-button k-button btn-sm margin-l">
                            <i class="fa fa-thumbs-up m-0"></i> 
                            Convert to Project 
                        </button>  
                    </span>
                }
            } else {
                if (!Model.ProjectId.HasValue) {
                    <a class="btn btn-success btn-sm margin-l" href="/Sales/Reopen?opportunityId=@Model.LinkOpportunityId&revision=@Model.Revision">
                        <i class="fa fa-check mr-2"></i>
                        Re-open
                    </a>
                }
                if (Model.Type == OpportunityTypeConstant.Lead && Html.IsGlobalAdmin()) {
                    <button class="btn btn-danger btn-sm margin-l" data-bind="click:deleteLead">
                        <i class="fa fa-ban"></i>
                        Delete
                    </button>
                } else if (Model.Type == OpportunityTypeConstant.Opportunity && Html.IsGlobalAdmin() && !Model.ProjectId.HasValue) {
                    <button class="btn btn-danger btn-sm margin-l" data-bind="click:deleteOpportunity">
                        <i class="fa fa-ban"></i>
                        Delete
                    </button>
                }
            }
        }
        @if (Model.OpportunityId.HasValue && Model.Type == OpportunityTypeConstant.Opportunity && Model.Stage != OpportunityStageConstant.Realised && Model.CanConvertToProject && !Model.HasProject && Html.IsAnsaAdministrator() && !Html.IsSalesAdmin()) {
            <button class="btn btn-success btn-sm margin-l" data-bind="click:convertToProject">
                <i class="fa fa-thumbs-up"></i>
                Convert to Project
            </button>
        }
    </div>
</div>
<hr />
@if (Model.MaxRevision > 0) {
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                @(Html.Kendo().SliderFor(m => m.MaxRevision)
                    .IncreaseButtonTitle("Right")
                    .DecreaseButtonTitle("Left")
                    .Min(0)
                    .Max(Model.MaxRevision.Value)
                    .SmallStep(1)
                    .LargeStep(1)
                    .Value(Model.Revision)
                    .Events(events => events.Change("revisionSliderChange"))
                    .Tooltip(t => t.Template("sliderTemplate"))
                    .HtmlAttributes(new { @class = "w-100"})
                )
            </div>
        </div>
    </div>
}
<br />
@using (Html.BeginForm("Edit", "Sales", new { @revision = ViewBag.Revision })) {
    @Html.ValidationSummary(false)
    @(Html.Kendo().TabStrip()
        .Name("EditTabStrip")
        .SelectedIndex(0)
        .Animation(animation =>
        {
            animation.Enable(false);
        })
        .Items(tabstrip =>
        {
            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind="html:salesEditDetailsText"})
                .Selected(true)
                .Content(@<text>
                    <partial name="Details"/>
                </text>);
            if (Model.OpportunityId.HasValue && (Html.IsSalesAdmin() || Html.IsGlobalAdmin())) { 
                    tabstrip.Add().Text("")
                        .HtmlAttributes(new { @data_bind="html:totalLeadContactsText"})
                        .Content(@<text>
                            <partial name="Contacts"/>
                        </text>);

                    tabstrip.Add().Text("")
                        .HtmlAttributes(new { @data_bind="html:totalDocumentsText"})
                        .Content(@<text>
                            <partial name="Documents"/>
                        </text>);

                    tabstrip.Add().Text("")
                        .HtmlAttributes(new { @data_bind="html:totalOpportunityEventsText"})
                        .Content(@<text>
                            <partial name="Events"/>
                        </text>);

                    tabstrip.Add().Text("")
                        .HtmlAttributes(new { @data_bind="html:totalOpportunityActionsText"})
                        .Content(@<text>
                            <partial name="Actions"/>
                        </text>);
                    if (Model.Type == OpportunityTypeConstant.Opportunity) {
                        tabstrip.Add().Text("")
                        .HtmlAttributes(new { @data_bind="html:totalWellsText"})
                        .Content(@<text>
                            <partial name="Wells"/>
                        </text>);   
                    }

                    tabstrip.Add().Text("")
                        .HtmlAttributes(new { @data_bind="html:totalOpportunityLogsText"})
                        .Content(@<text>
                            <partial name="Logs"/>
                        </text>);

            }
        })
    )
}

@(Html.Kendo().Window()
    .Name("addContactRoleWindowOpen")
    .Title("Add Contact Role")
    .Content(@<text>
        <partial name="ContactRole"/>
    </text>)
    .Width(600)
    .Modal(true)
    .Visible(false)
)

<div id="closeWindow">
    @(Html.Kendo().Window()
        .Name("closedReasonWindowOpen")
        .Title("Reason for closure")
        .Content(@<text>
            <partial name="ClosureReason"/>
        </text>)
        .Width(600)
        .Modal(true)
        .Visible(false)
    )
</div>

@(Html.Kendo().Window()
    .Name("followUpActionWindow")
    .Width(1000)
    .Title("Follow-up Actions")
    .Visible(false)
    .Modal(true)
    .Events(e => e.Open("followUpActionWindowOpened"))
    .Content(@<text>
        @(Html.Kendo().Grid<OpportunityActionModel>()
            .Name("followUpActionsGrid")
            .Columns(c => {
                c.Bound(p => p.NewName).Title("Action ID").ClientTemplate("<a href='" + @Url.Action("EditAction", "Sales", new { @id = "" }) + "/#=OpportunityActionId#?revision=" + ViewBag.Revision + "'>#=NewName#</a>"); ;
                c.Bound(p => p.OpportunityName).Title("Lead / Opportunity ID").ClientTemplate("<a href='" + @Url.Action("Edit", "Sales") + "/#=OpportunityId#'>#=GridName#</a>");
                c.Bound(p => p.AssignedUserName).Title("Assignee");
                c.Bound(p => p.TargetDate).Title("Target Date").Format(DateConstants.DateFormat);
                c.Bound(p => p.CompletedDate).Title("Completed Date").Format(DateConstants.DateFormat);
                c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
                c.Bound(p => p.CreatedBy).Title("Created By");
                c.Bound(p => p.Status).Title("Status").ClientTemplate("<span class='badge' style='background:#=StatusColor#;color:#=StatusTextColor#'>#=Status#</span>");
            })
            .Events(e => e.DataBound("updateFollowUpActionsGrid"))
            .ColumnMenu(c => c.Columns(true))
            .Sortable()
            .Resizable(r => r.Columns(true))
            .Filterable()
            .Groupable()
            .Scrollable(s => s.Height(400))
            .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Model(model => model.Id(p => p.OpportunityActionId))
                .Read(read => read.Action("GetAllFollowupActionsByOpportunityEventId", "Sales").Data("opportunityEventData"))
            )
        )
    </text>)
)

@(Html.Kendo().Window()
    .Name("wellAttachmentWindow")
    .Width(1000)
    .Title("Well Attachments")
    .Visible(false)
    .Modal(true)
    .Events(e => e.Open("wellAttachmentWindowOpened"))
    .Content(@<text>
        @(Html.Kendo().Grid<CompanyWellDocumentModel>()
            .Name("companyWellDocumentsGrid")
            .Columns(c => {
                c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                c.Bound(p => p.CompanyWellDocumentTypeDescription).Title("Type").ClientTemplate("#=CompanyWellDocumentTypeDescription ? CompanyWellDocumentTypeDescription : 'N/A'#");
                c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateFormat);
                c.Bound(p => p.Username).Title("Created By");
            })
            .Events(e => e.DataBound("updateCompanyWellDocumentsGrid"))
            .Sortable()
            .Resizable(r => r.Columns(true))
            .ColumnMenu(c => c.Columns(true))
            .Filterable()
            .Groupable()
            .Scrollable(s => s.Height(300))
            .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Model(model => model.Id(p => p.CompanyWellDocumentId))
                .Read(read => read.Action("GetCompanyWellDocumentsByWellId", "Sales").Data("companyWellData"))
            )
        )
    </text>)
)

<script type="text/x-kendo-tmpl" id="sliderTemplate">
    <p>#=Revison# - #=StageDescription#</p>
</script>
<script>
    history.forward();

    const salesModel={
        modelStage: "@Model.Stage",
        linkOpportunityId: '@(Model.LinkOpportunityId ?? 0)',
        maxRevision: '@(Model.MaxRevision ?? 0)',
        type: "@Model.Type",
        opportunity: "@OpportunityTypeConstant.Opportunity",
        companyId: @(Model.CompanyId ?? 0),
        projectId: @(Model.ProjectId ?? 0),
        partnerCompanyId: @(Model.PartnerCompanyId ?? 0),
        companyFieldIds: @Html.Raw(Json.Serialize(Model.CompanyFieldIds ?? Array.Empty<int>())),
        companyWellIds: @Html.Raw(Json.Serialize(Model.CompanyWellIds ?? Array.Empty<int>())),
        equipmentCategoryIds: @Html.Raw(Json.Serialize(Model.EquipmentCategoryIds ?? Array.Empty<int?>())),
        value: @Model.Value,
        probabilityOverridePercentage: @(Model.ProbabilityOverride ?? 0),
        opportunityId: @(Model.OpportunityId ?? 0),
        canConvertToProject: @Json.Serialize(Model.CanConvertToProject)
    }
</script>

<environment include="Development">
    <script src="~/js/views/sales/edit.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/sales/edit.min.js" asp-append-version="true"></script>
</environment>