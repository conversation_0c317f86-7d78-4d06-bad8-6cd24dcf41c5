﻿using Centerpoint.Common;
using Centerpoint.Common.Constants;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Globalization;
using System.Reflection;
using System.Security.Claims;

namespace Centerpoint.Extensions
{
    public static class HtmlHelperExtensions {

        #region Application Info

        public static string Version(this IHtmlHelper helper) {
            var version = Assembly.GetExecutingAssembly().GetName().Version;

            return IsTestInstance(helper) ?
             string.Format("{0}.{1}.{2}.{3}", version.Major, version.Minor, version.Build, version.Revision) :
             string.Format("{0}.{1}.{2}", version.Major, version.Minor, version.Build);
        }
        public static bool IsTestInstance(this IHtmlHelper helper) {
            string result;

            if (helper.ViewContext.HttpContext.Request.Cookies.TryGetValue("test_system", out result))
            {
                return Convert.ToBoolean(result);
            }
            else
            {
                result = helper.ViewContext.HttpContext.Request.Host.Value.Contains("test").ToString();
                helper.ViewContext.HttpContext.Response.Cookies.Append("test_system", result);
            }

            return Convert.ToBoolean(result);
        }
        //public static string MessageType(this IHtmlHelper helper) {
        //    return Settings.Default.MessageType;
        //}

        //public static string Message(this IHtmlHelper helper) {
        //    return Settings.Default.Message;
        //}

        //public static bool ShowMessage(this IHtmlHelper helper) {
        //    return Settings.Default.ShowMessage;
        //}
        #endregion

        #region User Info

        public static bool IsAuthenticated(this IHtmlHelper helper) {
            return helper.ViewContext.HttpContext.User.Identity.IsAuthenticated;
        }

        public static string UserAccountId(this IHtmlHelper helper) {
            var emailAddress = helper.ViewContext.HttpContext.User.Identity.Name;
            if (emailAddress.Contains("#")) {
                return emailAddress.Substring(emailAddress.LastIndexOf('#') + 1);
            } else {
                return emailAddress;
            }
        }

        public static string AccountEmailAddress(this IHtmlHelper helper) {
            return helper.IsAuthenticated() ? helper.ViewContext.HttpContext.User.Identity.Name : null;
        }

        public static string AccountUserFullName(this IHtmlHelper helper)
        {
            return helper.IsAuthenticated() ? helper.ViewContext.HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.GivenName)?.Value : null;
        }

        public static IEnumerable<string> UserAccountRole(this IHtmlHelper helper)
        {
            if (!helper.IsAuthenticated())
            {
                return Enumerable.Empty<string>();
            }

            return helper.ViewContext.HttpContext.User.Claims.Where(x => x.Type == ClaimTypes.Role).Select(r => r.Value);
        }
        public static bool IsUsaUser(this IHtmlHelper helper)
        {
            var hasLocationClaim = helper.ViewContext.HttpContext.User.Claims.Any(x => x.Type == "BaseCompanyLocation");
            if (!helper.IsAuthenticated() || !hasLocationClaim)
            {
                return false;
            }

            return CountriesConstant.IsUS( helper.ViewContext.HttpContext.User.Claims.FirstOrDefault(x => x.Type == "BaseCompanyLocation").Value);
        }

        public static bool IsGlobalAdmin(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Contains(UserRoleConstant.GlobalAdministrator);
        }

        public static bool IsQhseAdmin(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Contains(UserRoleConstant.QHSEAdministrator);
        }

        public static bool IsAssetAdmin(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Contains(UserRoleConstant.AssetsAdministrator);
        }

        public static bool IsLogisticsAdmin(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Contains(UserRoleConstant.LogisticsAdministrator);
        }

        public static bool IsOperationAdmin(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Contains(UserRoleConstant.OperationsAdministrator);
        }

        public static bool IsMaintenanceAdmin(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Contains(UserRoleConstant.MaintenanceAdministrator);
        }

        public static bool IsLessonsLearnedAdmin(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Contains(UserRoleConstant.LessonsLearnedAdmin);
        }

        public static bool IsSalesAdmin(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Contains(UserRoleConstant.SalesAdministrator);
        }

        public static bool IsAdmin(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Any(r => r.EndsWith("AD")) || userRole.Contains("SLD") || userRole.Contains("ND");
        }

        public static bool IsDownloadsAdmin(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Contains(UserRoleConstant.DownloadAdministrator);
        }

        public static bool IsEng(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Any() && userRole.Contains("E");
        }

        //public static bool IsAnsaAnalyst(this IHtmlHelper helper) {
        //    var userRole = helper.UserAccountRole();
        //    return userRole.Any() && userRole.Contains(UserRoleConstant.AnsaAnalyst);
        //}

        public static bool IsUser(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Any(r => r.EndsWith("AD")) || userRole.Any(r => r.EndsWith("SLD")) || 
                userRole.Any(r => r.EndsWith("ND")) || userRole.Any(r => r.EndsWith("E")) || userRole.Any(r => r.EndsWith("U"));
        }

        public static bool IsSifAdmin(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Contains(UserRoleConstant.SifAdministrator);
        }

        public static bool IsPersonnelAdministrator(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Contains(UserRoleConstant.PersonnelAdministrator);
        }

        public static bool IsUserLocatonAdministrator(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return (userRole.Contains(UserRoleConstant.PersonnelAdministrator) || userRole.Contains(UserRoleConstant.GlobalAdministrator));
        }

        public static bool IsAssetEngineers(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return (userRole.Contains(UserRoleConstant.FieldEngineer) || userRole.Contains(UserRoleConstant.JuniorFieldEngineer) || userRole.Contains(UserRoleConstant.SeniorFieldEngineer));
        }

        public static bool IsFieldEngineer(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Contains(UserRoleConstant.FieldEngineer);
        }
        public static bool IsJuniorFieldEngineer(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Contains(UserRoleConstant.JuniorFieldEngineer);
        }

        public static bool IsSeniorFieldEngineer(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Contains(UserRoleConstant.SeniorFieldEngineer);
        }

        public static bool IsSeniorUSEngineer(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Contains(UserRoleConstant.SeniorUSEngineer);
        }
        public static bool IsMaintenanceEngineer(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Contains(UserRoleConstant.MaintenanceEngineer);
        }

        public static bool IsStandardUser(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Contains(UserRoleConstant.StandardUser);
        }

        public static bool IsAnsaAdministrator(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Contains(UserRoleConstant.AnsaAdministrator);
        }

        public static bool IsAnsaAnalyst(this IHtmlHelper helper) {
            var userRole = helper.UserAccountRole();
            return userRole.Contains(UserRoleConstant.AnsaAnalyst);
        }

        public static bool IsAccountRole(this IHtmlHelper helper, string role) {
            var userRole = helper.UserAccountRole();
            return userRole.Any() && userRole.Contains(role);
        }

        public static bool IsAbleToSelfAssign(this IHtmlHelper helper)
        {
            var userRole = helper.UserAccountRole();
            return (IsFieldEngineer(helper)
                                    ||IsSeniorFieldEngineer(helper)
                                    || IsJuniorFieldEngineer(helper)
                                    || IsMaintenanceEngineer(helper)
                                    || IsMaintenanceAdmin(helper)
                                    || IsGlobalAdmin(helper));
        }

        public static bool IsShipmentReceivers(this IHtmlHelper helper)
        {
            var userRoles = helper.UserAccountRole();
            return userRoles.Any(role => UserRoleConstant.ShipmentReceivers.Contains(role));
        }

        #endregion

        #region Is Current Action

        public static bool IsCurrentAction(this IHtmlHelper helper, string action, string controller) {
            helper.ViewContext.HttpContext.GetRouteData().Values.TryGetValue("action", out object? currentAction);
            return IsCurrentController(helper, controller) && currentAction?.ToString().ToLower() == action.ToLower();
        }

        public static bool IsCurrentAction(this IHtmlHelper helper, string action, string controller, string area) {
            helper.ViewContext.HttpContext.GetRouteData().Values.TryGetValue("action", out object? currentAction);
            return IsCurrentController(helper, controller, area) && currentAction?.ToString().ToLower() == action.ToLower();
        }

        #endregion

        #region Is Current Controller

        public static bool IsCurrentController(this IHtmlHelper helper, string controller) {
            helper.ViewContext.HttpContext.GetRouteData().Values.TryGetValue("controller", out object? currentController);
            return currentController?.ToString().ToLower() == controller.ToLower();
        }


        public static bool IsCurrentController(this IHtmlHelper helper, string controller, string area) {
            helper.ViewContext.HttpContext.GetRouteData().Values.TryGetValue("controller", out object? currentController);
            var currentArea = helper.ViewContext.RouteData.DataTokens.ContainsKey("area") ? helper.ViewContext.RouteData.DataTokens["area"].ToString().ToLower() : null;

            if (string.IsNullOrEmpty(area) && string.IsNullOrEmpty(currentArea)) {
                return currentController?.ToString().ToLower() == controller.ToLower();
            } else if (string.IsNullOrEmpty(area)) {
                return false;
            } else if (string.IsNullOrEmpty(currentArea)) {
                return false;
            } else {
                return currentController?.ToString().ToLower() == controller.ToLower() && currentArea == area.ToLower();
            }
        }

        #endregion

        #region Cultures

        public static Dictionary<string, string> Cultures(this IHtmlHelper helper) {
            return new Dictionary<string, string> {
                {"en", new CultureInfo("en").EnglishName},
                {"fr", new CultureInfo("fr").EnglishName},
                {"de", new CultureInfo("de").EnglishName},
                {"it", new CultureInfo("it").EnglishName},
                {"pt", new CultureInfo("pt").EnglishName},
                {"es", new CultureInfo("es").EnglishName},
                {"ms-MY", new CultureInfo("ms-MY").EnglishName},
                {"fi", new CultureInfo("fi-FI").EnglishName},
                {"th", new CultureInfo("th-TH").EnglishName},
                {"ru", new CultureInfo("ru-RU").EnglishName},
                {"cs", new CultureInfo("cs-CZ").EnglishName},
                {"pl", new CultureInfo("pl-PL").EnglishName},
                {"zh-CN", new CultureInfo("zh-CN").EnglishName},
            };
        }

        #endregion

        #region Boolean List Values

        public static Dictionary<string, string> BooleanListValues(this IHtmlHelper helper) {
            return new Dictionary<string, string> {
                {Boolean.FalseString, "No"},
                {Boolean.TrueString, "Yes"}
            };
        }

        public static Dictionary<string, string> BooleanListValuesJavascript(this IHtmlHelper helper) {
            return new Dictionary<string, string> {
                {"false", "No"},
                {"true", "Yes"}
            };
        }

        #endregion

        #region Employee On-Site

        //public static int EmployeeOnSiteCount(this IHtmlHelper helper) {

        //    int count = 0;

        //    var now = DateTime.Now;

        //    using (var context = DbConnectionFactory.CreateDataContext()) {

        //        count = CrewData.GetCrewOnsiteCount(context);
        //    }

        //    return count;
        //}

        #endregion

        #region Equipment Filter Options

        public static List<KeyValuePair<string, string>> EquipmentFilterOptions(this IHtmlHelper helper) 
        {
            return EquipmentConstant.ValuesAndDescriptions.Concat(EquipmentConstant.OtherValuesAndDescriptions).OrderBy(o => o.Key).ToList();
        }

        public static List<KeyValuePair<string, string>> DefaultEquipmentFilterOptions(this IHtmlHelper helper) 
        {
            return EquipmentConstant.ActiveValuesAndDescriptions.Concat(EquipmentConstant.OtherValuesAndDescriptions).OrderBy(o => o.Key).ToList();
        }
        public static List<string> DefaultEquipmentFilters(this IHtmlHelper helper) 
        {
            return EquipmentConstant.ActiveValuesAndDescriptions.Concat(EquipmentConstant.OtherValuesAndDescriptions).OrderBy(o => o.Key).Select(x => x.Value).ToList();
        }

        public static List<KeyValuePair<string, string>> AssetDefaultEquipmentFilterOptions(this IHtmlHelper helper) 
        {
            return EquipmentConstant.AssetValuesAndDescriptions.Concat(EquipmentConstant.OtherValuesAndDescriptions).OrderBy(o => o.Key).ToList();
        }
        #endregion
    }
}