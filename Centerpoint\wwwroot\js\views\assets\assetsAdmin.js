$(document).ready(function () {
    var length = $('#length').data("kendoNumericTextBox");
    var OD = $('#OD').data("kendoNumericTextBox");
    var width = $('#width').data("kendoNumericTextBox");
    var depth = $('#depth').data("kendoNumericTextBox");
    var weight = $('#weight').data("kendoNumericTextBox");

    length.wrapper
    .find(".k-select").hide();

    OD.wrapper
    .find(".k-select").hide();

    width.wrapper
    .find(".k-select").hide();

    depth.wrapper
    .find(".k-select").hide();

    weight.wrapper
    .find(".k-select").hide();

    var equipmentItemGrid = $('#equipmentItemGrid').data("kendoGrid");
    var equipmentCategoryMaintenanceStepGrid = $('equipmentCategoryMaintenanceStepGrid').data("kendoGrid");

    equipmentItemGrid.bind('dataBound', function (e) {
        this.element.find('.k-i-excel').remove();
    });
});

$("#length").kendoNumericTextBox({
    change: function () {
        var value = this.value();
        if (value > 0) {
            $("#heightUint").attr("data-val-required", "Length unit is required");
        } else {
            $("#heightUint").removeAttr("data-val-required");
        }
    }
});
$("#OD").kendoNumericTextBox({
    change: function () {
        var value = this.value();
        if (value > 0) {
            $("#ODUint").attr("data-val-required", "OD unit is required");
        } else {
            $("#ODUint").removeAttr("data-val-required");
        }
    }
});
$("#width").kendoNumericTextBox({
    change: function () {
        var value = this.value();
        if (value > 0) {
            $("#widthUint").attr("data-val-required", "Width unit is required");
        } else {
            $("#widthUint").removeAttr("data-val-required");
        }
    }
});
$("#depth").kendoNumericTextBox({
    change: function () {
        var value = this.value();
        if (value > 0) {
            $("#depthUint").attr("data-val-required", "Depth unit is required");
        } else {
            $("#depthUint").removeAttr("data-val-required");
        }
    }
});
$("#weight").kendoNumericTextBox({
    change: function () {
        var value = this.value();
        if (value > 0) {
            $("#weightUint").attr("data-val-required", "Weight unit is required");
        } else {
            $("#weightUint").removeAttr("data-val-required");
        }
    }
});


function runEnabledChanged(e) {
    $(e.container).find(".k-edit-buttons").html("<a class='btn btn-primary btn-sm k-grid-update' href='#'>Update</a> " +
        "<a class='btn btn-primary btn-sm k-grid-cancel' href='#'>Cancel</a>");

    var isRunEnabled = $("#RunEnabled").data("kendoDropDownList");
    var isRunEnabledValue = isRunEnabled.value();

    if (isRunEnabledValue == "true") {
        $("#Points").parent().hide();
        $("#pointsLabel").hide();
        $("#Points").removeAttr("data-val-required");
    } else {
        $("#Points").parent().show();
        $("#pointsLabel").show();
        $("#Points").attr("data-val-required", "Points is required");
    }
}

function changeLength(e) {
    var value = this.value();

    if (value > 0) {
        $("#itemHeightUnit").attr("data-val-required", "Length unit is required");
    } else {
        $("#itemHeightUnit").removeAttr("data-val-required");
    }
}

function changeOD(e) {
    var value = this.value();

    if (value > 0) {
        $("#itemOdUnit").attr("data-val-required", "OD unit is required");
    } else {
        $("#itemOdUnit").removeAttr("data-val-required");
    }
}

function changeWidth(e) {
    var value = this.value();

    if (value > 0) {
        $("#itemWidthUnit").attr("data-val-required", "Width unit is required");
    } else {
        $("#itemWidthUnit").removeAttr("data-val-required");
    }
}

function changeLength(e) {
    var value = this.value();

    if (value > 0) {
        $("#itemHeightUnit").attr("data-val-required", "Length unit is required");
    } else {
        $("#itemHeightUnit").removeAttr("data-val-required");
    }
}

function changeWeight(e) {
    var value = this.value();

    if (value > 0) {
        $("#itemWeightUnit").attr("data-val-required", "Weight unit is required");
    } else {
        $("#itemWeightUnit").removeAttr("data-val-required");
    }
}

function changeDepth(e) {
    var value = this.value();

    if (value > 0) {
        $("#itemDepthUnit").attr("data-val-required", "Depth unit is required");
    } else {
        $("#itemDepthUnit").removeAttr("data-val-required");
    }
}

$(function () {
    $("form").kendoValidator();
});

var yesNoDropDownDataSource = new kendo.data.DataSource({
    data: [{ Value: "false", Text: "No" }, { Value: "true", Text: "Yes" }]
});

function cancelRow() {
    grid = $("#equipmentCategoryMaintenanceStepGrid").data("kendoGrid");
    grid.cancelRow();
}

function editRow(element) {
    grid = $("#equipmentCategoryMaintenanceStepGrid").data("kendoGrid");
    grid.editRow($(element).closest("tr"));
}

function updateRow() {
    grid = $("#equipmentCategoryMaintenanceStepGrid").data("kendoGrid");
    grid.saveRow();
}

function deleteRow(element) {
    grid = $("#equipmentCategoryMaintenanceStepGrid").data("kendoGrid");
    grid.removeRow($(element).closest("tr"));
}

function onError(e, status) {
    if (e.status == "customerror") {
        alert(e.errors);

        var equipmentItemGrid = $("#equipmentItemGrid").data("kendoGrid");
        equipmentItemGrid.dataSource.cancelChanges();
    }
}

function onStepError(e, status) {
    if (e.status == "customerror") {
        alert(e.errors);
        var equipmentCategoryMaintenanceStepGrid = $("#equipmentCategoryMaintenanceStepGrid").data("kendoGrid");
        equipmentCategoryMaintenanceStepGrid.dataSource.cancelChanges();
    }
}

function equipmentItemMaintenanceScheduleData() {
    return {
        eId: viewModel.get("selectedEquipmentItemId")
    }
}

function scheduleDates(equipmentItemId) {
    viewModel.set("selectedEquipmentItemId", equipmentItemId);

    $("#scheduleDatesWindow").data("kendoWindow").center().open();
}

function scheduleDatesWindowOpened() {
    var equipmentItemMaintenanceScheduleGrid = $("#equipmentItemMaintenanceScheduleGrid").data("kendoGrid");
    equipmentItemMaintenanceScheduleGrid.dataSource.read();
}

function filterCompanyLocations() {
    return {
        companyId: $("#ManufacturerCompanyId").data("kendoDropDownList").value()
    };
}

function maintenanceRecordData() {
    return {
        equipmentItemId: viewModel.get("selectedEquipmentItemId")
    }
}

function maintenanceRecordCount(equipmentItemId) {
    viewModel.set("selectedEquipmentItemId", equipmentItemId);

    $("#maintenanceRecordWindow").data("kendoWindow").center().open();
}

function maintenanceRecordWindowOpened() {
    var maintenanceRecordGrid = $("#maintenanceRecordGrid").data("kendoGrid");
    maintenanceRecordGrid.dataSource.read();
}

function filterCompanies() {
    return {
        projectId: $("#ProjectId").data("kendoDropDownList").value()
    };
}

function updateEquipmentCategoryMaintenanceStepTotal() {
    var equipmentCategoryMaintenanceStepGrid = $("#equipmentCategoryMaintenanceStepGrid").data("kendoGrid");
    var totalEquipmentCategoryMaintenanceSteps = equipmentCategoryMaintenanceStepGrid.dataSource.total();
    viewModel.set("totalEquipmentCategoryMaintenanceSteps", totalEquipmentCategoryMaintenanceSteps);

    kendo.bind(document.body.children, viewModel);

}

function updateEquipmentTotals() {
    var equipmentItemGrid = $("#equipmentItemGrid").data("kendoGrid");
    var totalEquipmentItems = equipmentItemGrid.dataSource.total();
    viewModel.set("totalEquipmentItems", totalEquipmentItems);

    var equipmentData = equipmentItemGrid.dataSource.data();

    $.each(equipmentData, function (i, item) {
        if (item.MaintenanceScheduleDaysAlert) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(15)').css("color", "#E31E33");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(16)').css("color", "#E31E33");
        }
    });
}

async function equipmentItemEdit(e) {
    var equipmentCategory = viewModel.get("selectedEquipmentCategory");

    $(e.container).find(".k-edit-buttons").html("<a class='btn btn-primary btn-sm k-grid-update' href='#'>Save</a> " +
        "<a class='btn btn-primary btn-sm k-grid-cancel' href='#'>Cancel</a>");

    $("#DivisionId").data("kendoDropDownList").value(equipmentCategory.DivisionId)

    var division = equipmentCategory.DivisionName

    if (equipmentCategory.Name === 'FLI Probes' && (division === 'USA' || division === 'UK')) {
        var currency = $("#CurrencyId").data("kendoDropDownList");
        var company = $("#ManufacturerCompanyId").data("kendoDropDownList");
        var diameter = $("#OuterDiameter").data("kendoNumericTextBox");
        await currency.dataSource.read();
        await company.dataSource.read();

        diameter.value(2.000);
        $("#ImportCommodityCode").val("9033.00.90.00").change();
        $("#ExportCommodityCode").val("9033.00.90").change();
        $("#CountryOfOrigin").val(division).change();

        if (division === 'USA') {

            var usDollarCurrency = currency.dataSource._data.map(function (element) {
                if (element.Name == "US Dollar" || element.CurrencyFormat == "USD") {
                    return element.CurrencyId;
                }
            }).filter(function (value) {
                return value !== undefined;
            })[0];

            var wstOilfieldCompany = company.dataSource._data.map(function (element) {
                if (element.Name == "WST Oilfield Systems Inc") {
                    return element.CompanyId;
                }
            }).filter(function (value) {
                return value !== undefined;
            })[0];

            currency.value(usDollarCurrency);
            company.value(wstOilfieldCompany);
        }
        else if (division === 'UK') {
            var britishPoundCurrency = currency.dataSource._data.map(function (element) {
                if (element.Name == "British Pound" || element.CurrencyFormat == "GBP") {
                    return element.CurrencyId;
                }
            }).filter(function (value) {
                return value !== undefined;
            })[0];

            var wellSenseCompany = company.dataSource._data.map(function (element) {
                if (element.Name == "Well-Sense Technology Limited") {
                    return element.CompanyId;
                }
            }).filter(function (value) {
                return value !== undefined;
            })[0];

            currency.value(britishPoundCurrency);
            company.value(wellSenseCompany);
        }
        diameter.trigger("change", { value: diameter.value() });
        company.trigger("change", { value: company.value() });
        currency.trigger("change", { value: currency.value() });

        $("#ManufacturerCompanyLocationId").data("kendoDropDownList").bind("dataBound", location_dataBound);
    }
}


function equipmentData() {
    return {
        equipmentCategoryId: viewModel.get("selectedEquipmentCategory").EquipmentCategoryId
    }
}

function equipmentCreateData(e) {
    e.DivisionId = $("#DivisionId").data("kendoDropDownList").value();
    e.EquipmentCategoryId = viewModel.get("selectedEquipmentCategory").EquipmentCategoryId;
}

function equipmentCategoryData() {
    return {
        eCId: viewModel.get("selectedEquipmentCategory").EquipmentCategoryId
    }
}

function refreshEquipmentItems() {
    var equipmentItemGrid = $("#equipmentItemGrid").data("kendoGrid");
    equipmentItemGrid.dataSource.read();
}

function refreshEquipmentCategories() {
    var equipmentCategory = $("#equipmentCategoryTreeView").data("kendoTreeView");
    equipmentCategory.dataSource.read();
}

function userEquipmentCategorySelected(e) {
    if ($.DirtyForms.isDirty()) {
        var result = confirm("You have made changes to this page which are not saved. If you proceed now you will lose these changes.");

        if (!result) {
            e.preventDefault();
            var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
            var selectedEquipmentCategory = viewModel.get("selectedEquipmentCategory");
            var selectedEquipmentCategoryNode = equipmentCategoryTreeView.findByText(selectedEquipmentCategory.NewName);
            equipmentCategoryTreeView.select(selectedEquipmentCategoryNode);
        } else {
            $('form:dirty').dirtyForms('setClean');
        }
    }
}

function equipmentCategorySelected(e) {
    var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
    var node = equipmentCategoryTreeView.select();
    var selectedEquipmentCategory = equipmentCategoryTreeView.dataItem(node);
    if (selectedEquipmentCategory) {
        viewModel.set("selectedAssetItemNameWithCount", selectedEquipmentCategory.NewName)
    }


    if (selectedEquipmentCategory) {
        $.ajax({
            type: "GET",
            url: `/Admin/GetEquipmentCategory?equipmentCategorId=${selectedEquipmentCategory.EquipmentCategoryId}`,
            success: function (data) {
                $.removeCookie('equipmentCategory');
                $.cookie('equipmentCategory', selectedEquipmentCategory.EquipmentCategoryId, { expires: 7, path: '/' });
                viewModel.set("selectedEquipmentCategory", data);
                $("#equipmentItemGrid").data("kendoGrid").dataSource.read();
                $("#equipmentCategoryMaintenanceStepGrid").data("kendoGrid").dataSource.read();
            },
            dataType: "json"
        });

    }
}

function equipmentCategoryLoaded(e) {
    $(".k-treeview-item").click(function (e) {
        var equipmentCategoryTree = $("#equipmentCategoryTreeView").data("kendoTreeView");
        var equipmentCategorySelected = equipmentCategoryTree.select();

        if (equipmentCategorySelected && $(e.currentTarget).attr("date-uid") == $(equipmentCategorySelected.context).attr("data-uid")) {
            equipmentCategoryTree.select($());
        }
    });
}

function equipmentCategoryDropped(e) {
    var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
    var equipmentCategory = equipmentCategoryTreeView.dataItem(e.sourceNode);

    var parentEquipmentCategory = "";

    if (e.dropPosition == "over") {
        parentEquipmentCategory = equipmentCategoryTreeView.dataItem(e.destinationNode);
    } else {
        parentEquipmentCategory = equipmentCategoryTreeView.dataItem(equipmentCategoryTreeView.parent(e.destinationNode));
    }

    if (parentEquipmentCategory.EquipmentCategoryId != equipmentCategory.EquipmentCategoryId) {
        $.ajax({
            type: "POST",
            url: "/Admin/UpdateEquipmentCategoryParent",
            data: {
                equipmentCategoryId: equipmentCategory.EquipmentCategoryId,
                parentEquipmentCategoryId: parentEquipmentCategory ? parentEquipmentCategory.EquipmentCategoryId : ""
            },
            success: function (data) {
                viewModel.set("selectedEquipmentCategory", equipmentCategory);
            },
            dataType: "json"
        });
    }
}

function refreshEquipmentCategoryTreeViewAndLoadCategories() {
    $("#equipmentCategoryTreeView").data("kendoTreeView").dataSource.read().then( () => categoriesLoaded() )
}

function onRequestEnd(e) {
    if(e.type === "create" && !e.response.Errors) {
        refreshEquipmentCategoryTreeViewAndLoadCategories()
     }
}

function onEquipmentCategoryMaintenanceRequestEnd(e) {
    var grid = $("#equipmentCategoryMaintenanceStepGrid").data("kendoGrid");
    var data = grid.dataSource;
    if (e.type == "create" || e.type == "update") {
        data.read();
    }
}

function acceptanceClick(equipmentItemId) {
    viewModel.set("acceptEquipmentItemId", equipmentItemId);

    $("#acceptanceWindow").data("kendoWindow").center().open();
}

function refreshEquipmentCategoryMaintenanceStepGrid() {
    var equipmentCategoryMaintenanceStepGrid = $("#equipmentCategoryMaintenanceStepGrid").data("kendoGrid");
    equipmentCategoryMaintenanceStepGrid.dataSource.read();

}
function markAsActive(equipmentCategoryMaintenanceStepId) {
    $.ajax({
        type: "POST",
        url: "/Admin/ActiveMaintenanceStep",
        dataType: "json",
        data: {
            equipmentCategoryMaintenanceStepId: equipmentCategoryMaintenanceStepId
        },
        success: function () {
            refreshEquipmentCategoryMaintenanceStepGrid();
        }
    });
}
function equipmentCategoryMaintenanceStepData() {
    return {
        equipmentCategoryId: viewModel.get("selectedEquipmentCategory").EquipmentCategoryId
    }
}


function location_dataBound(e) {
    var equipmentCategory = viewModel.get("selectedEquipmentCategory");
    var location = $("#ManufacturerCompanyLocationId").data("kendoDropDownList");

    const division = equipmentCategory.DivisionName;

    if (division === 'USA') {
        var houstonLocation = e.sender.dataSource._data.map(function (element) {
            if (element.Name == "Houston") {
                return element.CompanyLocationId;
            }
        }).filter(function (value) {
            return value !== undefined;
        })[0];

        location.value(houstonLocation);
    } else if (division === 'UK') {
        var aberdeenLocation = e.sender.dataSource._data.map(function (element) {
            if (element.Name == "Aberdeen") {
                return element.CompanyLocationId;
            }
        }).filter(function (value) {
            return value !== undefined;
        })[0];

        location.value(aberdeenLocation);
    }
    location.trigger("change", { value: location.value() });
}



$("#filterText").keyup(function (e) {

    let treeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
    let filterText = e.target.value;

    if (!filterText) {
        treeView.collapse(".k-treeview-item");
    } else {
        treeView.expand(".k-treeview-item");
        treeView.dataSource.filter(
            { field: "NewName", operator: "contains", value: filterText }
        )
    }
});


var viewModel = new kendo.observable({
    selectedEquipmentCategory: "",
    selectedAssetItemNameWithCount: "All categories (0)",
    selectedEquipmentCategoryPath: [],
    totalEquipmentCategories: 0,
    totalEquipmentItems: 0,
    selectedEquipmentId: 0,
    selectedEquipmentItemId: 0,
    totalEquipmentCategoryMaintenanceSteps: 0,
    selectedMaintenanceStep: "",
    selectedMaintenanceStepRow: "",
    equipmentCategoryMaintenanceStepId: "",
    saveBtnEnabled: true,

    moveStepUp: function () {
        var selectedMaintenanceStep = this.get("selectedMaintenanceStep");

        $.ajax({
            url: "/Admin/MoveMaintenanceStepUp",
            dataType: "json",
            data: {
                equipmentCategoryMaintenanceStepId: selectedMaintenanceStep.EquipmentCategoryMaintenanceStepId
            },
            success: function () {
                refreshEquipmentCategoryMaintenanceStepGrid();
            }
        });
    },

    moveStepDown: function () {
        var selectedMaintenanceStep = this.get("selectedMaintenanceStep");

        $.ajax({
            url: "/Admin/MoveMaintenanceStepDown",
            dataType: "json",
            data: {
                equipmentCategoryMaintenanceStepId: selectedMaintenanceStep.EquipmentCategoryMaintenanceStepId
            },
            success: function () {
                refreshEquipmentCategoryMaintenanceStepGrid();
            }
        });
    },

    maintenanceStepChanged: function (e) {
        var equipmentCategoryMaintenanceStepGrid = $("#equipmentCategoryMaintenanceStepGrid").data("kendoGrid");
        var selectedRow = equipmentCategoryMaintenanceStepGrid.select();

        var maintenanceStep = equipmentCategoryMaintenanceStepGrid.dataItem(selectedRow);

        if (maintenanceStep) {
            this.set("selectedMaintenanceStep", maintenanceStep);
        }
    },

    canEditEquipmentItems: function () {
        var selectedEquipmentCategory = this.get("selectedEquipmentCategory");
        let canEditEquipmentItem = !selectedEquipmentCategory.IsTopLevelOnly && selectedEquipmentCategory.EquipmentCategoryId;
        if(!canEditEquipmentItem) {
            if($("#adminEquipmentTabStrip").data("kendoTabStrip")) {
                $("#adminEquipmentTabStrip").data("kendoTabStrip").select("#category")
            }
        }
        return canEditEquipmentItem
    },

    canSaveEquipmentCategory: function () {
        var equipmentCategory = this.get("selectedEquipmentCategory");

        return equipmentCategory.DivisionId && equipmentCategory.Name;
    },

    addEquipmentCategory: function () {
        var parentEquipmentCategory = this.get("selectedEquipmentCategory");

        this.set("selectedEquipmentCategory", {
            ParentEquipmentCategoryId: parentEquipmentCategory ? parentEquipmentCategory.EquipmentCategoryId : "",
            Name: "",
            DivisionId: "",
            Height: parentEquipmentCategory.Height,
            HeightUnit: parentEquipmentCategory.HeightUnit,
            Width: parentEquipmentCategory.Width,
            WidthUnit: parentEquipmentCategory.WidthUnit,
            Depth: parentEquipmentCategory.Depth,
            DepthUnit: parentEquipmentCategory.DepthUnit,
            Weight: parentEquipmentCategory.Weight,
            WeightUnit: parentEquipmentCategory.WeightUnit,
            OuterDiameter: parentEquipmentCategory.OuterDiameter,
            OuterDiameterUnit: parentEquipmentCategory.OuterDiameterUnit,
            PointsPerMonth: parentEquipmentCategory.PointsPerMonth,
            PointsPerMove: parentEquipmentCategory.PointsPerMove,
            PointsPerRun: parentEquipmentCategory.PointsPerRun,
            IsDangerousGoods: parentEquipmentCategory.IsDangerousGoods,
            IsTopLevelOnly: parentEquipmentCategory.IsTopLevelOnly,
            DisplayInOpportunity: parentEquipmentCategory.DisplayInOpportunity,
        });

        this.set("saveEnabled", false);

        refreshEquipmentItems();

        $('#equipmentCategoryTabs a[href="#category"]').tab('show');
    },
    saveEquipmentCategory: function () {
        var equipmentCategory = this.get("selectedEquipmentCategory");

        if (equipmentCategory) {
            $.ajax({
                type: "POST",
                url: "/Admin/UpdateEquipmentCategory",
                traditional: true,
                data: {
                    EquipmentCategoryId: equipmentCategory.EquipmentCategoryId,
                    ParentEquipmentCategoryId: equipmentCategory.ParentEquipmentCategoryId,
                    Name: equipmentCategory.Name,
                    Details: equipmentCategory.Details,
                    DivisionId: equipmentCategory.DivisionId && equipmentCategory.DivisionId.DivisionId ? equipmentCategory.DivisionId.DivisionId : equipmentCategory.DivisionId,
                    Height: equipmentCategory.Height,
                    HeightUnit: equipmentCategory.HeightUnit,
                    Width: equipmentCategory.Width,
                    WidthUnit: equipmentCategory.WidthUnit,
                    Depth: equipmentCategory.Depth,
                    DepthUnit: equipmentCategory.DepthUnit,
                    Weight: equipmentCategory.Weight,
                    WeightUnit: equipmentCategory.WeightUnit,
                    OuterDiameter: equipmentCategory.OuterDiameter,
                    OuterDiameterUnit: equipmentCategory.OuterDiameterUnit,
                    PointsPerMonth: equipmentCategory.PointsPerMonth,
                    PointsPerMove: equipmentCategory.PointsPerMove,
                    PointsPerRun: equipmentCategory.PointsPerRun,
                    IsDangerousGoods: equipmentCategory.IsDangerousGoods,
                    IsTopLevelOnly: equipmentCategory.IsTopLevelOnly,
                    DisplayInOpportunity: equipmentCategory.DisplayInOpportunity,
                },
                success: function (result) {
                    $('form:dirty').dirtyForms('setClean');
                    if (result) {
                        equipmentCategory.EquipmentCategoryId = result.id;
                        viewModel.set("selectedEquipmentCategory", equipmentCategory);
                    }

                    refreshEquipmentCategories();
                    refreshEquipmentItems();
                },
                dataType: "json"
            });
        }
    },

    newEquipmentVisible: function () {
        var equipmentCategory = this.get("selectedEquipmentCategory");

        return !equipmentCategory.EquipmentCategoryId;
    },
    canDelete: function () {
        var equipmentCategory = this.get("selectedEquipmentCategory");
        return equipmentCategory && !equipmentCategory.HasChildren && equipmentCategory.EquipmentCategoryId;
    },
    deleteItem: function (e) {
        e.preventDefault();
        var equipmentCategory = this.get("selectedEquipmentCategory");

        if (equipmentCategory) {
            var confirmDelete = confirm("Are you sure you wish to delete Equipment Category '" + equipmentCategory.Name + "'?");

            if (confirmDelete) {
                $.ajax({
                    type: "POST",
                    url: "/Admin/DeleteEquipmentCategory",
                    data: {
                        equipmentCategoryId: equipmentCategory.EquipmentCategoryId,
                    },
                    success: function (result) {
                        window.location.reload();
                    },
                    dataType: "json"
                });
            }
        }
    },
    expandAll: function () {
        var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
        equipmentCategoryTreeView.expand(".k-treeview-item");

    },
    collapseAll: function () {
        var equipmentCategoryTreeView = $("#equipmentCategoryTreeView").data("kendoTreeView");
        equipmentCategoryTreeView.collapse(".k-treeview-item");
    },
    categorySaveEnabled: function () {

        if (this.get("selectedEquipmentCategory.DivisionId") !== "" &&
            this.get("selectedEquipmentCategory.Name") !== "") {
            return true;
        } else {
            return false;
        }

    }
});

kendo.bind(document.body.children, viewModel);
$(function () {
    $("#createAssetButton").click(function (e) {
        $("#createAssetWindow").data('kendoWindow').open();
    });
});

function showExportLoading(element) {
    try {
        var btnText = element.querySelector('#exportBtnText');
        var btnIcon = element.querySelector('i');

        if (!btnText || !btnIcon) {
            console.error('Export button elements not found');
            return true; // Allow the link to proceed
        }

        // Check if there's already an active export job
        var activeJobId = localStorage.getItem('activeExportJobId');
        if (activeJobId) {
            // Resume tracking existing job
            resumeExportTracking(activeJobId, btnText, btnIcon, element);
            return false;
        }

        // Show loading state
        element.classList.add('disabled');
        element.style.pointerEvents = 'none';
        btnIcon.className = 'fas fa-spinner fa-spin';
        btnText.textContent = 'Starting export...';

        // Function to reset button state
        function resetButton() {
            try {
                element.classList.remove('disabled');
                element.style.pointerEvents = 'auto';
                btnIcon.className = 'fas fa-download';
                btnText.textContent = 'Export All Assets';
                localStorage.removeItem('activeExportJobId');
            } catch (resetError) {
                console.error('Error resetting button state:', resetError);
            }
        }

        // Start the background export job
        startExportJob(btnText, btnIcon, resetButton);
        return false; // Prevent the default link behavior
    } catch (error) {
        console.error('Error in showExportLoading:', error);
        return true; // Allow the link to proceed on error
    }
}

function startExportJob(btnText, btnIcon, resetButton) {
    fetch('/Admin/ExportAllAssets', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.jobId) {
            // Store job ID for navigation persistence
            localStorage.setItem('activeExportJobId', data.jobId);
            // Start polling for job status
            pollExportJobStatus(data.jobId, btnText, btnIcon, resetButton);
        } else {
            console.error('No job ID received');
            resetButton();
        }
    })
    .catch(error => {
        console.error('Error starting export job:', error);
        resetButton();
    });
}

function pollExportJobStatus(jobId, btnText, btnIcon, resetButton) {
    var pollInterval = setInterval(function() {
        fetch('/Admin/ExportAllAssets?jobId=' + jobId)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    console.error('Export job error:', data.error);
                    clearInterval(pollInterval);
                    resetButton();
                    return;
                }

                // Update progress display
                if (data.totalItems > 0) {
                    btnText.textContent = `Exporting... ${data.processedItems}/${data.totalItems} (${data.progressPercentage}%)`;
                }

                if (data.status === 'completed') {
                    clearInterval(pollInterval);
                    localStorage.removeItem('activeExportJobId');

                    // Show download ready state
                    btnIcon.className = 'fas fa-check';
                    btnText.textContent = 'Download Ready';

                    // Trigger download
                    if (data.downloadUrl) {
                        window.location.href = data.downloadUrl;
                    }

                    // Reset button after a short delay
                    setTimeout(resetButton, 3000);
                } else if (data.status === 'failed') {
                    clearInterval(pollInterval);
                    localStorage.removeItem('activeExportJobId');
                    btnIcon.className = 'fas fa-exclamation-triangle';
                    btnText.textContent = 'Export Failed';
                    console.error('Export failed:', data.errorMessage);
                    setTimeout(resetButton, 5000);
                }
            })
            .catch(error => {
                console.error('Error polling export status:', error);
                clearInterval(pollInterval);
                resetButton();
            });
    }, 2000); // Poll every 2 seconds

    // Fallback timeout after 10 minutes
    setTimeout(function() {
        clearInterval(pollInterval);
        resetButton();
    }, 600000); // 10 minutes maximum
}

function resumeExportTracking(jobId, btnText, btnIcon, element) {
    // Set button to loading state
    element.classList.add('disabled');
    element.style.pointerEvents = 'none';
    btnIcon.className = 'fas fa-spinner fa-spin';
    btnText.textContent = 'Resuming export...';

    // Function to reset button state
    function resetButton() {
        try {
            element.classList.remove('disabled');
            element.style.pointerEvents = 'auto';
            btnIcon.className = 'fas fa-download';
            btnText.textContent = 'Export All Assets';
            localStorage.removeItem('activeExportJobId');
        } catch (resetError) {
            console.error('Error resetting button state:', resetError);
        }
    }

    // Resume polling
    pollExportJobStatus(jobId, btnText, btnIcon, resetButton);
}

// Check for active export job on page load
$(document).ready(function() {
    var activeJobId = localStorage.getItem('activeExportJobId');
    if (activeJobId) {
        var exportBtn = document.getElementById('exportAllAssetsBtn');
        if (exportBtn) {
            var btnText = exportBtn.querySelector('#exportBtnText');
            var btnIcon = exportBtn.querySelector('i');
            if (btnText && btnIcon) {
                resumeExportTracking(activeJobId, btnText, btnIcon, exportBtn);
            }
        }
    }
});

$("#createEquipmentItem").click(function () {
    if ($('#createAssetsDetails').kendoValidator().data('kendoValidator').validate()) {
        $.ajax({
            type: 'POST',
            dataType: 'json',
            traditional: true,
            url: '/Admin/CreateEquipmentItem',
            data: {
                equipmentNumber: $("#EquipmentNumber").val(),
                equipmentCategoryId: viewModel.get("selectedEquipmentCategory").EquipmentCategoryId,
                equipmentInfo: $("#EquipmentInfo").val(),
                divisionId: $("#DivisionId").val(),
                currentCompanyId: $("#CurrentCompanyId").val(),
                currentClientLocationName: $("#CurrentClientLocationName").val(),
                currentCompanyName: $("#CurrentCompanyName").val(),
                currentCompanyLocationId: $("#CurrentCompanyLocationId").val(),
                currentCompanyLocationCompanyName: $("#currentCompanyLocationCompanyName").val(),
                currentCompanyLocationName: $("#CurrentCompanyLocationName").val(),
                countryOfOrigin: $("#CountryOfOrigin").val(),
                commodityCode: $("#CommodityCode").val(),
                exportcommodityCode: $("#ExportCommodityCode").val(),
                importcommodityCode: $("#ImportCommodityCode").val(),
                ushtsCommodityCode: $("#USHTSCommodityCode").val(),
                waiverCode: $("#WaiverCode").val(),
                waiverRequirement: $("#WaiverRequirement").val(),
                manufacturerCompanyId: $("#ManufacturerCompanyId").val(),
                manufacturerCompanyLocationId: $("#ManufacturerCompanyLocationId").val(),
                depreciatedPrice: $("#DepreciatedPrice").val(),
                currencyId: $("#CurrencyId").val(),
                retestDate: $("#RetestDate").val(),
                price: $("#Price").val(),
                trackedNonAssetItem: $("#TrackedNonAssetItem").val(),
                purchasedDate: $("#PurchasedDate").val(),
                internalInvoiceNumber: $("#InternalInvoiceNumber").val(),
                receivedDate: $("#ReceivedDate").val(),
                externalInvoiceNumber: $("#ExternalInvoiceNumber").val(),
                height: $("#Height").val(),
                heightUnit: $("#itemHeightUnit").val(),
                outerDiameter: $("#OuterDiameter").val(),
                outerDiameterUnit: $("#itemOdUnit").val(),
                width: $("#Width").val(),
                widthUnit: $("#itemWidthUnit").val(),
                weight: $("#Weight").val(),
                weightUnit: $("#itemWeightUnit").val(),
                depth: $("#Depth").val(),
                depthUnit: $("#itemDepthUnit").val(),
                pointsPerMonth: $("#PointsPerMonth").val(),
                points: $("#Points").val(),
                pointsPerMove: $("#PointsPerMove").val(),
                pointsPerRun: $("#PointsPerRun").val(),
                h2sCo20to5: $("#H2sCo20to5").val(),
                h2sCo25to10: $("#H2sCo25to10").val(),
                h2sCo210to15: $("#H2sCo210to15").val(),
                h2sCo215Greater: $("#H2sCo215Greater").val(),
                status: $("#Status").val(),
                equipmentCategoryName: $("#EquipmentCategoryName").val(),
                equipmentItemName: $("#EquipmentItemName").val(),
                acceptanceUser: $("#AcceptanceUser").val(),
                acceptanceCreated: $("#AcceptanceCreated").val(),
                comments: $("#Comments").val(),
                depreciationYears: $("#DepreciationYears").val(),
                currentEquipmentItemLocation: $("#CurrentEquipmentItemLocation").val(),
                isTransit: $("#IsTransit").val(),
                isReserved: $("#IsReserved").val(),
                shipmentTransit: $("#ShipmentTransit").val(),
                points: $("#Points").val(),
                projectId: $("#ProjectId").val(),
                isSelected: $("#IsSelected").val(),
                pointsUpdated: $("#PointsUpdated").val(),
                equipmentPackingListProjectId: $("#EquipmentPackingListProjectId").val(),
                serialNumber: $("#SerialNumber").val()
            },
            success: function (e) {
                updateMessage($("#EquipmentNumber").val());
                $("#createAssetWindow").data('kendoWindow').close();
                window.location.reload();
            },
            error: function (e) {
                jqXHRErrors(e);
            }
        });
    } else {
        scrolToFirstError();
    }

})