﻿@model ProjectModel

@Html.Partial( "_GridNotification", EntityType.Project)
@if (Model.ProjectId.HasValue && Model.Status == Centerpoint.Common.Constants.ProjectStatusConstant.Active) {
    <div class="alert alert-warning alert-dismissible" data-bind="invisible:crewOut" role="alert">
        <button type="button" class="close" data-bs-dismiss="alert">×</button>
        <strong>Warning!</strong> Project cannot be closed if the crew is still on-site.
    </div>
}
@if (!Model.AllJobsInvoiced && Model.Status == Centerpoint.Common.Constants.ProjectStatusConstant.Active) {
    <div class="alert alert-dismissible alert-warning">
        <button type="button" class="close" data-bs-dismiss="alert">×</button>
        <strong>Warning!</strong> Project cannot be closed if the Job Invoice Number is not provided.
    </div>
}
<div class="header-container-between">
    <div>
        @if (!Model.ProjectId.HasValue) {
            <h4>
                <i class="fa-solid fa-circle-plus"></i>
                Create New Project
            </h4>
        } 
        @if (Model.Status == Centerpoint.Common.Constants.ProjectStatusConstant.Opportunity) {
            <h4>
                <i class="fa fa-lightbulb"></i>
                    @(Model.ProjectId.HasValue ? Model.ProjectNameandObjectives : "Create New Project")
            </h4>
        }
        @if (Model.Status == Centerpoint.Common.Constants.ProjectStatusConstant.Active) {
            <h4>
                <i class="fa fa-caret-square-up"></i>
                @(Model.ProjectId.HasValue ? Model.ProjectNameandObjectives : "Create New Project")
            </h4>
        }
        @if (Model.Status == Centerpoint.Common.Constants.ProjectStatusConstant.Closed) {
            <h4>
                <i class=" fa fa-archive"></i>
                @(Model.ProjectId.HasValue ? Model.ProjectNameandObjectives : "Create New Project")
            </h4>
        }
        @if (Model.Status == Centerpoint.Common.Constants.ProjectStatusConstant.Lost) {
            <h4>
                <i class="fa fa-caret-square-down"></i>
                @(Model.ProjectId.HasValue ? Model.ProjectNameandObjectives : "Create New Project")
            </h4>
        }
    </div>
</div>
<br />
<div class="d-flex actionsContainer">
    @if (Model.ProjectId.HasValue && !GlobalSettings.IsAisus && Model.Status == Centerpoint.Common.Constants.ProjectStatusConstant.Active && (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Model.IsAssociatedEngineer) || Html.IsSeniorUSEngineer())
    {
        <a class="btn btn-primary btn-sm" href="@Url.Action("AddJob", "Operation", new { @id = Model.ProjectId, @companyId = Model.CompanyId })">
            <i class="fa fa-plus"></i>
            Create New Job
        </a>
    }
    @if (Model.ProjectId.HasValue && Model.Status == Centerpoint.Common.Constants.ProjectStatusConstant.Opportunity && (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsSeniorUSEngineer()))
    {
        <a class="btn btn-danger btn-sm" href="@Url.Action("UpdateStatus","Operation",new { @id = Model.ProjectId, @status = ProjectStatusConstant.Lost } )">
            <i class="fa fa-thumbs-down"></i>
            Mark as Lost
        </a>
    }
    @if (Model.ProjectId.HasValue && Model.Status == Centerpoint.Common.Constants.ProjectStatusConstant.Opportunity && (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsSeniorUSEngineer() || Model.IsAssociatedEngineer))
    {
        <a class="btn btn-success btn-sm" href="@Url.Action("UpdateStatus", "Operation", new { @id = Model.ProjectId, @status = ProjectStatusConstant.Active })">
            <i class="fa fa-thumbs-up"></i>Activate
        </a>
    } else if (Model.ProjectId.HasValue && Model.Status == Centerpoint.Common.Constants.ProjectStatusConstant.Opportunity && !Html.IsGlobalAdmin() && !Html.IsOperationAdmin())
    {
        <a class="btn btn-success btn-sm" data-bind="visible:isTransit" style="margin-right: 10px; margin-top: 10px" href="@Url.Action("UpdateStatus", "Operation", new { @id = Model.ProjectId, @status = ProjectStatusConstant.Active })">
            <i class="fa fa-thumbs-up"></i>
            Activate
        </a>
    }
    @if (Model.ProjectId.HasValue && Model.Status == Centerpoint.Common.Constants.ProjectStatusConstant.Active && Model.AllJobsInvoiced && (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsLogisticsAdmin() || Html.IsSeniorFieldEngineer() || Html.IsJuniorFieldEngineer() || Html.IsFieldEngineer()))
    {
        <a class="btn btn-success btn-sm" data-bind="visible:crewOut" href="@Url.Action("UpdateStatus", "Operation", new { @id = Model.ProjectId, @status = ProjectStatusConstant.Closed })">
            <i class="fa fa-check"></i>
            Mark as Closed
        </a>
    }
    @if (Model.ProjectId.HasValue && Model.Status == Centerpoint.Common.Constants.ProjectStatusConstant.Closed && (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsLogisticsAdmin() || Html.IsSeniorFieldEngineer() || Html.IsJuniorFieldEngineer() || Html.IsFieldEngineer()))
    {
        <a class="btn btn-success btn-sm" href="@Url.Action("UpdateStatus", "Operation", new { @id = Model.ProjectId, @status = ProjectStatusConstant.Active })">
            <i class="fa fa-thumbs-up"></i>
            Re-Open
        </a>
    }
    @if (Model.ProjectId.HasValue && Model.Status == Centerpoint.Common.Constants.ProjectStatusConstant.Lost && (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsLogisticsAdmin() || Html.IsSeniorFieldEngineer() || Html.IsJuniorFieldEngineer() || Html.IsFieldEngineer()))
    {
        <a class="btn btn-success btn-sm" href="@Url.Action("UpdateStatus", "Operation", new { @id = Model.ProjectId, @status = ProjectStatusConstant.Opportunity })">
            <i class="fa fa-thumbs-up"></i>
            Re-Open
        </a>
    }
    @if (!Model.HasJobs && Model.ProjectId.HasValue && (Html.IsGlobalAdmin() || Html.IsOperationAdmin() || Html.IsLogisticsAdmin() || Html.IsSeniorFieldEngineer() || Html.IsJuniorFieldEngineer() || Html.IsFieldEngineer()))
    {
        <a class="btn btn-danger btn-sm" href="#" data-bind="click:deleteProject, invisible:totalProjectEquipmentItems">
            <i class="fa fa-ban"></i>
            Delete Project
        </a>
    }
    <a class="btn btn-info btn-sm" href="@Url.Action("Index","Operation",new { @id = Model.ProjectId} )">
        <i class="fa fa-refresh"></i>
        Return to Operations Dashboard
    </a>
    @if (Html.IsLogisticsAdmin() || Html.IsGlobalAdmin())
    {
        <a class="btn btn-primary btn-sm" id="currentLocationChangeButton" href="#" data-bind="click: showShipmentToLocationWindow, visible:totalProjectEquipmentItems">
            <i class="fa fa-refresh"></i>
            Create Backload Shipment
        </a>
    }
</div>

<hr />


    @* @Html.ValidationSummary(false) *@
    @(Html.Kendo().TabStrip()
        .Name("addEditTabStrip")
        .Events( events => events
          .Activate("onActivate")
          )
        .SelectedIndex(0)
        .Animation(animation =>
        {
            animation.Enable(false);
        })
        .Items(tabstrip =>
        {
            tabstrip.Add().Text("")
                .HtmlAttributes(new { @data_bind="html:detailsText"})
                .Selected(true)
                .Content(@<text>
                    <form id="editProjectForm">
                        <partial name="Details" />
                    </form>
                        </text>);

            if (Model.ProjectId.HasValue) {
                tabstrip.Add().Text("")
                    .HtmlAttributes(new { @data_bind="html:totalCrewsText", @id="CrewTab"})
                    .Content(@<text>
                            <partial name="Crew"/>
                            </text>);

                tabstrip.Add().Text("")
                    .HtmlAttributes(new { @data_bind="html:totalProjectCommentsText"})
                    .Content(@<text>
                            <partial name="CommentsGrid"/>
                            </text>);
                if (Model.OppsProjectId) {
                    tabstrip.Add().Text("")
                        .HtmlAttributes(new { @data_bind="html:totalWellsText"})
                        .Content(@<text>
                                <partial name="WellsGrid"/>
                                </text>);
                }

                tabstrip.Add().Text("")
                    .HtmlAttributes(new { @data_bind="html:totalDocumentsText", @id="AttachmentsTab"})
                    .Content(@<text>
                            <partial name="Attachments" />
                            </text>);

                tabstrip.Add().Text("")
                    .HtmlAttributes(new { @data_bind="html:totalProjectLessonsText"})
                    .Content(@<text>
                            <partial name="Lessons"/>
                            </text>);

                tabstrip.Add().Text("logs")
                    .HtmlAttributes(new { @data_bind="html:totalProjectLogsText"})
                    .Content(@<text>
                        <div class="grid-container-standard">
                           @(Html.Kendo().Grid<ProjectLogModel>()
                            .Name("projectLogGrid")
                            .Columns(columns => {
                                columns.Bound(c => c.Log).ClientTemplate("#=Log#");
                                columns.Bound(c => c.UserName).Width(125);
                                columns.Bound(c => c.Date).Format(DateConstants.DateTimeFormat).Width(150);
                            })
                            .Events(e => e.DataBound("updateTotalLogs"))
                            .Sortable()
                            .Groupable()
                            .Reorderable(reorder => reorder.Columns(true))
                            .Resizable(resize => resize.Columns(true))
                            .Scrollable()
                            .DataSource(dataSource => dataSource
                                .Ajax()
                                .ServerOperation(false)
                                .Read(read => read.Action("GetProjectLogByProjectId", "Operation").Data("projectData"))
                            )
                            ) 
                        </div>
                             </text>);

                tabstrip.Add().Text("")
                    .HtmlAttributes(new { @data_bind="html:totalProjectShipmentsText, visible:totalProjectShipments", @id="ShipmentGirdTab"})
                    .Content(@<text>
                            <partial name="ShipmentGird"/>
                            </text>);

                tabstrip.Add().Text("")
                    .HtmlAttributes(new { @data_bind="html:totalBackloadProjectShipmentsText, visible:totalBackloadProjectShipments"})
                    .Content(@<text>
                            <partial name="BackloadShipmentsGrid"/>
                             </text>);

                if (Model.Status != ProjectStatusConstant.Opportunity)
                {

                  tabstrip.Add().Text("")
                    .HtmlAttributes(new { @data_bind="html:totalEquipmentItemsText", @id="EquipmentItemGridTab"})
                    .Content(@<text>
                            <partial name="EquipmentItemGrid"/>
                            </text>);
                
                  tabstrip.Add().Text("")
                    .HtmlAttributes(new { @data_bind="html:totalEquipmentShipmentNonAssetItemsText"})
                    .Content(@<text>
                            <partial name="NonAssetItemsGrid"/>
                            </text>);   
                }
            }
        })
    )
    @Html.HiddenFor(m => m.ProjectId)
    @Html.HiddenFor(m => m.UserId)
    @Html.HiddenFor(m => m.OpportunityId)
    @Html.HiddenFor(m => m.ParentOpportunityId)
    @Html.HiddenFor(m => m.OppsCompanyName)
    @Html.HiddenFor(m => m.CompanyLocationName)
    @Html.HiddenFor(m => m.FirstNameLastName)
    @Html.HiddenFor(m => m.BaseCompanyLocationId)





@(Html.Kendo().Window().Name("toLocationWindow")
    .Title(Model.ProjectName + " - Backload Shipment")
    .Events( e => e.Close("onWindowClose"))
    .Content(@<text>
            <partial name="ShipmentBackload"/>
    </text>)
    .Width(650)
    .Modal(true)
    .Draggable()
    .Visible(false)
)

@(Html.Kendo().Window()
    .Name("wellAttachmentWindow")
    .Width(1000)
    .Title("Well Attachments")
    .Visible(false)
    .Modal(true)
    .Events(e => e.Open("wellAttachmentWindowOpened"))
    .Content(@<text>
            @(Html.Kendo().Grid<CompanyWellDocumentModel>()
            .Name("companyWellDocumentsGrid")
            .Columns(c => {
                c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                c.Bound(p => p.CompanyWellDocumentTypeDescription).Title("Type").ClientTemplate("#=CompanyWellDocumentTypeDescription ? CompanyWellDocumentTypeDescription : 'N/A'#");
                c.Bound(p => p.Created).Format(DateConstants.DateFormat).Title("Created");
                c.Bound(p => p.Username).Title("Created By");
            })
            .Sortable()
            .Resizable(r => r.Columns(true))
            .ColumnMenu(c => c.Columns(true))
            .Filterable()
            .Groupable()
            .Scrollable(s => s.Height(300))
            .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Model(model => model.Id(p => p.CompanyWellDocumentId))
                .Read(read => read.Action("GetCompanyWellDocumentsByWellId", "Sales").Data("companyWellData"))
            ).AutoBind(false)
            )
    </text>)
)

@(Html.Kendo().Window()
    .Name("addCompanyLocationWindow")
    .Title("Add Location")
    .Content(
        @<text>
            <partial name="AddCompanyLocation"/>
    </text>
    )
    .Width(750)
    .Modal(true)
    .Draggable()
    .Visible(false)
)
@(Html.Kendo().Window()
    .Name("addCompanyWindow")
    .Title("Add Company")
    .Content(
        @<text>
            <partial name="AddCompanyView" />
        </text>
    )
    .Width(750)
    .Modal(true)
    .Draggable()
    .Visible(false)
)

@(Html.Kendo().Window()
    .Name("addCompanyContactWindow")
    .Title("Add Contact")
    .Content(
        @<text>
            <partial name="AddCompanyContact"/>
    </text>
    )
    .Width(750)
    .Modal(true)
    .Draggable()
    .Visible(false)
)


@(Html.Kendo().Window()
    .Name("addWellWindow")
    .Events( e => e.Close("onWindowClose"))
    .Title("Add Well")
    .Content(@<text>
            <partial name="EditCompanyWell"/>
    </text>)
    .Width(750)
    .Modal(true)
    .Draggable()
    .Visible(false)
)
@(Html.Kendo().Window()
    .Name("addFieldWindow")
    .Events(e => e.Close("onWindowClose"))
    .Title("Add field")
    .Content(@<text>
        <partial name="EditCompanyField" />
    </text>)
    .Width(750)
    .Modal(true)
    .Draggable()
    .Visible(false)
)
<script>
    const editProjectModel = {
        projectId: "@(Model.ProjectId.HasValue ? Model.ProjectId : null)",
        companyId: "@Model.CompanyId",
        partnerCompanyId: @(Model.PartnerCompanyId.HasValue ? Model.PartnerCompanyId : 0),
        customerCompanyId: @(Model.CustomerCompanyId.HasValue ? Model.CustomerCompanyId : 0),
        oppsProjectId: @Model.OppsProjectId.ToString().ToLower(),
        oppsCompanyId: @(Model.OppsCompanyId.HasValue ? Model.OppsCompanyId : 0),
        oppsPartnerCompanyId: @(Model.OppsPartnerCompanyId.HasValue ? Model.OppsPartnerCompanyId : 0),
        oppsCustomerCompanyId: @(Model.OppsCustomerCompanyId.HasValue ? Model.OppsCustomerCompanyId : 0),
        name: "@(!String.IsNullOrEmpty(Model.Name)? Model.Name  : " ")",
        divisionName: "@(!String.IsNullOrEmpty(Model.DivisionName)? Model.DivisionName : String.Empty)", 
        isOther: @(Model.IsOther ? "true" : "false"),
        hasJobs: @(Model.HasJobs ? "true" : "false"),
        projectShipmentSent: @(Model.ProjectShipmentSent ? "true" : "false"),
        status: {
            received: "@EquipmentShipmentStatusConstant.Received.ToString().ToLower()"
        },
        companyFieldIds: @Json.Serialize(Model.OppsFieldIds),
        companyWellIds: @Html.Raw(Json.Serialize(Model.CompanyWellIds ?? Array.Empty<int>())),

        projectCompanyFieldIds: @Json.Serialize(Model.CompanyFieldIds),
        projectCompanyWellIds: @Json.Serialize(Model.CompanyWellIds),
        inboundCount: "@Model.ShipmentCount",
        equipmentItemCount: "@Model.EquipmentItemCount",
        attachmentsCount: "@Model.AttachmentsCount",
        crewCount: "@Model.CrewCount",
        currentCompanyLocationId: @(Model.CompanyLocationId.HasValue ? Model.CompanyLocationId:0),
    }
</script>

<script type="text/x-kendo-template" id="projectCommentGrid">
    #if (messages.length) { #
    <li>
        #=field#
        <ul>
            # for (var i = 0; i < messages.length; ++i) { #
            <li>#= messages[i] #</li>
            # } #
        </ul>
    </li>
    # } #
</script>
<script type="text/x-kendo-tmpl" id="jobTemplate">
    <div class="container-fluid">
        <div class="row mt-4">
            <div class="col-md-5">
                #if(LatestJobUpdate){#
                    <div class="mb-4">
                        <h5 class="text-primary">
                            <i class="fa fa-clock"></i>
                            Latest Update - #=kendo.toString(parseDate(LatestJobUpdate.Date, 'yyyy-MM-dd'),'dd-MMM-yyyy HH:mm')# (Centerpoint Time)
                        </h5>
                        <p>#=LatestJobUpdate.Log# by <span class="text-primary">#=LatestJobUpdate.UserName#</span></p>
                    </div>
                #} else {#
                    <div class="mb-4">
                        <h5 class="text-primary">
                            <i class="fa fa-clock"></i>
                            Latest Update
                        </h5>
                        <p>No recent updates found.</p>
                    </div>
                #}#
                <div>
                    <div class="mb-4">
                        <h5 class="text-primary">
                            <i class="fa fa-usd"></i>
                            Invoice Number
                        </h5>
                        <p>#=InvoiceNumber ? InvoiceNumber : 'No Invoice Number'#</p>
                    </div> 
                    <div class="mb-4">
                        <h5 class="text-primary"">
                            <i class="fa fa-users"></i>
                            Crews
                        </h5>
                        <p>#=Crews ? Crews : 'No crew assigned'#</p>
                    </div>  
                    <div class="mb-4">
                        <h5 class="text-primary">
                            <i class="fa fa-cog"></i>
                            #=RunCount# #=RunCount == 1 ? 'Run' : 'Runs'#
                        </h5>
                        #if(Runs && Runs.length > 0){
                            for (var i = 0;i < Runs.length; i++){#
                                <p>
                                    <a class="text-primary" href="@Url.Action("EditRun", "Operation")/#=Runs[i].RunId#">#=Runs[i].RunName#</a>
                                </p>
                                <p>
                                    <span>Logged Depth:</span> 
                                    #=Runs[i].LoggedDepth#, 
                                    <span>Total Time:</span>
                                    #=Runs[i].TotalTime#, 
                                    <span>Total Lost Time:</span> #=Runs[i].TotalLostTime ? Runs[i].TotalLostTime :'No Lost Time'#
                                </p>
                        #}} else {#
                            <p>No runs found.</p>
                        #}#
                    </div>                     
                    #if(OppsName){#
                        <p>
                            <a class="text-primary" href="@Url.Action("Edit", "Sales")/#=LinkOpportunityId#?revision=#=OppsMaxRevision#"><i class="fa fa-usd"></i> #=OppsName#</a>
                        </p>
                    #}#
                </div>
            </div>
            <div class="col-md-6">
                <h5 class="mb-3">
                    <a href="@Url.Action("EditJob", "Operation")/#=JobId#?tab=comments">Job Comments</a>
                </h5>
                #if(RecentComments && RecentComments.length > 0){
                    for (var i =0;i < RecentComments.length;i++){#
                    <div class="mb-5"> 
                        <p class="mb-2">
                            <a href="@Url.Action("EditJob", "Operation")/#=RecentComments[i].JobId#?tab=comments">
                                <i class="fa fa-comment"></i>
                                #=RecentComments[i].JobName#
                            </a> 
                            <span>
                                #=kendo.toString(kendo.parseDate(RecentComments[i].Date, 'yyyy-MM-dd'),'dd-MMM-yyyy HH:mm')#
                            </span>
                        </p>
                        <p class="mb-2">
                            <i>"#=RecentComments[i].TrimmedComment#"</i> - 
                            <span class="text-primary">
                                <i class="fa fa-user"></i>
                                #=RecentComments[i].UserName#
                            </span>
                        </p>
                    </div>
                #}} else {#
                <p class="mb-2">No job comments found.</p>
                #}#
            </div>
        </div>
        <div class="d-flex justify-content-end">
            <a class="btn btn-sm btn-primary mb-2" href="@Url.Action("EditJob", "Operation")/#=JobId#">
                <i class="fa fa-folder-open"></i> Open Job
            </a>
        </div>
    </div>
</script>

<environment include="Development">
    <script src="~/js/views/operation/editProject.js" asp-append-version="true"></script>
</environment>
<environment exclude="Development">
    <script src="~/js/views/operation/editProject.min.js" asp-append-version="true"></script>
</environment>
