﻿@model CurrencyModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fas fa-money-bill"></i>
        Currencies
        (<span data-bind="text:totalCurrencies"></span>)
    </h4>
</div>
<hr />

<div class="grid-container">
    @(Html.Kendo().Grid<CurrencyModel>()
        .Name("currencyGrid")
        .Columns(c =>
        {
            c.<PERSON>(p => p.Name);
            c.<PERSON>und(p => p.Multiple).Format("{0:n4}");
            c.Bound(p => p.Format);
            c.Command(p=>{
                p.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                p.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"});
            }).Width(200);
        })
        .Editable(editable => editable.Mode(GridEditMode.InLine))
        .ToolBar(t => {
            t.Create().Text("Add Currency");
            t.Excel().Text("Export");
        }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
        .Sortable()
        .Filterable()
        .Scrollable()
        .Resizable(c => c.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .Events(e => e.DataBound("updateCurrencyTotal"))
        .Excel(excel => excel
            .FileName(string.Format("Centerpoint_Currencies_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Admin"))
        )
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(m => m.Id(p => p.CurrencyId))
            .Events(e => e.Error("onError"))
            .Read(read => read.Action("GetCurrencies", "Admin"))
            .Create(create => create.Action("UpdateCurrency", "Admin"))
            .Update(update => update.Action("UpdateCurrency", "Admin"))
            .Destroy(destroy => destroy.Action("DeleteCurrency", "Admin"))
        )
    )
</div>


    <script>

        function updateCurrencyTotal() {
            var currencyGrid = $("#currencyGrid").data("kendoGrid");
            var totalCurrencies = currencyGrid.dataSource.total();
            viewModel.set("totalCurrencies", totalCurrencies);
        }

        function onError(e, status) {
            if (e.status == "customerror") {
                alert(e.errors);

                var currencyGrid = $("#currencyGrid").data("kendoGrid");
                currencyGrid.dataSource.cancelChanges();
            }
        }

        var viewModel = new kendo.observable({
            totalCurrencies: 0
        });

        kendo.bind(document.body.children, viewModel);
    </script>
