﻿@model PersonnelCertificateDocumentModel
<div>
    <div class="row">
        <p>Click the link below to attach certificate</p>
        @(Html.Kendo().Upload()
          .Name("personnelCertificateAttachmentDocuments")
          .Messages(m => m.Select("Attach Certificate"))
          .Events(e => e.Success("onPersonnelCertificateAttached").Complete("onPersonnelCertificateDocumentComplete").Upload("onPersonnelCertificateDocumentUpload"))
          .Async(async => async
              .Save("AttachPersonnelCertificateDocuments", "Admin" ,new { @userId = Model.UserId })
              .AutoUpload(true)))
    </div>
    <hr />
    <div class="row">
        <div class="col-md-5">
                <div class="form-group">
                    <label>Category</label>
                    <br />
                    @(Html.Kendo().DropDownListFor(m => m.CertificateCategoryId)
                    .DataTextField("Name")
                    .DataValueField("CertificateCategoryId")
                    .Filter("contains")
                    .OptionLabel("Select Category")
                    .DataSource(d => d.Read("GetCertificateCategories", "Lookup"))
                    .HtmlAttributes(new { @data_value_primitive = "true", @style = "width:250px" }))
                </div>
                <div class="form-group">
                    <label>Aquisition Date</label>
                    <br />
                    @(Html.Kendo().DatePickerFor(m => m.AquisitionDate).HtmlAttributes(new { @style = "width:250px"}))
                </div>
            </div>
        <div class="col-md-5">
            <div class="form-group">
                <label>Expiry Warning at Week(s)</label>
                <br />
                @(Html.Kendo().DropDownListFor(m => m.WarningThreshold)
                    .Name("warningThreshold")
                    .Filter("contains")
                    .OptionLabel("Select Threshold")
                    .DataSource(d => d.Read("GetWarningThresholds", "Lookup"))
                    .HtmlAttributes(new { @style = "width:250px" }))
            </div>
            <div class="form-group">
                <label>Expiry Date</label>
                <br />
                @(Html.Kendo().DatePickerFor(m => m.ExpiryDate).HtmlAttributes(new { @style = "width:250px" }))
            </div>
        </div>
        <div class="col-md-10">
            <div class="form-group">
                <label>Details</label>
                <br />
                @Html.TextAreaFor(p => p.Details, new { @class = "form-control", @style = "width:100%", @rows = "5", })
            </div>
        </div>
    </div>
</div>



