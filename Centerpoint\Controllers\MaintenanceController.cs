﻿using Centerpoint.Extensions;
using Centerpoint.Model.ViewModels;
using Centerpoint.Service.Interfaces;
using Centerpoint.Services;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Centerpoint.Controllers
{
    [Authorize]
    public class MaintenanceController : Controller
    {
        private readonly IMaintenanceService _maintenanceService;
        private readonly ICurrentUserService _currentUserService;

        public MaintenanceController(IMaintenanceService maintenanceService,
            ICurrentUserService currentUserService)
        {
            _maintenanceService = maintenanceService;
            _currentUserService = currentUserService;
        }
        public async Task<ActionResult> Index()
        {
            var model = await _maintenanceService.GetMaintenanceRecordDashboardModel();

            this.SetTitle("Maintenance Records");

            return View(model);
        }

        public async Task<ActionResult> MaintenanceHistory()
        {
            var model = new MaintenanceRecordDashboardModel
            {
                FromDate = DateTime.UtcNow.AddDays(-90),
                ToDate = DateTime.UtcNow,
            };

            this.SetTitle("Maintenance History");

            return View(model);
        }

        public async Task<ActionResult> UpcomingMaintenance()
        {
            this.SetTitle("Upcoming Maintenance");

            return View();
        }

        public async Task<ActionResult> GetAllEquipmentItemMaintenanceSchedulesBySummary([DataSourceRequest] DataSourceRequest request, int days, int points)
        {
            var result = await _maintenanceService.GetAllEquipmentItemMaintenanceSchedulesBySummary(days, points);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetAllEquipmentItemMaintenanceSchedulesByDaysAndPoints([DataSourceRequest] DataSourceRequest request, int days, int points)
        {
            var result = await _maintenanceService.GetAllEquipmentItemMaintenanceSchedulesByDaysAndPoints(days, points);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetAllEquipmentItemMaintenanceSchedulesByPoints([DataSourceRequest] DataSourceRequest request, int points)
        {
            var result = await _maintenanceService.GetAllEquipmentItemMaintenanceSchedulesByPoints(points);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetAllEquipmentItemMaintenanceSchedulesByDays([DataSourceRequest] DataSourceRequest request, int days)
        {
            var result = await _maintenanceService.GetAllEquipmentItemMaintenanceSchedulesByDays(days);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetSummary(int? days, int? points)
        {
            var model = new MaintenanceRecordDashboardModel();

            //_maintenanceService.GetSummary(days, points);

            return Json(model);
        }

        public async Task<ActionResult> GetRecentMaintenanceRecords([DataSourceRequest] DataSourceRequest request)
        {
            var result = await _maintenanceService.GetRecentMaintenanceRecords();
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetMaintenanceRecordsByRunId([DataSourceRequest] DataSourceRequest request, int runId)
        {
            var result = await _maintenanceService.GetMaintenanceRecordsByRunId(runId);
            return Json(result.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateMaintenanceRecord([DataSourceRequest] DataSourceRequest request, MaintenanceRecordModel model, int mrId)
        {
            var result = await _maintenanceService.UpdateMaintenanceRecord(model, mrId);
            return Json(result.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> UpdateMaintenanceRecords([DataSourceRequest] DataSourceRequest request, MaintenanceRecordModel model, int maintenanceRecordId)
        {
            var result = await _maintenanceService.UpdateMaintenanceRecords(model, maintenanceRecordId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetMonthlySummary(int month, int year)
        {
            var model = await _maintenanceService.GetMonthlySummary(month, year);
            return Json(model);
        }

        public async Task<ActionResult> GetEngineeerSummary()
        {
            var model = await _maintenanceService.GetEngineeerSummary(_currentUserService.UserId);

            return Json(model);
        }

        public async Task<ActionResult> GetMaintenanceRecords([DataSourceRequest] DataSourceRequest request, string type, int? month, int? year, int? engineerUserId, int? companyLocationId)
        {
            var result = await _maintenanceService.GetMaintenanceRecords(type, month, year, engineerUserId, companyLocationId, _currentUserService.UserId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetFromToSummary(string from, string to)
        {
            var result = await _maintenanceService.GetFromToSummary(from, to);
            return Json(result);
        }

        public async Task<ActionResult> GetAllMaintenanceRecordsByClosed([DataSourceRequest] DataSourceRequest request, string type, string from, string to)
        {
            var result = await _maintenanceService.GetAllMaintenanceRecordsByClosed(type, from, to);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetMaintenanceRecordLogByMaintenanceRecordId([DataSourceRequest] DataSourceRequest request, int mRId)
        {
            var result = await _maintenanceService.GetMaintenanceRecordLogByMaintenanceRecordId(mRId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetDocumentsByEquipmentItemId([DataSourceRequest] DataSourceRequest request, int equipmentItemId)
        {
            var result = await _maintenanceService.GetDocumentsByEquipmentItemId(equipmentItemId);
            return Json(result.ToDataSourceResult(request));
        }
        public async Task<ActionResult> GetMaintenanceRecordByEquipmentItemId([DataSourceRequest] DataSourceRequest request, int equipId)
        {
            var result = await _maintenanceService.GetMaintenanceRecordByEquipmentItemId(equipId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> GetMaintenanceRecordByEquipmentItem([DataSourceRequest] DataSourceRequest request, int equipmentItemId)
        {
            var result = await _maintenanceService.GetMaintenanceRecordByEquipmentItem(equipmentItemId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> AddMaintenanceRecord()
        {
            var model = new MaintenanceRecordModel
            {
                UserId = _currentUserService.UserId,
                Created = DateTime.UtcNow,
            };

            this.SetTitle("Add New MR");

            return View("EditMaintenanceRecord", model);
        }

        public async Task<ActionResult> EditMaintenanceRecord(int id, string tab)
        {
            var model = await _maintenanceService.GetMaintenanceRecord(id, _currentUserService.UserId);

            this.SetTitle(string.Format("Edit MaintenanceRecord - {0}", model.Number));

            ViewBag.Tab = tab;

            return View(model);
        }

        [HttpPost]
        public async Task<ActionResult> EditMaintenanceRecord([DataSourceRequest] DataSourceRequest request, MaintenanceRecordModel model)
        {
            if (model.StartDate < model.Created)
            {
                ModelState.AddModelError("StartDate", "Start date and time cannot be set before the Created date and time");
            }

            if (model.CompletedDate < model.StartDate)
            {
                ModelState.AddModelError("CompletedDate", "Completed date and time cannot be set before the Start date and time");
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(new { message = "An error occurred while updating the MR" });
            }

           var maintenanceRecord = await _maintenanceService.EditMaintenanceRecord(model, _currentUserService.UserId);

            if (maintenanceRecord != null)
            {
                this.SetMessage(MessageType.Success, string.Format("'{0}' MR has been successfully updated", maintenanceRecord.Number));
                return Json(new { mrId = maintenanceRecord.MaintenanceRecordId, mrNumber = maintenanceRecord.Number });
            }

            return BadRequest(new { message = "An error occurred while updating the MR" });
        }

        [HttpPost]
        public async Task<ActionResult> EditMaintenanceRecordDates(List<int> selectedMaintenanceRecordIds, DateTime? created, DateTime? startDate, DateTime? completedDate)
        {

            if (selectedMaintenanceRecordIds.Count < 1)
            {
                return BadRequest(new { message = "An error occurred while updating  MR's Dates" });
            }

            var result = await _maintenanceService.EditMaintenanceRecordDates(selectedMaintenanceRecordIds, created, startDate, completedDate);

            if (!result.HasErrors())
            {
                this.SetMessage(MessageType.Success, " MRs has been successfully updated");
                return Ok();
            }

            return BadRequest(new { message = "An error occurred while updating the MR" });
        }

        public async Task<ActionResult> DeleteMaintenanceRecord(int id)
        {
            var maintenanceRecord = await _maintenanceService.DeleteMaintenanceRecord(id, _currentUserService.UserId);

            this.SetMessage(MessageType.Success, string.Format("'{0}' MR has been successfully deleted", maintenanceRecord.Number));

            return RedirectToAction("Index");

        }

        [HttpPost]
        public async Task<ActionResult> StartMaintenanceRecord(int equipmentItemId, int maintenanceBlueprintId)
        {
            int? maintenanceRecordId = await _maintenanceService.StartMaintenanceRecord(equipmentItemId, maintenanceBlueprintId, _currentUserService.UserId);

            return Json(new { maintenanceRecordId = maintenanceRecordId });
        }

        [HttpPost]
        public async Task<ActionResult> RevertStart(int id)
        {
            await _maintenanceService.RevertStart(id, _currentUserService.UserId);

            return Json(new { maintenanceRecordId = id });
        }

        [HttpPost]
        public async Task<ActionResult> StartRunTriggeredMR(int maintenanceRecordId)
        {
            await _maintenanceService.StartRunTriggeredMR(maintenanceRecordId, _currentUserService.UserId);

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<ActionResult> PassRunTriggeredMR(int maintenanceRecordId)
        {
            await _maintenanceService.PassRunTriggeredMR(maintenanceRecordId);

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<ActionResult> FailRunTriggeredMR(int maintenanceRecordId)
        {
            await _maintenanceService.FailRunTriggeredMR(maintenanceRecordId, _currentUserService.UserId);

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<ActionResult> CloseRunTriggeredMR(int maintenanceRecordId)
        {
            await _maintenanceService.CloseRunTriggeredMR(maintenanceRecordId, _currentUserService.UserId);

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<ActionResult> AssignEngineer(int id, int engineerId, string status)
        {
            await _maintenanceService.AssignEngineer(id, engineerId, status, _currentUserService.UserId);

            return Json(new { success = "true" });
        }

        [HttpPost]
        public async Task<ActionResult> SelfAssign(int id)
        {
            await _maintenanceService.SelfAssign(id, _currentUserService.UserId);

            return Json(new { success = "true" });
        }


        [HttpPost]
        public async Task<ActionResult> ReAssignEngineer(int id, int engineerId)
        {
            await _maintenanceService.ReAssignEngineer(id, engineerId, _currentUserService.UserId);

            return Json(new { success = "true" });
        }

        #region Maintenance Record Attachment Documents

        public async Task<ActionResult> GetMaintenanceRecordDocuments([DataSourceRequest] DataSourceRequest request, int maintenanceRecordId)
        {
            var result = await _maintenanceService.GetMaintenanceRecordDocuments(maintenanceRecordId);
            return Json(result.ToDataSourceResult(request));
        }

        public async Task<ActionResult> AttachMaintenanceRecordDocuments(IEnumerable<IFormFile> maintenanceRecordAttachmentDocuments, int mId)
        {
            await _maintenanceService.AttachMaintenanceRecordDocuments(maintenanceRecordAttachmentDocuments, mId, _currentUserService.UserId);

            return Json(true);
        }

        public async Task<ActionResult> DeleteMaintenanceRecordDocument([DataSourceRequest] DataSourceRequest request, DocumentModel model, int mId)
        {
            await _maintenanceService.DeleteMaintenanceRecordDocument(model, mId, _currentUserService.UserId);

            return Json(ModelState.ToDataSourceResult());
        }

        #endregion

        #region Maintenance Record Status

        public async Task<ActionResult> UpdateMaintenanceRecordStatus(int id, string status)
        {
            await _maintenanceService.UpdateMaintenanceRecordStatus(id, status, _currentUserService.UserId);

            return Json(new { success = "true" });
        }

        #endregion

        [HttpPost]
        public async Task<ActionResult> RejectMaintenanceRecordReason(int id, string comment)
        {
            await _maintenanceService.RejectMaintenanceRecordReason(id, comment, _currentUserService.UserId);

            return Json(new { success = "true" });
        }


        #region Maintenance Record Steps

        [HttpPost]
        public async Task<ActionResult> AddRepairStep(int maintenanceRecordStepId, int maintenanceRecordId)
        {
            await _maintenanceService.AddRepairStep(maintenanceRecordStepId, maintenanceRecordId, _currentUserService.UserId);

            return Json(new { success = "true" });
        }

        public async Task<ActionResult> EditMaintenanceRecordStep(int id)
        {
            var model = await _maintenanceService.GetMaintenanceRecordStep(id);
            this.SetTitle(string.Format("Edit MaintenanceRecord Step - {0}", model.Name));

            return View(model);
        }

        [HttpPost]
        public async Task<ActionResult> EditMaintenanceRecordStep([FromBody] MaintenanceRecordStepModel model)
        {
            await _maintenanceService.EditMaintenanceRecordStep(model, _currentUserService.UserId);

            return Json(true);
        }

        public async Task<ActionResult> GetMaintenanceRecordSteps(int maintenanceRecordId)
        {
            var result = await _maintenanceService.GetMaintenanceRecordSteps(maintenanceRecordId);
            return Json(result);
        }


        #region Maintenance Record  Step Document

        public async Task<ActionResult> GetMaintenanceRecordStepDocuments([DataSourceRequest] DataSourceRequest request, int maintenanceRecordStepId)
        {
            var result = await _maintenanceService.GetMaintenanceRecordStepDocuments(maintenanceRecordStepId);
            return Json(result.ToDataSourceResult(request));
        }

        [HttpPost]
        public async Task<ActionResult> AttachMaintenanceRecordStepDocument(IEnumerable<IFormFile> files, int id)
        {
            await _maintenanceService.AttachMaintenanceRecordStepDocument(files, id, _currentUserService.UserId);

            return Json(true);
        }

        [HttpPost]
        public async Task<ActionResult> DeleteMaintenanceRecordStepDocument(int maintenanceRecordStepId, int documentId)
        {
            await _maintenanceService.DeleteMaintenanceRecordStepDocument(maintenanceRecordStepId, documentId);

            return Json(true);
        }
        #endregion

        #region Maintenance Record Step Status

        [HttpPost]
        public async Task<ActionResult> UpdateMaintenanceRecordStepStatus(int maintenanceRecordStepId, string status)
        {
            await _maintenanceService.UpdateMaintenanceRecordStepStatus(maintenanceRecordStepId, status, _currentUserService.UserId);

            return Json(true);
        }

        #endregion

        #endregion

        public async Task<ActionResult> CheckMaintenance()
        {
            var count = await _maintenanceService.CheckMaintenance(_currentUserService.UserId);

            return Json(new { count = count });
        }

        public async Task<ActionResult> RunMaintenance()
        {
            await _maintenanceService.RunMaintenance(_currentUserService.UserId);

            return RedirectToAction("Index", "Maintenance");
        }
    }
}
