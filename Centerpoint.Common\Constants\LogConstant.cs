﻿namespace Centerpoint.Common.Constants
{
    public static class EquipmentLogConstant
    {
        public const string RemovedFromShipment = "Removed from Shipment";
        public const string RemovedFromPackingList = "Equipment Item with bundles was removed from Packing List and marked as unselected, unreserved";
        public const string DeletedFromShipment = "Equipment Item with bundles was deleted from Shipment. Equipment Item statuses: Selected - {0}, Reserved - {1}";
        public const string AddedToShipment = "Equipment item added to shipment and marked as reserved";
        public const string AddedToPackingList = "Equipment item added to Packing List and marked as selected.";
        public const string EquipmentShipmentInTransit = "Equipment Shipment of Equipment Item transiting: it lost Equipment Packing List Project and Project, marked as unreserved, marked as unselected, marked as InTransit";
        public const string EquipmentReceived = "Received - {0}, {1}. Statuses: Selected - {2}, Reserved - {3}, Transit - {4}.";
        public const string EquipmentNotReceived = "Not Received - {0} - {1}. The current location of the company remains - {2} - {3}";
        public const string EquipmentReserved = "Equipment Item marked as reserved";
        public const string EquipmentUnreserved = "Equipment Item marked as unreserved";
        public const string EquipmentShipped = "Shipped - {0}, {1} - {2}, {3}";
        public const string MarkedAs = "Marked as {0}";
        public const string DeletedFromProject = "Deleted from Project";
        public const string StatusMaintenanceOnHold = "Status has been changed to Maintenance On Hold";
        public const string StatusUnderMaintenance = "Status has been changed to Under Maintenance";
        public const string StatusInactive = "Status has been changed to Inactive";
        public const string MRClosed = "Maintenance Record has been Closed";
        public const string MRRaisedBlueprint = "Maintenance Record raised using blueprint '{0}'";
        public const string EquipmentCreated = "Equipment Item Created";
        public const string CurrentLocationChanged = "Current location changed from {0} to {1}";
        public const string CurrentLocationChangedTo = "Current location changed to {0}";
        public const string FileDeleted = "'{0}' Deleted";
        public const string ExDateUpdated = "'{0}' expiry date updated";
        public const string MRDeleted = "Maintenance Record has been deleted";
        public const string DeletedItemFromRun = "'{0}' has been deleted from the run '{1}'";
        public const string MRClosedInactivity = "Maintenance record closed due to item inactivity";
        public const string FileAttached = "'{0}' Attached";
        public const string UndoReceiving = "Proccess receiving cancel! Shipped - {0}, {1} - {2}, {3}";
        public const string RemovedFromProject = "'{0}' has been removed from the project '{1}'";
        public const string AddedToProject = "'{0}' has been added to the project '{1}'";
        public const string AddedToRun = "'{0}' has been added to a run '{1}'";
    }

    public static class ServiceImprovementLogConstant
    {
        public const string RejectReason = "Reject Reason has been updated";
        public const string RejectReasonNot = "Reject Reason has not been updated: {0}";
        public const string ReRequestInvestigation = "Re-Request Investigation reason has been updated";
        public const string ReRequestInvestigationDetails = "<p><strong>Investigation Re-Requested Reason</strong></p><p>{0}</p><hr /><p><strong>Investigation Results</strong></p><p>{1}</p>";
        public const string ReEvaluatePreventive = "Re-Evaluate Preventive reason has been updated";
        public const string ReEvaluatePreventiveDetails = "<p><strong>Preventive Action Re-Evaluated Reason </strong></p><p>{0}</p><hr /><p><strong>Preventive Action Comments</strong></p><p>{1}</p>";
        public const string ReEvaluateCorrective = "Re-Evaluate Corrective reason has been updated";
        public const string ReEvaluateCorrectiveDetails = "<p><strong>Corrective Action Re-Evaluated Reason</strong></p><p>{0}</p><hr /><p><strong>Corrective Action Comments</strong></p><p>{1}</p>";
        public const string InvestigationExtensionRequested = "SIF investigation extension requested. From :'{0}' To : '{1}'";
        public const string PreventiveActionExtensionRequested = "SIF preventive action extension requested. From :'{0}' To : '{1}'";
        public const string CorrectiveActionExtensionRequested = "SIF corrective action extension requested. From :'{0}' To : '{1}'";
        public const string InvestigationExtensionRequestedDenied = "SIF investigation extension requested denied";
        public const string InvestigationExtensionRequestedG = "SIF investigaion extension requested granted";
        public const string PreventiveActionExtensionRequestedDenied = "SIF preventive action extension requested denied";
        public const string PreventiveActionExtensionRequestedG= "SIF preventive action extension requested granted";
        public const string CorrectiveActionExtensionRequestedDenied = "SIF corrective action extension requested denied";
        public const string CorrectiveActionExtensionRequestedG = "SIF corrective action extension requested granted";
        public const string DocumentDeleted = "Document '{0}' Deleted";
        public const string DocumentAttached = "Document '{0}' Attached";
        public const string Accepted = "SIF accepted";
        public const string SignedOff = "SIF signed off and closed";
        public const string InvestigationReRequested = "SIF investigation re-requested";
        public const string PreventiveActionSubmitted = "SIF preventive actions submitted";
        public const string PreventiveActionReEvaluation = "SIF preventive actions re-evaluation requested";
        public const string CorrectiveActionSubmitted = "SIF corrective actions submitted";
        public const string CorrectiveActionReEvaluation = "SIF corrective actions re-evaluation requested";
        public const string InvestigationAccepted = "SIF investigation accepted";
        public const string PreventiveActionAccepted = "SIF preventive actions accepted";
        public const string CorrectiveActionAccepted = "SIF corrective actions accepted";
        public const string SIFAdded = "SIF added";
        public const string SIFSubmitted = "SIF is submitted";
        public const string SIFRejected = "SIF rejected";
        public const string CorrectiveActionRequested = "SIF corrective actions requested";
        public const string CorrectiveActionRequestedDetails = "SIF corrective actions requested. Party :'{0}' - Due Date :'{1}'";
        public const string RequestedCorrectiveAction = "Requested Corrective Action has been Updated";
        public const string PreventiveActionsRequested = "SIF preventive actions requested";
        public const string PreventiveActionsRequestedDetails = "SIF preventive actions requested. Party :'{0}' - Due Date :'{1}'";
        public const string RequestedPreventiveAction = "Requested Preventive Action has been Updated";
        public const string InvestigationRequested = "SIF investigation requested";
        public const string InvestigationRequestedDetails = "SIF investigation actions requested. Party :'{0}' - Due Date :'{1}'";
        public const string RequestedInvestigationAction = "Required Investigation Action has been Updated";
        public const string StatusChanged = "Status changed to '{0}' ";
    }

    public static class OpportunityLogConstant
    {
        public const string OpportunityDeleted = "Opportunity has been deleted";
        public const string ContactAdded = "New Contact {0} {1} has been added to {2}";
        public const string ContactDeleted = "New Contact {0} {1} has been deleted from {2}";
        public const string Cloned = "{0} has been cloned";
        public const string DocumentDeleted = "Document '{0}' Deleted";
        public const string EventCreated = "New event has been created for {0}";
        public const string EventDeleted = "Event for {0} has been deleted";
        public const string ActionCreated = "New action has been created for {0}";
        public const string ActionDeleted = "Action for {0} has been deleted";
        public const string LeadToOpportunity = "Lead has been converted in to Opportunity";
        public const string OpportunityToLead = "Opportunity has been reverted in to Lead";
        public const string ReOpened = "{0} has been re-opened";
        public const string ClosedDue = "{0} has been closed due to the following reason - {1} : {2}";
        public const string OpportunityToProject = "Opportunity has been converted in to a project";
        public const string StageChanged = "Stage has been changed from '{0}' to '{1}'";
        public const string ValueChanged = "Value has been changed from '{0}' to '{1}'";
        public const string Created = "New {0} has been created";
        public const string DocumentAttached = "Document '{0}' Attached";
    }

    public static class MaintenanceRecordLogConstant
    {
        public const string MROnHold = "Maintenance Record is On Hold";
        public const string MRInProgress = "Maintenance Record has started and is In-Progress";
        public const string MRAwaitingApproval = "Maintenance Record is Awaiting Approval";
        public const string MROnGoing = "Maintenance Record is On-Going";
        public const string MRClosed = "Maintenance Record has been Closed";
        public const string MRCreated = "MR has been created";
        public const string MRCreatedM = "MR has been created manually";
        public const string MRToAssigned = "MR has been reverted back to Assigned";
        public const string DocumentDeleted = "Document '{0}' Deleted";
        public const string MRRejected = "MR has been rejected";
        public const string MRRejectedDetails = "<p><strong>MR Reject Reason</strong></p><p>{0}</p>";
        public const string MRStepOnHold = "MR Step '{0}' is On Hold since '{1}'";
        public const string MRStepFailed = "MR Step '{0}' has Failed";
        public const string MRStepPassed = "MR Step '{0}' has Passed";
        public const string OnHoldRemoved = "OnHold has been removed for MR Step '{0}'";
        public const string DocumentAttached = "Document '{0}' Attached";
        public const string DocumentAttachedToStep = "Document '{0}' Attached to Maintenance Record Step '{1}'";
    }

    public static class ProjectLogConstant
    {
        public const string ShipmentReceived = "Shipment '{0}' Received";
        public const string ProjectAdded = "Project added";
        public const string ProjectUpdated = "Project updated";
        public const string Closed = "Project status has been updated to Closed";
        public const string Lost = "Project status has been updated to Lost";
        public const string Active = "Project status has been updated to Active";
        public const string ProjectDeleted = "Project '{0}' Deleted";
        public const string CommentUpdated = "Project comment updated";
        public const string CommentAdded = "Project comment added";
        public const string CommentDeleted = "Project's comment was deleted";
        public const string CrewDeleted = "Crew update: '{0}' has been deleted";
        public const string JobDeleted = "Job '{0}' was deleted";
        public const string CommentJobUpdated = "Comment updated to Job '{0}'";
        public const string CommentJobAdded = "Comment added to Job '{0}'";
        public const string CommentJobDeleted = "Comment deleted from Job '{0}'";
        public const string RunUpdated = "Run '{0}' was updated";
        public const string RunAdded = "Run '{0}' was added";
        public const string RunDeleted = "Run '{0}' was deleted";
        public const string ProjectAttached = "Project '{0}' Attached";
        public const string CrewUpdateOut = "Crew update: '{0}' out at '{1}'";
        public const string CrewUpdateIn = "Crew update: '{0}' in at '{1}'";
        public const string JobUpdated = "Job '{0}' was updated";
        public const string JobAdded = "Job '{0}' was added";
        public const string InvoiceNumberUpdated = "Invoice number has been updated '{0}'";
    }

    public static class MaintenanceBlueprintLogConstant
    {
        public const string MBCreated = "Maintenance Blueprint Created";
        public const string DocumentDeleted = "Document '{0}' has been deleted";
        public const string MBStepCreated = "Maintenance Blueprint Step '{0}' has been created";
        public const string MBStepDeleted = "Maintenance Blueprint Step '{0}' has been deleted";
        public const string DocumentAttached = "Document '{0}' has been attached";

    }
}
