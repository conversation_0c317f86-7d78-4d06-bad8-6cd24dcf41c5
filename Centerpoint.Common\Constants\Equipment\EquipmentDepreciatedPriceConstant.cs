﻿using Microsoft.Extensions.Configuration;

namespace Centerpoint.Common.Constants
{
    public static class EquipmentDepreciatedPriceConstant
    {
        private static string _client;
        public static void Initialize(IConfiguration configuration)
        {
            _client = configuration["Settings:Client"];
        }

        public static decimal CalculateDepreciatedPrice(decimal price, DateTime? purchasedDate, bool? trackedNonAssetItem)
        {
            if (GlobalSettings.IsWellsense)
            {
                return CalculateWellsensePrice(price, purchasedDate, trackedNonAssetItem);
            } else
            {
                return CalculatePrice(price, purchasedDate);
            }
        }

        public static decimal CalculateWellsensePrice(decimal price, DateTime? purchasedDate, bool? trackedNonAssetItem)
        {
            decimal originalPrice = price;
            decimal minDepreciatedValue = originalPrice * 0.30m; // 30% of original price

            if (trackedNonAssetItem == true || !purchasedDate.HasValue || price == 0)
                return Math.Max(Math.Round(price, 2), minDepreciatedValue); // Ensure price doesn't drop below 30%

            var now = DateTime.UtcNow;
            var monthsSincePurchase = ((now.Year - purchasedDate.Value.Year) * 12) + now.Month - purchasedDate.Value.Month;

            if (monthsSincePurchase >= 60)
                return minDepreciatedValue; // Ensure price doesn't drop below 30%

            for (int i = 0; i < monthsSincePurchase; i++)
                price *= 0.98m;  // Reduce by 2% each month.

            price = Math.Max(minDepreciatedValue, price); // Ensure price doesn't drop below 30%

            return Math.Round(price, 2); // Rounded to 2 decimal places for currency standards.
        }


        public static decimal CalculatePrice(decimal price, DateTime? purchasedDate)
        {

            if (!purchasedDate.HasValue)
                return Math.Round(price, 2);

            var now = DateTime.UtcNow;
            double depreciationFactor = 0;

            var depreciationTimeSpan = now.Date - purchasedDate.Value.Date;
            var totalYears = (int)Math.Floor(depreciationTimeSpan.TotalDays / 365);
            if (totalYears == 1)
            {
                depreciationFactor = 0.9;
            } else if (totalYears == 2)
            {
                depreciationFactor = 0.8;
            } else if (totalYears == 3)
            {
                depreciationFactor = 0.7;
            } else if (totalYears > 3)
            {
                depreciationFactor = 0.3;
            }

            var pricePercentage = depreciationFactor > 0 ? price * (decimal)depreciationFactor : price;

            return Math.Round(pricePercentage, 2);
        }
    }
}
