$(document).ready(function () {
    if (maintenanceModel.maintenanceBlueprintId) {
        getMaintenanceSteps();
    }
});

function getMaintenanceSteps() {
    $.ajax({
        url: "/Admin/GetMaintenanceBlueprintSteps",
        dataType: "json",
        data: {
            maintenanceBluePrintId: maintenanceModel.maintenanceBlueprintId
        },
        success: function (result) {
            var maintenanceStepPanelBar = $("#maintenanceStepPanelBar").data("kendoPanelBar");

            if (maintenanceStepPanelBar != undefined) {
                maintenanceStepPanelBar.remove($("li", maintenanceStepPanelBar.element));
            }

            if (result && result.length > 0) {
                viewModel.set("maintenanceSteps", result);

                for (var i = 0; i < result.length; i++) {
                    var maintenanceStepTemplate = kendo.template($("#maintenanceStepTemplate").html()); // load detail template
                    var maintenanceStepHtml = maintenanceStepTemplate(result[i]); // fill template with data

                    maintenanceStepPanelBar.append({
                        text: result[i].IsRepair ?result[i].NewName + "<i class='fa fa-warning' title='Repair Step' style='margin-left:5px'></i>" : result[i].NewName,
                        encoded: false,
                        content: maintenanceStepHtml
                    });
                }

                maintenanceStepPanelBar.expand($("li", maintenanceStepPanelBar.element));
            }
        }
    });
}

function updateMaintenanceBlueprintTotal() {
    var editMaintenanceBlueprintGrid = $("#editMaintenanceBlueprintGrid").data("kendoGrid");
    var totalMaintenanceBlueprints = editMaintenanceBlueprintGrid.dataSource.total();
    viewModel.set("totalMaintenanceBlueprints", totalMaintenanceBlueprints);
    saveMaintenanceBlueprintGrid();
}

function onMaintenanceBlueprintDocumentAttached() {
    var maintenanceBlueprintDocumentsGrid = $("#maintenanceBlueprintDocumentsGrid").data("kendoGrid");
    maintenanceBlueprintDocumentsGrid.dataSource.read();
    refreshMaintenanceBlueprintLogGrid();
}

function onMaintenanceBlueprintDocumentUpload(e) {
    uploadValidation(e);

    $(".k-upload-files.k-reset").show();
}

function onMaintenanceBlueprintDocumentComplete(e) {
    $(".k-upload-files.k-reset").find("li").remove();
    $(".k-upload-files.k-reset").slideUp();
}

function updateMaintenanceBlueprintDocumentGrid() {
    var maintenanceBlueprintDocumentsGrid = $("#maintenanceBlueprintDocumentsGrid").data("kendoGrid");
    var totalMaintenanceBlueprintDocuments = maintenanceBlueprintDocumentsGrid.dataSource.total();
    viewModel.set("totalMaintenanceBlueprintDocuments", totalMaintenanceBlueprintDocuments);
}

function divisionChange() {
    var equipmentCategoryIds = $("#EquipmentCategoryIds").data("kendoMultiSelect");
    equipmentCategoryIds.dataSource.read();
}

function divisionData(e) {
    var divisionId = $("#DivisionId").data("kendoDropDownList").value();
    return {
        divisionId: divisionId,
        text: getTextValue(e)
    };
}
function maintenanceBlueprintData() {
    return {
        maintenanceBlueprintId: maintenanceModel.maintenanceBlueprintId
    }
}

var viewModel = new kendo.observable({
    equipmentCategoryIds: maintenanceModel.equipmentCategoryIds,
    totalMaintenanceBlueprints: 0,
    totalMaintenanceBlueprintDocuments: 0,
    totalMaintenanceBlueprintLogs: 0,
    totalMaintenanceBlueprintDocumentsText: function(){
        return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Attachments (<span data-bind="text: totalMaintenanceBlueprintDocuments"></span>)</span>`;   
    },
    totalMaintenanceBlueprintLogsText: function () {
        return `<span class="k-link"><i class="fa fa-clock mr-1"></i> Maintenance Blueprint History (<span data-bind="text: totalMaintenanceBlueprintLogs"></span>)</span>`;
    },
    maintenanceSteps: [],
    detailsTextWithIcon: function(){
        return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Details </span>`;   
    },
    totalMaintenanceSteps: function () {
        var maintenanceSteps = this.get("maintenanceSteps");
        return maintenanceSteps.length;
    },

    deleteMaintenanceBlueprint: function (e) {
        e.preventDefault();
        var confirmDelete = confirm("Are you sure you wish to delete this maintenance blueprint?");

        if (confirmDelete) {
            $.ajax({
                type: "POST",
                url: "/Admin/DeleteMaintenanceBlueprint",
                data: {
                    id: maintenanceModel.maintenanceBlueprintId,
                },
                success: function (result) {
                    window.location.href = `/Admin/MaintenanceBlueprints`;
                },
                dataType: "json"
            });
        }
    },
});

function moveStepUp(maintenanceBlueprintStepId) {
    $.ajax({
        type: "POST",
        url: "/Admin/MoveMaintenanceBlueprintStepUp",
        dataType: "json",
        data: {
            maintenanceBlueprintStepId: maintenanceBlueprintStepId
        },
        success: function () {
            getMaintenanceSteps();
            refreshMaintenanceBlueprintLogGrid();
        }
    });
}

function moveStepDown(maintenanceBlueprintStepId) {
    $.ajax({
        type: "POST",
        url: "/Admin/MoveMaintenanceBlueprintStepDown",
        dataType: "json",
        data: {
            maintenanceBlueprintStepId: maintenanceBlueprintStepId
        },
        success: function () {
            getMaintenanceSteps();
            refreshMaintenanceBlueprintLogGrid();
        }
    });
}

function deleteStep(maintenanceBlueprintStepId) {
    var confirmDelete = confirm("Are you sure you wish to delete this step");

    if (confirmDelete) {
        $.ajax({
            type: "POST",
            url: "/Admin/DeleteMaintenanceBlueprintStep",
            dataType: "json",
            data: {
                id: maintenanceBlueprintStepId
            },
            success: function () {
                window.location.reload();
            }
        });
    }
}

function updateMaintenanceBlueprintTotalLogs() {
    var maintenanceBlueprintLog = $("#maintenanceBlueprintLogGrid").data("kendoGrid");
    var totalMaintenanceBlueprintLogs = maintenanceBlueprintLog.dataSource.total();
    viewModel.set("totalMaintenanceBlueprintLogs", totalMaintenanceBlueprintLogs);
}

function refreshMaintenanceBlueprintLogGrid() {
    var maintenanceBlueprintLogGrid = $("#maintenanceBlueprintLogGrid").data("kendoGrid");
    maintenanceBlueprintLogGrid.dataSource.read();
}

kendo.bind(document.body.children, viewModel);