﻿@model SalesDashboardModel

<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-bar-chart"></i>
        Matrix
    </h4>
</div>
<hr />

@(Html.Kendo().TabStrip()
    .Name("matrixTabStrip")
    .SelectedIndex(0)
    .HtmlAttributes(new { @id="matrixTabStrip"})
    .Animation(false)
    .Items( tabstrip => {
    tabstrip.Add().Text("")
        .HtmlAttributes(new { @data_bind = "html:leadsStripText", @id = "leads" })
        .Selected(true)
        .Content(@<text>

            <div id="leads">
                        @(Html.Kendo().Grid<OpportunityModel>()
                            .Name("salesGrid")
                            .Columns(columns => {
                            columns.Bound(c => c.Name).Title("Lead ID").ClientTemplate("<a href='" + @Url.Action("Edit", "Sales") + "/#=OpportunityId#?revision=#=MaxRevision#'>#=GridName#</a>");
                            columns.Bound(c => c.CompanyName).Title("Operator").ClientTemplate("#if(CompanyName){#<a href='" + @Url.Action("EditCompany", "Admin") + "/#=CompanyId#'>#=CompanyName#</a>#}else{#N/A#}#").Hidden(true);
                            columns.Bound(c => c.PartnerCompanyName).Title("Partner").ClientTemplate("#if(PartnerCompanyName){#<a href='" + @Url.Action("EditCompany", "Admin") + "/#=PartnerCompanyId#'>#=PartnerCompanyName#</a>#}else{#N/A#}#").Hidden(true);
                            columns.Bound(c => c.CustomerCompanyName).Title("Customer").ClientTemplate("<a href='" + @Url.Action("EditCompany", "Admin") + "/#=CustomerCompanyId#'>#=CustomerCompanyName#</a>");
                            columns.Bound(c => c.Services).Title("Service").Hidden(true);
                            columns.Bound(c => c.Description).Title("Description").Hidden(true);
                            columns.Bound(c => c.Revision).Title("Revision").Hidden(true);
                            columns.Bound(c => c.CurrencyValue).Title("Currency").ClientTemplate("#=CurrencyValue ? CurrencyValue : 'N/A'#").Hidden(true);
                            columns.Bound(c => c.Value).Title("Value").Format("{0:n2}").Hidden(true);
                            columns.Bound(c => c.Created).Title("Created Date").Format(DateConstants.DateTimeFormat);
                            columns.Bound(c => c.Modified).Title("Last Updated").Format(DateConstants.DateTimeFormat);
                            columns.Bound(c => c.Contacts).Title("Contacts");
                            columns.Bound(c => c.LeadStageDescription).Title("Stage").ClientTemplate("<span class='badge' style='background:#=LeadStageColour#;color:#=LeadStagetextColour#'>#=LeadStageDescription#</span>");
                            columns.Bound(c => c.TotalLeadAttachments).Title("Attachments").ClientTemplate("#if(TotalLeadAttachments){#<a class='badge' style='background:\\#0073D0;color:\\#fff' href='\\#' onclick='opportunityAttachmentCount(#=LinkOpportunityId#)'>#=TotalLeadAttachments#</a>#} else {##=TotalLeadAttachments##}#");
                            columns.Bound(c => c.OpportunityEventCount).Title("Events").ClientTemplate("#if(OpportunityEventCount){#<a class='badge' style='background:\\#0073D0;color:\\#fff' href='\\#' onclick='opportunityEventCount(#=LinkOpportunityId#)'>#=OpportunityEventCount#</a>#} else {##=OpportunityEventCount##}#");
                            columns.Bound(c => c.OpportunityActionCount).Title("Actions").ClientTemplate("#if(OpportunityActionCount && IsActionOverdue){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else if(OpportunityActionCount && IsActionUpcoming){#<a class='badge' style='background:\\#F7A54A;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else if(OpportunityActionCount && IsActionRegistered){#<a class='badge' style='background:\\#e5e500 ;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else if(OpportunityActionCount && IsActionCompleted) {#<a class='badge' style='background:\\#7CBB00;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else {##=OpportunityActionCount##}#");
                            columns.Bound(c => c.CreatedByUserName).Title("Owner").Hidden(true);
                            columns.Template("<a class='btn btn-sm btn-success' onclick=convertLead(#=LinkOpportunityId#)><i class='fa fa-star' title='Convert to Opportunity'></i></a> | <a class='btn btn-sm btn-warning' onclick=showClosedReasonWindow(#=LinkOpportunityId#)><i class='fa fa-thumbs-down' title='Close Lead'></i></a> | <a class='btn btn-sm btn-danger' onclick=deleteLead(#=LinkOpportunityId#)><i class='fa fa-ban' title='Delete Lead'></i></a> | <a class='btn btn-sm btn-primary' href='" + Url.Action("Clone", "Sales") + "/#=LinkOpportunityId#?index=true&tab=" + "" + "'><i class='fa fa-copy' title='Copy Lead'></i></a>").Width(190);
                            })
                            .ToolBar(t => t.ClientTemplate("#=salesGridToolbarGenerate(data)#"))
                            .ColumnMenu(c => c.Columns(true))
                            .Sortable()
                            .Groupable()
                            .Filterable()
                            .Reorderable(c => c.Columns(true))
                            .Resizable(c => c.Columns(true))
                            .Events(e => e.DataBound("updateSalesGrid").ColumnReorder("saveSalesGrid").ColumnResize("saveSalesGrid").ColumnShow("saveSalesGrid").ColumnHide("saveSalesGrid").Filter("saveSalesGrid").Sort("saveSalesGrid").Group("saveSalesGrid"))
                            .Excel(excel => excel
                            .FileName(string.Format("Centerpoint_Sales_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                            .Filterable(true)
                            .ProxyURL(Url.Action("Export", "Admin")))
                            .AutoBind(false)
                            .DataSource(dataSource => dataSource
                            .Ajax()
                            .ServerOperation(false)
                            .Model(model => {
                                model.Id(m => m.OpportunityId);
                            })
                            .Read(read => read.Action("GetAllLeads", "Sales"))))
            </div>

        </text>);

    tabstrip.Add().Text("")
        .HtmlAttributes(new { @data_bind="html:opportunitiesStripText", @id="opportunities"})
        .Content(@<text>

            <div id="opportunities">
                        @(Html.Kendo().Grid<OpportunityModel>()
                                    .Name("opportunitiesGrid")
                                    .Columns(columns => {
                                        columns.Bound(c => c.GridName).Title("Opportunity ID").ClientTemplate("<a href='" + @Url.Action("Edit", "Sales") + "/#=LinkOpportunityId#?revision=#=MaxRevision#'>#=GridName#</a>");
                                        columns.Bound(c => c.CompanyName).Title("Operator").ClientTemplate("#if(CompanyName){#<a href='" + @Url.Action("EditCompany", "Admin") + "/#=CompanyId#'>#=CompanyName#</a>#}else{#N/A#}#").Hidden(true);
                                        columns.Bound(c => c.PartnerCompanyName).Title("Partner").ClientTemplate("#if(PartnerCompanyName){#<a href='" + @Url.Action("EditCompany", "Admin") + "/#=PartnerCompanyId#'>#=PartnerCompanyName#</a>#}else{#N/A#}#").Hidden(true);
                                        columns.Bound(c => c.CustomerCompanyName).Title("Customer").ClientTemplate("<a href='" + @Url.Action("EditCompany", "Admin") + "/#=CustomerCompanyId#'>#=CustomerCompanyName#</a>");
                                        columns.Bound(c => c.ProjectName).Title("Project").ClientTemplate("#if(ProjectId){#<a href='" + @Url.Action("EditProject", "Operation") + "/#=ProjectId#'>#=ProjectName##} else{#N/A#}#</a>");
                                        columns.Bound(c => c.Services).Title("Service").Hidden(true);
                                        columns.Bound(c => c.Description).Title("Description").Hidden(true);
                                        columns.Bound(c => c.Comments).Title("Comments").Hidden(true);
                                        columns.Bound(c => c.OpportunityObjectives).Title("Objectives").Hidden(true);
                                        columns.Bound(c => c.CompanyFields).Title("Fields").ClientTemplate("#=CompanyFields ? CompanyFields : 'N/A'#");
                                        columns.Bound(c => c.CompanyWells).Title("Wells").ClientTemplate("#=CompanyWells ? CompanyWells : 'N/A'#");
                                        columns.Bound(c => c.RigTypeDescription).Title("Rig Type").ClientTemplate("#=RigTypeDescription ? RigTypeDescription : 'N/A'#").Hidden(true);
                                        columns.Bound(c => c.CurrencyValue).Title("Currency").ClientTemplate("#=CurrencyValue ? CurrencyValue : 'N/A'#").Hidden(true);
                                        columns.Bound(c => c.Value).Title("Value").Format("{0:n2}").Hidden(true);
                                        columns.Bound(c => c.Probability).Title("Probability Value").Format("{0:n2}").Hidden(true);
                                        columns.Bound(c => c.Conveyances).Title("Conveyance").ClientTemplate("#=Conveyances ? Conveyances : 'N/A'#");
                                        columns.Bound(c => c.CloseoutDate).Title("Closeout").Format(DateConstants.DateTimeFormat);
                                        columns.Bound(c => c.MobilisationDate).Title("Mobilisation").Format(DateConstants.DateFormat);
                                        columns.Bound(c => c.Revision).Title("Revision").Hidden(true);
                                        columns.Bound(c => c.Contacts).Title("Contacts");
                                        columns.Bound(c => c.StageDescription).Title("Stage").ClientTemplate("<span class='badge' style='background:#=StageColour#;color:#=StagetextColour#'>#=StageDescription#</span>");
                                        columns.Bound(c => c.TotalOpportunityAttachments).Title("Attachments").ClientTemplate("#if(TotalOpportunityAttachments){#<a class='badge' style='background:\\#0073D0;color:\\#fff' href='\\#' onclick='opportunityAttachmentCount(#=LinkOpportunityId#,#=OpportunityId#)'>#=TotalOpportunityAttachments#</a>#} else {##=TotalOpportunityAttachments##}#");
                                        columns.Bound(c => c.OpportunityEventCount).Title("Events").ClientTemplate("#if(OpportunityEventCount){#<a class='badge' style='background:\\#0073D0;color:\\#fff' href='\\#' onclick='opportunityEventCount(#=LinkOpportunityId#)'>#=OpportunityEventCount#</a>#} else {##=OpportunityEventCount##}#");
                                        columns.Bound(c => c.OpportunityActionCount).Title("Actions").ClientTemplate("#if(OpportunityActionCount && IsActionOverdue){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else if(OpportunityActionCount && IsActionUpcoming){#<a class='badge' style='background:\\#F7A54A;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else if(OpportunityActionCount && IsActionRegistered){#<a class='badge' style='background:\\#e5e500 ;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else if(OpportunityActionCount && IsActionCompleted) {#<a class='badge' style='background:\\#7CBB00;color:\\#fff' href='\\#' onclick='opportunityActionCount(#=LinkOpportunityId#)'>#=OpportunityActionCount#</a>#} else {##=OpportunityActionCount##}#");
                                        columns.Bound(c => c.Created).Title("Created Date").Hidden(true).Format(DateConstants.DateTimeFormat);
                                        columns.Bound(c => c.CreatedByUserName).Title("Owner").Hidden(true);
                                        if (Html.IsOperationAdmin() || Html.IsGlobalAdmin() || Html.IsSeniorUSEngineer()) {
                                            columns.Template("#if(CanConvertToProject && !HasProject){#<a class='btn btn-sm btn-success' onclick='convertToProject(this)'><i class='fa fa-thumbs-up' title='Convert to Project'></i></a> |#}# <a class='btn btn-sm btn-warning' onclick=showClosedReasonWindow(#=LinkOpportunityId#)><i class='fa fa-thumbs-down' title='Close Opportunity'></i></a> | #if(!HasProject){#<a class='btn btn-sm btn-danger' onclick=deleteOpportunity(#=LinkOpportunityId#)><i class='fa fa-ban' title='Delete Opportunity'></i></a> |#}# <a class='btn btn-sm btn-primary' href='" + Url.Action("Clone", "Sales") + "/#=MaxRevisionOpportunityId#?index=true&tab=" + "opportunities" + "'><i class='fa fa-copy' title='Copy Opportunity'></i></a>").Width(190);
                                        } else {
                                            columns.Template("<a class='btn btn-sm btn-warning' onclick=showClosedReasonWindow(#=LinkOpportunityId#)><i class='fa fa-thumbs-down' title='Close Opportunity'></i></a> | #if(!HasProject){#<a class='btn btn-sm btn-danger' onclick=deleteOpportunity(#=LinkOpportunityId#)><i class='fa fa-ban' title='Delete Opportunity'></i></a> |#}# <a class='btn btn-sm btn-primary' href='" + Url.Action("Clone", "Sales") + "/#=MaxRevisionOpportunityId#?index=true&tab=opportunities'><i class='fa fa-copy' title='Copy Opportunity'></i></a>").Width(190);
                                        }
                            })
                        .ToolBar(t => t.ClientTemplate("#=opportunitiesGridToolbarGenerate(data)#"))
                        .ColumnMenu(c => c.Columns(true))
                        .Sortable()
                        .Groupable()
                        .Filterable()
                        .Reorderable(c => c.Columns(true))
                        .Resizable(c => c.Columns(true))
                        .Events(e => e.DataBound("updateOpportunityGrid").ColumnReorder("saveOpportunityGrid").ColumnResize("saveOpportunityGrid").ColumnShow("saveOpportunityGrid").ColumnHide("saveOpportunityGrid").Filter("saveOpportunityGrid").Sort("saveOpportunityGrid").Group("saveOpportunityGrid"))
                        .Excel(excel => excel
                        .FileName(string.Format("Centerpoint_Sales_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
                        .Filterable(true)
                        .ProxyURL(Url.Action("Export", "Admin")))
                        .AutoBind(false)
                        .DataSource(dataSource => dataSource
                            .Ajax()
                            .ServerOperation(false)
                            .Model(model => {
                                model.Id(m => m.OpportunityId);
                            })
                            .Read(read => read.Action("GetAllOpportunities", "Sales"))))
            </div>

        </text>);

    tabstrip.Add().Text("")
        .HtmlAttributes(new { @data_bind="html:eventsStripText", @id="events"})
        .Content(@<text>
            <div id="events">
                @(Html.Kendo().Grid<OpportunityEventModel>()
                        .Name("eventsGrid")
                        .Columns(c => {
                            c.Bound(p => p.NewName).Title("Event ID").ClientTemplate("<a href='" + @Url.Action("EditEvent", "Sales") + "/#=OpportunityEventId#?revision=#=MaxRevision#'>#=NewName#</a>");
                            c.Bound(p => p.OpportunityName).Title("Lead / Opportunity ID").ClientTemplate("#if(OpportunityName){#<a href='" + @Url.Action("Edit", "Sales") + "/#=OpportunityId#?revision=#=MaxRevision#'>#=OpportunityName#</a>#} else{#N/A#}#");
                            c.Bound(p => p.GridCompanyName).Title("Company");
                            c.Bound(p => p.CompanyContacts).Title("Contact (s)");
                            c.Bound(p => p.TrimmedTopic).Title("Topic").ClientTemplate("#if(IsTopicLength){#<a href='\\#' onclick=\"showTopic()\">#=TrimmedTopic#</a>#} else {##=Topic##}#");
                            c.Bound(p => p.EventDate).Title("Event Date").Format(DateConstants.DateTimeFormat);
                            c.Bound(p => p.CurrencyValue).Title("Currency").ClientTemplate("#=CurrencyValue ? CurrencyValue : 'N/A'#").Hidden(true);
                            c.Bound(p => p.OpportunityValue).Title("Value").Format("{0:n2}").Hidden(true);
                            c.Bound(p => p.TotalEventActionCount).Title("Follow-up Actions").ClientTemplate("#if(TotalEventActionCount && IsActionOverdue){#<a class='badge' style='background:\\#FF0000;color:\\#fff' href='\\#' onclick='followUpActionCount(#=OpportunityEventId#)'>#=TotalEventActionCount#</a>#} else if(TotalEventActionCount && IsActionUpcoming){#<a class='badge' style='background:\\#F7A54A;color:\\#fff' href='\\#' onclick='followUpActionCount(#=OpportunityEventId#)'>#=TotalEventActionCount#</a>#} else if(TotalEventActionCount && IsActionRegistered){#<a class='badge' style='background:\\#e5e500 ;color:\\#fff' href='\\#' onclick='followUpActionCount(#=OpportunityEventId#)'>#=TotalEventActionCount#</a>#} else if(TotalEventActionCount && IsActionCompleted) {#<a class='badge' style='background:\\#7CBB00;color:\\#fff' href='\\#' onclick='followUpActionCount(#=OpportunityEventId#)'>#=TotalEventActionCount#</a>#} else {##=TotalEventActionCount##}#");
                            c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
                            c.Bound(p => p.CreatedBy).Title("Created By");
                        })
                        .ToolBar(t => t.ClientTemplate("#=eventsGridToolbarGenerate(data)#"))
                        .Events(e => e.DataBound("updateEventsGrid").ColumnReorder("saveEventGrid").ColumnResize("saveEventGrid").ColumnShow("saveEventGrid").ColumnHide("saveEventGrid").Filter("saveEventGrid").Sort("saveEventGrid").Group("saveEventGrid"))
                        .Sortable()
                        .Reorderable(c => c.Columns(true))
                        .Resizable(c => c.Columns(true))
                        .ColumnMenu(c => c.Columns(true))
                        .Filterable()
                        .Groupable()
                        .AutoBind(false)
                        .DataSource(dataSource => dataSource
                        .Ajax()
                        .ServerOperation(false)
                        .Aggregates(aggregates => {
                            aggregates.Add(p => p.OpportunityCompanyName).Min().Max().Count();
                        })
                        .Model(model => model.Id(p => p.OpportunityEventId))
                        .Read(read => read.Action("GetAllEvents", "Sales"))))
            </div>            
        </text>);    

    tabstrip.Add().Text("")
        .HtmlAttributes(new { @data_bind="html:actionsStripText", @id="actions"})
        .Content(@<text>
            <div id="actions">
                @(Html.Kendo().Grid<OpportunityActionModel>()
                    .Name("actionsGrid")
                    .Columns(c => {
                        c.Bound(p => p.NewName).Title("Action ID").ClientTemplate("<a href='" + @Url.Action("EditAction", "Sales", new { @id = "" }) + "/#=OpportunityActionId#?revision=#=MaxRevision#'>#=NewName#</a>"); ;
                        c.Bound(p => p.OpportunityName).Title("Lead / Opportunity ID").ClientTemplate("#if(HasOpportunity){#<a href='" + @Url.Action("Edit", "Sales") + "/#=OpportunityId#?revision=#=MaxRevision#'>#=OpportunityName#</a>#} else{#N/A#}#");
                        c.Bound(p => p.GridCompanyName).Title("Company");
                        c.Bound(p => p.AssignedUserName).Title("Assignee");
                        c.Bound(p => p.TargetDate).Title("Target Date").Format(DateConstants.DateFormat);
                        c.Bound(p => p.CompletedDate).Title("Completed Date").Format(DateConstants.DateFormat);
                        c.Bound(p => p.CurrencyValue).Title("Currency").ClientTemplate("#=CurrencyValue ? CurrencyValue : 'N/A'#").Hidden(true);
                        c.Bound(p => p.OpportunityValue).Title("Value").Format("{0:n2}").Hidden(true);
                        c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
                        c.Bound(p => p.CreatedBy).Title("Created By");
                        c.Bound(p => p.Status).Title("Status").ClientTemplate("<span class='badge' style='background:#=StatusColor#;color:#=StatusTextColor#'>#=Status#</span>");
                    })
                    .ToolBar(t => t.ClientTemplate("#=actionsGridToolbarGenerate(data)#"))
                    .Events(e => e.DataBound("updateActionsGrid").ColumnReorder("saveActionGrid").ColumnResize("saveActionGrid").ColumnShow("saveActionGrid").ColumnHide("saveActionGrid").Filter("saveActionGrid").Sort("saveActionGrid").Group("saveActionGrid"))
                    .Sortable()
                    .Reorderable(c => c.Columns(true))
                    .Resizable(c => c.Columns(true))
                    .ColumnMenu(c => c.Columns(true))
                    .Filterable()
                    .Groupable()
                    .AutoBind(false)
                    .DataSource(dataSource => dataSource
                    .Ajax()
                    .ServerOperation(false)
                    .Model(model => model.Id(p => p.OpportunityActionId))
                    .Read(read => read.Action("GetAllActions", "Sales"))))
            </div>    
        </text>); 
    }))


@(Html.Kendo().Window().Name("customerWindow")
 .Title("Customers")
 .Content(@<text><partial name="OpportunityCustomer"/></text>)
 .Width(500)
 .Modal(true)
 .Events(e => e.Open("onCustomerWindow"))
 .Draggable()
 .Visible(false))

@(Html.Kendo().Window().Name("closedReasonWindowOpen")
 .Title("Reason for closure")
 .Content(@<text><partial name="ClosureReason"/></text>)
 .Width(600)
 .Modal(true)
 .Events(e => e.Open("onClosedReasonWindowOpen"))
 .Visible(false))

@(Html.Kendo().Window().Name("opportunityEventWindow")
.Width(1000)
.Height(500)
.Title("Events")
.Visible(false)
.Modal(true)
.Events(e => e.Open("opportunityEventWindowOpened"))
.Content(@<text>
        @(Html.Kendo().Grid<OpportunityEventModel>()
        .Name("opportunityEventsGrid")
        .Columns(c => {
            c.Bound(p => p.NewName).Title("Event ID").ClientTemplate("<a href='" + @Url.Action("EditEvent", "Sales") + "/#=OpportunityEventId#?revision=#=MaxRevision#'>#=NewName#</a>"); ;
            c.Bound(p => p.OpportunityName).Title("Lead / Opportunity ID").ClientTemplate("<a href='" + @Url.Action("Edit", "Sales") + "/#=OpportunityId#?revision=#=MaxRevision#'>#=OpportunityName#</a>");
            c.Bound(p => p.GridCompanyName).Title("Company");
            c.Bound(p => p.CompanyContacts).Title("Contact(s)");
            c.Bound(p => p.TrimmedTopic).Title("Topic").ClientTemplate("#if(IsTopicLength){#<a href='\\#' onclick=\"showEventTopic()\">#=TrimmedTopic#</a>#} else {##=Topic##}#");
            c.Bound(p => p.EventDate).Title("Event Date").Format(DateConstants.DateTimeFormat);
            c.Bound(p => p.TotalEventActionCount).Title("Follow-up Actions").ClientTemplate("#if(TotalEventActionCount){#<a class='badge' style='background:\\#0073D0;color:\\#fff' href='" + @Url.Action("EditEvent", "Sales") + "/#=OpportunityEventId#?tab=actions'>#=TotalEventActionCount#</a>#} else {##=TotalEventActionCount##}#");
            c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
            c.Bound(p => p.CreatedBy).Title("Created By");
        })
        .Events(e => e.DataBound("updateOpportunityEventsGrid"))
        .Sortable()
        .Resizable(r => r.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .Filterable()
        .Groupable()
        .Scrollable(s => s.Height(400))
        .AutoBind(false)
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(model => model.Id(p => p.OpportunityEventId))
            .Read(read => read.Action("GetAllEventsByOpportunityId", "Sales").Data("opportunityData"))
        )
    )
</text>
         ))

@(Html.Kendo().Window().Name("opportunityActionWindow")
.Width(1000)
.Height(500)
.Title("Actions")
.Visible(false)
.Modal(true)
.Events(e => e.Open("opportunityActionWindowOpened"))
.Content(@<text>
        @(Html.Kendo().Grid<OpportunityActionModel>()
            .Name("opportunityActionsGrid")
            .Columns(c => {
                c.Bound(p => p.NewName).Title("Action ID").ClientTemplate("<a href='" + @Url.Action("EditAction", "Sales", new { @id = "" }) + "/#=OpportunityActionId#?revision=#=MaxRevision#'>#=NewName#</a>"); ;
                c.Bound(p => p.OpportunityName).Title("Lead / Opportunity ID").ClientTemplate("<a href='" + @Url.Action("Edit", "Sales") + "/#=OpportunityId#?revision=#=MaxRevision#'>#=OpportunityName#</a>");
                c.Bound(p => p.GridCompanyName).Title("Company");
                c.Bound(p => p.AssignedUserName).Title("Assignee");
                c.Bound(p => p.TargetDate).Title("Target Date").Format(DateConstants.DateFormat);
                c.Bound(p => p.CompletedDate).Title("Completed Date").Format(DateConstants.DateFormat);
                c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
                c.Bound(p => p.CreatedBy).Title("Created By");
                c.Bound(p => p.Status).Title("Status").ClientTemplate("<span class='badge' style='background:#=StatusColor#;color:#=StatusTextColor#'>#=Status#</span>");
            })
            .Events(e => e.DataBound("updateOpportunityActionsGrid"))
            .Sortable()
            .Resizable(r => r.Columns(true))
            .ColumnMenu(c => c.Columns(true))
            .Filterable()
            .Groupable()
            .Scrollable(s => s.Height(400))
            .AutoBind(false)
            .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Aggregates(aggregates => {
                    aggregates.Add(p => p.OpportunityCompanyName).Min().Max().Count();
                })
                .Model(model => model.Id(p => p.OpportunityActionId))
                .Read(read => read.Action("GetAllActionsByOpportunityId", "Sales").Data("opportunityData"))
            )
        )
</text>
         ))

@(Html.Kendo().Window().Name("followUpActionWindow")
.Width(1000)
.Height(500)
.Title("Follow-up Actions")
.Visible(false)
.Modal(true)
.Events(e => e.Open("followUpActionWindowOpened"))
.Content(@<text>
        @(Html.Kendo().Grid<OpportunityActionModel>()
            .Name("followUpActionsGrid")
            .Columns(c => {
                c.Bound(p => p.NewName).Title("Action ID").ClientTemplate("<a href='" + @Url.Action("EditAction", "Sales", new { @id = "" }) + "/#=OpportunityActionId#?revision=#=MaxRevision#'>#=NewName#</a>"); ;
                c.Bound(p => p.OpportunityName).Title("Lead / Opportunity ID").ClientTemplate("<a href='" + @Url.Action("Edit", "Sales") + "/#=OpportunityId#?revision=#=MaxRevision#'>#=OpportunityName#</a>");
                c.Bound(p => p.GridCompanyName).Title("Company");
                c.Bound(p => p.AssignedUserName).Title("Assignee");
                c.Bound(p => p.TargetDate).Title("Target Date").Format(DateConstants.DateFormat);
                c.Bound(p => p.CompletedDate).Title("Completed Date").Format(DateConstants.DateFormat);
                c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
                c.Bound(p => p.CreatedBy).Title("Created By");
                c.Bound(p => p.Status).Title("Status").ClientTemplate("<span class='badge' style='background:#=StatusColor#;color:#=StatusTextColor#'>#=Status#</span>");
            })
            .Events(e => e.DataBound("updateFollowUpActionsGrid"))
            .Sortable()
            .Resizable(r => r.Columns(true))
            .Filterable()
            .ColumnMenu(c => c.Columns(true))
            .Groupable()
            .Scrollable(s => s.Height(400))
            .AutoBind(false)
            .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Aggregates(aggregates => {
                    aggregates.Add(p => p.OpportunityCompanyName).Min().Max().Count();
                })
                .Model(model => model.Id(p => p.OpportunityActionId))
                .Read(read => read.Action("GetAllFollowupActionsByOpportunityEventId", "Sales").Data("opportunityEventData"))
            )
        )
</text>
         ))

@(Html.Kendo().Window().Name("opportunityAttachmentWindow")
.Width(1000)
.Title("Attachments")
.Visible(false)
.Modal(true)
.Events(e => e.Open("opportunityAttachmentWindowOpened"))
.Content(@<text>  
    <ul class="nav nav-tabs" id="opportunityAttachmentWindowTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="pill1-tab" data-bs-toggle="tab" data-bs-target="#pill1" type="button" role="tab" aria-controls="pill1" aria-selected="true">
                General (<span data-bind="text:totalOpportunityDocuments"></span>)
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="pill2-tab" data-bs-toggle="tab" data-bs-target="#pill2" type="button" role="tab" aria-controls="pill2" aria-selected="false">
                Events (<span data-bind="text:totalOpportunityEventDocuments"></span>)
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="pill3-tab" data-bs-toggle="tab" data-bs-target="#pill3" type="button" role="tab" aria-controls="pill3" aria-selected="false">
                Actions (<span data-bind="text:totalOpportunityActionDocuments"></span>)
            </button>        
        </li>
        <li class="nav-item" role="presentation" data-bind="visible:totalOpportunityWellDocuments">
            <button class="nav-link" id="pill4-tab" data-bs-toggle="tab" data-bs-target="#pill4" type="button" role="tab" aria-controls="pill4" aria-selected="false">
                Wells (<span data-bind="text:totalOpportunityWellDocuments"></span>)
            </button>  
        </li>
    </ul>
    <div class="tab-content" id="opportunityAttachmentWindowTab">
        <div class="tab-pane show active" id="pill1" role="tabpanel" aria-labelledby="pill1-tab">
            <br />
            <div>
                @(Html.Kendo().Grid<DocumentModel>()
                    .Name("opportunityDocumentsGrid")
                    .Columns(c => {
                        c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                        c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateTimeFormat);
                        c.Bound(p => p.UserName).Title("Created By");
                    })
                    .Sortable()
                    .Events(e => e.DataBound("updateOpportunityDocumentsGrid"))
                    .Resizable(r => r.Columns(true))
                    .ColumnMenu(c => c.Columns(true))
                    .Filterable()
                    .Groupable()
                    .Scrollable(s => s.Height(300))
                    .AutoBind(false)
                    .DataSource(dataSource => dataSource
                        .Ajax()
                        .ServerOperation(false)
                        .Model(model => model.Id(p => p.DocumentId))
                        .Read(read => read.Action("GetOpportunityDocuments", "Sales").Data("opportunityData"))
                    )
                )
            </div>
        </div>
        <div class="tab-pane fade" id="pill2" role="tabpanel" aria-labelledby="pill2-tab">
            <br />
            <div>
                @(Html.Kendo().Grid<OpportunityEventDocumentModel>()
                    .Name("opportunityEventDocumentsGrid")
                    .Columns(c => {
                        c.Bound(p => p.Event).Title("Event").ClientGroupHeaderTemplate("Event : #= value # (#= count#)").Width(200).Hidden(true);
                        c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                        c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateFormat);
                        c.Bound(p => p.Username).Title("Created By");
                    })
                    .Sortable()
                    .Events(e => e.DataBound("updateOpportunityEventDocumentsGrid"))
                    .Resizable(r => r.Columns(true))
                    .ColumnMenu(c => c.Columns(true))
                    .Editable(e => e.Mode(GridEditMode.InLine))
                    .Filterable()
                    .Groupable()
                    .Scrollable(s => s.Height(300))
                    .AutoBind(false)
                    .DataSource(dataSource => dataSource
                        .Ajax()
                        .ServerOperation(false)
                        .Aggregates(aggregates => {
                            aggregates.Add(p => p.Event).Min().Max().Count();
                        })
                        .Group(group => group.Add(p => p.Event))
                        .Model(model => model.Id(p => p.DocumentId))
                        .Read(read => read.Action("GetOpportunityEventDocumentsByOpportunityId", "Sales").Data("opportunityData"))))
            </div>
        </div>
        <div class="tab-pane fade" id="pill3" role="tabpanel" aria-labelledby="pill3-tab">
            <br />
            <div>
                @(Html.Kendo().Grid<OpportunityActionDocumentModel>()
                    .Name("opportunityActionDocumentsGrid")
                    .Columns(c => {
                        c.Bound(p => p.Action).Title("Action").ClientGroupHeaderTemplate("Action : #= value # (#= count#)").Width(200).Hidden(true);
                        c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                        c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateFormat);
                        c.Bound(p => p.Username).Title("Created By");
                    })
                    .Events(e => e.DataBound("updateOpportunityActionDocumentsGrid"))
                    .Editable(e => e.Mode(GridEditMode.InLine))
                    .Sortable()
                    .Resizable(r => r.Columns(true))
                    .ColumnMenu(c => c.Columns(true))
                    .Filterable()
                    .Groupable()
                    .Scrollable(s => s.Height(300))
                    .AutoBind(false)
                    .DataSource(dataSource => dataSource
                        .Ajax()
                        .ServerOperation(false)
                        .Aggregates(aggregates => {
                            aggregates.Add(p => p.Action).Min().Max().Count();
                        })
                        .Group(group => group.Add(p => p.Action))
                        .Model(model => model.Id(p => p.DocumentId))
                        .Read(read => read.Action("GetOpportunityActionDocumentsByOpportunityId", "Sales").Data("opportunityData"))))
            </div>
        </div>
        <div class="tab-pane fade" id="pill4" data-bind="visible:totalOpportunityWellDocuments" role="tabpanel" aria-labelledby="pill4-tab">
            <br />
            <div>
                @(Html.Kendo().Grid<CompanyWellDocumentModel>()
                .Name("wellDocumentsGrid")
                .Columns(c => {
                    c.Bound(p => p.CompanyWellName).Title("Well").ClientGroupHeaderTemplate("Well : #= value # (#= count#)").Width(200).Hidden(true);
                    c.Bound(p => p.FileName).Title("Document").ClientTemplate("<a target='_blank' href='/Document?id=#=DocumentId#'>#=FileName#</a>");
                    c.Bound(p => p.CompanyWellDocumentTypeDescription).Title("Type").ClientTemplate("#=CompanyWellDocumentTypeDescription ? CompanyWellDocumentTypeDescription : 'N/A'#");
                    c.Bound(p => p.Created).Title("Created").Format(DateConstants.DateFormat);
                    c.Bound(p => p.Username).Title("Created By");
                })
                .Events(e => e.DataBound("updateWellDocumentsGrid"))
                .Sortable()
                .Resizable(r => r.Columns(true))
                .ColumnMenu(c => c.Columns(true))
                .Filterable()
                .Groupable()
                .Scrollable(s => s.Height(300))
                .AutoBind(false)
                .DataSource(dataSource => dataSource
                    .Ajax()
                    .ServerOperation(false)
                    .Aggregates(aggregates => {
                        aggregates.Add(p => p.CompanyWellName).Min().Max().Count();
                    })
                    .Group(group => group.Add(p => p.CompanyWellName))
                    .Model(model => model.Id(p => p.CompanyWellDocumentId))
                    .Read(read => read.Action("GetCompanyWellDocumentByOpportunityId", "Sales").Data("opportunityData"))))
            </div>
        </div>
    </div>
</text>
         ))

    <script type="text/x-kendo-tmpl" id="template">
        <div class="col-md-3">
            <div class="panel panel-default" style="margin-left:-15px">
                <div class="panel-heading">
                    <a class="btn btn-danger btn-sm pull-right" onclick="deleteCustomer(#=CompanyId#)"><i class="fa fa-close" style="margin-left:5px"></i></a>
                    <h3 class="panel-title">#=Name#</h3>
                </div>
                <div class="panel-body" style="padding:20px ;margin:0; min-height:145px">
                    <div class="row">
                        <div class="col-md-7">
                            <h4 style="padding-bottom:10px">Total Leads</h4>
                        </div>
                        <div class="col-md-3">
                            <h4 class="pull-right" style="margin-top:-2px;margin-right:5px"><a href="\\#" class="btn disabled" style="background: \\#008000; padding-top:0px; padding-right:8px;padding-bottom:0px; padding-left:8px; color: \\#fff">#=TotalLeads#</a></h4>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-7">
                            <h4 style="padding-bottom:10px">Total Opportunities</h4>
                        </div>
                        <div class="col-md-3">
                            <h4 class="pull-right" style="margin-top:-2px;margin-right:5px"><a href="\\#" class="btn disabled" style="background: \\#FF748C; padding-top:0px; padding-right:8px;padding-bottom:0px; padding-left:8px; color: \\#fff">#=TotalOpportunities#</a></h4>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-7">
                            <h4 style="padding-bottom:10px">Opportunity Value</h4>
                        </div>
                        <div class="col-md-3">
                            <h4 class="pull-right" style="margin-top:-2px;margin-right:5px"><a href="\\#" class="btn disabled" style="background: \\#000; padding-top:0px; padding-right:8px;padding-bottom:0px; padding-left:8px; color: \\#fff">#=TotalCustomerOpportunities#</a></h4>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-7">
                            <h4 style="padding-bottom:10px">Last Contact</h4>
                        </div>
                        <div class="col-md-3">
                            #if(HasLastContact){#
                            <h4 class="pull-right" style="margin-top:-2px;margin-right:5px">#=LastContactDate#</h4>
                            #} else{#
                            <h4 class="pull-right" style="margin-top:-2px;margin-right:5px">N/A</h4>
                            #}#
                        </div>
                    </div>
                </div>
                #if(UserActions.length > 0){#
                <div class="panel panel-default" style="border:none">
                    <div class="panel-heading">
                        <h3 class="panel-title">Actions by User</h3>
                    </div>
                    <div class="panel-body">
                        #for(var i = 0; i < UserActions.length; ++i){#
                        <h4 class="pull-right" style="margin-right:5px"><a href="\\#" class="btn disabled" style="background: \\#FF0000; padding-top:0px; padding-right:8px;padding-bottom:0px; padding-left:8px; color: \\#fff" ; title="Overdue">#=UserActions[i].Overdue#</a></h4>
                        <h4 class="pull-right" style="margin-right:5px"><a href="\\#" class="btn disabled" style="background: \\#F7A54A; padding-top:0px; padding-right:8px;padding-bottom:0px; padding-left:8px; color: \\#fff" ; title="Upcoming">#=UserActions[i].Upcoming#</a></h4>
                        <h4 class="pull-right" style="margin-right:5px"><a href="\\#" class="btn disabled" style="background: \\#7CBB00; padding-top:0px; padding-right:8px;padding-bottom:0px; padding-left:8px; color: \\#fff" ; title="Registered">#=UserActions[i].Registered#</a></h4>
                        <h4 style="padding-bottom:10px">#=UserActions[i].User#</h4>
                        <div style="clear:both"></div>
                        #}#
                    </div>
                </div>
                #}#
            </div>
        </div>
    </script>

    <script type="text/javascript">
        function format(value) {
            return value.substring(0, 100);
        }
        function getTheSubstring(value, length) {
            if (value.length > length)
                return kendo.toString(value.substring(0, length)) + "...";
            else return kendo.toString(value);
        }
        function showTopic() {
            var grid = $("#eventsGrid").data("kendoGrid");
            var model = grid.dataItem($(event.target).closest("tr"));
            var topic = model.Topic;
            swal({ html: true, title: '<i>Topic Details</i>', text: '<b> ' + topic + ' </b>' });
        }
        function showEventTopic() {
            var grid = $("#opportunityEventsGrid").data("kendoGrid");
            var model = grid.dataItem($(event.target).closest("tr"));
            var topic = model.Topic;
            swal({ html: true, title: '<i>Topic Details</i>', text: '<b> ' + topic + ' </b>' });
        }


        function actionsGridToolbarGenerate(data) {
           let template = kendo.template($("#actionsGridToolbar").html())
           let result = template(data)
           return result
        }

        function eventsGridToolbarGenerate(data) {
           let template = kendo.template($("#eventsGridToolbar").html())
           let result = template(data)
           return result
        }

        function opportunitiesGridToolbarGenerate(data) {
           let template = kendo.template($("#opportunitiesGridToolbar").html())
           let result = template(data)
           return result
        }

        function salesGridToolbarGenerate(data) {
           let template = kendo.template($("#salesGridToolbar").html())
           let result = template(data)
           return result
        }
    </script>

    <script id="actionsGridToolbar" type="text/x-kendo-tmpl">
        <div class="d-flex w-100 justify-content-between toolbar-inline-padding">
            <a class="btn btn-success" href="@Url.Action("AddAction", "Sales" )"><i class="fa fa-plus"></i>Add New Action</a>
            <div>
                <button id="resetActionsGrid" class="btn btn-danger">
                    Reset Grid View
                </button>                
                <button class="btn btn-success k-grid-excel">
                    <i class="fa fa-file-excel"></i>
                    Export
                </button>
            </div>
        </div>
    </script>                      

    <script id="eventsGridToolbar" type="text/x-kendo-tmpl">
        <div class="d-flex w-100 justify-content-between toolbar-inline-padding">
            <a class="btn btn-success" href="@Url.Action("AddEvent", "Sales" )"><span></span><i class="fa fa-plus"></i>Add New Event</a>
            <div>
                <button id="resetEventsGrid" class="btn btn-danger">
                    Reset Grid View
                </button>                
                <button class="btn btn-success k-grid-excel">
                    <i class="fa fa-file-excel"></i>
                    Export
                </button>
            </div>
        </div>
    </script>                          

    <script id="opportunitiesGridToolbar" type="text/x-kendo-tmpl">
        <div class="d-flex w-100 justify-content-between toolbar-inline-padding">
            <a class="btn btn-success" href="@Url.Action("Add", "Sales" , new { @type=OpportunityTypeConstant.Opportunity })"><span></span><i class="fa fa-plus"></i>Add New Opportunity</a>
            <div>
                <button id="resetOpportunitiesGrid" class="btn btn-danger">
                    Reset Grid View
                </button>                
                <button class="btn btn-success k-grid-excel">
                    <i class="fa fa-file-excel"></i>
                    Export
                </button>
            </div>
        </div>
    </script>                                

    <script id="salesGridToolbar" type="text/x-kendo-tmpl">
        <div class="d-flex w-100 justify-content-between toolbar-inline-padding">
            <a class="btn btn-success" href="@Url.Action("Add", "Sales" , new { @type=OpportunityTypeConstant.Lead })"><span></span><i class="fa fa-plus"></i>Add New Lead</a>
            <div>
                <button id="resetSalesGrid" class="btn btn-danger">
                    Reset Grid View
                </button>                
                <button class="btn btn-success k-grid-excel">
                    <i class="fa fa-file-excel"></i>
                    Export
                </button>
            </div>
        </div>
    </script>

    <environment include="Development">
        <script src="~/js/views/sales/matrix.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script src="~/js/views/sales/matrix.min.js" asp-append-version="true"></script>
    </environment>