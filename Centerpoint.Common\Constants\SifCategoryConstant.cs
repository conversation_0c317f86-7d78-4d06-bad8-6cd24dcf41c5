﻿namespace Centerpoint.Common.Constants
{
    public static class SifCategoryConstant
    {

        public const string Quality = "QUA";
        public const string Health = "HEA";
        public const string Safety = "SAF";
        public const string Environment = "ENV";
        public const string Security = "SEC";
        public const string Improvement = "IMP";

        public static string GetDescription(string value)
        {
            return !string.IsNullOrEmpty(value) && ValuesAndDescriptions.ContainsKey(value) ? ValuesAndDescriptions[value] : null;
        }

        public static string GetValue(string description)
        {
            return ValuesAndDescriptions.Where(v => v.Value == description).Select(v => v.Key).FirstOrDefault();
        }

        public static Dictionary<string, string> ValuesAndDescriptions
        {
            get
            {
                return new Dictionary<string, string> {
                    {Quality,"Quality"},
                    {Health,"Health"},
                    {Safety,"Safety"},
                    {Environment,"Environment"},
                    {Security,"Security"},
                    {Improvement,"Improvement"}
                };
            }
        }
    }
}
