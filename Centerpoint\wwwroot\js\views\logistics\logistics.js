$(document).ready(function () {
    getEquipmentShipmentMonthlySummary();
    var currentEquipmentShipmentGrid = $('#currentEquipmentShipmentGrid').data("kendoGrid");
    var receivedEquipmentShipmentGrid = $('#receivedEquipmentShipmentGrid').data("kendoGrid");
    currentEquipmentShipmentGrid.bind('dataBound', function (e) {
        this.element.find('.k-i-excel').remove();
    });
    receivedEquipmentShipmentGrid.bind('dataBound', function (e) {
        this.element.find('.k-i-excel').remove();
    });
    loadCurrentEquipmentShipmentGrid();
    loadReceivedEquipmentShipmentGrid();
    kendo.bind(document.body.children, viewModel);
});

function saveCurrentEquipmentShipmentGrid(e) {
    setTimeout(function(){
        var currentEquipmentShipmentGrid = $("#currentEquipmentShipmentGrid").data("kendoGrid");
        localStorage["currentEquipmentShipmentGrid"] = kendo.stringify(currentEquipmentShipmentGrid.getOptions());
    },10);
}

function loadCurrentEquipmentShipmentGrid() {
    var currentEquipmentShipmentGrid = $("#currentEquipmentShipmentGrid").data("kendoGrid");
    var toolBar = $("#currentEquipmentShipmentGrid .k-grid-toolbar").html();
    var options = localStorage["currentEquipmentShipmentGrid"];
    viewModel.set("initialCurrentEquipmentShipmentGridOptions", kendo.stringify(currentEquipmentShipmentGrid.getOptions()));
    if (options) {
        currentEquipmentShipmentGrid.setOptions(JSON.parse(options));
        $("#currentEquipmentShipmentGrid .k-grid-toolbar").html(toolBar);
        $("#currentEquipmentShipmentGrid .k-grid-toolbar").addClass("k-grid-top");
    }
}

function saveReceivedEquipmentShipmentGrid(e) {
    setTimeout(function(){
        var receivedEquipmentShipmentGrid = $("#receivedEquipmentShipmentGrid").data("kendoGrid");
        localStorage["receivedEquipmentShipmentGrid"] = kendo.stringify(receivedEquipmentShipmentGrid.getOptions());
    },10);
}

function loadReceivedEquipmentShipmentGrid() {
    var receivedEquipmentShipmentGrid = $("#receivedEquipmentShipmentGrid").data("kendoGrid");
    var toolBar = $("#receivedEquipmentShipmentGrid .k-grid-toolbar").html();
    var options = localStorage["receivedEquipmentShipmentGrid"];
    viewModel.set("initialReceivedEquipmentShipmentGridOptions", kendo.stringify(receivedEquipmentShipmentGrid.getOptions()));
    if (options) {
        receivedEquipmentShipmentGrid.setOptions(JSON.parse(options));
        $("#receivedEquipmentShipmentGrid .k-grid-toolbar").html(toolBar);
        $("#receivedEquipmentShipmentGrid .k-grid-toolbar").addClass("k-grid-top");
    }
}

function monthChanged(e){
    getEquipmentShipmentMonthlySummary();
    refreshCurrentEquipmentShipmentGrid();
    refreshReceivedEquipmentShipmentGrid();
}

function yearChanged(e){
    getEquipmentShipmentYearlySummary();
    refreshCurrentEquipmentShipmentGrid();
    refreshReceivedEquipmentShipmentGrid();
}

function getEquipmentShipmentMonthlySummary() {
    $.ajax({
        url: "/Logistics/GetEquipmentShipmentMonthlySummary",
        dataType: "json",
        data: {
            month:viewModel.get("month")
        },
        success: function(result){
            if(result){
                viewModel.set("totalMonth",result.Total);
                viewModel.set("totalSentMonth",result.TotalSent);
                viewModel.set("totalReceivedMonth",result.TotalReceived);
                viewModel.set("totalInTransitMonth",result.TotalInTransit);
                viewModel.set("totalPendingMonth",result.TotalPending);
            }
        }
    });
}

function getEquipmentShipmentYearlySummary() {
    $.ajax({
        url: "/Logistics/GetEquipmentShipmentYearlySummary",
        dataType: "json",
        data: {
            year:viewModel.get("year")
        },
        success: function(result){
            if(result){
                viewModel.set("totalYear",result.Total);
                viewModel.set("totalSentYear",result.TotalSent);
                viewModel.set("totalReceivedYear",result.TotalReceived);
                viewModel.set("totalInTransitYear",result.TotalInTransit);
                viewModel.set("totalPendingYear",result.TotalPending);
            }
        }
    });
}

function onCurrentError(e, status) {
    if (e.status == "customerror") {
        alert(e.errors);

        var currentEquipmentShipmentGrid = $("#currentEquipmentShipmentGrid").data("kendoGrid");
        currentEquipmentShipmentGrid.dataSource.cancelChanges();
    }
}

function onReceivedError(e, status) {
    if (e.status == "customerror") {
        alert(e.errors);

        var receivedEquipmentShipmentGrid = $("#receivedEquipmentShipmentGrid").data("kendoGrid");
        receivedEquipmentShipmentGrid.dataSource.cancelChanges();
    }
}

function updateCurrentEquipmentShipmentGrid() {
    $("#resetCurrentEquipmentShipmentGrid").click(function (e) {
        e.preventDefault();
        resetGridView('currentEquipmentShipmentGrid', 'initialCurrentEquipmentShipmentGridOptions')
    });

    var currentEquipmentShipmentGrid = $("#currentEquipmentShipmentGrid").data("kendoGrid");
    var totalCurrentEquipmentShipments = currentEquipmentShipmentGrid.dataSource.total();
    viewModel.set("totalCurrentEquipmentShipments", totalCurrentEquipmentShipments);

    var shipmentData = currentEquipmentShipmentGrid.dataSource.data();

    $.each(shipmentData, function (i, item) {
        if (item.ShipmentCount && item.Received) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(13)').css("background-color", "#ff7f7f  ");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(14)').css("background-color", "#ff7f7f  ");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(13)').css("color", "#fff");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(14)').css("color", "#fff");
        }
        if (item.NonAssetItemShipmentCount && item.Received) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(15)').css("background-color", "#ff7f7f  ");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(16)').css("background-color", "#ff7f7f  ");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(15)').css("color", "#fff");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(16)').css("color", "#fff");
        }
    });
}

function updateReceivedEquipmentShipmentGrid() {
    $("#resetReceivedEquipmentShipmentGrid").click(function (e) {
        e.preventDefault();
        resetGridView('receivedEquipmentShipmentGrid', 'initialReceivedEquipmentShipmentGridOptions')
    });

    var receivedEquipmentShipmentGrid = $("#receivedEquipmentShipmentGrid").data("kendoGrid");
    var totalReceivedEquipmentShipments = receivedEquipmentShipmentGrid.dataSource.total();
    viewModel.set("totalReceivedEquipmentShipments", totalReceivedEquipmentShipments);

    var shipmentData = receivedEquipmentShipmentGrid.dataSource.data();

    $.each(shipmentData, function (i, item) {
        if (item.ShipmentCount && item.Received) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(13)').css("background-color", "#ff7f7f  ");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(14)').css("background-color", "#ff7f7f  ");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(13)').css("color", "#fff");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(14)').css("color", "#fff");
        }
        if (item.NonAssetItemShipmentCount && item.Received) {
            $('tr[data-uid="' + item.uid + '"] td:nth-child(15)').css("background-color", "#ff7f7f  ");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(16)').css("background-color", "#ff7f7f  ");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(15)').css("color", "#fff");
            $('tr[data-uid="' + item.uid + '"] td:nth-child(16)').css("color", "#fff");
        }
    });
}

function refreshCurrentEquipmentShipmentGrid() {
    kendo.ui.progress($("#currentEquipmentShipmentGrid"), true);
    var currentEquipmentShipmentGrid = $("#currentEquipmentShipmentGrid").data("kendoGrid");
    currentEquipmentShipmentGrid.dataSource.read();
    kendo.ui.progress($("#currentEquipmentShipmentGrid"), false);
}

function refreshReceivedEquipmentShipmentGrid() {
    kendo.ui.progress($("#receivedEquipmentShipmentGrid"), true);
    var receivedEquipmentShipmentGrid = $("#receivedEquipmentShipmentGrid").data("kendoGrid");
    receivedEquipmentShipmentGrid.dataSource.read();
    kendo.ui.progress($("#receivedEquipmentShipmentGrid"), false);

}

function logisticsData() {
    return{
        type: viewModel.get("type"),
        month: viewModel.get("month"),
        year: viewModel.get("year"),
        shipmentMethod:viewModel.get("shipmentMethod")
    }
}

function shipmentMethodFilter(shipmentMethod){
    viewModel.set("type", "shipmentMethod");
    viewModel.set("shipmentMethod", shipmentMethod);

    refreshCurrentEquipmentShipmentGrid();
    refreshReceivedEquipmentShipmentGrid();
}

var viewModel = kendo.observable({
    type: "",
    totalCurrentEquipmentShipments: 0,
    totalCurrentEquipmentShipmentsText: function(){
        return `<span class="k-link"><i class="fa fa-file-text mr-1"></i> Current Shipments (<span data-bind="text: totalCurrentEquipmentShipments"></span>)</span>`;
    },
    totalReceivedEquipmentShipments: 0,
    totalEquipmentShipments: function() {
        let totalEquipmentShipments = this.get("totalCurrentEquipmentShipments") + this.get("totalReceivedEquipmentShipments")
        return totalEquipmentShipments;
    },
    totalReceivedEquipmentShipmentsText: function(){
        return `<span class="k-link"><i class="fa fa-check mr-1"></i> Received Shipments (<span data-bind="text: totalReceivedEquipmentShipments"></span>)</span>`;   
    },
    month:logisticsModel.month,
    year: logisticsModel.year,
    shipmentMethod:"",
    totalSentMonth: logisticsModel.totalSentMonth,
    totalReceivedMonth: logisticsModel.totalReceivedMonth,
    totalInTransitMonth: logisticsModel.totalInTransitMonth,
    totalPendingMonth: logisticsModel.totalPendingMonth,
    totalSentYear: logisticsModel.totalSentYear,
    totalReceivedYear: logisticsModel.totalReceivedYear,
    totalInTransitYear: logisticsModel.totalInTransitYear,
    totalPendingYear: logisticsModel.totalPendingYear,
    totalMonth: logisticsModel.totalMonth,
    totalYear: logisticsModel.totalYear,

    totalMonthClick: function () {
        this.set("type", "totalMonth");
        refreshCurrentEquipmentShipmentGrid();
        refreshReceivedEquipmentShipmentGrid();
    },

    totalSentMonthClick: function () {
        this.set("type", "totalSentMonth");
        refreshCurrentEquipmentShipmentGrid();
        refreshReceivedEquipmentShipmentGrid();
    },

    totalReceivedMonthClick: function () {
        this.set("type", "totalReceivedMonth");
        refreshReceivedEquipmentShipmentGrid();
        $("#mainTabStrip").data("kendoTabStrip").select(1);
    },

    totalInTransitMonthClick: function () {
        this.set("type", "totalInTransitMonth");
        refreshCurrentEquipmentShipmentGrid();
        $("#mainTabStrip").data("kendoTabStrip").select(0);
    },

    totalPendingMonthClick: function () {
        this.set("type", "totalPendingMonth");
        refreshCurrentEquipmentShipmentGrid();
        $("#mainTabStrip").data("kendoTabStrip").select(0);
    },

    totalYearClick: function () {
        this.set("type", "totalYear");
        refreshCurrentEquipmentShipmentGrid();
        refreshReceivedEquipmentShipmentGrid();
    },

    totalSentYearClick: function () {
        this.set("type", "totalSentYear");
        refreshCurrentEquipmentShipmentGrid();
        refreshReceivedEquipmentShipmentGrid();
    },

    totalReceivedYearClick: function () {
        this.set("type", "totalReceivedYear");
        refreshReceivedEquipmentShipmentGrid();
        $("#mainTabStrip").data("kendoTabStrip").select(1);
    },

    totalInTransitYearClick: function () {
        this.set("type", "totalInTransitYear");
        refreshCurrentEquipmentShipmentGrid();
        $("#mainTabStrip").data("kendoTabStrip").select(0);
    },

    totalPendingYearClick: function () {
        this.set("type", "totalPendingYear");
        refreshCurrentEquipmentShipmentGrid();
        $("#mainTabStrip").data("kendoTabStrip").select(0);
    },
});


