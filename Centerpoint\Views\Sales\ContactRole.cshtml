﻿<div class="form-group">
    <label class="d-block">Role</label>
    @(Html.Kendo().DropDownList()
        .Name("role")
        .Filter(FilterType.Contains)
        .OptionLabel("Select Role")
        .DataValueField("Key")
        .DataTextField("Value")
        .HtmlAttributes(new {@data_bind = "value:role", @data_value_update = "keyup" })
        .BindTo(Centerpoint.Common.Constants.OpportunityContactRoleConstant.ValuesAndDescriptions.ToList())
    )
</div>
<div class="d-flex justify-content-end">
    <button id="addContactConfirm" class="btn btn-primary btn-sm" data-bind="visible:role">Confirm</button>
</div>


