<div class="grid-container-standard">
    @(
        Html.<PERSON>().Grid<ProjectCommentModel>()
                        .Name("projectCommentGrid")
                        .Columns(c => {
                            c.Bound(p => p.Comment).Encoded(false).ClientTemplate("#= getHtmlNewLinesString(Comment) #");
                            c.<PERSON>(p => p.UserName).Width(300);
                            c.<PERSON>(c => c.Date).Format(DateConstants.DateTimeFormat).Width(300);
                            c.Command(command => {
                                command.Edit().HtmlAttributes(new { @class = "bg-primary text-white grid-action-button" }).Visible("projectCommentGridActionsVisible");
                                command.Destroy().HtmlAttributes(new { @class = "bg-danger text-white grid-action-button" }).Visible("projectCommentGridActionsVisible");
                            }).Width(220);
                        })
                            .Editable(editable => editable
                                .Mode(GridEditMode.PopUp)
                                .DisplayDeleteConfirmation("Are you sure you want to delete this Comment?")
                                .TemplateName("ProjectCommentWindow")
                            .Window(w => w
                                .Name("ProjectCommentWindow")
                                .Title("Project Comment")
                                .Width(850)
                                .Draggable(false)
                                )
                            )
                            .ToolBar(c => c.Create().Text("Add Comments")
                            )
                            .Sortable()
                            .Filterable()
                            .Groupable()
                            .Events(e => e.DataBound("updateProjectCommentGrid").Edit("projectCommentEdit"))
                            .Scrollable()
                            .Resizable(c => c.Columns(true))
                            .ColumnMenu(c => c.Columns(true))
                            .DataSource(dataSource => dataSource
                                .Ajax()
                                .ServerOperation(false)
                                .Model(m => {
                                    m.Id(p => p.ProjectCommentId);
                                    m.Field(p => p.Date);
                                })
            .Events(e => e.Error("ProjectCommentError").RequestEnd("onRequestEndRefreshCommentGrid"))
            .Read(read => read.Action("GetProjectComments", "Operation", new { @projectId = Model.ProjectId }))
            .Create(create => create.Action("UpdateProjectComment", "Operation", new { @pId = Model.ProjectId }).Data("commentData"))
            .Update(update => update.Action("UpdateProjectComment", "Operation", new { @pId = Model.ProjectId }).Data("commentData"))
            .Destroy(destroy => destroy.Action("DeleteProjectComment", "Operation"))
        )
    )
)
</div>

<script>
    function projectCommentGridActionsVisible (data) {
        return data.UserName && data.UserName == "@Html.AccountUserFullName()" ? true : false
    }
</script>
