﻿using Microsoft.AspNetCore.StaticFiles;

namespace Centerpoint.Extensions
{
    public static class FileExtensions
    {
        public static string GetMimeTypeForFileExtension(this string fileName)
        {
            const string DefaultContentType = "application/octet-stream";

            var provider = new FileExtensionContentTypeProvider();

            if (!provider.TryGetContentType(fileName, out string contentType))
            {
                contentType = DefaultContentType;
            }

            return contentType;
        }
    }
}
