@import "defaultCorrections.css";
@import url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css");

html,
body{
    padding: 0;
    margin: 0;
    background-color: #e5e5e5;
    height: 100vh;
    font-family: "Open Sans", sans-serif;
    font-size: 13px;
}

.img-cover{
    height: 100%;
    width: 100%;
    object-fit: cover; 
}

.btn,
.btn:hover,
.btn:active,
.btn:focus{
    color: #ffffff!important;
}

.k-panelbar > .k-item > .k-link.k-state-selected,
.k-panelbar > .k-item > .k-link.k-selected,
.k-panelbar > .k-panelbar-header > .k-link.k-state-selected,
.k-panelbar > .k-panelbar-header > .k-link.k-selected,
.k-panelbar > .k-item > .k-link.k-state-selected:hover, 
.k-panelbar > .k-item > .k-link.k-selected:hover, 
.k-panelbar > .k-item > .k-link.k-state-selected.k-state-hover, 
.k-panelbar > .k-item > .k-link.k-selected.k-hover, 
.k-panelbar > .k-panelbar-header > .k-link.k-state-selected:hover, 
.k-panelbar > .k-panelbar-header > .k-link.k-selected:hover, 
.k-panelbar > .k-panelbar-header > .k-link.k-state-selected.k-state-hover, 
.k-panelbar > .k-panelbar-header > .k-link.k-selected.k-hover {
    background: var(--secondary);
}

.k-panelbar > .k-item > .k-link {
    background: var(--veryLightGray)
}

.k-panelbar > .k-item.k-state-active > .k-link {
    border-bottom: 1px solid #dadfe3;
}

.k-panelbar > .k-item > .k-link,
.k-panelbar > .k-panelbar-header > .k-link {
    color: var(--secondary);
}

.k-grid .k-command-cell .k-grid-update,
.k-grid .k-command-cell .k-grid-save-command {
    background-color: var(--success) !important;
}

.k-grid .k-command-cell .k-grid-cancel,
.k-grid .k-command-cell .k-grid-cancel-command {
    background-color: var(--warning) !important;
}

.k-grid tbody a{
    color: var(--primary);
}

.k-command-cell > .k-button-solid-base > .k-icon {
    font-size: 1rem;
}

.k-listview-content a h3 i {
    margin-right: 5px;
}

.btn-sm {
    font-size: 1rem!important;
}
table th{
    font-weight: 400!important;
}
.k-tooltip {
    border-color: var(--accent);
    color: white;
    background-color: var(--accent);
}
.k-tooltip .k-callout {
    color: var(--accent);
}

.k-dialog-buttongroup .btn-primary {
    color: #fff !important;
    background-color: #6eb6b4 !important;
    border-color: #6eb6b4 !important;
}