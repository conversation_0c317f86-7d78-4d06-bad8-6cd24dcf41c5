﻿
<div class="header-container-single-item-with-hr">
    <h4>
        <i class="fa fa-cogs"></i>
        Action Types
        (<span data-bind="text:totalActionTypes"></span>)
    </h4>
</div>
<hr />

<div class="grid-container">
    @(Html.Kendo().Grid<ActionTypeModel>()
        .Name("actionTypeGrid")
        .Columns(c => {
            c.Bound(p => p.Name);
            c.Command(command => { 
                command.Edit().HtmlAttributes(new{@class="bg-primary text-white grid-action-button"});
                command.Destroy().HtmlAttributes(new{@class="bg-danger text-white grid-action-button"}); 
            }).Width(200);
        })
        .Editable(editable => editable.Mode(GridEditMode.InLine))
        .ToolBar(t => {
            t.Create().Text("Add Action Type");
            t.Excel().Text("Export");
        }).HtmlAttributes( new { @class="justify-toolbar-content-between"})
        .Sortable()
        .Mobile(MobileMode.Auto)
        .Filterable()
        .Scrollable(s => s.Height("auto"))
        .Resizable(c => c.Columns(true))
        .ColumnMenu(c => c.Columns(true))
        .Events(e => e.DataBound("updateActionTypeTotal"))
        .Excel(excel => excel
            .FileName(string.Format("Centerpoint_Action_Types_Export_{0}.xlsx", DateTime.Now.ToString("ddMMyyHHmm")))
            .Filterable(true)
            .ProxyURL(Url.Action("Export", "Admin"))
        )
        .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Model(m => m.Id(p => p.ActionTypeId))
            .Events(e => e.Error("onError"))
            .Read(read => read.Action("GetActionTypes", "Admin"))
            .Create(create => create.Action("UpdateActionType", "Admin"))
            .Update(update => update.Action("UpdateActionType", "Admin"))
            .Destroy(destroy => destroy.Action("DeleteActionType", "Admin"))
        )
    )
</div>


<script>
    $(document).ready(function () {
        var actionTypeGrid = $('#actionTypeGrid').data("kendoGrid");
        actionTypeGrid.bind('dataBound', function (e) {
            this.element.find('.k-i-excel').remove();
        });
    });

    function updateActionTypeTotal() {
        var actionTypeGrid = $("#actionTypeGrid").data("kendoGrid");
        var totalActionTypes = actionTypeGrid.dataSource.total();
        viewModel.set("totalActionTypes", totalActionTypes);
    }

    function onError(e, status) {
        if (e.status == "customerror") {
            alert(e.errors);

            var actionTypeGrid = $("#actionTypeGrid").data("kendoGrid");
            actionTypeGrid.dataSource.cancelChanges();
        }
    }

    var viewModel = new kendo.observable({
        totalActionTypes: 0
    });

    kendo.bind(document.body.children, viewModel);
</script>