using Centerpoint.Extensions;

var builder = WebApplication.CreateBuilder(args);

builder.Services.ConfigureLocalizationService();
builder.Services.ConfigureAuthentication(builder.Configuration);
builder.Services.ConfigureControllers();
builder.Services.AddKendo();
builder.Services.ConfigureEmail(builder.Configuration);
builder.Services.ConfigureLogger();
builder.Services.AddHttpContextAccessor();
builder.Services.AddMemoryCache();
builder.Services.ConfigureSettings(builder.Configuration);
builder.Services.ConfigureServices(builder.Configuration);
builder.Services.ConfigureUtilities();
builder.Services.ConfigureAutoMapper();
builder.Services.AddResponseCompression();
builder.Services.AddOptions();
builder.Services.ConfigureHangFire(builder.Configuration);
builder.Services.ConfigureMvc();
builder.Services.ConfigureFormOptions();
//builder.Services.ConfigurationQuerySplittingBehavior(builder.Configuration);

var app = builder.Build();

var logger = app.Services.GetService<ILogger<Program>>();
logger.LogInformation(" builder.Build worked");

try
{
    app.ConfigureLocalizationApp();
    app.MigrateDatabase();
    app.UseCors(x => x.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader());
    app.UseHttpsRedirection();
    app.ConfigureCookiePolicy();
    app.UseResponseCaching();
    app.UseAuthentication();
    app.UseResponseCompression();
    app.ConfigureExceptionHandler();
    app.UseStaticFiles();
    app.UseRouting();
    app.UseAuthorization();
    app.ConfigureEndpoints();
    app.ConfigureHangfire();
    app.ConfigureSyncfusion();
    app.ApplyOneTimeMigrations();

    app.Run();
}
catch(Exception ex)
{
    logger.LogError(ex, "Couldn't run the app");
}