﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Centerpoint.DataAccess.Migrations
{
    /// <inheritdoc />
    public partial class InsertSapRole : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(
                @"IF NOT EXISTS (SELECT 1 FROM dbo.AspNetRoles WHERE Name = 'SAP')
                  INSERT INTO dbo.AspNetRoles (Name, NormalizedName, ConcurrencyStamp)
                  VALUES ('SAP', 'SAP', NULL);"
            );
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
