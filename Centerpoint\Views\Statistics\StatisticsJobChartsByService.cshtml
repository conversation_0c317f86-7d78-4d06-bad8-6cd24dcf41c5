
<div class="card">
    <div class="card-header"> 
        <h6 class="mb-0">Jobs by Service (per <span data-bind="text:jobServiceChartShowBy"></span>)</h6>
    </div>
    <div class="card-body">
        <div class="d-flex">
            <div class="form-group w-50 mr-5">
               <label>Show By</label>
               <input 
                   data-role="dropdownlist" 
                   data-spinners="false" 
                   data-bind="value:jobServiceChartShowBy, source:showByOptions, events:{change:refreshJobServiceChart}" 
                   data-format="0 months" 
                   data-max="12" 
                   data-min="3"
                   />
            </div>
            <div class="form-group w-50">
                <label>Period</label>
                <input 
                   data-role="numerictextbox" 
                   data-spinners="false" 
                   data-bind="value:jobServiceChartMonths, events:{change:refreshJobServiceChart}" 
                   data-format="Last 0 months" 
                   data-min="3"
                   />
            </div>
        </div>
        @(Html.Kendo().Chart<StatisticsServiceModel>()
        .Name("jobServiceChart")
        .Series(c => {
            c.Bar(p => p.Count).Stack("service").Name("#=group.value#");
        })
        .AutoBind(true)
        .ValueAxis(v => v.Numeric().Title("Count").MajorGridLines(l => l.Visible(false)).MinorGridLines(l => l.Visible(false)))
        .Legend(l => l.Visible(true).Position(ChartLegendPosition.Bottom))
        .CategoryAxis(c => c.Categories(p => p.Category))
        .Events(e => e.DataBound("onJobServiceChartDataBound"))
        .Tooltip(t => t.Visible(true).Template("#=category# - #=series.name# (#=value# jobs)"))
        .DataSource(d => d.Group(g => g.Add(p => p.Service)).Sort(s => 
        { 
            s.Add(p => p.Year); 
            s.Add(p => p.Quarter); 
            s.Add(p => p.Month); 
        })
        .Read(read => read.Action("GetJobServiceChartData", "Statistics").Data("jobServiceChartData")))
        )
    </div>
</div>