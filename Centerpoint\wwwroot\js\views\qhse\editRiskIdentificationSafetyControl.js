
    $(document).ready(function () {
        var notJobRelated = viewModel.get("notRelated");
        if (notJobRelated == "true") {
            resetJob(true);
        }
    });
    function resetJob(chk) {
        var dropdownlist = $("#ItemId").data("kendoDropDownList");
        dropdownlist.text("");
        if(chk == true){
            dropdownlist.enable(false)
            viewModel.set("notRelated", "true")
        } else {
            dropdownlist.enable(true)
            viewModel.set("notRelated", "false")
        }
    }

    if (editRiskIdentificationSafetyControlModel.modelDateMonth == editRiskIdentificationSafetyControlModel.riscStatusConstantCompleted ) {
        $('#riscForm').find('input, textarea, button, select').attr('disabled', 'disabled');
    }

function dialogMessage(titleMes, contentMes, hrefMes) {
    $("<div id='dialog'></div>").kendoDialog({
        closable: false,
        title: titleMes,
        close: () => {
            var dialog = $("#dialog").data("kendoDialog");
            dialog.destroy();
        },
        width: 500,
        buttonLayout: "normal",
        content: contentMes,
        actions: [
            {
                text: "OK",
                action: function () {
                    window.location.href = hrefMes;
                },
                cssClass: 'btn-primary'
            }]
    }).data("kendoDialog").open().center()
}

    var viewModel = new kendo.observable({
        notJobRelated: editRiskIdentificationSafetyControlModel.modelNotJobRelated,
        comment: editRiskIdentificationSafetyControlModel.modelComment,
        deleteRisc: function (event) {
            event.preventDefault();
            var confirmation = confirm(`Are you sure you want to delete this ${settings.safetyCardName}`);

            if (confirmation) {
                dialogMessage(`${settings.safetyCardName} Deleted`, `${settings.safetyCardName} has been successfully deleted`, `/Qhse/DeleteRiskIdentificationSafetyControl?id=${editRiskIdentificationSafetyControlModel.modelRiskIdentificationSafetyControlId}`);
            }
        },

        canEdit: function () {
            return editRiskIdentificationSafetyControlModel.modelStatus == editRiskIdentificationSafetyControlModel.riscStatusConstantSubmitted;
        },
        
        signOffRisc: function (event) {
            event.preventDefault();
            var confirmation = confirm(`Are you sure you want to sign off this ${editRiskIdentificationSafetyControlModel.cardName}?`);
            var comment = this.get("comment") || $("#HseComment").val();
            if (confirmation) {
                var status = editRiskIdentificationSafetyControlModel.riscStatusConstantCompleted;

                $.ajax({
                    type: "POST",
                    url: `/Qhse/SignoffRiskIdentificationSafetyControl?id=${editRiskIdentificationSafetyControlModel.modelRiskIdentificationSafetyControlId}`,
                    data: {
                        id: editRiskIdentificationSafetyControlModel.modelRiskIdentificationSafetyControlId,
                        comment: comment,

                    },
                    success: function () {
                        dialogMessage(`${settings.safetyCardName} Completed`, `${settings.safetyCardName} has been successfully updated`, `/Qhse/RiskIdentificationSafetyControl?month=${editRiskIdentificationSafetyControlModel.modelDateMonth}`);
                    },
                    dataType: "json"
                });
            }
        },
    });
    kendo.bind(document.body.children, viewModel);