﻿<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            @(Html.Kendo().DropDownList()
             .Name("customer")
             .Filter(FilterType.Contains)
             .OptionLabel("Select Focus Entity")
             .DataTextField("Text")
             .DataValueField("Value")
             .AutoBind(false)
             .DataSource(d => d.Read("GetFocusedEntities", "Lookup"))
             .HtmlAttributes(new { @style = "width:100%", @data_bind = "value:customerId" }))
        </div>
    </div>
</div>
<div class="d-flex justify-content-end">
    <button id="confirmCustomer" class="btn btn-primary btn-sm" data-bind="enabled:customerId">Confirm</button>
</div>


