﻿using Centerpoint.Service.Interfaces;
using Centerpoint.Services;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;

namespace Centerpoint.Controllers
{
    [Authorize]
    public class LookupController : Controller
    {
        private readonly ICurrentUserService _currentUser;
        private readonly ILookupService _lookupService;
        private readonly ICompanyService _companyService;
        private readonly IUserService _userService;

        public LookupController(ICurrentUserService currentUser,
            ILookupService lookupService,
            ICompanyService companyService,
            IUserService userService
            )
        {
            _currentUser = currentUser;
            _lookupService = lookupService;
            _companyService = companyService;
            _userService = userService;
        }

        public async Task<ActionResult> GetCompanies()
        {
            return Json(await _companyService.GetCompaniesAsync());
        }

        public async Task<ActionResult> GetCompanyByOperatorOrPartnerId(int? companyId, int? partnerCompanyId, string text)
        {
            return Json(await _companyService.GetCompanyByOperatorOrPartnerId(companyId, partnerCompanyId, text));
        }

        public async Task<ActionResult> GetFocusedCompanies()
        {
            return Json(await _companyService.GetFocusedCompanies());
        }

        public async Task<JsonResult> GetPartnerCompanies()
        {
            return Json(await _companyService.GetPartnerCompanies());
        }

        public async Task<JsonResult> GetOperatorCompanies()
        {
            return Json(await _companyService.GetOperatorCompanies());
        }

        public async Task<ActionResult> GetActionEventCompanies()
        {
            return Json(await _companyService.GetActionEventCompanies());
        }

        public async Task<ActionResult> GetFocusedEntities()
        {
            return Json(await _companyService.GetFocusedEntities());
        }

        public async Task<ActionResult> GetCountriesByFromCompanyId(int companyId)
        {
            return Json(await _companyService.GetCountriesByFromCompanyId(companyId));
        }

        public async Task<ActionResult> GetCountriesByPartnerCompanyId(int partnerCompanyId)
        {
            return Json(await _companyService.GetCountriesByPartnerCompanyId(partnerCompanyId));
        }

        public async Task<ActionResult> GetAllCompanyLocations()
        {
            return Json(await _companyService.GetAllCompanyLocations());
        }

        public async Task<ActionResult> GetInternalCompanyLocations()
        {
            return Json(await _companyService.GetInternalCompanyLocations());
        }
        public async Task<ActionResult> GetBaseCompanyLocations()
        {
            var result = await _companyService.GetBaseCompanyLocations();
            return Json(result);
        }

        public async Task<JsonResult> GetUsers()
        {
            var result = await _userService.GetUsers();
            return Json(result);
        }

        public async Task<ActionResult> GetSalesUsers(bool excludeUser = false)
        {
            return Json(await _userService.GetSalesUsers(_currentUser.UserId, excludeUser));
        }

        public async Task<ActionResult> GetEngineers()
        {
            return Json(await _userService.GetEngineers());
        }

        public async Task<ActionResult> GetFieldEngineers()
        {
            return Json(await _userService.GetFieldEngineers());
        }

        public async Task<ActionResult> GetSifAdmins()
        {
            return Json(await _userService.GetSifAdmins());
        }

        public async Task<ActionResult> GetSifActionParties()
        {
            return Json(await _userService.GetSifActionParties());
        }

        public async Task<ActionResult> GetShipmentMethods()
        {
            return Json(await _lookupService.GetShipmentMethods());
        }

        public async Task<ActionResult> GetMaintenanceBlueprints()
        {
            return Json(await _lookupService.GetMaintenanceBlueprints());
        }

        public async Task<ActionResult> GetAllMaintenanceBlueprintsByCategoryId(int equipmentCategoryId)
        {
            return Json(await _lookupService.GetAllMaintenanceBlueprintsByCategoryId(equipmentCategoryId));
        }

        public async Task<ActionResult> GetManufacturers()
        {
            return Json(await _lookupService.GetManufacturers());
        }

        public async Task<ActionResult> GetEquipmentItemCustomStatusCodes()
        {
            return Json(await _lookupService.GetEquipmentItemCustomStatusCodes());
        }

        public async Task<ActionResult> GetCustomStatusCode()
        {
            return Json(await _lookupService.GetCustomStatusCode());
        }

        public async Task<ActionResult> GetCustomStatusCodeByEquipmentItemId(int equipmentItemId)
        {
            return Json(await _lookupService.GetCustomStatusCodeByEquipmentItemId(equipmentItemId));
        }
        public async Task<ActionResult> GetEquipmentItems()
        {
            return Json(await _lookupService.GetEquipmentItems());
        }

        public async Task<ActionResult> GetEquipmentItemNotExpiredCustomStatusCodeByEquipmentItemId(int equipmentItemId)
        {
            
            var result = await _lookupService.GetEquipmentItemNotExpiredCustomStatusCodeByEquipmentItemId(equipmentItemId);
            return Json(result);
        }

        public async Task<JsonResult> GetDivisions()
        {
            return Json(await _lookupService.GetDivisions());
        }

        public async Task<ActionResult> GetActionTypes()
        {
            return Json(await _lookupService.GetActionTypes());
        }

        public async Task<ActionResult> GetClosureReasons()
        {
            return Json(await _lookupService.GetClosureReasons());
        }

        public async Task<ActionResult> GetEventTypes()
        {
            return Json(await _lookupService.GetEventTypes());
        }

        public async Task<ActionResult> GetAllOpportunities(int? opportunityId)
        {
            return Json(await _lookupService.GetAllOpportunities(opportunityId));
        }

        public async Task<ActionResult> GetCompanyWellFluids(string text)
        {
            return Json(await _lookupService.GetCompanyWellFluids(text));
        }

        public async Task<ActionResult> GetCompanyWellFluidsByCompanyWellId(int? companyWellId)
        {
            return Json(await _lookupService.GetCompanyWellFluidsByCompanyWellId(companyWellId));
        }

        public async Task<ActionResult> GetCurrencies()
        {
            return Json(await _lookupService.GetCurrencies());
        }
        public async Task<ActionResult> GetFullCurrencies()
        {
            return Json(await _lookupService.GetFullCurrencies());
        }

        public async Task<ActionResult> GetObjectives(string text)
        {
            return Json(await _lookupService.GetObjectives(text));
        }

        public async Task<ActionResult> GetCategories(string text)
        {
            return Json(await _lookupService.GetCategories(text));
        }

        public async Task<ActionResult> GetSeverities()
        {
            return Json(await _lookupService.GetSeverities());
        }

        public async Task<ActionResult> GetServiceImprovementLocations()
        {
            return Json(await _lookupService.GetServiceImprovementLocations());
        }

        public async Task<ActionResult> GetSubCategories(string text)
        {
            return Json(await _lookupService.GetSubCategories(text));
        }

        public async Task<ActionResult> GetCertificateCategories()
        {
            return Json(await _lookupService.GetCertificateCategories());
        }

        public async Task<ActionResult> GetObjectivesbyJobId(int jobId, string text)
        {
            return Json(await _lookupService.GetObjectivesbyJobId(jobId, text));
        }

        public async Task<ActionResult> GetObjectivesbyProjectId(int projectId, string text)
        {
            return Json(await _lookupService.GetObjectivesbyProjectId(projectId, text));
        }

        public async Task<ActionResult> GetCrewUsersByProjectId(int projectId, string text)
        {
            return Json(await _lookupService.GetCrewUsersByProjectId(projectId, text));
        }
        public async Task<ActionResult> GetProjects(string status)
        {
            return Json(await _lookupService.GetProjects(status));
        }

        public async Task<ActionResult> GetActiveProjects(string status)
        {
            return Json(await _lookupService.GetActiveProjects(status));
        }
        public async Task<ActionResult> GetProjectsNamesByStatus(string status)
        {
            return Json(await _lookupService.GetProjectsNamesByStatus(status));
        }

        public async Task<ActionResult> GetAllProjects()
        {
            return Json(await _lookupService.GetAllProjects());
        }

        public async Task<ActionResult> GetAllActiveandClosedProjects()
        {
            return Json(await _lookupService.GetAllActiveandClosedProjects());
        }

        public async Task<ActionResult> GetAllActiveandUpcomingProjects()
        {
            return Json(await _lookupService.GetAllActiveandUpcomingProjects());
        }

        public async Task<ActionResult> GetProjectsByUserId(int userId)
        {
            return Json(await _lookupService.GetProjectsByUserId(userId));
        }

        public async Task<ActionResult> GetActiveOppurtunityProjects(string status)
        {
            return Json(await _lookupService.GetActiveOppurtunityProjects(status));
        }
        public async Task<ActionResult> GetActiveOppurtunityAssociatedProjects(string status)
        {
            return Json(await _lookupService.GetActiveOppurtunityAssociatedProjects(status));
        }

        public async Task<ActionResult> GetJobs()
        {
            return Json(await _lookupService.GetJobs());
        }

        public async Task<ActionResult> GetJobsWithRuns(int projectId)
        {
            return Json(await _lookupService.GetJobsWithRuns(projectId));
        }

        public async Task<ActionResult> GetJobsByCompanyId(int companyId, string text)
        {
            var result = await _lookupService.GetJobsByCompanyId(companyId, text);
            return Json(result);
        }
        public async Task<ActionResult> GetProjectsByCompanyId(int companyId, string text)
        {
            var result = await _lookupService.GetProjectsByCompanyId(companyId, text);
            return Json(result);
        }

        public async Task<ActionResult> GetCountries()
        {
            return Json(_lookupService.GetCountries());
        }

        public async Task<ActionResult> GetEquipmentItemStatuses()
        {
            return Json(_lookupService.GetEquipmentItemStatuses());
        }

        public async Task<ActionResult> GetWarningThresholds()
        {
            return Json(_lookupService.GetWarningThresholds());
        }

        public async Task<ActionResult> GetMonths()
        {
            return Json(_lookupService.GetMonths());
        }

        public async Task<ActionResult> GetSummaryByDays()
        {
            return Json(_lookupService.GetSummaryByDays());
        }

        public async Task<ActionResult> GetSummaryByPoints()
        {
            return Json(_lookupService.GetSummaryByPoints());
        }

        public async Task<ActionResult> GetYears(int years)
        {
            return Json(_lookupService.GetYears(years));
        }

        public async Task<ActionResult> GetAllYears()
        {
            return Json(_lookupService.GetAllYears());
        }

        public async Task<ActionResult> GetDays()
        {
            return Json(_lookupService.GetDays());
        }

        public async Task<ActionResult> GetRISCYears()
        {
            return Json(_lookupService.GetRISCYears());
        }

        public async Task<ActionResult> GetFieldsByCompanyId(int? companyId, int? operatorCompanyId, string text)
        {
            return Json(await _lookupService.GetFieldsByCompanyId(companyId, operatorCompanyId, text));
        }

        public async Task<ActionResult> GetFieldsByProjectId(int projectId)
        {
            var result = await _lookupService.GetFieldsByProjectId(projectId);
            return Json(result);
        }

        public async Task<ActionResult> GetFieldsByProjecOpportunityId(int projectOpportunityId)
        {
            return Json(await _lookupService.GetFieldsByProjecOpportunityId(projectOpportunityId));
        }

        public async Task<ActionResult> GetFieldsByCompanyOrPartnerId(int? companyId, int? partnerCompanyId, string text)
        {
            return Json(await _lookupService.GetFieldsByCompanyOrPartnerId(companyId, partnerCompanyId, text));
        }

        public async Task<ActionResult> GetOppsFieldsByCompanyId(int? oppsCompanyId, int? oppsPartnerCompanyId, string text)
        {
            return Json(await _lookupService.GetOppsFieldsByCompanyId(oppsCompanyId, oppsPartnerCompanyId, text));
        }

        public async Task<ActionResult> GetWellsByCompanyId(int companyId)
        {
            return Json(await _lookupService.GetWellsByCompanyId(companyId));
        }

        public async Task<ActionResult> GetCompanyWellsByCompanyFieldIds(string companyFieldIdString, string text)
        {
            if (string.IsNullOrWhiteSpace(companyFieldIdString))
                return Json(Array.Empty<object>());

            var companyFieldIds = companyFieldIdString
                .Split(";")
                .Select(s =>
                {
                    bool isSuccess = int.TryParse(s, out int id);
                    return new { id, isSuccess };
                })
                .Where(x => x.isSuccess)
                .Select(x => x.id)
                .ToArray();

            return Json(await _lookupService.GetCompanyWellsByCompanyFieldIds(companyFieldIds, text));
        }

        public async Task<ActionResult> GetWellsByCompanyFieldId([DataSourceRequest] DataSourceRequest request, int companyFieldId)
        {
            var result = await _lookupService.GetWellsByCompanyFieldId(companyFieldId);
            return Json(result);
        }

        public async Task<ActionResult> GetLessonCategories(string text)
        {
            return Json(await _lookupService.GetLessonCategories(text));
        }

        public async Task<ActionResult> GetAllOpertionalEquipmentItems()
        {
            return Json(await _lookupService.GetAllOpertionalEquipmentItems());
        }

        public async Task<ActionResult> GetEquipmentCategoriesByEquipmentItem(string text)
        {
            return Json(await _lookupService.GetEquipmentCategoriesByEquipmentItem(text));
        }

        public async Task<ActionResult> GetEquipmentCategoryItemsByDivisionId(int divisionId, string text)
        {
            return Json(await _lookupService.GetEquipmentCategoryItemsByDivisionId(divisionId, text));
        }

        public async Task<ActionResult> GetEquipmentCategoriesByDivisionId(int divisionId, string text)
        {
            return Json(await _lookupService.GetEquipmentCategoriesByDivisionId(divisionId, text));
        }

        public async Task<ActionResult> GetEquipmentItemByEquipmentCategoryId(int equipmentCategoryId)
        {
            return Json(await _lookupService.GetEquipmentItemByEquipmentCategoryId(equipmentCategoryId));
        }

        public async Task<ActionResult> GetAllServiceImprovementsByStatus()
        {
            return Json(await _lookupService.GetAllServiceImprovementsByStatus());
        }

        #region Company Lookups

        public async Task<ActionResult> GetCompanyByProjectId(int projectId)
        {
            return Json(await _companyService.GetCompanyByProjectId(projectId));
        }

        public async Task<ActionResult> GetAllProjectsByCompanyLocationId(int companyLocationId)
        {
            return Json(await _companyService.GetAllProjectsByCompanyLocationId(companyLocationId));
        }
        public async Task<ActionResult> GetProjectsByCompanyLocationId(int companyLocationId, int currentProjectId)
        {
            return Json(await _companyService.GetProjectsByCompanyLocationId(companyLocationId, currentProjectId));
        }

        public async Task<JsonResult> GetLocationsByCompanyId(int? companyId, int? partnerCompanyId)
        {
            return Json(await _companyService.GetLocationsByCompanyId(companyId, partnerCompanyId));
        }

        public async Task<ActionResult> GetEventLocationsByCompanyId(int? eventCompanyId)
        {
            return Json(await _companyService.GetEventLocationsByCompanyId(eventCompanyId));
        }

        public async Task<JsonResult> GetObjectivesByCompanyId(int? companyId, string text)
        {
            return Json(await _companyService.GetObjectivesByCompanyId(companyId, text));
        }

        public async Task<ActionResult> GetLocationsByFromCompanyId(int fromCompanyId)
        {
            return Json(await _companyService.GetLocationsByFromCompanyId(fromCompanyId));
        }

        public async Task<ActionResult> GetLocationsWellsense()
        {
            return Json(await _companyService.GetLocationsWellsense());
        }

        public async Task<ActionResult> GetLocationsByToCompanyId(int toCompanyId)
        {
            return Json(await _companyService.GetLocationsByToCompanyId(toCompanyId));
        }

        public async Task<ActionResult> GetAssetsByFromCompanyId(int fromCompanyId)
        {
            return Json(await _companyService.GetAssetsByFromCompanyId(fromCompanyId));
        }

        public async Task<ActionResult> GetAssetsByToCompanyId(int toCompanyId)
        {
            return Json(await _companyService.GetAssetsByToCompanyId(toCompanyId));
        }

        public async Task<JsonResult> GetContactsByCompanyId(int companyId, string text)
        {
            return Json(await _companyService.GetContactsByCompanyId(companyId, text));
        }

        public async Task<ActionResult> GetDistributionListByCompanyId(int companyId, int projectCompanyId)
        {
            return Json(await _companyService.GetDistributionListByCompanyId(companyId, projectCompanyId));
        }

        public async Task<ActionResult> GetContactsByCompanyLocationId(int companyLocationId)
        {
            return Json(await _companyService.GetContactsByCompanyLocationId(companyLocationId));
        }

        public async Task<ActionResult> GetCompanyContactsFromOpportunityId(int opportunityId, string text)
        {
            return Json(await _companyService.GetCompanyContactsFromOpportunityId(opportunityId, text));
        }

        public async Task<ActionResult> GetLeadCompanyContactsByOpportunityCustomerCountry(string opportunityCustomerCountry, 
            int opportunityCustomerCompanyId, string text)
        {
            return Json(await _companyService.GetLeadCompanyContactsByOpportunityCustomerCountry(opportunityCustomerCountry, opportunityCustomerCompanyId, text));
        }
        #endregion

    }
}
