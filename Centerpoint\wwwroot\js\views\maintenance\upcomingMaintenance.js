function filterChanged(e) {
    var isMaintenance = $("#maintenanceFilter").data("kendoDropDownList");
    viewModel.set("upcomingMaintenance", isMaintenance.value());
}

function updateEquipmentItemMaintenanceScheduleTotals() {
    var equipmentItemMaintenanceScheduleGrid = $("#equipmentItemMaintenanceScheduleGrid").data("kendoGrid");
    var totalEquipmentItemMaintenanceSchedules = equipmentItemMaintenanceScheduleGrid.dataSource.total();
    viewModel.set("totalEquipmentItemMaintenanceSchedules", totalEquipmentItemMaintenanceSchedules);
}

function refreshEquipmentItemMaintenanceScheduleGrid() {
    var equipmentItemMaintenanceScheduleGrid = $("#equipmentItemMaintenanceScheduleGrid").data("kendoGrid");
    equipmentItemMaintenanceScheduleGrid.dataSource.read();
}

function updateEquipmentItemMaintenanceSchedulePointsTotals() {
    var equipmentItemMaintenanceSchedulePointsGrid = $("#equipmentItemMaintenanceSchedulePointsGrid").data("kendoGrid");
    var totalEquipmentItemMaintenanceSchedulesPoints = equipmentItemMaintenanceSchedulePointsGrid.dataSource.total();
    viewModel.set("totalEquipmentItemMaintenanceSchedulesPoints", totalEquipmentItemMaintenanceSchedulesPoints);
}

function refreshEquipmentItemMaintenanceSchedulePointsGrid() {
    var equipmentItemMaintenanceSchedulePointsGrid = $("#equipmentItemMaintenanceSchedulePointsGrid").data("kendoGrid");
    equipmentItemMaintenanceSchedulePointsGrid.dataSource.read();
}

function updateEquipmentItemMaintenanceScheduleDaysTotals() {
    var equipmentItemMaintenanceScheduleDaysGrid = $("#equipmentItemMaintenanceScheduleDaysGrid").data("kendoGrid");
    var totalEquipmentItemMaintenanceSchedulesDays = equipmentItemMaintenanceScheduleDaysGrid.dataSource.total();
    viewModel.set("totalEquipmentItemMaintenanceSchedulesDays", totalEquipmentItemMaintenanceSchedulesDays);
}

function refreshEquipmentItemMaintenanceScheduleDaysGrid() {
    var equipmentItemMaintenanceScheduleDaysGrid = $("#equipmentItemMaintenanceScheduleDaysGrid").data("kendoGrid");
    equipmentItemMaintenanceScheduleDaysGrid.dataSource.read();
}

function maintenanceScheduleData() {
    return {
        days: viewModel.get("days"),
        points: viewModel.get("points")
    }
}

function daysChanged(e) {
    getSummaryByDays();
    refreshEquipmentItemMaintenanceScheduleDaysGrid();
}

function pointsChanged(e) {
    getSummaryByDays();
    refreshEquipmentItemMaintenanceSchedulePointsGrid();
}

function getSummaryByDays() {
    var days = viewModel.get("days");
    var points = viewModel.get("points");

    $.ajax({
        url: "/Maintenance/GetSummary",
        data: {
            days: days,
            points: points
        },
        success: function (result) {
            if (result && viewModel.get("upcomingMaintenance")) {
                refreshEquipmentItemMaintenanceScheduleGrid();
            }
        },
        dataType: "json"
    });
}

var viewModel = kendo.observable({

    days: 30,
    points: 70,
    totalEquipmentItemMaintenanceSchedules: 0,
    totalEquipmentItemMaintenanceSchedulesPoints: 0,
    totalEquipmentItemMaintenanceSchedulesDays: 0,
    upcomingMaintenance: "",

    daysAndPointsVisible: function(){
        var value = this.get("upcomingMaintenance");
        return value == "1";
    },

    pointsVisible: function(){
        var value = this.get("upcomingMaintenance");
        return value == "2";
    },

    daysVisible: function () {
        var value = this.get("upcomingMaintenance");
        return value == "3" || !value;
    }

});
kendo.bind(document.body.children, viewModel);